<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="185dp"
    android:height="185dp"
    android:viewportWidth="185"
    android:viewportHeight="185">
  <group>
    <clip-path android:pathData="M0,0h185v185h-185z M 0,0"/>
    <path
        android:pathData="M0,0h185v185h-185z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M92.5,92.5m-92.5,0a92.5,92.5 0,1 1,185 0a92.5,92.5 0,1 1,-185 0"
        android:fillColor="#E3F3FF"/>
    <path
        android:pathData="M104.509,104.884L95.763,108.967L90.068,86.92L80.893,51.421L85.341,51.098C88.349,50.883 91.078,52.903 91.852,55.89L104.509,104.884Z"
        android:fillColor="#969083"/>
    <path
        android:pathData="M90.111,87.565L90.154,87.758L94.387,69.385L87.232,76.627L90.111,87.565Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M87.124,85.802L85.512,86.189C78.4,87.865 70.922,86.834 64.518,83.288L59.554,58.447C58.673,54.021 60.199,49.465 63.573,46.457L71.652,39.279L80.785,43.212L94.065,65.646C94.903,67.902 94.495,70.438 93.012,72.329C91.443,74.285 90.24,76.519 89.445,78.883L87.124,85.802Z"
        android:fillColor="#969083"/>
    <path
        android:pathData="M87.124,85.802L85.512,86.189C78.4,87.865 70.922,86.834 64.518,83.288L59.554,58.447C58.673,54.021 60.199,49.465 63.573,46.457L71.652,39.279L80.785,43.212L88.134,55.611L94.065,65.625C94.903,67.881 94.495,70.416 93.012,72.308C91.443,74.263 90.24,76.498 89.445,78.862L87.124,85.802Z"
        android:fillColor="#969083"/>
    <path
        android:pathData="M84.825,56.686L72.254,48.026V30.856H80.807V43.212L84.825,56.686Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M77.433,180.438C75.005,180.266 72.662,179.557 70.578,178.332L67.462,176.506H67.419L69.353,169.264H60.994L56.825,184.951H60.994H65.163H83.256C82.418,182.415 80.097,180.61 77.433,180.438Z"
        android:fillColor="#E43C28"/>
    <path
        android:pathData="M92.346,180.438C89.918,180.266 87.575,179.557 85.491,178.332L82.375,176.506H82.332L84.266,169.264H75.907L71.738,184.951H75.907H80.076H98.191C97.331,182.415 95.032,180.61 92.346,180.438Z"
        android:fillColor="#E43C28"/>
    <path
        android:pathData="M58.759,115.736L64.131,137.848L59.232,170.382H70.385L79.41,139.567C79.925,137.805 80.033,135.914 79.689,134.087L76.487,116.703L58.759,115.736Z"
        android:fillColor="#EE927C"/>
    <path
        android:pathData="M73.737,115.736L79.109,137.848L74.231,170.36H85.383L94.409,139.545C94.924,137.783 95.032,135.892 94.688,134.066L91.465,116.703L73.737,115.736Z"
        android:fillColor="#EE927C"/>
    <path
        android:pathData="M91.465,116.703L73.737,115.714L73.952,116.552L58.759,115.714L61.617,127.426C72.018,130.284 83.084,129.703 93.141,125.75L91.465,116.703Z"
        android:fillColor="#E3503E"/>
    <path
        android:pathData="M69.031,170.36H60.693L59.361,175.367L69.031,170.36Z"
        android:fillColor="#E43C28"/>
    <path
        android:pathData="M59.898,165.891L59.232,170.36H70.384L72.168,163.935L59.898,165.891Z"
        android:fillColor="#E3503E"/>
    <path
        android:pathData="M81.344,137.826L81.15,139.094"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M80.763,141.694L77.82,161.184"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M77.626,162.474L77.433,163.763"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M94.516,139.352C93.098,140.77 90.777,140.77 89.359,139.352"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M60.972,115.736L61.273,116.982"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M61.854,119.346L66.366,137.826L62.82,161.27"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M62.649,162.474L62.455,163.763"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M79.539,139.352C78.12,140.77 75.8,140.77 74.381,139.352"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.965,170.36H75.606L74.274,175.367L83.965,170.36Z"
        android:fillColor="#E43C28"/>
    <path
        android:pathData="M79.882,177.881C80.463,177.516 83.041,176.592 83.407,177.172C83.772,177.752 81.773,179.643 81.193,180.009C80.613,180.374 79.84,180.18 79.474,179.6C79.109,179.02 79.302,178.246 79.882,177.881Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M70.384,170.36H57.19L57.964,166.213H71.545L70.384,170.36Z"
        android:fillColor="#ECE5D4"/>
    <path
        android:pathData="M64.948,177.881C65.528,177.516 68.107,176.592 68.472,177.172C68.837,177.752 66.839,179.643 66.259,180.009C65.678,180.374 64.905,180.18 64.54,179.6C64.196,179.02 64.368,178.246 64.948,177.881Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M68.751,181.104C68.493,180.46 68.042,177.752 68.687,177.516C69.331,177.258 70.836,179.557 71.072,180.202C71.33,180.846 71.008,181.577 70.363,181.813C69.718,182.05 69.009,181.749 68.751,181.104Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.664,181.104C83.407,180.46 82.955,177.752 83.6,177.516C84.245,177.258 85.749,179.557 85.985,180.202C86.243,180.846 85.921,181.577 85.276,181.813C84.653,182.05 83.922,181.749 83.664,181.104Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M73.35,160.282L79.431,139.567C79.947,137.805 80.054,135.914 79.711,134.087L78.636,128.242"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M74.94,165.891L74.274,170.36H85.426L87.21,163.935L74.94,165.891Z"
        android:fillColor="#E3503E"/>
    <path
        android:pathData="M85.362,170.36H72.168L72.942,166.213H86.522L85.362,170.36Z"
        android:fillColor="#ECE5D4"/>
    <path
        android:pathData="M83.256,184.951C82.418,182.394 80.098,180.61 77.433,180.438C75.005,180.266 72.662,179.557 70.578,178.332L67.462,176.506"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M72.168,170.36H83.965"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M58.308,170.36H70.084"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M85.835,82.966C89.531,91.239 91.981,100.006 93.098,108.989L94.731,122.355C83.041,127.426 69.869,127.834 57.878,123.515L55.45,122.634L55.171,114.21C54.784,102.306 58.587,90.637 65.894,81.225L85.835,82.966Z"
        android:fillColor="#969083"/>
    <path
        android:pathData="M86.63,93.775C88.005,98.996 88.929,104.347 89.359,109.762L89.831,115.629"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M76.616,124.074C70.298,124.288 63.938,123.321 57.878,121.151"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M88.134,32.425C89.558,32.425 90.713,31.27 90.713,29.846C90.713,28.422 89.558,27.268 88.134,27.268C86.71,27.268 85.555,28.422 85.555,29.846C85.555,31.27 86.71,32.425 88.134,32.425Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M86.479,27.912C88.414,27.912 89.982,26.344 89.982,24.41C89.982,22.475 88.414,20.907 86.479,20.907C84.545,20.907 82.977,22.475 82.977,24.41C82.977,26.344 84.545,27.912 86.479,27.912Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M84.868,24.56C86.802,24.56 88.37,22.992 88.37,21.057C88.37,19.123 86.802,17.555 84.868,17.555C82.933,17.555 81.365,19.123 81.365,21.057C81.365,22.992 82.933,24.56 84.868,24.56Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M90.283,32.726C90.111,32.511 90.004,32.253 90.004,31.995C90.004,30.577 88.843,29.417 87.425,29.417C86.157,29.417 85.126,30.319 84.889,31.522C83.041,32.296 81.795,34.23 82.031,36.4C82.268,38.571 84.008,40.333 86.2,40.569C89.058,40.891 91.465,38.657 91.465,35.863C91.486,34.66 91.035,33.564 90.283,32.726Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M78.701,38.162C84.053,38.162 88.392,33.823 88.392,28.471C88.392,23.118 84.053,18.779 78.701,18.779C73.348,18.779 69.009,23.118 69.009,28.471C69.009,33.823 73.348,38.162 78.701,38.162Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M73.737,28.106C76.941,28.106 79.538,25.508 79.538,22.304C79.538,19.099 76.941,16.502 73.737,16.502C70.532,16.502 67.935,19.099 67.935,22.304C67.935,25.508 70.532,28.106 73.737,28.106Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M81.945,29.309C82.16,28.729 82.719,28.278 83.385,28.256"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M84.717,28.278C85.341,28.149 85.985,28.385 86.372,28.944"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M80.506,36.013C79.173,36.185 77.949,35.24 77.777,33.908"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M45.329,44.372C45.827,44.372 46.232,43.968 46.232,43.47C46.232,42.972 45.827,42.567 45.329,42.567C44.831,42.567 44.426,42.972 44.426,43.47C44.426,43.968 44.831,44.372 45.329,44.372Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M52.914,41.514C53.247,41.514 53.516,41.245 53.516,40.913C53.516,40.58 53.247,40.311 52.914,40.311C52.582,40.311 52.313,40.58 52.313,40.913C52.313,41.245 52.582,41.514 52.914,41.514Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M42.449,36.765H41.805V36.121C41.805,35.992 41.719,35.906 41.59,35.906C41.461,35.906 41.375,35.992 41.375,36.121V36.765H40.73C40.601,36.765 40.515,36.852 40.515,36.98C40.515,37.109 40.601,37.195 40.73,37.195H41.375V37.84C41.375,37.969 41.461,38.055 41.59,38.055C41.719,38.055 41.805,37.969 41.805,37.84V37.195H42.449C42.578,37.195 42.664,37.109 42.664,36.98C42.664,36.852 42.578,36.765 42.449,36.765Z"
        android:fillColor="#F0917A"/>
    <path
        android:pathData="M138.031,23.915C138.766,23.915 139.363,23.319 139.363,22.583C139.363,21.847 138.766,21.251 138.031,21.251C137.295,21.251 136.698,21.847 136.698,22.583C136.698,23.319 137.295,23.915 138.031,23.915Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#EDA257"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M143.832,35.455C144.153,35.455 144.413,35.195 144.413,34.874C144.413,34.554 144.153,34.294 143.832,34.294C143.512,34.294 143.252,34.554 143.252,34.874C143.252,35.195 143.512,35.455 143.832,35.455Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#EDA257"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M85.19,30.663C85.19,30.907 85.029,31.071 84.868,31.071C84.707,31.071 84.546,30.907 84.546,30.663C84.546,30.418 84.707,30.254 84.868,30.254C85.029,30.254 85.19,30.418 85.19,30.663Z"
        android:strokeWidth="0.214886"
        android:fillColor="#161E24"
        android:strokeColor="#161E24"/>
    <path
        android:pathData="M83.858,30.663C83.858,30.907 83.696,31.071 83.535,31.071C83.375,31.071 83.213,30.907 83.213,30.663C83.213,30.418 83.375,30.254 83.535,30.254C83.696,30.254 83.858,30.418 83.858,30.663Z"
        android:strokeWidth="0.214886"
        android:fillColor="#161E24"
        android:strokeColor="#161E24"/>
    <path
        android:pathData="M88.22,19.596C87.704,19.833 87.124,19.94 86.501,19.875C84.997,19.704 83.793,18.479 83.664,16.953C83.621,16.545 83.664,16.158 83.772,15.793C83.879,15.427 83.621,15.062 83.235,15.062C79.195,15.062 75.928,17.748 75.928,21.057C75.928,24.367 79.195,27.053 83.235,27.053C87.274,27.053 90.541,24.367 90.541,21.057V21.036C90.519,19.854 89.273,19.08 88.22,19.596Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M67.505,32.897C70.947,32.897 73.737,30.107 73.737,26.666C73.737,23.224 70.947,20.434 67.505,20.434C64.063,20.434 61.273,23.224 61.273,26.666C61.273,30.107 64.063,32.897 67.505,32.897Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M74.253,32.425C75.926,32.425 77.282,31.068 77.282,29.395C77.282,27.722 75.926,26.365 74.253,26.365C72.579,26.365 71.223,27.722 71.223,29.395C71.223,31.068 72.579,32.425 74.253,32.425Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M75.864,27.504C77.419,27.504 78.679,26.244 78.679,24.689C78.679,23.134 77.419,21.874 75.864,21.874C74.309,21.874 73.049,23.134 73.049,24.689C73.049,26.244 74.309,27.504 75.864,27.504Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M80.807,25.548C84.011,25.548 86.608,22.951 86.608,19.747C86.608,16.542 84.011,13.945 80.807,13.945C77.602,13.945 75.005,16.542 75.005,19.747C75.005,22.951 77.602,25.548 80.807,25.548Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M67.526,38.915C69.841,38.915 71.717,37.038 71.717,34.724C71.717,32.41 69.841,30.534 67.526,30.534C65.212,30.534 63.336,32.41 63.336,34.724C63.336,37.038 65.212,38.915 67.526,38.915Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M70.449,37.109C72.538,37.109 74.231,35.416 74.231,33.327C74.231,31.239 72.538,29.545 70.449,29.545C68.36,29.545 66.667,31.239 66.667,33.327C66.667,35.416 68.36,37.109 70.449,37.109Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M77.304,18.93C77.304,17.77 78.249,16.824 79.41,16.824C80.57,16.824 81.516,17.77 81.516,18.93C81.516,19.166 81.473,19.403 81.387,19.639"
        android:strokeLineJoin="round"
        android:strokeWidth="0.214886"
        android:fillColor="#00000000"
        android:strokeColor="#FFF4EE"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M73.243,27.891C72.899,28.449 72.168,28.643 71.609,28.299C71.051,27.955 70.857,27.225 71.201,26.666"
        android:strokeLineJoin="round"
        android:strokeWidth="0.214886"
        android:fillColor="#00000000"
        android:strokeColor="#FFF4EE"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M72.254,25.914C71.76,26.752 70.664,27.01 69.826,26.515C68.988,26.021 68.73,24.925 69.224,24.087C69.332,23.915 69.461,23.765 69.611,23.636"
        android:strokeLineJoin="round"
        android:strokeWidth="0.214886"
        android:fillColor="#00000000"
        android:strokeColor="#FFF4EE"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M82.332,18.006C83.235,18.006 84.008,18.586 84.309,19.381"
        android:strokeLineJoin="round"
        android:strokeWidth="0.214886"
        android:fillColor="#00000000"
        android:strokeColor="#FFF4EE"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M66.194,31.221C65.399,30.792 64.991,29.932 65.099,29.073"
        android:strokeLineJoin="round"
        android:strokeWidth="0.214886"
        android:fillColor="#00000000"
        android:strokeColor="#FFF4EE"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M77.777,24.71C78.959,26.129 80.828,27.139 83.041,27.396"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#CB765E"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M81.322,65.625L82.676,68.096C83.514,70.331 83.106,72.845 81.623,74.714"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M85.512,86.189C78.4,87.865 70.922,86.834 64.518,83.288L64.497,83.159C64.002,83.868 63.508,84.599 63.035,85.329C70.234,89.283 78.615,90.422 86.608,88.531L87.984,88.209C87.683,87.414 87.36,86.597 87.038,85.802L85.512,86.189Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M73.328,89.67L70.836,89.198L70.341,84.986L72.834,85.459L73.328,89.67Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M70.814,89.198L73.328,89.67L75.305,85.931L72.813,85.459L70.814,89.198Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M72.254,38.162L84.825,56.685L86.759,65.624L80.033,57.523L67.527,53.72L71.352,46.543L65.636,43.212L72.254,38.162Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M71.824,101.704H69.955L70.879,88.832H72.748L71.824,101.704Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M73.672,95.752H75.542L74.596,88.832H72.748L73.672,95.752Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M72.254,38.162L84.825,56.685L86.759,65.624L80.398,56.685L68.988,52.731L72.748,46.328L65.636,43.212L72.254,38.162Z"
        android:fillColor="#969083"/>
    <path
        android:pathData="M53.495,184.951H100.598"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M50.55,88.102C47.714,86.748 46.511,83.331 47.886,80.495L62.004,51.055L72.275,55.976L68.15,64.55L58.158,85.416C56.782,88.274 53.387,89.477 50.55,88.102Z"
        android:fillColor="#969083"/>
    <path
        android:pathData="M65.915,105.894C65.485,108.623 63.53,110.858 60.887,111.653L59.919,111.954L47.907,85.566C47.005,83.331 48.079,80.774 50.314,79.85L57.104,77.1L65.915,105.894Z"
        android:fillColor="#969083"/>
    <path
        android:pathData="M66.903,104.175C66.28,108.064 63.487,111.266 59.726,112.427L58.329,112.856"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M60.048,112.233L56.008,103.359C55.793,104.562 55.6,105.787 55.471,107.033L60.048,112.233Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M58.329,81.247L64.905,102.585"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M87.984,70.18L84.846,86.34"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M84.717,125.255V119.668H90.305C90.305,122.763 87.812,125.255 84.717,125.255Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M83.192,84.126C83.714,84.126 84.137,83.703 84.137,83.181C84.137,82.658 83.714,82.235 83.192,82.235C82.669,82.235 82.246,82.658 82.246,83.181C82.246,83.703 82.669,84.126 83.192,84.126Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M87.232,84.126C87.754,84.126 88.177,83.703 88.177,83.181C88.177,82.658 87.754,82.235 87.232,82.235C86.709,82.235 86.286,82.658 86.286,83.181C86.286,83.703 86.709,84.126 87.232,84.126Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M87.769,92.142C88.291,92.142 88.714,91.718 88.714,91.196C88.714,90.674 88.291,90.251 87.769,90.251C87.247,90.251 86.823,90.674 86.823,91.196C86.823,91.718 87.247,92.142 87.769,92.142Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.428,92.851C83.95,92.851 84.373,92.427 84.373,91.905C84.373,91.383 83.95,90.96 83.428,90.96C82.906,90.96 82.482,91.383 82.482,91.905C82.482,92.427 82.906,92.851 83.428,92.851Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M88.177,54.494L92.604,63.132"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M72.254,38.162L84.825,56.685L87.232,66.849"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M85.341,58.791L88.693,51.894L84.825,56.686L85.341,58.791Z"
        android:fillColor="#969083"/>
    <path
        android:pathData="M80.806,43.212L89.122,51.378L84.825,56.686L80.806,43.212Z"
        android:fillColor="#969083"/>
    <path
        android:pathData="M84.825,56.686L80.806,43.212"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M88.156,26.365C88.306,27.031 88.392,27.74 88.392,28.449C88.392,33.8 84.051,38.141 78.701,38.141"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M73.737,36.787C70.9,35.089 69.009,31.995 69.009,28.449"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M63.594,78.561L67.913,65.087L62.95,75.402L63.594,78.561Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M49.003,85.588C48.982,85.502 48.939,85.437 48.917,85.351C48.23,83.869 48.208,82.106 48.96,80.516L60.736,54.494L48.165,80.516C47.413,82.106 47.435,83.869 48.122,85.351C48.144,85.437 48.165,85.502 48.208,85.588L60.22,111.976L60.929,111.761L49.003,85.588Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M60.349,54.494L47.864,80.516C47.112,82.106 47.134,83.869 47.821,85.351C47.843,85.437 47.864,85.502 47.907,85.588L59.919,111.976"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M68.752,64.529L67.054,65.947"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M78.701,38.162C77.712,38.162 76.767,38.012 75.885,37.754L80.828,41.407V37.926C80.14,38.076 79.431,38.162 78.701,38.162Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M90.519,122.204V124.912"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M85.749,32.468C85.749,33.091 84.975,33.585 84.03,33.585C83.084,33.585 82.311,33.091 82.311,32.468"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M81.731,33.499C81.344,33.499 81.043,33.199 81.043,32.812C81.043,32.425 81.344,32.124 81.731,32.124"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M114.393,178.998C115.64,178.44 116.478,177.215 116.585,175.797L115.833,159.594H108.312L110.267,176.14C110.267,178.375 112.438,179.879 114.393,178.998Z"
        android:fillColor="#0D9AFF"/>
    <path
        android:pathData="M121.098,178.998C119.851,178.44 119.013,177.215 118.906,175.797L119.658,159.594H127.179L125.223,176.14C125.223,178.375 123.053,179.879 121.098,178.998Z"
        android:fillColor="#0D9AFF"/>
    <path
        android:pathData="M109.343,168.362C111.256,169.5 113.577,169.586 115.532,168.576C115.769,168.448 116.005,168.319 116.198,168.147L115.812,159.616H108.291L109.343,168.362Z"
        android:fillColor="#007DD6"/>
    <path
        android:pathData="M119.271,168.19C119.465,168.34 119.658,168.448 119.873,168.555C121.871,169.565 124.214,169.479 126.148,168.297L127.179,159.594H119.658L119.271,168.19Z"
        android:fillColor="#007DD6"/>
    <path
        android:pathData="M138.031,184.951H123.44V182.394C123.44,181.577 122.86,180.846 122.043,180.761C121.076,180.653 120.281,181.384 120.281,182.329V184.951H117.724V177.645C117.724,176.678 118.648,175.99 119.572,176.248L134.894,180.739C136.742,181.319 138.031,183.017 138.031,184.951Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M97.482,184.951H112.073V182.394C112.073,181.577 112.653,180.846 113.469,180.761C114.436,180.653 115.232,181.384 115.232,182.329V184.951H117.789V177.645C117.789,176.678 116.865,175.99 115.941,176.248L100.619,180.739C98.75,181.319 97.482,183.017 97.482,184.951Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M108.613,181.534C108.72,182.179 109.3,182.609 109.945,182.523C110.59,182.437 111.02,181.835 110.934,181.19C110.826,180.546 109.365,178.633 109.365,178.633C109.365,178.633 108.505,180.89 108.613,181.534Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M106.4,179.342C105.884,179.729 105.777,180.46 106.163,180.975C106.55,181.491 107.281,181.598 107.797,181.212C108.312,180.825 109.301,178.612 109.301,178.612C109.301,178.612 106.915,178.955 106.4,179.342Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M115.081,171.155C114.479,171.155 113.985,171.65 113.985,172.251"
        android:strokeAlpha="0.7"
        android:strokeLineJoin="round"
        android:strokeWidth="1.07443"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.7"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M113.211,175.045C112.825,174.594 112.116,174.529 111.664,174.937"
        android:strokeAlpha="0.7"
        android:strokeLineJoin="round"
        android:strokeWidth="1.07443"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.7"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M111.793,170.274C111.707,169.672 111.127,169.264 110.525,169.35"
        android:strokeAlpha="0.7"
        android:strokeLineJoin="round"
        android:strokeWidth="1.07443"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.7"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M126.9,181.534C126.792,182.179 126.212,182.609 125.568,182.523C124.923,182.437 124.493,181.835 124.579,181.19C124.686,180.546 126.148,178.633 126.148,178.633C126.148,178.633 126.986,180.89 126.9,181.534Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M129.092,179.342C129.607,179.729 129.715,180.46 129.328,180.975C128.941,181.491 128.211,181.598 127.695,181.212C127.179,180.825 126.191,178.612 126.191,178.612C126.191,178.612 128.576,178.955 129.092,179.342Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M120.475,171.692C120.904,171.263 121.592,171.263 122.022,171.692"
        android:strokeAlpha="0.7"
        android:strokeLineJoin="round"
        android:strokeWidth="1.07443"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.7"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M122.58,174.379C123.182,174.25 123.762,174.615 123.891,175.217"
        android:strokeAlpha="0.7"
        android:strokeLineJoin="round"
        android:strokeWidth="1.07443"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.7"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M123.161,169.028C123.719,168.77 124.364,169.006 124.622,169.543"
        android:strokeAlpha="0.7"
        android:strokeLineJoin="round"
        android:strokeWidth="1.07443"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.7"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M117.789,179.127V184.22"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M131.885,90.186L129.22,85.717L127.05,86.855C121.205,89.907 114.243,89.907 108.398,86.855L106.227,85.717L103.563,90.186C98.212,99.147 96.3,109.977 98.234,120.442L107.323,163.677C107.495,164.494 107.925,165.224 108.57,165.74C110.611,167.395 113.297,167.653 115.554,166.492C116.155,166.17 116.692,165.719 117.144,165.203L117.724,164.515L118.304,165.203C118.755,165.719 119.271,166.17 119.894,166.492C122.129,167.631 124.837,167.373 126.878,165.74C127.501,165.224 127.931,164.515 128.124,163.677L137.236,120.442C139.148,109.999 137.236,99.147 131.885,90.186Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M108.376,86.855L106.206,85.717L103.541,90.186C103.198,90.788 102.854,91.368 102.531,91.991L100.34,104.369L104.229,111.954L111.105,88.016C110.181,87.693 109.257,87.307 108.376,86.855Z"
        android:strokeAlpha="0.5"
        android:fillColor="#161E24"
        android:fillAlpha="0.5"/>
    <path
        android:pathData="M103.004,111.954L95.634,112.234L100.662,64.787C101.35,60.188 105.024,56.6 109.666,56.062L113.512,55.611L103.004,111.954Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M103.004,111.954L95.634,112.234L100.662,64.787C101.35,60.188 105.024,56.6 109.666,56.062L113.512,55.611L103.004,111.954Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="55.6111"
            android:startX="104.573"
            android:endY="112.234"
            android:endX="104.573"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M58.308,183.812H80.742"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M86.909,183.812H94.882"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M106.185,85.781L106.228,85.717L108.398,86.856C114.243,89.907 121.205,89.907 127.05,86.856L129.221,85.717L129.285,85.824L132.358,69.901C134.184,61.284 127.824,52.947 119.013,52.667C118.842,52.667 118.67,52.667 118.498,52.667H115.618C108.076,52.667 102.188,58.469 102.961,65.152L106.185,85.781Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M106.185,85.781L106.228,85.717L108.398,86.856C114.243,89.907 121.205,89.907 127.05,86.856L129.221,85.717L129.285,85.824L132.358,69.901C134.184,61.284 127.824,52.947 119.013,52.667C118.842,52.667 118.67,52.667 118.498,52.667H115.618C108.076,52.667 102.188,58.469 102.961,65.152L106.185,85.781Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="52.6672"
            android:startX="117.783"
            android:endY="89.1441"
            android:endX="117.783"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M106.056,97.643L104.229,71.835L108.377,86.856L106.056,97.643Z"
        android:fillColor="#E7E7E7"/>
    <path
        android:pathData="M64.389,137.913L62.95,140.018"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M64.089,139.803L62.499,143.242"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M111.32,38.721C111.041,34.896 108.334,32.017 105.261,32.253C102.209,32.511 99.953,35.799 100.232,39.624C100.275,40.29 100.404,40.935 100.598,41.536C101.522,44.502 98.836,47.381 96.042,46.35C95.956,46.328 95.87,46.285 95.784,46.242C95.72,46.221 95.677,46.199 95.612,46.156C96.3,46.93 97.16,47.574 98.148,47.983C101.049,49.229 104.251,48.348 106.249,46.049H106.271C109.365,45.834 111.6,42.546 111.32,38.721Z"
        android:fillColor="#0D9AFF"/>
    <path
        android:pathData="M116.048,40.612C115.769,36.787 113.061,33.908 109.988,34.144C106.937,34.402 104.681,37.69 104.96,41.515C105.003,42.181 105.132,42.826 105.325,43.427C106.249,46.393 103.563,49.272 100.77,48.241C100.684,48.219 100.598,48.176 100.512,48.133C100.447,48.112 100.404,48.09 100.34,48.047C101.027,48.821 101.887,49.466 102.875,49.874C105.776,51.12 108.978,50.239 110.977,47.94H110.998C114.071,47.725 116.327,44.416 116.048,40.612Z"
        android:fillColor="#529ED6"/>
    <path
        android:pathData="M109.988,34.144C109.838,34.166 109.687,34.187 109.558,34.209C108.871,33.392 108.033,32.79 107.109,32.489C103.8,34.08 101.522,37.475 101.522,41.386C101.522,45.318 103.821,48.735 107.173,50.304C108.634,49.981 109.988,49.165 111.02,47.983H111.041C114.093,47.725 116.349,44.437 116.07,40.612C115.747,36.787 113.04,33.886 109.988,34.144Z"
        android:fillColor="#0D9AFF"/>
    <path
        android:pathData="M119.873,56.686H111.815L110.203,45.468H118.261L119.873,56.686Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M111.32,38.721C111.041,34.896 108.334,32.017 105.261,32.253C102.209,32.511 99.953,35.799 100.232,39.624C100.275,40.29 100.404,40.935 100.598,41.536C101.522,44.502 98.836,47.381 96.042,46.35C95.956,46.328 95.87,46.285 95.784,46.242C95.72,46.221 95.677,46.199 95.612,46.156C96.3,46.93 97.16,47.574 98.148,47.983"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M103.284,48.176C102.532,48.52 101.672,48.585 100.791,48.262C100.705,48.241 100.619,48.198 100.533,48.155C100.469,48.133 100.426,48.112 100.361,48.069"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M102.897,49.917C105.798,51.163 109,50.282 110.998,47.983H111.02C114.071,47.725 116.327,44.437 116.048,40.612C115.769,36.787 113.061,33.908 109.988,34.144C106.937,34.402 104.681,37.69 104.96,41.515C105.003,42.181 105.132,42.826 105.325,43.427"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M118.175,32.232C118.39,30.942 119.529,29.997 120.84,29.997"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M118.175,31.329C119.035,30.341 119.035,28.858 118.197,27.848"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M111.385,49.745C116.737,49.745 121.076,45.406 121.076,40.054C121.076,34.701 116.737,30.362 111.385,30.362C106.033,30.362 101.694,34.701 101.694,40.054C101.694,45.406 106.033,49.745 111.385,49.745Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M104.014,38.936C104.014,39.559 104.509,40.053 105.132,40.053C105.755,40.053 106.249,39.559 106.249,38.936"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M108.205,39.301C108.205,39.925 108.699,40.419 109.322,40.419C109.945,40.419 110.44,39.925 110.44,39.301"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M115.361,43.921C115.554,43.341 115.253,42.697 114.651,42.503"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#F0917A"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M108.742,37.045C108.978,37.282 109.279,37.432 109.623,37.496"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M106.679,37.045C106.443,37.282 106.142,37.432 105.798,37.496"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M111.342,46.543C112.524,46.543 113.491,45.576 113.491,44.394"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M107.109,39.129L107.088,39.194C106.443,40.934 106.163,42.783 106.292,44.631C106.314,44.867 106.421,45.082 106.593,45.211C106.916,45.468 107.345,45.533 107.732,45.383L108.721,44.996"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M111.922,49.723C111.75,49.723 111.557,49.745 111.385,49.745C106.034,49.745 101.694,45.404 101.694,40.054C101.694,38.077 102.274,36.25 103.284,34.724"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M115.038,30.663C109.945,30.663 110.311,35.799 112.846,35.992C116.349,36.25 117.617,38.85 117.617,42.288C117.617,45.727 114.823,48.52 111.385,48.52C111.299,48.52 111.235,48.52 111.149,48.52C112.331,49.057 113.641,49.358 115.038,49.358C120.195,49.358 124.386,45.168 124.386,40.011C124.386,34.853 120.195,30.663 115.038,30.663Z"
        android:fillColor="#0D9AFF"/>
    <path
        android:pathData="M116.199,30.749C120.324,31.716 123.419,35.476 123.419,39.946C123.419,44.996 119.486,49.121 114.565,49.379C114.716,49.379 114.888,49.401 115.038,49.401C120.195,49.401 124.386,45.211 124.386,40.053C124.386,35.261 120.819,31.308 116.199,30.749Z"
        android:fillColor="#529ED6"/>
    <path
        android:pathData="M112.846,35.992C116.349,36.25 117.617,38.85 117.617,42.288C117.617,45.727 114.823,48.52 111.385,48.52C111.299,48.52 111.235,48.52 111.149,48.52C112.331,49.057 113.641,49.358 115.038,49.358C120.195,49.358 124.386,45.168 124.386,40.011C124.386,35.498 121.184,31.716 116.929,30.856"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M115.812,49.38C119.83,48.327 122.817,44.674 122.817,40.333C122.817,37.282 121.356,34.574 119.078,32.855"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M118.519,45.49C119.637,43.943 120.303,42.052 120.303,40.011C120.303,38.205 119.787,36.508 118.906,35.09"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M110.934,29.632C106.271,29.868 102.295,26.279 102.059,21.616C102.059,21.509 102.059,21.401 102.059,21.315C100.447,23.013 99.523,25.377 99.76,27.934C100.147,32.167 103.692,35.519 107.947,35.627C109.645,35.67 111.235,35.219 112.567,34.424C114.866,33.027 113.727,29.503 111.041,29.653C110.977,29.632 110.955,29.632 110.934,29.632Z"
        android:fillColor="#0D9AFF"/>
    <path
        android:pathData="M110.998,29.632C110.977,29.632 110.955,29.632 110.934,29.632C106.271,29.868 102.295,26.279 102.059,21.616C102.059,21.509 102.059,21.401 102.059,21.315C102.016,21.358 101.973,21.401 101.952,21.444C101.952,21.487 101.93,21.53 101.93,21.595C101.242,26.215 104.444,30.513 109.064,31.2C109.086,31.2 109.107,31.2 109.129,31.2C111.17,31.479 111.901,33.693 110.998,35.133C111.536,34.939 112.073,34.703 112.545,34.402C114.823,33.005 113.684,29.481 110.998,29.632Z"
        android:fillColor="#529ED6"/>
    <path
        android:pathData="M102.038,21.315C100.426,23.013 99.502,25.377 99.738,27.934C100.125,32.167 103.671,35.519 107.925,35.627"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M101.307,27.569C101.586,30.727 103.628,33.37 106.4,34.574"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M113.942,27.139C112.825,27.031 111.772,27.332 110.912,27.891C109.924,28.557 108.549,28.256 107.904,27.268C107.431,26.537 107.152,25.699 107.109,24.775C107.109,24.711 107.109,24.646 107.109,24.582C106.099,25.635 105.562,27.117 105.734,28.686C105.948,30.577 107.238,32.146 108.935,32.812C109.408,34.896 111.278,36.444 113.491,36.444C116.177,36.444 118.326,34.187 118.154,31.458C118.004,29.202 116.199,27.354 113.942,27.139Z"
        android:fillColor="#0D9AFF"/>
    <path
        android:pathData="M109.322,33.908C110.096,35.433 111.665,36.465 113.491,36.465C114.501,36.465 115.425,36.143 116.177,35.605"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M107.109,24.603C106.099,25.656 105.562,27.139 105.734,28.707C105.841,29.588 106.185,30.405 106.679,31.093"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M107.259,27.289C106.98,28.708 107.302,30.255 108.312,31.501C108.871,32.189 109.602,32.704 110.397,33.005"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M110.053,29.632C109.773,31.05 110.096,32.597 111.106,33.843C111.664,34.531 112.395,35.047 113.19,35.348"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M118.154,31.48C118.003,29.202 116.198,27.354 113.942,27.139"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M117.724,123.515V162.066"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M120.754,105.078L117.724,108.108L114.673,105.078"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M117.724,118.659V108.108"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M129.135,139.18C128.339,140.534 126.599,140.985 125.245,140.19"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M106.872,139.18C107.667,140.534 109.408,140.985 110.762,140.19"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M119.358,91.347V92.636"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M119.357,95.408V99.555"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M119.357,100.952V102.242"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M127.738,91.841L130.553,100.308C131.176,102.177 132.938,103.445 134.893,103.445H135.624"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M122.151,64.572H112.03L109.988,50.476H120.109L122.151,64.572Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M122.151,64.572H112.03L109.988,50.476H120.109L122.151,64.572Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="50.4756"
            android:startX="116.07"
            android:endY="64.5721"
            android:endX="116.07"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M143.854,110.257L145.81,108.538C147.206,107.291 148.13,105.594 148.388,103.746C148.496,103.015 149.183,102.499 149.914,102.607C150.645,102.714 151.16,103.402 151.053,104.132C150.709,106.625 149.463,108.903 147.593,110.557L145.638,112.277C145.423,112.47 145.144,112.577 144.864,112.599C144.456,112.642 144.026,112.491 143.725,112.148C143.231,111.589 143.296,110.751 143.854,110.257Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M153.159,108.387L147.314,111.955C146.669,112.341 145.853,112.148 145.466,111.503L145.401,111.396C145.015,110.751 145.208,109.935 145.853,109.548L151.697,105.981C152.342,105.594 153.159,105.787 153.546,106.432L153.61,106.539C153.997,107.163 153.803,108.001 153.159,108.387Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M145.896,109.505L151.633,106.002C152.299,105.594 153.18,105.809 153.567,106.475"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M153.718,111.116L147.164,113.093C146.455,113.308 145.702,112.9 145.488,112.191L145.445,112.062C145.23,111.353 145.638,110.6 146.347,110.386L152.901,108.409C153.61,108.194 154.362,108.602 154.577,109.311L154.62,109.44C154.835,110.149 154.448,110.901 153.718,111.116Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M147.078,113.609L145.423,114.619C144.284,115.307 142.801,114.963 142.114,113.824L141.985,113.631C141.297,112.492 141.641,111.009 142.78,110.321L144.434,109.311C145.573,108.624 147.056,108.968 147.744,110.106L147.873,110.3C148.582,111.439 148.216,112.921 147.078,113.609Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M151.784,108.753L146.863,110.364"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M146.003,110.472L143.983,102.07L139.127,104.691L142.522,112.835L146.003,110.472Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M146.669,117.67L142.608,113.91C142.2,113.544 142.178,112.9 142.565,112.513L142.629,112.449C142.995,112.04 143.639,112.019 144.026,112.406L148.088,116.188C148.496,116.553 148.517,117.198 148.131,117.584L148.066,117.649C147.701,118.036 147.078,118.057 146.669,117.67Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M144.929,113.222L146.734,114.898"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M140.824,89.026C140.674,88.51 140.695,87.952 140.867,87.436C140.91,87.307 140.932,87.157 140.932,87.006C140.932,86.512 140.674,86.061 140.287,85.803C139.9,85.523 139.621,85.115 139.492,84.642L132.207,60.812C131.563,58.684 129.07,57.545 126.663,58.276C124.257,59.007 122.838,61.349 123.483,63.476L126.341,71.448L140.438,110.773L145.767,105.164L140.824,89.026Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M140.824,89.026C140.674,88.51 140.695,87.952 140.867,87.436C140.91,87.307 140.932,87.157 140.932,87.006C140.932,86.512 140.674,86.061 140.287,85.803C139.9,85.523 139.621,85.115 139.492,84.642L132.207,60.812C131.563,58.684 129.07,57.545 126.663,58.276C124.257,59.007 122.838,61.349 123.483,63.476L126.341,71.448L140.438,110.773L145.767,105.164L140.824,89.026Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="58.0547"
            android:startX="134.547"
            android:endY="110.773"
            android:endX="134.547"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M135.861,86.34L133.604,88.661L136.591,89.091"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#ECE5D4"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M112.137,72.093C110.762,72.093 109.623,71.083 109.451,69.751C109.43,69.88 109.43,69.987 109.43,70.116C109.43,71.534 110.569,72.673 111.987,72.673C113.147,72.673 114.136,71.9 114.437,70.825C113.964,71.577 113.104,72.093 112.137,72.093Z"
        android:fillColor="#E7E7E7"/>
    <path
        android:pathData="M123.676,72.222C125.052,72.222 126.191,71.212 126.363,69.88C126.384,70.009 126.384,70.116 126.384,70.245C126.384,71.663 125.245,72.802 123.827,72.802C122.667,72.802 121.678,72.029 121.377,70.954C121.871,71.706 122.709,72.222 123.676,72.222Z"
        android:fillColor="#E7E7E7"/>
    <path
        android:pathData="M130.037,81.827L132.057,71.341L131.434,85.717L130.037,81.827Z"
        android:fillColor="#E7E7E7"/>
    <path
        android:pathData="M131.842,71.835L130.66,78.561"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M103.928,71.341L106.314,80.022"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M100.453,67.543L99.433,76.488"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M111.385,51.593L111.75,54.107"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#ECE5D4"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M113.125,51.593L113.491,54.107"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#ECE5D4"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M114.866,51.593L115.232,54.107"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#ECE5D4"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M116.607,51.593L116.972,54.107"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#ECE5D4"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M118.347,51.593L118.713,54.107"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#ECE5D4"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M110.139,51.593L110.568,54.494"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M120.324,51.894L120.475,53.054"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M123.891,62.015C122.151,62.015 120.733,63.433 120.733,65.174H121.055C121.055,63.605 122.344,62.337 123.913,62.337C125.482,62.337 126.749,63.605 126.771,65.174H127.093C127.05,63.412 125.632,62.015 123.891,62.015Z"
        android:strokeAlpha="0.8"
        android:fillColor="#E43C28"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M126.427,65.152H126.749C126.749,63.583 125.46,62.316 123.891,62.316C122.323,62.316 121.055,63.583 121.033,65.152H121.356C121.356,63.755 122.495,62.638 123.891,62.638C125.288,62.638 126.427,63.755 126.427,65.152Z"
        android:strokeAlpha="0.8"
        android:fillColor="#EDA257"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M123.891,62.638C122.495,62.638 121.356,63.777 121.356,65.152H121.678C121.678,63.927 122.688,62.939 123.891,62.939C125.116,62.939 126.105,63.927 126.105,65.152H126.427C126.427,63.755 125.288,62.638 123.891,62.638Z"
        android:strokeAlpha="0.8"
        android:fillColor="#FCBA00"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M123.891,62.96C122.666,62.96 121.678,63.949 121.678,65.174H122C122,64.121 122.86,63.283 123.913,63.283C124.966,63.283 125.804,64.121 125.825,65.174H126.148C126.105,63.927 125.116,62.96 123.891,62.96Z"
        android:strokeAlpha="0.8"
        android:fillColor="#378C4D"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M123.891,63.261C122.838,63.261 122,64.099 121.979,65.152H122.301C122.301,64.271 123.032,63.583 123.891,63.583C124.772,63.583 125.481,64.292 125.481,65.152H125.804C125.782,64.12 124.944,63.261 123.891,63.261Z"
        android:strokeAlpha="0.8"
        android:fillColor="#529ED6"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M123.891,63.583C123.01,63.583 122.301,64.293 122.301,65.152H122.624C122.624,64.443 123.204,63.884 123.913,63.884C124.622,63.884 125.181,64.443 125.202,65.152H125.525C125.482,64.271 124.772,63.583 123.891,63.583Z"
        android:strokeAlpha="0.8"
        android:fillColor="#771C73"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M129.221,85.717L127.05,86.856C121.205,89.907 113.663,89.606 107.818,86.576"
        android:strokeLineJoin="round"
        android:strokeWidth="1.07443"
        android:fillColor="#00000000"
        android:strokeColor="#F0917A"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M145.316,107.592L144.929,106.045L141.297,109.913L145.316,107.592Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M143.897,105.078L138.847,109.913"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M123.311,91.196L122.581,89.434"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M124.622,89.972L123.892,89.091"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M112.631,90.96L112.975,89.671"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M90.906,90.186L95.763,108.967L104.509,104.885L97.697,78.561L90.906,90.186Z"
        android:fillColor="#969083"/>
    <path
        android:pathData="M95.87,114.619L95.483,114.318C94.323,113.373 94.13,111.675 95.075,110.515L96.493,108.731C97.589,107.356 99.609,107.141 100.985,108.237L95.87,114.619Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M105.927,118.358L101.436,120.142L97.009,109.097V109.075C97.095,108.366 97.546,107.743 98.212,107.442C100.103,106.604 102.446,107.055 104.444,107.915C104.358,109.225 104.53,110.536 105.003,111.761L106.765,116.446C107.044,117.219 106.679,118.057 105.927,118.358Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M103.499,121.452L101.285,117.348C100.92,116.66 101.178,115.779 101.865,115.414C102.553,115.049 103.434,115.306 103.799,115.994L105.991,120.098C106.357,120.786 106.099,121.667 105.411,122.032C104.723,122.398 103.864,122.14 103.499,121.452Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M94.882,105.529L95.376,107.442L104.1,104.713L94.882,105.529Z"
        android:fillColor="#969083"/>
    <path
        android:pathData="M96.622,74.371L102.145,95.752"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M104.616,110.429C104.444,109.612 104.401,108.774 104.444,107.936C104.122,107.807 103.799,107.678 103.455,107.571L97.288,108.022C96.987,108.215 96.708,108.452 96.472,108.731L95.118,110.429H104.616Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M106.142,107.872L95.311,110.106L94.538,107.549L105.089,104.369L106.142,107.872Z"
        android:fillColor="#969083"/>
    <path
        android:pathData="M94.559,107.549L105.11,104.369"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M106.378,119.798L100.533,123.365C99.889,123.752 99.072,123.558 98.685,122.914L98.621,122.806C98.234,122.162 98.427,121.345 99.072,120.958L104.917,117.391C105.562,117.004 106.378,117.198 106.765,117.842L106.829,117.95C107.216,118.594 107.001,119.411 106.378,119.798Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M105.175,117.413L99.33,120.98C98.685,121.366 97.868,121.173 97.482,120.528L97.417,120.421C97.03,119.776 97.224,118.96 97.868,118.573L103.713,115.006C104.358,114.619 105.175,114.812 105.561,115.457L105.626,115.565C106.013,116.209 105.819,117.026 105.175,117.413Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M103.735,115.049L97.89,118.616C97.246,119.003 96.429,118.809 96.042,118.165L95.978,118.057C95.591,117.413 95.784,116.596 96.429,116.209L102.274,112.642C102.919,112.255 103.735,112.449 104.122,113.093L104.186,113.201C104.573,113.824 104.358,114.662 103.735,115.049Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M102.317,112.749L96.472,116.317C95.827,116.703 95.011,116.51 94.624,115.865L94.559,115.758C94.173,115.113 94.366,114.297 95.011,113.91L100.855,110.343C101.5,109.956 102.317,110.149 102.703,110.794L102.768,110.901C103.155,111.525 102.961,112.363 102.317,112.749Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M106.829,117.95L106.765,117.842C106.485,117.37 105.97,117.155 105.454,117.198C105.862,116.768 105.97,116.102 105.626,115.564L105.561,115.457C105.218,114.898 104.53,114.683 103.95,114.898C104.423,114.469 104.53,113.759 104.186,113.201L104.122,113.093C103.821,112.578 103.219,112.363 102.66,112.492C103.026,112.062 103.09,111.417 102.789,110.923L102.725,110.815C102.338,110.171 101.5,109.977 100.877,110.364L95.032,113.931C94.387,114.318 94.194,115.156 94.581,115.779L94.645,115.887C94.946,116.403 95.548,116.617 96.106,116.488C95.741,116.918 95.677,117.563 95.978,118.057L96.042,118.165C96.386,118.723 97.074,118.938 97.654,118.723C97.181,119.153 97.074,119.862 97.417,120.421L97.482,120.528C97.761,121.001 98.277,121.216 98.793,121.173C98.384,121.603 98.277,122.269 98.621,122.806L98.685,122.914C99.072,123.558 99.91,123.752 100.533,123.365L106.378,119.798C107.001,119.411 107.216,118.594 106.829,117.95Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M99.824,114.275L102.639,112.47"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M102.574,115.736L103.928,114.898"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M102.381,119.131L105.432,117.198"
        android:strokeLineJoin="round"
        android:strokeWidth="0.429772"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M24.635,184.951H40.73"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M30.523,184.22C30.523,183.619 25.903,170.468 28.869,169.565C31.834,168.663 32.694,184.22 32.694,184.22C32.694,184.22 33.145,175.389 35.165,175.045C37.185,174.722 34.327,182.673 34.327,182.673"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M26.247,178.633L28.503,181.728"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M113.512,185.016H160.379"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M152.836,174.873C153.954,177.881 154.319,184.478 154.319,184.478C154.319,184.478 154.663,177.817 156.189,177.559C157.714,177.323 155.565,183.318 155.565,183.318"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M150.322,182.63L152.02,184.973"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M155.114,171.542C155.028,170.833 154.383,170.339 153.674,170.425C152.965,170.511 152.471,171.155 152.557,171.864C152.578,171.95 152.578,171.95 152.557,171.864C152.471,171.155 151.826,170.661 151.117,170.747C150.408,170.833 149.914,171.478 150,172.187C150.064,172.724 150.881,173.304 150.881,173.304L152.922,174.916L154.534,172.874C154.512,172.831 155.178,172.058 155.114,171.542Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.644658"
        android:fillColor="#00000000"
        android:strokeColor="#EDA257"
        android:strokeLineCap="round"/>
  </group>
</vector>
