<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="30dp"
    android:height="30dp"
    android:viewportWidth="30"
    android:viewportHeight="30">
  <path
      android:strokeWidth="1"
      android:pathData="M14.6417,6.4587L15,6.8269L15.3583,6.4587C16.5783,5.2053 18.2412,4.5 20,4.5C23.5839,4.5 26.5,7.4161 26.5,11C26.5,12.6093 25.856,14.1939 24.597,15.5959L24.5968,15.5962C24.1233,16.1241 23.4946,16.862 22.7848,17.6949C22.6294,17.8773 22.47,18.0643 22.3076,18.2546C21.4003,19.3178 20.3906,20.4921 19.4001,21.5856C18.4076,22.6812 17.4438,23.685 16.6277,24.4118C16.219,24.7758 15.859,25.0597 15.5595,25.2497C15.2395,25.4528 15.0626,25.5 15,25.5C14.9375,25.5 14.7605,25.4528 14.4405,25.2497C14.1411,25.0597 13.7812,24.7758 13.3724,24.4118C12.5565,23.6851 11.5928,22.6812 10.6005,21.5856C9.61,20.4921 8.6005,19.3179 7.6933,18.2547C7.5314,18.0649 7.3725,17.8785 7.2175,17.6966C6.5073,16.863 5.878,16.1244 5.4042,15.5962L5.404,15.5959C4.1449,14.1938 3.5,12.6091 3.5,11C3.5,7.4162 6.4171,4.5 10,4.5C11.7588,4.5 13.4217,5.2053 14.6417,6.4587Z"
      android:fillColor="#C4C9CC"
      android:fillAlpha="0.7"
      android:strokeColor="#BFCCD4"/>
  <path
      android:pathData="M4.0803,8.6716C4.0803,8.6716 3.4297,6.9088 3,11C3,12.75 3.703,14.45 5.032,15.93C7.319,18.48 13.2833,26 15,26C15.8245,26 17.396,24 17.396,24L4.0803,8.6716Z"
      android:fillColor="#B1BEC4"
      android:fillAlpha="0.39"/>
  <path
      android:strokeWidth="1"
      android:pathData="M21.1396,7C21.1396,7 21.9985,7.3463 22.5848,7.9804C23.1711,8.6144 23.457,9.1667 23.457,9.1667"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M24.0004,10.3679L24.0719,10.5676L24.0004,10.3679Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="9.96777"
          android:startX="23.5361"
          android:endY="10.9678"
          android:endX="23.5361"
          android:type="linear">
        <item android:offset="0" android:color="#7F007755"/>
        <item android:offset="1" android:color="#7F000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:strokeWidth="1"
      android:pathData="M24.0004,10.3679L24.0719,10.5676"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
</vector>
