<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="200dp"
    android:height="200dp"
    android:viewportWidth="200"
    android:viewportHeight="200">
  <path
      android:pathData="M100,100m-100,0a100,100 0,1 1,200 0a100,100 0,1 1,-200 0"
      android:fillColor="#E3F3FF"/>
  <path
      android:pathData="M131.697,114.458L133.268,117.378C137.184,124.66 138.162,133.166 136.001,141.147L134.666,146.078C132.803,152.959 134.38,160.317 138.899,165.831L147.204,175.961"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M82.719,76.144L62.888,90.044V66.63L82.719,47.87V76.144Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M105.581,188.987L99.441,188.573L99.864,166.882C99.904,164.86 100.233,162.855 100.842,160.922L101.701,158.199C100.206,154.26 99.441,150.094 99.441,145.895V107.948H118.089L116.66,117.201L105.581,188.987Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M142.419,116.234L135.76,115.56L118.562,74.689H129.592L142.419,116.234Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M136.233,113.407L141.441,113.067L142.281,115.787L136.233,113.407Z"
      android:fillColor="#B76452"/>
  <path
      android:pathData="M122.672,197.3H97.584L99.593,188.503L106.666,188.72L120.233,193.922C121.709,194.488 122.672,195.821 122.672,197.3Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M93.97,189.49L99.919,188.573L99.497,166.881C99.458,164.86 99.129,162.854 98.521,160.922L97.665,158.199C99.156,154.26 99.919,150.094 99.919,145.895V107.948H81.314L93.97,189.49Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M91.586,158.868C91.586,159.875 90.77,160.692 89.762,160.692C88.755,160.692 87.938,159.875 87.938,158.868C87.938,157.861 88.755,157.044 89.762,157.044C90.77,157.044 91.586,157.861 91.586,158.868Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M107.684,158.868C107.684,159.875 108.501,160.692 109.508,160.692C110.515,160.692 111.332,159.875 111.332,158.868C111.332,157.861 110.515,157.044 109.508,157.044C108.501,157.044 107.684,157.861 107.684,158.868Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M96.054,158.311L97.245,158.199"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.341,158.311L102.15,158.199"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M142.245,122.527L143.127,119.019C143.151,118.921 143.165,118.823 143.17,118.726C143.2,118.047 142.751,117.418 142.066,117.246C141.284,117.049 140.49,117.524 140.293,118.306L139.411,121.814C139.214,122.597 139.689,123.391 140.472,123.587C141.254,123.784 142.048,123.309 142.245,122.527Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M139.436,122.06L140.318,118.551C140.515,117.769 140.04,116.975 139.257,116.778C138.475,116.582 137.681,117.057 137.484,117.839L136.602,121.347C136.406,122.13 136.88,122.923 137.663,123.12C138.445,123.317 139.239,122.842 139.436,122.06Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M136.758,121.386L137.64,117.878C137.837,117.096 137.362,116.302 136.58,116.105C135.797,115.908 135.003,116.383 134.807,117.166L133.925,120.674C133.728,121.456 134.203,122.25 134.985,122.447C135.768,122.643 136.561,122.169 136.758,121.386Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M134.28,118.368L136.594,115.588C137.111,114.968 137.026,114.047 136.406,113.531C135.786,113.015 134.865,113.099 134.349,113.719L132.035,116.499C131.519,117.119 131.603,118.041 132.223,118.557C132.843,119.073 133.764,118.989 134.28,118.368Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M133.242,121.584L134.325,118.132C134.566,117.362 134.138,116.543 133.368,116.301C132.598,116.06 131.778,116.488 131.537,117.258L130.454,120.709C130.213,121.479 130.641,122.299 131.411,122.54C132.18,122.782 133,122.354 133.242,121.584Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M144.123,118.765C144.549,117.694 144.025,116.48 142.954,116.055L136.52,113.498C135.449,113.072 134.235,113.595 133.81,114.666C133.384,115.738 133.907,116.951 134.978,117.377L141.413,119.933C142.484,120.359 143.697,119.836 144.123,118.765Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M77.166,55.055C76.951,48.869 81.907,43.738 88.096,43.738H111.745C117.994,43.738 122.973,48.965 122.669,55.207L118.09,107.948L117.704,108.174C106.45,114.778 92.484,114.691 81.314,107.948L79.142,80.254"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M83.548,44.725C84.931,44.091 86.471,43.738 88.096,43.738H107.928"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M80.772,101.035L77.166,55.055C77.079,52.547 77.841,50.213 79.192,48.323"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M111.745,43.738C117.994,43.738 122.972,48.965 122.669,55.207L118.09,107.948L117.704,108.175C106.45,114.779 92.484,114.692 81.314,107.948H81.314"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M105.583,44.25L99.69,54.346L93.796,44.25V29.705H105.583V44.25Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M93.796,34.804L105.583,38.266V31.622L93.796,31.398V34.804Z"
      android:fillColor="#B76452"/>
  <path
      android:pathData="M98.511,42.738C97.428,42.738 96.551,41.861 96.551,40.778"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.97,48.728L99.69,54.347L93.796,44.25V29.705H105.583V44.25L104.747,45.683"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M135.189,88.22H128.922H122.682L105.583,47.87H117.395C119.814,47.87 121.965,49.409 122.745,51.699L135.189,88.22Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M106.65,10.48C106.65,12.084 106.356,13.384 105.994,13.384C105.632,13.384 105.338,12.084 105.338,10.48C105.338,8.877 105.632,7.577 105.994,7.577C106.356,7.577 106.65,8.877 106.65,10.48Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M107.486,11.78C106.582,13.104 105.607,14.012 105.308,13.808C105.008,13.604 105.498,12.365 106.402,11.04C107.306,9.715 108.281,8.807 108.581,9.012C108.88,9.216 108.39,10.455 107.486,11.78Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M90.787,16.058C90.787,17.084 89.955,17.915 88.93,17.915C87.905,17.915 87.073,17.084 87.073,16.058C87.073,15.033 87.905,14.201 88.93,14.201C89.955,14.201 90.787,15.033 90.787,16.058Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M90.256,22.093C90.256,23.85 88.831,25.275 87.073,25.275C85.315,25.275 83.891,23.85 83.891,22.093C83.891,20.335 85.315,18.91 87.073,18.91C88.831,18.91 90.256,20.335 90.256,22.093Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M90.256,22.093C90.256,23.85 88.831,25.275 87.073,25.275C85.315,25.275 83.891,23.85 83.891,22.093C83.891,20.335 85.315,18.91 87.073,18.91C88.831,18.91 90.256,20.335 90.256,22.093Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M111.414,23.595C110.708,30.297 104.702,35.159 97.999,34.453C91.297,33.747 86.435,27.741 87.141,21.038C87.847,14.335 93.853,9.474 100.556,10.18C107.259,10.886 112.12,16.892 111.414,23.595Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M76.73,197.3H101.819L99.809,188.503L92.736,188.72L79.169,193.922C77.693,194.488 76.73,195.821 76.73,197.3Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M96.845,195.867H83.579"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M136.406,113.531L141.269,115.385"
      android:strokeLineJoin="round"
      android:strokeWidth="0.335008"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M134.344,119.007L133.925,120.674C133.728,121.456 134.203,122.25 134.985,122.447C135.768,122.643 136.561,122.169 136.758,121.386L137.64,117.878"
      android:strokeLineJoin="round"
      android:strokeWidth="0.335008"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M137.663,123.12C138.445,123.317 139.239,122.842 139.436,122.059L140.175,118.774"
      android:strokeLineJoin="round"
      android:strokeWidth="0.335008"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M143.171,118.774L142.209,122.491"
      android:strokeLineJoin="round"
      android:strokeWidth="0.335008"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.796,195.867H115.823"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M106.179,13.401L96.335,15.168C93.606,15.658 90.793,14.559 89.235,12.265C88.546,11.25 88.173,10.116 88.348,9.055C89,5.125 93.243,2.923 96.832,4.653L103.256,7.749C105.043,8.61 106.179,10.418 106.179,12.402V13.401Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M112.004,20.017C108.787,20.017 106.179,17.409 106.179,14.192V11.167H106.895C109.717,11.167 112.004,13.455 112.004,16.276V20.017Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M92.034,8.004C92.034,9.463 90.851,10.646 89.391,10.646C87.932,10.646 86.748,9.463 86.748,8.004C86.748,6.544 87.932,5.361 89.391,5.361C90.851,5.361 92.034,6.544 92.034,8.004Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M90.173,5.41C90.173,4.41 90.984,3.598 91.985,3.598C92.985,3.598 93.797,4.41 93.797,5.41C93.797,6.411 92.985,7.222 91.985,7.222C90.984,7.222 90.173,6.411 90.173,5.41Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M94.459,12.044C94.459,13.833 93.009,15.283 91.221,15.283C89.432,15.283 87.982,13.833 87.982,12.044C87.982,10.256 89.432,8.806 91.221,8.806C93.009,8.806 94.459,10.256 94.459,12.044Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M95.764,19.799C95.764,20.5 95.196,21.069 94.494,21.069C93.793,21.069 93.224,20.5 93.224,19.799"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M95.095,27.233C94.393,27.233 93.825,26.664 93.825,25.963"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M100.548,27.233C99.846,27.233 99.278,26.664 99.278,25.963"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.298,19.799C99.298,20.5 98.729,21.069 98.028,21.069C97.326,21.069 96.757,20.5 96.757,19.799"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M96.877,17.08C97.698,16.631 98.728,16.932 99.178,17.753"
      android:strokeLineJoin="round"
      android:strokeWidth="0.837521"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M93.443,17.482C93.705,16.583 94.647,16.068 95.545,16.331"
      android:strokeLineJoin="round"
      android:strokeWidth="0.837521"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.278,25.372C99.278,26.496 98.366,27.407 97.242,27.407C96.118,27.407 95.206,26.496 95.206,25.372V23.377"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M101.944,30.673C103.818,30.673 105.338,29.153 105.338,27.278"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M124.256,88.22H133.77L135.07,92.431L124.868,89.676L124.256,88.22Z"
      android:fillColor="#B76452"/>
  <path
      android:pathData="M118.09,107.948L117.704,108.175C106.97,114.474 93.77,114.681 82.88,108.835L82.887,108.925H82.887C99.441,120.064 117.783,109.981 117.783,109.981L118.09,107.948Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M131.697,114.458L131.48,110.365C131.454,109.886 131.059,109.511 130.58,109.511H129.7C128.951,109.511 128.529,110.372 128.988,110.964L131.697,114.458Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98,34.453C92.195,33.842 87.771,29.255 87.148,23.684"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M87.141,21.038C87.847,14.335 93.853,9.474 100.556,10.18C107.259,10.886 112.12,16.892 111.414,23.595C110.917,28.317 107.789,32.125 103.64,33.716"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M115.187,22.093C115.187,23.85 113.762,25.275 112.004,25.275C110.246,25.275 108.822,23.85 108.822,22.093C108.822,20.335 110.246,18.91 112.004,18.91C113.762,18.91 115.187,20.335 115.187,22.093Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M111.176,23.015C111.176,22.1 111.918,21.359 112.833,21.359"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M108.822,22.093C108.822,20.335 110.246,18.91 112.004,18.91C113.762,18.91 115.187,20.335 115.187,22.093C115.187,23.85 113.762,25.275 112.004,25.275C111.358,25.275 110.757,25.083 110.255,24.752"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M110.273,18.891C110.273,19.67 109.642,20.301 108.864,20.301C108.085,20.301 107.454,19.67 107.454,18.891C107.454,18.113 108.085,17.482 108.864,17.482C109.642,17.482 110.273,18.113 110.273,18.891Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M113.488,17.242C113.488,18.153 112.75,18.891 111.839,18.891C110.928,18.891 110.189,18.153 110.189,17.242C110.189,16.331 110.928,15.593 111.839,15.593C112.75,15.593 113.488,16.331 113.488,17.242Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M112.339,15.109C112.339,15.61 111.933,16.016 111.432,16.016C110.931,16.016 110.525,15.61 110.525,15.109C110.525,14.608 110.931,14.201 111.432,14.201C111.933,14.201 112.339,14.608 112.339,15.109Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M99.919,189.677V179.129"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M100.372,189.094L102.558,197.442"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M96.054,121.703L98.12,123.11C98.915,123.652 99.963,123.646 100.753,123.095L102.746,121.703"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.4,123.632V148.26"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M119.684,88.643L116.957,74.711L120.386,82.802L119.684,88.643Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M116.245,75.247C116.833,74.916 117.197,74.293 117.197,73.618V72.288"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M122.966,85.205H133.942"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#0D9AFF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M127.58,86.284H135.976"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M127.581,65.891L123.371,53.537C122.955,52.315 122.401,51.145 121.72,50.048L120.648,48.324"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M116.957,74.711L122.682,88.22H128.922H135.189L129.761,72.288"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M95.31,64.62H82.719V66.63L95.31,64.62Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M93.796,63.168H95.31V64.62H83.026V63.168H90.545"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M118.089,107.576L113.988,104.152L118.388,104.858L118.089,107.576Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M117.466,111.984L110.854,154.818"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M109.204,165.511L107.102,179.129"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M90.178,165.511L93.969,189.49"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M84.366,115.486L81.314,113.922"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M82.021,116.734H84.366"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M90.212,192.901L88.386,190.008"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M91.492,191.621L89.666,188.728"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M109.537,192.901L111.363,190.008"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M108.257,191.621L110.082,188.728"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M122.646,176.235C119.614,174.457 117.631,171.116 117.756,167.365C117.348,168.325 117.102,169.373 117.055,170.477C116.891,174.358 119.235,177.762 122.646,179.129V176.235Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M117.756,167.365C117.348,168.325 117.101,169.373 117.055,170.476C116.891,174.358 119.235,177.762 122.646,179.129V176.235"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M134.063,194.255H133.774V185.694L130.376,181.971V195.79C130.376,196.669 131.089,197.382 131.968,197.382H132.075H133.774H135.961V196.153C135.961,195.105 135.111,194.255 134.063,194.255Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M153.398,194.255H153.109V181.971L149.711,185.694V195.79C149.711,196.669 150.424,197.382 151.303,197.382H151.41H153.109H155.296V196.153C155.296,195.105 154.446,194.255 153.398,194.255Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M142.463,189.49H133.175C126.514,189.49 121.115,184.091 121.115,177.43C121.115,176.361 121.981,175.495 123.05,175.495H154.524V177.43C154.524,184.091 149.124,189.49 142.463,189.49Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M128.306,175.495C127.991,175.999 127.805,176.593 127.805,177.232C127.805,179.047 129.277,180.519 131.093,180.519C132.908,180.519 134.38,179.047 134.38,177.232C134.38,177.212 134.377,177.192 134.377,177.172C134.459,177.181 134.541,177.186 134.625,177.186C135.684,177.186 136.573,176.469 136.839,175.495H128.306Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M139.754,176.569C139.754,177.71 140.679,178.635 141.82,178.635C142.961,178.635 143.886,177.71 143.886,176.569C143.886,176.175 143.774,175.808 143.582,175.495H140.058C139.866,175.808 139.754,176.175 139.754,176.569Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M123.05,175.495H154.524V177.43C154.524,179.037 154.21,180.57 153.639,181.972C152.294,185.279 149.523,187.855 146.09,188.936"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M146.668,194.255H146.38V185.694H142.982V187.308V195.79C142.982,196.669 143.694,197.382 144.574,197.382H144.681H146.38H148.567V196.153C148.567,195.105 147.716,194.255 146.668,194.255Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M127.192,194.255H126.903V185.694L123.505,181.971V195.79C123.505,196.669 124.218,197.382 125.097,197.382H125.204H126.903H129.09V196.153C129.09,195.105 128.24,194.255 127.192,194.255Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M142.982,188.004V191.809"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M142.463,189.49H133.175C131.89,189.49 130.652,189.29 129.491,188.918C128.342,188.549 127.267,188.013 126.297,187.338"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M123.505,180.966V190.211"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M153.633,181.979C154.206,180.575 154.524,179.04 154.524,177.43V175.495H145.099C146.552,178.958 149.773,181.494 153.633,181.979Z"
      android:fillColor="#E43C28"/>
  <path
      android:pathData="M153.633,181.979C154.206,180.575 154.524,179.04 154.524,177.43V175.495H145.099C146.552,178.958 149.773,181.494 153.633,181.979Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M150.282,171.432C148.619,173.094 145.808,172.582 144.839,170.44L141.162,162.313L149.289,165.989C151.431,166.958 151.944,169.77 150.282,171.432Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M146.417,166.165C147.13,166.165 147.789,165.938 148.329,165.555L143.123,163.199C143.296,164.866 144.705,166.165 146.417,166.165Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M150.282,171.432C148.619,173.094 145.808,172.582 144.839,170.44L141.162,162.313L149.289,165.989C151.431,166.958 151.944,169.77 150.282,171.432Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M153.418,169.815C151.778,168.831 151.627,166.511 153.126,165.324L158.814,160.817L157.518,167.957C157.176,169.839 155.058,170.798 153.418,169.815Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M153.418,169.815C151.778,168.831 151.627,166.511 153.126,165.324L158.814,160.817L157.518,167.957C157.176,169.839 155.058,170.798 153.418,169.815Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M157.242,168.339C157.242,166.062 155.396,164.216 153.118,164.216H153.054C149.034,164.216 145.774,167.475 145.774,171.496C145.774,176.726 150.014,180.966 155.245,180.966H155.567C162.652,180.966 168.425,175.358 168.696,168.339H157.242Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M153.914,169.359C153.914,169.926 153.454,170.387 152.886,170.387C152.318,170.387 151.858,169.926 151.858,169.359"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M154.251,176.984C153.362,176.984 152.642,176.263 152.642,175.374"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M156.962,169.359C156.962,169.926 156.501,170.387 155.934,170.387C155.366,170.387 154.906,169.926 154.906,169.359"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M165.403,169.767C165.959,170.769 166.766,171.886 167.705,172.147C168.121,172.263 168.555,171.984 168.667,171.567C168.918,170.626 169.093,169.654 169.181,168.656C169.221,168.207 168.863,167.821 168.412,167.821L166.406,167.938C165.499,167.991 164.963,168.972 165.403,169.767Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M164.135,171.916C164.135,172.05 164.027,172.158 163.893,172.158C163.76,172.158 163.651,172.05 163.651,171.916C163.651,171.783 163.76,171.674 163.893,171.674C164.027,171.674 164.135,171.783 164.135,171.916Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M165.717,171.646C165.717,171.733 165.646,171.804 165.559,171.804C165.471,171.804 165.401,171.733 165.401,171.646C165.401,171.559 165.471,171.488 165.559,171.488C165.646,171.488 165.717,171.559 165.717,171.646Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M165.401,173.182C165.401,173.315 165.292,173.424 165.159,173.424C165.025,173.424 164.917,173.315 164.917,173.182C164.917,173.048 165.025,172.94 165.159,172.94C165.292,172.94 165.401,173.048 165.401,173.182Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M150.808,179.865C152.131,180.568 153.641,180.967 155.245,180.967H155.567C157.951,180.967 160.187,180.331 162.115,179.221"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M146.091,169.366C145.885,170.04 145.774,170.755 145.774,171.496C145.774,173.631 146.481,175.602 147.673,177.186"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M155.633,165.071C154.937,164.535 154.065,164.216 153.119,164.216H153.054C150.802,164.216 148.789,165.238 147.454,166.844"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M168.696,168.339H157.242C157.242,167.812 157.143,167.308 156.963,166.845"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M146.659,175.494H145.099C146.552,178.958 149.773,181.494 153.633,181.979C153.775,181.632 153.901,181.276 154.011,180.913"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M66.019,188.315H61.947V111.662H65.354C75.062,111.662 81.042,125.776 78.73,135.4L66.019,188.315Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M58.106,188.315H62.181V111.662H58.771C49.055,111.662 43.07,125.776 45.383,135.4L58.106,188.315Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M72.125,54.832C75.225,54.927 77.626,57.556 77.465,60.653C77.362,62.65 77.718,64.662 78.526,65.767L79.729,67.413C81.727,70.146 80.568,74.03 77.399,75.221C74.657,76.252 73.448,79.459 74.827,82.044L76.961,86.044C78.301,88.555 77.333,91.678 74.806,92.991L71.33,94.797L54.065,86.869L65.348,54.624L72.125,54.832Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M58.113,49.559L53.992,52.672C51.156,53.807 49.219,57.911 50.669,60.6L49.945,63.355C51.39,66.034 50.04,69.365 47.138,70.282L45.194,70.897C41.966,71.917 40.601,75.734 42.45,78.569C44.05,81.023 43.127,84.324 40.485,85.591L36.398,87.552C33.831,88.783 32.763,91.873 34.022,94.427L35.755,97.94L54.364,101.769L64.356,69.1L58.113,49.559Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M65.906,77.499H58.425V72.995H65.906V77.499Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M65.906,76.346L58.424,76.42V72.995H65.906V76.346Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M67.052,80.574V77.202H57.076V80.574C57.076,81.347 56.58,82.041 55.84,82.268C52.817,83.197 50.68,86.106 50.894,89.437L52.291,111.192C52.491,114.308 55.077,116.734 58.2,116.734H65.928C69.051,116.734 71.637,114.308 71.838,111.192L73.235,89.437C73.449,86.106 71.311,83.197 68.288,82.268C67.549,82.041 67.052,81.347 67.052,80.574Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M77.16,61.155C77.16,68.63 70.835,74.689 63.034,74.689C55.233,74.689 48.909,68.63 48.909,61.155C48.909,53.681 55.233,47.621 63.034,47.621C70.835,47.621 77.16,53.681 77.16,61.155Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M76.489,67.248C76.489,68.08 75.814,68.755 74.982,68.755C74.149,68.755 73.474,68.08 73.474,67.248C73.474,66.415 74.149,65.74 74.982,65.74C75.814,65.74 76.489,66.415 76.489,67.248Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M76.489,67.248C76.489,68.08 75.814,68.755 74.982,68.755C74.149,68.755 73.474,68.08 73.474,67.248C73.474,66.415 74.149,65.74 74.982,65.74C75.814,65.74 76.489,66.415 76.489,67.248Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M74.981,64.657C74.981,70.794 70.007,75.769 63.87,75.769C57.734,75.769 52.759,70.794 52.759,64.657C52.759,58.521 57.734,53.546 63.87,53.546C70.007,53.546 74.981,58.521 74.981,64.657Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M54.31,70.284C53.325,68.626 52.759,66.689 52.759,64.62C52.759,58.484 57.734,53.509 63.87,53.509C70.007,53.509 74.981,58.484 74.981,64.62C74.981,65.281 74.924,65.929 74.813,66.558"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M61.034,75.366C58.895,74.803 57.008,73.618 55.579,72.017"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M74.6,67.52C73.324,72.25 69.004,75.731 63.871,75.731"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M66.712,91.309H60.403L59.805,85.977H67.354L66.712,91.309Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M78.454,197.442H67.979C67.214,197.442 66.593,196.821 66.593,196.056V195.359C66.593,194.7 66.12,194.109 65.467,194.032C64.689,193.94 63.976,196.622 63.976,197.382H61.857L62.072,189.878L75.906,194.038C77.417,194.481 78.454,195.867 78.454,197.442Z"
      android:fillColor="#72BAE5"/>
  <path
      android:pathData="M65.623,191.672L65.996,188.315H62.072L62.077,189.938L62.837,190.793C63.559,191.484 64.556,191.811 65.548,191.681L65.623,191.672Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M57.126,99.369L58.744,93.339L62.561,93.146L61.797,99.257L57.126,99.369Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M45.568,197.442H56.043C56.808,197.442 57.429,196.821 57.429,196.056V195.359C57.429,194.7 57.902,194.109 58.556,194.032C59.333,193.94 60.047,196.622 60.047,197.382H62.165L62.077,189.938L48.116,194.038C46.605,194.481 45.568,195.867 45.568,197.442Z"
      android:fillColor="#72BAE5"/>
  <path
      android:pathData="M58.495,191.672L58.131,188.315H62.181L62.077,189.938L61.248,190.754C60.521,191.468 59.505,191.807 58.495,191.672Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M70.842,156.105C70.842,156.69 70.368,157.165 69.782,157.165C69.197,157.165 68.722,156.69 68.722,156.105"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M55.538,156.105C55.538,156.69 55.063,157.165 54.477,157.165C53.892,157.165 53.417,156.69 53.417,156.105"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M50.879,64.534C50.879,65.466 50.982,66.374 51.174,67.249C52.803,68.012 54.619,68.44 56.537,68.44C63.536,68.44 69.21,62.766 69.21,55.767C69.21,54.835 69.106,53.927 68.915,53.052C67.286,52.289 65.469,51.861 63.552,51.861C56.553,51.861 50.879,57.535 50.879,64.534Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M59.769,62.458C63.445,61.04 66.293,57.958 67.391,54.135"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M54.031,64.706C57.928,64.127 61.382,61.743 63.29,58.253"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M55.905,57.098C58.024,57.156 60.077,56.216 61.415,54.546"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M69.518,53.974C69.005,53.974 68.506,54.033 68.024,54.14C67.605,55.056 67.369,56.076 67.369,57.154C67.369,61.087 70.491,64.276 74.34,64.276C74.854,64.276 75.353,64.217 75.834,64.11C76.254,63.194 76.489,62.174 76.489,61.096C76.489,57.163 73.368,53.974 69.518,53.974Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M68.154,67.244C68.154,67.974 67.562,68.566 66.832,68.566C66.102,68.566 65.511,67.974 65.511,67.244"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M71.686,67.282C71.686,68.011 71.095,68.603 70.365,68.603C69.635,68.603 69.044,68.011 69.044,67.282"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M69.383,65.041L70.365,65.37"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M67.199,65.436L68.126,64.974"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M64.895,72.848C63.366,72.848 62.126,71.608 62.126,70.08"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M68.138,69.632C68.498,69.632 68.79,69.924 68.79,70.283C68.79,70.643 68.498,70.935 68.138,70.935"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M61.151,69.183C61.151,68.542 61.671,68.023 62.311,68.023"
      android:strokeLineJoin="round"
      android:strokeWidth="0.335008"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M54.031,75.34L52.452,75.838C49.832,76.666 48.724,79.765 50.225,82.067C51.524,84.058 50.774,86.738 48.63,87.767L45.312,89.358C43.228,90.358 42.362,92.867 43.384,94.939"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M45.079,78.395C44.42,76.386 45.505,74.222 47.509,73.548L47.773,73.459C49.024,73.039 49.972,72.006 50.285,70.723"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M70.968,79.083C70.309,77.075 71.394,74.91 73.397,74.237L73.661,74.148C74.913,73.727 75.861,72.694 76.173,71.412"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M36.465,94.884C35.806,92.876 36.89,90.711 38.894,90.038L39.158,89.949C40.41,89.528 41.358,88.495 41.67,87.213"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M67.69,91.049H61.697C61.142,91.049 60.692,90.599 60.692,90.044C60.692,89.489 61.142,89.038 61.697,89.038H67.69C68.245,89.038 68.695,89.489 68.695,90.044C68.695,90.599 68.245,91.049 67.69,91.049Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M67.69,93.059H61.697C61.142,93.059 60.692,92.609 60.692,92.054C60.692,91.499 61.142,91.049 61.697,91.049H67.69C68.245,91.049 68.695,91.499 68.695,92.054C68.695,92.609 68.245,93.059 67.69,93.059Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M67.69,95.042H61.697C61.142,95.042 60.692,94.592 60.692,94.037C60.692,93.482 61.142,93.032 61.697,93.032H67.69C68.245,93.032 68.695,93.482 68.695,94.037C68.695,94.592 68.245,95.042 67.69,95.042Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M119.475,99.195L113.554,98.272C113.005,98.187 112.63,97.673 112.715,97.124C112.801,96.576 113.315,96.201 113.863,96.286L119.785,97.209C120.333,97.294 120.708,97.808 120.623,98.357C120.537,98.905 120.024,99.28 119.475,99.195Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M119.166,101.181L113.244,100.258C112.696,100.173 112.321,99.659 112.406,99.11C112.491,98.562 113.005,98.187 113.554,98.272L119.475,99.195C120.024,99.28 120.399,99.794 120.313,100.343V100.343C120.228,100.891 119.714,101.267 119.166,101.181Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M118.86,103.141L113.393,102.289C112.845,102.203 112.47,101.689 112.555,101.141C112.64,100.593 113.154,100.217 113.703,100.303L119.17,101.155C119.718,101.24 120.093,101.754 120.008,102.302C119.923,102.851 119.409,103.226 118.86,103.141Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M118.591,104.869L113.988,104.152C113.44,104.067 113.065,103.553 113.15,103.005C113.236,102.456 113.749,102.081 114.298,102.166L118.9,102.883C119.449,102.969 119.824,103.483 119.739,104.031C119.653,104.58 119.139,104.955 118.591,104.869Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M62.888,95.042L58.66,96.257V91.115C58.66,89.968 59.59,89.039 60.737,89.039H62.888V95.042Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M68.315,85.977H58.845L59.062,84.302H68.041L68.315,85.977Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M66.927,84.302H60.233V83.971C60.233,83.645 60.498,83.381 60.824,83.381H66.336C66.662,83.381 66.927,83.645 66.927,83.971V84.302Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M80.666,99.684L70.296,98.122V84.302L79.963,90.719L80.666,99.684Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M118.566,104.865C117.579,104.712 116.904,103.787 117.058,102.8L117.695,98.713C117.849,97.726 118.773,97.051 119.76,97.205C120.746,97.358 121.422,98.283 121.268,99.27L120.631,103.357C120.477,104.344 119.553,105.019 118.566,104.865Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M57.817,96.792L62.085,96.997L62.064,97.297L57.439,98.203L57.817,96.792Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M55.578,119.141C53.564,118.861 52.158,117.001 52.439,114.987L57.028,98.287L62.374,97.026L59.732,116.002C59.452,118.016 57.592,119.421 55.578,119.141Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M55.513,103.802L53.854,109.839"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M62.165,98.212L60.025,114.254"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M70.4,96.993L61.348,95.741C60.944,95.685 60.661,95.312 60.717,94.908C60.773,94.504 61.146,94.222 61.55,94.277L70.602,95.53C71.006,95.586 71.288,95.959 71.232,96.363C71.176,96.767 70.804,97.049 70.4,96.993Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M64.694,93.032H67.69C68.245,93.032 68.695,93.482 68.695,94.037C68.695,94.592 68.245,95.042 67.69,95.042L67.026,95.035"
      android:strokeLineJoin="round"
      android:strokeWidth="0.335008"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M64.489,91.049H66.927"
      android:strokeLineJoin="round"
      android:strokeWidth="0.335008"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M72.879,94.972L72.436,101.88"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M58.533,77.462V74.559"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M65.829,77.555V75.559"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M51.174,67.249C52.803,68.011 54.62,68.44 56.537,68.44C63.536,68.44 69.21,62.766 69.21,55.767"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M68.248,60.617C69.439,62.799 71.721,64.276 74.34,64.276C74.854,64.276 75.353,64.217 75.834,64.11"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M74.6,55.438C75.524,57.15 76.046,59.094 76.046,61.155"
      android:strokeAlpha="0.300003"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.300003"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M71.705,51.86C72.274,52.347 72.798,52.882 73.27,53.458"
      android:strokeAlpha="0.300003"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.300003"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M50.1,61.155C50.1,56.616 52.647,52.647 56.446,50.489"
      android:strokeAlpha="0.300003"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.300003"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M70.077,59.626C71.242,61.397 73.187,62.543 75.323,62.685"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M59.805,85.977H67.355L67.282,86.582L60.061,88.261L59.805,85.977Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M68.425,84.302H58.533"
      android:strokeLineJoin="round"
      android:strokeWidth="0.335008"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M68.881,89.039H59.575"
      android:strokeLineJoin="round"
      android:strokeWidth="0.335008"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M62.561,82.16C62.561,82.329 62.698,82.465 62.866,82.465C63.035,82.465 63.171,82.329 63.171,82.16C63.171,81.991 63.035,81.855 62.866,81.855C62.698,81.855 62.561,81.991 62.561,82.16Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="81.8547"
          android:startX="62.8662"
          android:endY="82.4652"
          android:endX="62.8662"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M64.145,78.678C63.724,78.766 63.39,79.114 63.315,79.536C63.248,79.919 63.383,80.271 63.629,80.508C63.761,80.633 63.815,80.811 63.777,80.989C63.76,81.066 63.758,81.15 63.776,81.236C63.826,81.479 64.036,81.671 64.282,81.701C64.643,81.745 64.95,81.465 64.95,81.112C64.95,81.075 64.947,81.039 64.94,81.004C64.907,80.829 64.965,80.655 65.098,80.536C65.318,80.339 65.457,80.052 65.457,79.733C65.457,79.137 64.974,78.653 64.378,78.653C64.301,78.653 64.224,78.661 64.145,78.678Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="78.6532"
          android:startX="64.3776"
          android:endY="81.7056"
          android:endX="64.3776"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M105.625,21.359C106.182,21.359 106.634,21.81 106.634,22.368"
      android:strokeLineJoin="round"
      android:strokeWidth="0.335008"
      android:fillColor="#00000000"
      android:strokeColor="#B76452"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M87.8,22.368C87.8,21.81 88.252,21.359 88.81,21.359"
      android:strokeLineJoin="round"
      android:strokeWidth="0.335008"
      android:fillColor="#00000000"
      android:strokeColor="#B76452"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M118.591,104.869L113.988,104.152C113.44,104.067 113.065,103.553 113.15,103.005C113.236,102.456 113.749,102.081 114.298,102.166"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M115.187,100.317L118.388,100.764"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M115.864,98.632L113.553,98.272C113.005,98.187 112.63,97.673 112.715,97.124C112.801,96.576 113.314,96.2 113.863,96.286L119.784,97.209"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M118.089,98.979L118.798,99.089"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M62.077,197.154V190.36"
      android:strokeLineJoin="round"
      android:strokeWidth="0.335008"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M51.549,124.642C51.549,128.034 48.799,130.784 45.407,130.784"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M72.612,124.642C72.612,128.034 75.362,130.784 78.754,130.784"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M60.19,130.114L61.157,130.738C61.784,131.142 62.586,131.155 63.226,130.771L64.228,130.17"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M70.296,116.734L72.88,114.254"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M71.915,116.734L74.26,115.765"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M62.181,188.315H57.398L56.784,186.675H62.181V188.315Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.335008"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M62.077,188.315H66.86L67.474,186.675H62.077V188.315Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.335008"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M62.077,176.782V186.675H63.707L62.077,176.782Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M36.398,87.552C33.831,88.783 32.763,91.873 34.022,94.427L35.755,97.94"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M47.138,70.282L45.194,70.896C41.966,71.916 40.601,75.734 42.45,78.569C44.05,81.023 43.127,84.324 40.485,85.591"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M79.729,67.413L78.526,65.767C77.889,64.897 77.549,62.998 77.679,61.12C77.755,60.016 77.219,58.253 76.791,57.12"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M74.826,82.044C73.447,79.459 74.657,76.251 77.399,75.221H77.399C79.324,74.498 80.507,72.781 80.692,70.932"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M74.806,92.991C77.333,91.678 78.301,88.555 76.961,86.044"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M46.161,100.081L46.797,95.292L48.088,100.478L46.161,100.081Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M62.165,159.762V131.05"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M64.586,149.988L62.122,146.242"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M63.504,150.902L62.181,148.205"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M62.077,168.808V173.247"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M56.062,190.208C55.842,190.725 55.481,192.739 55.997,192.959C56.514,193.178 57.713,191.52 57.933,191.003C58.152,190.486 57.911,189.89 57.395,189.67C56.878,189.451 56.281,189.692 56.062,190.208Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.335008"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M53.024,192.8C53.541,193.019 55.555,193.381 55.774,192.864C55.994,192.347 54.336,191.148 53.819,190.929C53.302,190.709 52.706,190.95 52.486,191.467C52.266,191.983 52.507,192.58 53.024,192.8Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.335008"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M67.949,190.208C68.168,190.725 68.53,192.739 68.013,192.959C67.496,193.178 66.297,191.52 66.077,191.003C65.858,190.486 66.099,189.89 66.616,189.67C67.132,189.451 67.729,189.692 67.949,190.208Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.335008"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M70.986,192.8C70.47,193.019 68.455,193.381 68.236,192.864C68.016,192.347 69.674,191.148 70.191,190.929C70.708,190.709 71.305,190.95 71.524,191.467C71.744,191.983 71.503,192.58 70.986,192.8Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.335008"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M55.161,102.889L51.567,103.296C51.467,103.308 51.368,103.309 51.271,103.3C50.594,103.24 50.03,102.711 49.951,102.009C49.86,101.208 50.436,100.484 51.237,100.393L54.832,99.985C55.633,99.894 56.357,100.471 56.448,101.272C56.539,102.074 55.963,102.798 55.161,102.889Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M55.071,100.043L51.477,100.45C50.675,100.542 49.952,99.965 49.861,99.164C49.77,98.362 50.346,97.638 51.148,97.548L54.742,97.14C55.544,97.049 56.267,97.625 56.358,98.427V98.427C56.449,99.228 55.873,99.952 55.071,100.043Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M54.76,97.299L51.166,97.707C50.364,97.798 49.641,97.222 49.55,96.42C49.459,95.618 50.035,94.895 50.837,94.804L54.431,94.396C55.233,94.305 55.956,94.881 56.047,95.683C56.138,96.485 55.562,97.208 54.76,97.299Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M52.099,94.442L49.035,96.366C48.352,96.795 47.45,96.589 47.021,95.905C46.592,95.222 46.798,94.321 47.482,93.892L50.545,91.968C51.228,91.539 52.13,91.745 52.559,92.428C52.988,93.111 52.782,94.013 52.099,94.442Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M55.424,93.84L51.859,94.455C51.064,94.592 50.308,94.058 50.171,93.263C50.034,92.468 50.568,91.712 51.363,91.575L54.927,90.961C55.722,90.824 56.478,91.357 56.615,92.152C56.752,92.948 56.219,93.703 55.424,93.84Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M51.182,104.25C50.064,104.529 48.931,103.85 48.652,102.731L46.973,96.014C46.694,94.896 47.374,93.763 48.492,93.483C49.61,93.204 50.743,93.884 51.022,95.002L52.701,101.719C52.98,102.838 52.3,103.971 51.182,104.25Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M52.723,94.59L54.431,94.396C55.232,94.305 55.956,94.881 56.047,95.683C56.138,96.484 55.562,97.208 54.76,97.299L51.166,97.707"
      android:strokeLineJoin="round"
      android:strokeWidth="0.335008"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M56.359,98.427C56.449,99.228 55.873,99.952 55.072,100.043L51.717,100.339"
      android:strokeLineJoin="round"
      android:strokeWidth="0.335008"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M123.998,197.382H15.354"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M75.405,197.382H141.867"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M145.311,197.382H163.428"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M167.794,197.382H173.303"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M161.474,195.238L162.256,197.3L162.814,195.881"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M19.218,61.485C19.218,62.002 19.637,62.422 20.154,62.422C20.672,62.422 21.092,62.002 21.092,61.485C21.092,60.967 20.672,60.548 20.154,60.548C19.637,60.548 19.218,60.967 19.218,61.485Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M11.663,58.838C11.663,59.18 11.941,59.457 12.283,59.457C12.625,59.457 12.903,59.18 12.903,58.838C12.903,58.495 12.625,58.218 12.283,58.218C11.941,58.218 11.663,58.495 11.663,58.838Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M27.677,22.614C27.677,22.956 27.954,23.233 28.296,23.233C28.638,23.233 28.916,22.956 28.916,22.614C28.916,22.271 28.638,21.994 28.296,21.994C27.954,21.994 27.677,22.271 27.677,22.614Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M23.152,54.528H23.832V53.848C23.832,53.724 23.932,53.625 24.056,53.625C24.179,53.625 24.279,53.724 24.279,53.848V54.528H24.959C25.083,54.528 25.183,54.628 25.183,54.752C25.183,54.875 25.083,54.975 24.959,54.975H24.279V55.656C24.279,55.779 24.179,55.879 24.056,55.879C23.932,55.879 23.832,55.779 23.832,55.656V54.975H23.152C23.028,54.975 22.928,54.875 22.928,54.752C22.928,54.628 23.028,54.528 23.152,54.528Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M59.871,25.052H60.551V24.371C60.551,24.248 60.651,24.148 60.774,24.148C60.898,24.148 60.998,24.248 60.998,24.371V25.052H61.678C61.801,25.052 61.902,25.152 61.902,25.275C61.902,25.398 61.801,25.499 61.678,25.499H60.998V26.179C60.998,26.302 60.898,26.402 60.774,26.402C60.651,26.402 60.551,26.302 60.551,26.179V25.499H59.871C59.747,25.499 59.647,25.398 59.647,25.275C59.647,25.152 59.747,25.052 59.871,25.052Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M129.221,27.609H130.12V26.71C130.12,26.547 130.252,26.415 130.415,26.415C130.578,26.415 130.71,26.547 130.71,26.71V27.609H131.609C131.772,27.609 131.904,27.741 131.904,27.904C131.904,28.067 131.772,28.199 131.609,28.199H130.71V29.098C130.71,29.261 130.578,29.393 130.415,29.393C130.252,29.393 130.12,29.261 130.12,29.098V28.199H129.221C129.058,28.199 128.926,28.067 128.926,27.904C128.926,27.741 129.058,27.609 129.221,27.609Z"
      android:fillColor="#529ED6"/>
  <path
      android:pathData="M134.228,21.153C134.228,21.499 134.508,21.778 134.853,21.778C135.198,21.778 135.477,21.499 135.477,21.153C135.477,20.808 135.198,20.528 134.853,20.528C134.508,20.528 134.228,20.808 134.228,21.153Z"
      android:fillColor="#529ED6"/>
  <path
      android:pathData="M127.917,20.869C127.917,21.324 128.285,21.693 128.74,21.693C129.195,21.693 129.564,21.324 129.564,20.869C129.564,20.414 129.195,20.045 128.74,20.045C128.285,20.045 127.917,20.414 127.917,20.869Z"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#E4F4FC"/>
  <path
      android:pathData="M161.524,77.953C161.524,78.295 161.802,78.573 162.144,78.573C162.486,78.573 162.764,78.295 162.764,77.953C162.764,77.61 162.486,77.333 162.144,77.333C161.802,77.333 161.524,77.61 161.524,77.953Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.502513"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
</vector>
