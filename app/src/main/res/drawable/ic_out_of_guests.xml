<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="200dp"
    android:height="200dp"
    android:viewportWidth="200"
    android:viewportHeight="200">
  <path
      android:pathData="M0,0h200v200h-200z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M0,0h200v200h-200z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="0"
          android:startX="100"
          android:endY="200"
          android:endX="100"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path android:pathData="M167.608,86.785C165.788,87.922 164.821,90.046 165.106,92.188C165.162,92.681 165.2,93.192 165.2,93.704C165.2,99.96 160.139,105.022 153.883,105.022H142.376C141.295,105.022 140.252,105.439 139.437,106.14C136.613,108.624 132.897,110.14 128.821,110.14H111.399C109.333,110.14 107.437,111.411 106.736,113.363C105.352,117.193 101.693,119.941 97.39,119.941H54.337C50.413,119.941 47.039,117.685 45.408,114.387C44.119,111.79 41.484,110.14 38.584,110.14C29.693,110.14 22.508,102.937 22.508,94.064C22.508,85.173 29.712,77.989 38.584,77.989H75.835C75.627,77.989 75.418,77.97 75.228,77.951C77.522,77.647 79.304,75.695 79.304,73.306C79.304,70.917 77.522,68.965 75.228,68.661C75.437,68.643 75.627,68.624 75.835,68.624H73.522C68.404,68.624 64.271,64.472 64.271,59.372C64.271,54.254 68.423,50.121 73.522,50.121H151.418H157.068H159.437C161.181,50.121 162.812,50.595 164.196,51.448C171.646,54.311 176.935,61.534 176.935,69.989C176.935,77.041 173.219,83.278 167.608,86.785Z M 0,0"/>
    <path
        android:pathData="M167.608,86.785C165.788,87.922 164.821,90.046 165.106,92.188C165.162,92.681 165.2,93.192 165.2,93.704C165.2,99.96 160.139,105.022 153.883,105.022H142.376C141.295,105.022 140.252,105.439 139.437,106.14C136.613,108.624 132.897,110.14 128.821,110.14H111.399C109.333,110.14 107.437,111.411 106.736,113.363C105.352,117.193 101.693,119.941 97.39,119.941H54.337C50.413,119.941 47.039,117.685 45.408,114.387C44.119,111.79 41.484,110.14 38.584,110.14C29.693,110.14 22.508,102.937 22.508,94.064C22.508,85.173 29.712,77.989 38.584,77.989H75.835C75.627,77.989 75.418,77.97 75.228,77.951C77.522,77.647 79.304,75.695 79.304,73.306C79.304,70.917 77.522,68.965 75.228,68.661C75.437,68.643 75.627,68.624 75.835,68.624H73.522C68.404,68.624 64.271,64.472 64.271,59.372C64.271,54.254 68.423,50.121 73.522,50.121H151.418H157.068H159.437C161.181,50.121 162.812,50.595 164.196,51.448C171.646,54.311 176.935,61.534 176.935,69.989C176.935,77.041 173.219,83.278 167.608,86.785Z"
        android:strokeAlpha="0.8"
        android:fillColor="#DDF5D5"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M175.414,264.73C131.209,220.525 131.209,148.854 175.414,104.649C219.619,60.444 291.289,60.444 335.494,104.649C379.699,148.854 379.699,220.525 335.494,264.73C291.289,308.935 219.619,308.935 175.414,264.73Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.758298"
        android:fillColor="#00000000"
        android:strokeColor="#A5E0A2"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M165.996,261.109C121.791,216.905 121.791,145.234 165.996,101.029C210.201,56.824 281.872,56.824 326.077,101.029C370.282,145.234 370.282,216.905 326.077,261.109C281.872,305.314 210.201,305.314 165.996,261.109Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.758298"
        android:fillColor="#00000000"
        android:strokeColor="#A5E0A2"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M155.301,258.429C111.096,214.224 111.096,142.553 155.301,98.349C199.505,54.144 271.176,54.144 315.381,98.349C359.586,142.553 359.586,214.224 315.381,258.429C271.176,302.634 199.505,302.634 155.301,258.429Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.758298"
        android:fillColor="#00000000"
        android:strokeColor="#A5E0A2"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M144.442,254.901C100.237,210.697 100.237,139.026 144.442,94.821C188.647,50.616 260.317,50.616 304.522,94.821C348.727,139.026 348.727,210.697 304.522,254.901C260.317,299.106 188.647,299.106 144.442,254.901Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.758298"
        android:fillColor="#00000000"
        android:strokeColor="#A5E0A2"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M133.583,251.372C89.378,207.167 89.378,135.497 133.583,91.292C177.788,47.087 249.458,47.087 293.663,91.292C337.868,135.497 337.868,207.167 293.663,251.372C249.458,295.577 177.788,295.577 133.583,251.372Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.758298"
        android:fillColor="#00000000"
        android:strokeColor="#A5E0A2"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M122.724,247.843C78.519,203.638 78.519,131.968 122.724,87.763C166.929,43.558 238.6,43.558 282.805,87.763C327.01,131.968 327.01,203.638 282.805,247.843C238.6,292.048 166.929,292.048 122.724,247.843Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.758298"
        android:fillColor="#00000000"
        android:strokeColor="#A5E0A2"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M111.866,244.341C67.661,200.136 67.661,128.466 111.866,84.261C156.071,40.056 227.741,40.056 271.946,84.261C316.151,128.466 316.151,200.136 271.946,244.341C227.741,288.546 156.071,288.546 111.866,244.341Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.758298"
        android:fillColor="#00000000"
        android:strokeColor="#A5E0A2"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M101.007,240.812C56.802,196.607 56.802,124.936 101.007,80.731C145.212,36.526 216.882,36.526 261.087,80.731C305.292,124.936 305.292,196.607 261.087,240.812C216.882,285.017 145.212,285.017 101.007,240.812Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.758298"
        android:fillColor="#00000000"
        android:strokeColor="#A5E0A2"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M90.149,237.283C45.944,193.078 45.944,121.408 90.149,77.203C134.354,32.998 206.024,32.998 250.229,77.203C294.434,121.408 294.434,193.078 250.229,237.283C206.024,281.488 134.354,281.488 90.149,237.283Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.758298"
        android:fillColor="#00000000"
        android:strokeColor="#A5E0A2"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M79.303,233.768C35.098,189.563 35.098,117.892 79.303,73.687C123.508,29.483 195.178,29.483 239.383,73.687C283.588,117.892 283.588,189.563 239.383,233.768C195.178,277.973 123.508,277.973 79.303,233.768Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.758298"
        android:fillColor="#00000000"
        android:strokeColor="#A5E0A2"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M68.444,230.239C24.239,186.034 24.239,114.364 68.444,70.159C112.649,25.954 184.319,25.954 228.524,70.159C272.729,114.364 272.729,186.034 228.524,230.239C184.319,274.444 112.649,274.444 68.444,230.239Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.758298"
        android:fillColor="#00000000"
        android:strokeColor="#A5E0A2"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M57.585,226.71C13.38,182.505 13.38,110.835 57.585,66.63C101.79,22.425 173.46,22.425 217.665,66.63C261.87,110.835 261.87,182.505 217.665,226.71C173.461,270.915 101.79,270.915 57.585,226.71Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.758298"
        android:fillColor="#00000000"
        android:strokeColor="#A5E0A2"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M46.714,223.194C2.509,178.989 2.509,107.319 46.714,63.114C90.919,18.909 162.589,18.909 206.794,63.114C250.999,107.319 250.999,178.989 206.794,223.194C162.589,267.399 90.919,267.399 46.714,223.194Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.758298"
        android:fillColor="#00000000"
        android:strokeColor="#A5E0A2"
        android:fillAlpha="0.8"/>
  </group>
  <group>
    <clip-path android:pathData="M21.836,115.085C23.883,113.796 24.944,111.426 24.641,109.038C24.565,108.488 24.528,107.919 24.528,107.35C24.528,100.336 30.215,94.649 37.229,94.649H50.139C51.352,94.649 52.509,94.194 53.419,93.379C56.604,90.592 60.774,88.886 65.324,88.886H84.888C87.22,88.886 89.324,87.464 90.12,85.265C91.675,80.962 95.788,77.891 100.604,77.891H148.926C153.324,77.891 157.116,80.431 158.936,84.128C160.377,87.047 163.334,88.886 166.595,88.886C176.566,88.886 184.642,96.962 184.642,106.933C184.642,116.905 176.566,124.981 166.595,124.981H124.812C125.04,124.981 125.267,125 125.495,125.019C122.917,125.36 120.926,127.559 120.926,130.232C120.926,132.905 122.917,135.104 125.495,135.445C125.267,135.483 125.04,135.483 124.812,135.483H127.41C133.154,135.483 137.798,140.128 137.798,145.872C137.798,151.616 133.154,156.261 127.41,156.261H40.016H33.665H31.011C29.058,156.261 27.238,155.711 25.665,154.782C17.305,151.578 11.39,143.464 11.39,133.986C11.39,126.005 15.561,119.028 21.836,115.085Z M 0,0"/>
    <path
        android:pathData="M21.836,115.085C23.883,113.796 24.944,111.426 24.641,109.038C24.565,108.488 24.528,107.919 24.528,107.35C24.528,100.336 30.215,94.649 37.229,94.649H50.139C51.352,94.649 52.509,94.194 53.419,93.379C56.604,90.592 60.774,88.886 65.324,88.886H84.888C87.22,88.886 89.324,87.464 90.12,85.265C91.675,80.962 95.788,77.891 100.604,77.891H148.926C153.324,77.891 157.116,80.431 158.936,84.128C160.377,87.047 163.334,88.886 166.595,88.886C176.566,88.886 184.642,96.962 184.642,106.933C184.642,116.905 176.566,124.981 166.595,124.981H124.812C125.04,124.981 125.267,125 125.495,125.019C122.917,125.36 120.926,127.559 120.926,130.232C120.926,132.905 122.917,135.104 125.495,135.445C125.267,135.483 125.04,135.483 124.812,135.483H127.41C133.154,135.483 137.798,140.128 137.798,145.872C137.798,151.616 133.154,156.261 127.41,156.261H40.016H33.665H31.011C29.058,156.261 27.238,155.711 25.665,154.782C17.305,151.578 11.39,143.464 11.39,133.986C11.39,126.005 15.561,119.028 21.836,115.085Z"
        android:strokeAlpha="0.8"
        android:fillColor="#94EBAB"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M90.215,294.745C27.699,294.745 -22.98,244.066 -22.98,181.55C-22.98,119.035 27.699,68.355 90.215,68.355C152.731,68.355 203.41,119.035 203.41,181.55C203.41,244.066 152.731,294.745 90.215,294.745Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.947872"
        android:fillColor="#00000000"
        android:strokeColor="#87D69C"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M81.001,298.84C18.486,298.84 -32.194,248.161 -32.194,185.645C-32.194,123.129 18.486,72.45 81.001,72.45C143.517,72.45 194.196,123.129 194.196,185.645C194.196,248.161 143.517,298.84 81.001,298.84Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.947872"
        android:fillColor="#00000000"
        android:strokeColor="#87D69C"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M71.523,304.508C9.007,304.508 -41.672,253.829 -41.672,191.313C-41.672,128.797 9.007,78.118 71.523,78.118C134.039,78.118 184.718,128.797 184.718,191.313C184.718,253.829 134.039,304.508 71.523,304.508Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.947872"
        android:fillColor="#00000000"
        android:strokeColor="#87D69C"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M61.343,309.702C-1.173,309.702 -51.852,259.023 -51.852,196.507C-51.852,133.992 -1.173,83.313 61.343,83.313C123.859,83.313 174.538,133.992 174.538,196.507C174.538,259.023 123.859,309.702 61.343,309.702Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.947872"
        android:fillColor="#00000000"
        android:strokeColor="#87D69C"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M51.182,314.878C-11.334,314.878 -62.013,264.199 -62.013,201.683C-62.013,139.167 -11.334,88.488 51.182,88.488C113.697,88.488 164.376,139.167 164.376,201.683C164.376,264.199 113.697,314.878 51.182,314.878Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.947872"
        android:fillColor="#00000000"
        android:strokeColor="#87D69C"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M41.02,320.072C-21.495,320.072 -72.174,269.393 -72.174,206.878C-72.174,144.362 -21.495,93.683 41.02,93.683C103.536,93.683 154.215,144.362 154.215,206.878C154.215,269.393 103.536,320.072 41.02,320.072Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.947872"
        android:fillColor="#00000000"
        android:strokeColor="#87D69C"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M30.84,325.267C-31.676,325.267 -82.355,274.588 -82.355,212.072C-82.355,149.556 -31.676,98.877 30.84,98.877C93.356,98.877 144.035,149.556 144.035,212.072C144.035,274.588 93.356,325.267 30.84,325.267Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.947872"
        android:fillColor="#00000000"
        android:strokeColor="#87D69C"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M20.679,330.442C-41.837,330.442 -92.516,279.763 -92.516,217.247C-92.516,154.731 -41.837,104.052 20.679,104.052C83.195,104.052 133.874,154.731 133.874,217.247C133.874,279.763 83.195,330.442 20.679,330.442Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.947872"
        android:fillColor="#00000000"
        android:strokeColor="#87D69C"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M10.499,335.637C-52.017,335.637 -102.696,284.958 -102.696,222.442C-102.696,159.926 -52.017,109.247 10.499,109.247C73.015,109.247 123.694,159.926 123.694,222.442C123.694,284.958 73.015,335.637 10.499,335.637Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.947872"
        android:fillColor="#00000000"
        android:strokeColor="#87D69C"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M0.338,340.812C-62.178,340.812 -112.857,290.133 -112.857,227.617C-112.857,165.101 -62.178,114.422 0.338,114.422C62.854,114.422 113.533,165.101 113.533,227.617C113.533,290.133 62.854,340.812 0.338,340.812Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.947872"
        android:fillColor="#00000000"
        android:strokeColor="#87D69C"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M-9.823,346.006C-72.339,346.006 -123.018,295.327 -123.018,232.811C-123.018,170.295 -72.339,119.616 -9.823,119.616C52.693,119.616 103.372,170.295 103.372,232.811C103.372,295.327 52.693,346.006 -9.823,346.006Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.947872"
        android:fillColor="#00000000"
        android:strokeColor="#87D69C"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M-20.004,351.182C-82.52,351.182 -133.199,300.503 -133.199,237.987C-133.199,175.471 -82.52,124.792 -20.004,124.792C42.512,124.792 93.191,175.471 93.191,237.987C93.191,300.503 42.512,351.182 -20.004,351.182Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.947872"
        android:fillColor="#00000000"
        android:strokeColor="#87D69C"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M-30.165,356.376C-92.68,356.376 -143.36,305.697 -143.36,243.181C-143.36,180.665 -92.68,129.986 -30.165,129.986C32.351,129.986 83.03,180.665 83.03,243.181C83.03,305.697 32.351,356.376 -30.165,356.376Z"
        android:strokeAlpha="0.8"
        android:strokeWidth="0.947872"
        android:fillColor="#00000000"
        android:strokeColor="#87D69C"
        android:fillAlpha="0.8"/>
  </group>
  <path
      android:pathData="M19.517,109.461C22.002,106.154 22.607,101.81 21.008,98.007C20.208,96.083 19.755,93.965 19.755,91.761C19.755,87.979 21.051,84.5 23.212,81.755C24.682,79.897 25.46,77.606 25.438,75.229C25.438,75.185 25.438,75.164 25.438,75.12V64.056C25.438,58.264 30.128,53.574 35.92,53.574C41.712,53.574 46.401,58.264 46.401,64.056V75.12C46.401,75.207 46.401,75.315 46.401,75.401C46.336,77.692 47.179,79.918 48.605,81.712C50.81,84.478 52.106,87.958 52.106,91.761C52.106,93.965 51.653,96.083 50.853,98.007C49.254,101.832 49.859,106.154 52.344,109.461C54.008,111.687 55.002,114.453 55.002,117.457C55.002,123.378 51.134,128.392 45.796,130.121C44.283,130.618 43.03,131.634 42.187,132.995C40.89,135.092 38.556,136.475 35.92,136.475C33.283,136.475 30.949,135.092 29.653,132.995C28.81,131.655 27.556,130.618 26.044,130.121C20.705,128.392 16.837,123.378 16.837,117.457C16.837,114.453 17.831,111.687 19.517,109.461Z"
      android:strokeAlpha="0.8"
      android:fillColor="#12B83E"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M35.314,122.644V150.306"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.648335"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M38.621,136.021V136.95C38.621,138.96 37.26,140.71 35.336,141.207"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.648335"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M33.24,136.475C33.24,137.555 33.758,138.571 34.644,139.219L35.336,139.716"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.648335"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M41.82,87.309C42.382,87.309 42.857,86.856 42.857,86.272V82.382C42.857,81.82 42.403,81.345 41.82,81.345C41.258,81.345 40.782,81.799 40.782,82.382V86.272C40.782,86.856 41.258,87.309 41.82,87.309Z"
      android:strokeAlpha="0.8"
      android:fillColor="#3B7A25"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M27.383,109.742C27.945,109.742 28.42,109.288 28.42,108.705V104.815C28.42,104.253 27.967,103.777 27.383,103.777C26.821,103.777 26.346,104.231 26.346,104.815V108.705C26.367,109.267 26.821,109.742 27.383,109.742Z"
      android:strokeAlpha="0.8"
      android:fillColor="#3B7A25"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M38.924,113.199C39.486,113.199 39.961,112.745 39.961,112.162V108.272C39.961,107.71 39.507,107.234 38.924,107.234C38.362,107.234 37.886,107.688 37.886,108.272V112.162C37.886,112.724 38.362,113.199 38.924,113.199Z"
      android:strokeAlpha="0.8"
      android:fillColor="#3B7A25"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M40.89,71.447C41.409,71.447 41.819,71.036 41.819,70.518V66.433C41.819,65.914 41.409,65.504 40.89,65.504C40.371,65.504 39.961,65.914 39.961,66.433V70.518C39.939,71.015 40.371,71.447 40.89,71.447Z"
      android:strokeAlpha="0.8"
      android:fillColor="#3B7A25"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M30.733,79.789C31.187,79.789 31.554,79.421 31.554,78.968V74.991C31.554,74.537 31.187,74.17 30.733,74.17C30.279,74.17 29.912,74.537 29.912,74.991V78.989C29.933,79.443 30.301,79.789 30.733,79.789Z"
      android:strokeAlpha="0.8"
      android:fillColor="#3B7A25"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M33.391,62.867V64.855"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.648335"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M44.629,99.087V100.708"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.648335"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M28.42,117.564V119.185"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.648335"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M33.391,90.551V92.172"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.648335"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M44.629,119.899V121.52"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.648335"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M48.519,113.848V115.468"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.648335"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M150.919,56.028C151.368,55.545 151.522,54.832 151.281,54.218C151.171,53.933 151.116,53.625 151.116,53.296C151.116,52.265 151.708,51.376 152.586,50.949C153.277,50.609 153.738,49.928 153.738,49.16V49.127C153.738,47.306 155.175,45.759 156.996,45.705C158.894,45.65 160.441,47.175 160.441,49.051V52.781C160.441,53.987 160.891,55.15 161.703,56.039C162.537,56.95 163.052,58.178 163.052,59.517C163.052,61.634 161.78,63.455 159.948,64.245C159.498,64.443 159.114,64.75 158.839,65.156C158.368,65.858 157.567,66.33 156.645,66.33H155.384C154.396,66.33 153.541,65.792 153.091,64.991C152.86,64.574 152.542,64.212 152.136,63.982C150.6,63.093 149.558,61.426 149.558,59.528C149.558,58.167 150.074,56.939 150.919,56.028Z"
      android:strokeAlpha="0.8"
      android:fillColor="#12B83E"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M155.998,59.407V73.45"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.329124"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M157.666,66.209V66.681C157.666,67.701 156.974,68.59 155.998,68.842"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.329124"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M154.934,66.429C154.934,66.977 155.197,67.493 155.647,67.822L155.998,68.074"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.329124"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M151.928,62.018C152.334,62.018 152.674,61.689 152.674,61.272V59.736C152.674,59.33 152.345,59.001 151.939,59.001C151.533,59.001 151.193,59.33 151.193,59.736V61.272C151.193,61.689 151.522,62.018 151.928,62.018Z"
      android:strokeAlpha="0.8"
      android:fillColor="#3B7A25"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M159.903,61.053C160.178,61.053 160.397,60.833 160.397,60.559V58.639C160.397,58.365 160.178,58.146 159.903,58.146C159.629,58.146 159.41,58.365 159.41,58.639V60.559C159.41,60.833 159.64,61.053 159.903,61.053Z"
      android:strokeAlpha="0.8"
      android:fillColor="#3B7A25"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M157.665,51.662C157.94,51.662 158.159,51.443 158.159,51.168V49.249C158.159,48.974 157.94,48.755 157.665,48.755C157.391,48.755 157.172,48.974 157.172,49.249V51.168C157.172,51.443 157.391,51.662 157.665,51.662Z"
      android:strokeAlpha="0.8"
      android:fillColor="#3B7A25"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M154.506,54.152V54.975"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.329124"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M157.666,55.798V56.621"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.329124"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M154.506,63.094V63.917"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.329124"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M158.357,65.145C158.631,64.739 159.015,64.432 159.465,64.234C161.286,63.445 162.57,61.623 162.57,59.506C162.57,58.168 162.054,56.95 161.22,56.028C160.408,55.14 159.959,53.977 159.959,52.77V49.04C159.959,47.252 158.554,45.793 156.788,45.694C156.821,45.694 156.854,45.694 156.876,45.694C158.774,45.639 160.321,47.164 160.321,49.04V52.77C160.321,53.977 160.77,55.14 161.582,56.028C162.416,56.939 162.932,58.168 162.932,59.506C162.932,61.623 161.659,63.445 159.827,64.234C159.377,64.432 158.993,64.739 158.719,65.145C158.247,65.847 157.446,66.319 156.525,66.319H156.152C157.084,66.319 157.885,65.847 158.357,65.145Z"
      android:strokeAlpha="0.8"
      android:fillColor="#ffffff"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M152.136,63.972C150.6,63.083 149.558,61.416 149.558,59.518C149.558,58.179 150.074,56.95 150.919,56.04C151.368,55.557 151.522,54.844 151.281,54.23C151.171,53.944 151.116,53.637 151.116,53.308C151.116,52.277 151.708,51.388 152.586,50.96C153.277,50.62 153.738,49.94 153.738,49.172V49.139C153.738,47.318 155.175,45.771 156.996,45.716C158.894,45.661 160.441,47.186 160.441,49.062V52.792C160.441,53.999 160.891,55.162 161.703,56.051C162.537,56.961 163.052,58.19 163.052,59.528C163.052,61.646 161.78,63.467 159.948,64.257C159.498,64.454 159.114,64.762 158.839,65.168C158.368,65.87 157.567,66.341 156.645,66.341H155.384"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.329124"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M70.057,49.285C70.459,48.853 70.597,48.214 70.381,47.665C70.282,47.409 70.233,47.134 70.233,46.84C70.233,45.917 70.763,45.121 71.549,44.738C72.168,44.434 72.58,43.825 72.58,43.138V43.109C72.58,41.479 73.866,40.094 75.496,40.045C77.195,39.996 78.579,41.361 78.579,43.04V46.378C78.579,47.458 78.982,48.499 79.708,49.294C80.454,50.109 80.916,51.209 80.916,52.407C80.916,54.302 79.777,55.932 78.137,56.639C77.735,56.815 77.391,57.09 77.146,57.454C76.724,58.082 76.007,58.504 75.182,58.504H74.053C73.169,58.504 72.403,58.023 72.001,57.306C71.795,56.933 71.51,56.609 71.146,56.403C69.772,55.608 68.839,54.115 68.839,52.417C68.839,51.199 69.301,50.099 70.057,49.285Z"
      android:strokeAlpha="0.8"
      android:fillColor="#12B83E"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M74.603,52.309V64.877"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.294559"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M76.095,58.396V58.819C76.095,59.732 75.477,60.527 74.603,60.753"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.294559"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M73.65,58.593C73.65,59.084 73.886,59.545 74.288,59.84L74.603,60.066"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.294559"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M70.96,54.645C71.323,54.645 71.627,54.351 71.627,53.978V52.603C71.627,52.24 71.333,51.945 70.97,51.945C70.606,51.945 70.302,52.24 70.302,52.603V53.978C70.302,54.351 70.596,54.645 70.96,54.645Z"
      android:strokeAlpha="0.8"
      android:fillColor="#3B7A25"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M78.098,53.782C78.344,53.782 78.54,53.585 78.54,53.34V51.621C78.54,51.376 78.344,51.18 78.098,51.18C77.853,51.18 77.656,51.376 77.656,51.621V53.34C77.656,53.585 77.863,53.782 78.098,53.782Z"
      android:strokeAlpha="0.8"
      android:fillColor="#3B7A25"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M76.095,45.376C76.34,45.376 76.537,45.18 76.537,44.935V43.216C76.537,42.971 76.34,42.774 76.095,42.774C75.849,42.774 75.653,42.971 75.653,43.216V44.935C75.653,45.18 75.849,45.376 76.095,45.376Z"
      android:strokeAlpha="0.8"
      android:fillColor="#3B7A25"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M73.267,47.605V48.342"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.294559"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M76.095,49.078V49.814"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.294559"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M73.267,55.607V56.344"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.294559"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M76.714,57.444C76.959,57.081 77.303,56.806 77.705,56.629C79.335,55.922 80.484,54.292 80.484,52.397C80.484,51.199 80.023,50.109 79.276,49.285C78.55,48.489 78.147,47.449 78.147,46.368V43.03C78.147,41.43 76.891,40.124 75.31,40.035C75.339,40.035 75.369,40.035 75.388,40.035C77.087,39.986 78.471,41.351 78.471,43.03V46.368C78.471,47.449 78.874,48.489 79.6,49.285C80.347,50.1 80.808,51.199 80.808,52.397C80.808,54.292 79.669,55.922 78.029,56.629C77.627,56.806 77.283,57.081 77.038,57.444C76.616,58.072 75.899,58.494 75.074,58.494H74.74C75.575,58.494 76.291,58.072 76.714,57.444Z"
      android:strokeAlpha="0.8"
      android:fillColor="#ffffff"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M71.146,56.393C69.772,55.598 68.839,54.105 68.839,52.407C68.839,51.209 69.301,50.109 70.057,49.294C70.459,48.862 70.597,48.224 70.381,47.674C70.282,47.419 70.233,47.144 70.233,46.849C70.233,45.926 70.763,45.131 71.549,44.748C72.168,44.444 72.58,43.835 72.58,43.148V43.118C72.58,41.488 73.866,40.104 75.496,40.055C77.195,40.006 78.579,41.371 78.579,43.05V46.388C78.579,47.468 78.982,48.509 79.708,49.304C80.454,50.119 80.916,51.219 80.916,52.417C80.916,54.312 79.777,55.942 78.137,56.648C77.735,56.825 77.391,57.1 77.146,57.464C76.724,58.092 76.007,58.514 75.182,58.514H74.053"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.294559"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.902,77.469C99.502,76.825 99.707,75.873 99.385,75.053C99.239,74.673 99.165,74.263 99.165,73.824C99.165,72.448 99.956,71.262 101.127,70.691C102.049,70.238 102.664,69.33 102.664,68.305V68.261C102.664,65.831 104.582,63.768 107.012,63.694C109.544,63.621 111.608,65.656 111.608,68.159V73.136C111.608,74.746 112.208,76.298 113.291,77.483C114.404,78.698 115.092,80.338 115.092,82.124C115.092,84.949 113.394,87.379 110.949,88.433C110.349,88.696 109.837,89.106 109.471,89.648C108.841,90.585 107.773,91.214 106.543,91.214H104.86C103.542,91.214 102.401,90.497 101.8,89.428C101.493,88.872 101.069,88.389 100.527,88.081C98.478,86.896 97.087,84.671 97.087,82.138C97.087,80.323 97.775,78.684 98.902,77.469Z"
      android:strokeAlpha="0.8"
      android:fillColor="#12B83E"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M105.679,81.978V100.714"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.439143"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M107.904,91.053V91.682C107.904,93.044 106.982,94.229 105.679,94.566"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.439143"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M104.259,91.346C104.259,92.078 104.611,92.766 105.211,93.205L105.679,93.541"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.439143"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M100.249,85.461C100.79,85.461 101.244,85.022 101.244,84.466V82.416C101.244,81.875 100.805,81.436 100.263,81.436C99.722,81.436 99.268,81.875 99.268,82.416V84.466C99.268,85.022 99.707,85.461 100.249,85.461Z"
      android:strokeAlpha="0.8"
      android:fillColor="#3B7A25"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M110.891,84.173C111.257,84.173 111.549,83.88 111.549,83.514V80.953C111.549,80.587 111.257,80.294 110.891,80.294C110.525,80.294 110.232,80.587 110.232,80.953V83.514C110.232,83.88 110.539,84.173 110.891,84.173Z"
      android:strokeAlpha="0.8"
      android:fillColor="#3B7A25"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M107.904,71.643C108.27,71.643 108.563,71.35 108.563,70.984V68.422C108.563,68.056 108.27,67.764 107.904,67.764C107.538,67.764 107.246,68.056 107.246,68.422V70.984C107.246,71.35 107.538,71.643 107.904,71.643Z"
      android:strokeAlpha="0.8"
      android:fillColor="#3B7A25"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M103.688,74.966V76.064"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.439143"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M107.904,77.161V78.259"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.439143"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.688,86.896V87.993"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.439143"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M108.827,89.633C109.193,89.091 109.705,88.682 110.305,88.418C112.735,87.364 114.448,84.934 114.448,82.109C114.448,80.323 113.76,78.698 112.647,77.469C111.564,76.283 110.964,74.731 110.964,73.121V68.144C110.964,65.758 109.09,63.811 106.733,63.68C106.777,63.68 106.821,63.68 106.85,63.68C109.383,63.606 111.447,65.641 111.447,68.144V73.121C111.447,74.731 112.047,76.283 113.13,77.469C114.243,78.684 114.931,80.323 114.931,82.109C114.931,84.934 113.233,87.364 110.788,88.418C110.188,88.682 109.676,89.091 109.31,89.633C108.68,90.57 107.612,91.199 106.382,91.199H105.884C107.129,91.199 108.197,90.57 108.827,89.633Z"
      android:strokeAlpha="0.8"
      android:fillColor="#ffffff"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M100.527,88.067C98.478,86.881 97.087,84.656 97.087,82.124C97.087,80.338 97.775,78.698 98.902,77.483C99.502,76.839 99.707,75.888 99.385,75.068C99.239,74.688 99.165,74.278 99.165,73.839C99.165,72.463 99.956,71.277 101.127,70.706C102.049,70.252 102.664,69.345 102.664,68.32V68.276C102.664,65.846 104.582,63.782 107.012,63.709C109.544,63.636 111.608,65.671 111.608,68.174V73.15C111.608,74.761 112.208,76.312 113.291,77.498C114.404,78.713 115.092,80.353 115.092,82.138C115.092,84.964 113.394,87.393 110.949,88.447C110.349,88.711 109.837,89.121 109.471,89.662C108.841,90.599 107.773,91.229 106.543,91.229H104.86"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.439143"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M41.366,132.974C42.209,131.634 43.462,130.596 44.975,130.099C50.313,128.37 54.181,123.357 54.181,117.435C54.181,114.431 53.187,111.665 51.523,109.439C49.038,106.133 48.433,101.789 50.032,97.985C50.832,96.062 51.285,93.944 51.285,91.74C51.285,87.936 49.967,84.457 47.784,81.69C46.358,79.897 45.515,77.692 45.58,75.38C45.58,75.293 45.58,75.185 45.58,75.099V64.034C45.58,58.394 41.106,53.79 35.509,53.574C35.639,53.574 35.79,53.574 35.92,53.574C41.712,53.574 46.401,58.264 46.401,64.056V75.12C46.401,75.207 46.401,75.315 46.401,75.401C46.336,77.692 47.179,79.918 48.605,81.712C50.81,84.478 52.106,87.958 52.106,91.761C52.106,93.965 51.653,96.083 50.853,98.007C49.254,101.832 49.859,106.154 52.344,109.461C54.008,111.687 55.002,114.453 55.002,117.457C55.002,123.378 51.134,128.392 45.796,130.121C44.283,130.618 43.03,131.634 42.187,132.995C40.89,135.092 38.556,136.475 35.92,136.475C35.79,136.475 35.639,136.475 35.509,136.453C37.973,136.323 40.134,134.962 41.366,132.974Z"
      android:strokeAlpha="0.8"
      android:fillColor="#ffffff"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M19.755,91.761C19.755,87.979 21.051,84.5 23.212,81.755C24.682,79.897 25.46,77.606 25.438,75.229C25.438,75.185 25.438,75.164 25.438,75.12V64.056C25.438,58.264 30.128,53.574 35.92,53.574C41.712,53.574 46.401,58.264 46.401,64.056V75.12C46.401,75.207 46.401,75.315 46.401,75.401C46.336,77.692 47.179,79.918 48.605,81.712C50.81,84.478 52.106,87.958 52.106,91.761C52.106,93.965 51.653,96.083 50.853,98.007C49.254,101.832 49.859,106.154 52.344,109.461C54.008,111.687 55.002,114.453 55.002,117.457C55.002,123.378 51.134,128.392 45.796,130.121C44.283,130.618 43.03,131.634 42.187,132.995C40.89,135.092 38.556,136.475 35.92,136.475"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.648335"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M26.044,130.12C20.705,128.391 16.837,123.378 16.837,117.456C16.837,114.452 17.831,111.686 19.495,109.46"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.648335"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M115.125,148.677H106.822"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.568723"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M110.538,148.052C110.974,147.047 116.623,135.179 113.514,132.81C110.405,130.44 109.059,143.71 109.059,143.71C109.059,143.71 109.211,136.393 107.126,136.601C105.04,136.81 106.235,146.876 106.235,146.876C106.235,146.876 105.344,142.497 103.713,142.687C102.083,142.895 103.713,148.033 103.713,148.033"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.568723"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M170.064,121.473H161.742"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.568723"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M165.476,120.847C165.912,119.843 171.562,107.975 168.453,105.606C165.344,103.236 163.998,116.506 163.998,116.506C163.998,116.506 164.149,109.189 162.064,109.397C159.979,109.606 161.173,119.672 161.173,119.672C161.173,119.672 160.282,115.293 158.652,115.482C157.021,115.691 158.652,120.828 158.652,120.828"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.568723"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M121.306,109.454C121.552,108.904 124.699,102.269 122.955,100.942C121.211,99.615 120.471,107.028 120.471,107.028C120.471,107.028 120.547,102.952 119.391,103.066C118.234,103.179 118.898,108.81 118.898,108.81C118.898,108.81 118.405,106.364 117.495,106.478C116.585,106.592 117.495,109.473 117.495,109.473"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.568723"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M115.012,108.316L115.846,109.833"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.568723"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M52.225,151.406H40.945"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.568723"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M48.092,150.895C48.092,150.478 51.334,141.245 49.248,140.62C47.163,139.994 46.575,150.895 46.575,150.895C46.575,150.895 46.253,144.696 44.85,144.468C43.428,144.241 45.438,149.814 45.438,149.814"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.568723"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M51.087,146.989L49.514,149.15"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.568723"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M28.679,137.662H24.205"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.568723"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M25.798,137.302C25.798,137.018 28.073,130.554 26.613,130.118C25.153,129.682 24.736,137.302 24.736,137.302C24.736,137.302 24.509,132.961 23.523,132.81C22.537,132.658 23.94,136.544 23.94,136.544"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.568723"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M155.239,87.292H147.353"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.568723"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M152.358,86.932C152.358,86.648 154.633,80.184 153.173,79.747C151.713,79.312 151.296,86.932 151.296,86.932C151.296,86.932 151.069,82.591 150.083,82.439C149.097,82.288 150.5,86.174 150.5,86.174"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.568723"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M27.902,134.554L26.784,136.07"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.568723"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M135.941,111.368H130.064"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.568723"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M169.059,77.89H171.05"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.568723"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M22.423,137.662H21.381"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.568723"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M124.078,92.297L117.638,98.737L131.343,112.442L137.783,106.002L124.078,92.297Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M118.81,97.552L117.612,98.75L131.317,112.455L132.515,111.257L118.81,97.552Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M131.264,99.473L124.062,92.271L117.602,98.731L124.804,105.933C125.969,104.98 127.098,103.956 128.193,102.862C129.287,101.803 130.311,100.638 131.264,99.473Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M63.94,38.608C46.605,55.942 46.605,84.08 63.94,101.414C81.274,118.748 109.411,118.748 126.746,101.414C144.08,84.08 144.08,55.942 126.746,38.608C109.411,21.274 81.309,21.274 63.94,38.608ZM119.402,94.071C106.128,107.345 84.592,107.345 71.318,94.071C58.044,80.796 58.044,59.261 71.318,45.987C84.592,32.712 106.128,32.712 119.402,45.987C132.677,59.261 132.677,80.796 119.402,94.071Z"
      android:fillColor="#C5C5C5"/>
  <path
      android:pathData="M96.384,103.991C105.422,104.274 114.565,100.955 121.45,94.071C134.724,80.797 134.724,59.261 121.45,45.987C114.565,39.103 105.422,35.784 96.384,36.066C104.751,36.313 113.012,39.632 119.402,45.987C132.676,59.261 132.676,80.797 119.402,94.071C113.012,100.426 104.751,103.744 96.384,103.991Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M66.023,101.414C48.688,84.08 48.688,55.943 66.023,38.608C74.425,30.206 85.369,25.864 96.384,25.617C84.663,25.334 72.872,29.676 63.94,38.608C46.605,55.943 46.605,84.08 63.94,101.414C72.872,110.346 84.663,114.688 96.384,114.406C85.369,114.159 74.425,109.852 66.023,101.414Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M185.386,149.57L174.9,160.055C173.912,161.044 172.288,161.044 171.299,160.055L125.828,114.584C124.839,113.595 124.839,111.971 125.828,110.983L136.313,100.497C137.302,99.509 138.926,99.509 139.914,100.497L185.386,145.969C186.374,146.957 186.374,148.581 185.386,149.57Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M172.888,158.465L127.452,112.994C126.463,112.005 126.463,110.381 127.452,109.393L125.863,110.981C124.874,111.97 124.874,113.594 125.863,114.582L171.299,160.054C172.288,161.042 173.912,161.042 174.9,160.054L176.489,158.465C175.5,159.454 173.876,159.454 172.888,158.465Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M177.301,157.653L178.113,156.841L177.301,157.653Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M136.313,100.497L125.828,110.983C124.839,111.971 124.839,113.595 125.828,114.584L129.676,118.432L143.762,104.345L139.914,100.497C138.926,99.509 137.302,99.509 136.313,100.497Z"
      android:fillColor="#C5C5C5"/>
  <path
      android:pathData="M185.386,149.569L174.9,160.055C173.912,161.043 172.288,161.043 171.299,160.055L167.451,156.206L181.537,142.12L185.386,145.968C186.374,146.957 186.374,148.581 185.386,149.569Z"
      android:fillColor="#C5C5C5"/>
  <path
      android:pathData="M185.386,149.57L174.9,160.055C173.912,161.044 172.288,161.044 171.299,160.055L125.828,114.584C124.839,113.595 124.839,111.971 125.828,110.983L136.313,100.497C137.302,99.509 138.926,99.509 139.914,100.497L185.386,145.969C186.374,146.957 186.374,148.581 185.386,149.57Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.05912"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M119.398,94.062C132.675,80.785 132.675,59.259 119.398,45.983C106.121,32.706 84.595,32.706 71.319,45.983C58.042,59.259 58.042,80.785 71.319,94.062C84.595,107.339 106.121,107.339 119.398,94.062Z"
      android:strokeAlpha="0.1"
      android:fillColor="#72BAE5"
      android:fillAlpha="0.1"/>
  <path
      android:pathData="M74.213,49.658C81.062,42.809 90.453,40.055 99.314,41.397"
      android:strokeLineJoin="round"
      android:strokeWidth="2.11824"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.48,42.315C107.964,43.657 112.201,46.128 115.731,49.659"
      android:strokeLineJoin="round"
      android:strokeWidth="2.11824"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M124.592,99.26C107.611,116.241 80.285,116.595 62.88,100.319C63.233,100.672 63.586,101.061 63.939,101.414C81.274,118.748 109.411,118.748 126.745,101.414C144.08,84.079 144.08,55.942 126.745,38.608C126.392,38.255 126.004,37.902 125.651,37.549C141.926,54.989 141.573,82.279 124.592,99.26Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M119.402,94.072C106.128,107.346 84.593,107.346 71.318,94.072C70.965,93.719 70.612,93.33 70.259,92.977C72.236,94.778 74.354,96.296 76.579,97.567C89.535,104.839 106.234,102.968 117.249,91.953C130.17,79.032 130.523,58.309 118.308,44.964C118.696,45.317 119.049,45.67 119.402,46.023C132.677,59.262 132.677,80.797 119.402,94.072Z"
      android:strokeAlpha="0.6"
      android:fillColor="#ffffff"
      android:fillAlpha="0.6"/>
  <path
      android:pathData="M83.249,88.464C85.772,85.941 85.772,81.85 83.249,79.327C80.726,76.804 76.635,76.804 74.112,79.327C71.589,81.85 71.589,85.941 74.112,88.464C76.635,90.987 80.726,90.987 83.249,88.464Z"
      android:strokeAlpha="0.6"
      android:fillColor="#ffffff"
      android:fillAlpha="0.6"/>
  <path
      android:pathData="M71.601,72.009C72.4,71.21 72.4,69.913 71.601,69.114C70.801,68.314 69.504,68.314 68.705,69.114C67.905,69.913 67.905,71.21 68.705,72.009C69.504,72.809 70.801,72.809 71.601,72.009Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M61.398,71.794C60.903,62.509 64.222,53.083 71.318,45.986C84.592,32.712 106.128,32.712 119.402,45.986C132.677,59.261 132.677,80.796 119.402,94.071C106.128,107.345 84.592,107.345 71.318,94.071C68,90.752 65.493,86.904 63.834,82.809M97.373,25.651C85.334,25.087 73.119,29.429 63.94,38.608C46.605,55.942 46.605,84.079 63.94,101.414C81.274,118.748 109.411,118.748 126.746,101.414C144.08,84.079 144.08,55.942 126.746,38.608C123.498,35.36 119.861,32.712 115.978,30.664L97.373,25.651ZM108.917,27.734C106.446,26.958 103.939,26.393 101.362,26.04L108.917,27.734Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.05912"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M139.244,114.018C140.232,114.618 140.585,115.889 139.985,116.877"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.7652"
      android:fillColor="#00000000"
      android:strokeColor="#454545"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M146.057,125.634C147.045,126.234 147.398,127.505 146.798,128.493"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.7652"
      android:fillColor="#00000000"
      android:strokeColor="#454545"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M167.98,146.18C168.969,146.78 169.322,148.051 168.722,149.039"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.7652"
      android:fillColor="#00000000"
      android:strokeColor="#454545"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M146.304,117.796C146.445,116.666 147.505,115.819 148.634,115.96"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.7652"
      android:fillColor="#00000000"
      android:strokeColor="#454545"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M159.296,138.343C158.307,137.708 158.025,136.437 158.66,135.448"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.7652"
      android:fillColor="#00000000"
      android:strokeColor="#454545"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M153.365,125.422C154.071,124.504 155.377,124.363 156.295,125.069"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.7652"
      android:fillColor="#00000000"
      android:strokeColor="#454545"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M168.475,139.12C169.181,138.202 170.487,138.061 171.405,138.767"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.7652"
      android:fillColor="#00000000"
      android:strokeColor="#454545"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M44.77,132.341H49.994"
      android:strokeLineJoin="round"
      android:strokeWidth="1.05912"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
</vector>
