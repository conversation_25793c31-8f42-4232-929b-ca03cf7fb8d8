<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="30dp"
    android:height="34dp"
    android:viewportWidth="30"
    android:viewportHeight="34">
  <path
      android:pathData="M13.8689,19.9108L14.1555,20.2053L14.4422,19.9108C15.149,19.1846 16.1121,18.7762 17.131,18.7762C19.2072,18.7762 20.8967,20.4657 20.8967,22.5418C20.8967,23.4707 20.5253,24.39 19.7904,25.2084L19.7903,25.2086C19.5076,25.5238 19.1323,25.9642 18.71,26.4598C18.6175,26.5683 18.5228,26.6795 18.4263,26.7925C17.8864,27.4251 17.2865,28.1229 16.6981,28.7725C16.1081,29.4237 15.5375,30.0178 15.0561,30.4466C14.8147,30.6615 14.6052,30.8264 14.4336,30.9353C14.3481,30.9895 14.2792,31.0252 14.2264,31.0465C14.1821,31.0643 14.1599,31.0675 14.1555,31.0681C14.1512,31.0675 14.129,31.0643 14.0847,31.0465C14.0319,31.0252 13.963,30.9895 13.8775,30.9353C13.706,30.8264 13.4964,30.6615 13.2551,30.4466C12.7737,30.0179 12.2032,29.4237 11.6133,28.7725C11.025,28.123 10.4251,27.4252 9.8853,26.7925C9.7891,26.6799 9.6948,26.5691 9.6026,26.461C9.1799,25.9649 8.8043,25.524 8.5214,25.2086L8.5213,25.2084C7.7863,24.3899 7.4144,23.4706 7.4144,22.5418C7.4144,20.4657 9.1045,18.7762 11.1801,18.7762C12.199,18.7762 13.1621,19.1846 13.8689,19.9108Z"
      android:strokeWidth="0.8"
      android:fillColor="#C4C9CC"
      android:fillAlpha="0.7"
      android:strokeColor="#BFCCD4"/>
  <path
      android:pathData="M7.6573,21.1562C7.6573,21.1562 7.2702,20.1071 7.0144,22.5418C7.0144,23.5832 7.4328,24.5949 8.2236,25.4756C9.5846,26.9931 13.134,31.4682 14.1555,31.4682C14.6462,31.4682 15.5814,30.278 15.5814,30.278L7.6573,21.1562Z"
      android:fillColor="#B1BEC4"
      android:fillAlpha="0.39"/>
  <path
      android:pathData="M17.7354,20.0878C17.7354,20.0878 18.2465,20.2939 18.5954,20.6712C18.9443,21.0485 19.1144,21.3772 19.1144,21.3772"
      android:strokeLineJoin="round"
      android:strokeWidth="0.595095"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M19.4378,22.0921L19.4804,22.2109L19.4378,22.0921Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="21.8539"
          android:startX="19.1616"
          android:endY="22.449"
          android:endX="19.1616"
          android:type="linear">
        <item android:offset="0" android:color="#7F007755"/>
        <item android:offset="1" android:color="#7F000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M19.4378,22.0921L19.4804,22.2109"
      android:strokeLineJoin="round"
      android:strokeWidth="0.595095"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M20.8315,8.7892L21.0644,9.0284L21.2973,8.7892C21.8494,8.2219 22.6018,7.9028 23.3977,7.9028C25.0196,7.9028 26.3394,9.2226 26.3394,10.8445C26.3394,11.5697 26.0495,12.288 25.4748,12.928L25.4747,12.9282C25.2529,13.1755 24.9584,13.521 24.6272,13.9097C24.5548,13.9948 24.4805,14.0819 24.4049,14.1705C23.9815,14.6666 23.5111,15.2137 23.0498,15.7229C22.5873,16.2335 22.1402,16.699 21.7631,17.0349C21.574,17.2032 21.4102,17.3321 21.2764,17.417C21.2097,17.4593 21.1564,17.4869 21.1157,17.5032C21.0852,17.5155 21.0689,17.5185 21.0644,17.5193C21.0599,17.5185 21.0436,17.5155 21.0131,17.5032C20.9725,17.4869 20.9191,17.4593 20.8525,17.417C20.7187,17.3321 20.5549,17.2032 20.3659,17.0349C19.9888,16.6991 19.5417,16.2335 19.0792,15.7229C18.618,15.2137 18.1477,14.6666 17.7243,14.1705C17.649,14.0822 17.5749,13.9953 17.5027,13.9105C17.1712,13.5215 16.8766,13.1756 16.6546,12.9282L16.6545,12.928C16.0797,12.2879 15.7894,11.5696 15.7894,10.8445C15.7894,9.2227 17.1097,7.9028 18.7311,7.9028C19.5271,7.9028 20.2794,8.2219 20.8315,8.7892Z"
      android:strokeWidth="0.65"
      android:fillColor="#C4C9CC"
      android:fillAlpha="0.7"
      android:strokeColor="#BFCCD4"/>
  <path
      android:pathData="M15.9682,9.7579C15.9682,9.7579 15.6646,8.9352 15.4641,10.8444C15.4641,11.6611 15.7921,12.4544 16.4123,13.1451C17.4796,14.3351 20.2629,17.8444 21.0641,17.8444C21.4488,17.8444 22.1822,16.9111 22.1822,16.9111L15.9682,9.7579Z"
      android:fillColor="#B1BEC4"
      android:fillAlpha="0.39"/>
  <path
      android:pathData="M23.9292,8.9778C23.9292,8.9778 24.33,9.1394 24.6036,9.4353C24.8772,9.7311 25.0106,9.9889 25.0106,9.9889"
      android:strokeLineJoin="round"
      android:strokeWidth="0.466667"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M25.2642,10.5495L25.2976,10.6427L25.2642,10.5495Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="10.3627"
          android:startX="25.0476"
          android:endY="10.8294"
          android:endX="25.0476"
          android:type="linear">
        <item android:offset="0" android:color="#7F007755"/>
        <item android:offset="1" android:color="#7F000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M25.2642,10.5495L25.2976,10.6427"
      android:strokeLineJoin="round"
      android:strokeWidth="0.466667"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M10.8858,3.6264L11.065,3.8104L11.2441,3.6264C11.679,3.1797 12.2714,2.9283 12.8983,2.9283C14.1756,2.9283 15.215,3.9678 15.215,5.245C15.215,5.8163 14.9866,6.382 14.5343,6.8857L14.5342,6.8858C14.3599,7.08 14.1287,7.3514 13.8684,7.6568C13.8115,7.7237 13.7531,7.7921 13.6937,7.8618C13.3611,8.2516 12.9914,8.6815 12.6289,9.0816C12.2655,9.4829 11.914,9.8488 11.6175,10.1129C11.4688,10.2452 11.3399,10.3467 11.2344,10.4136C11.1818,10.447 11.1396,10.4689 11.1073,10.4819C11.0814,10.4923 11.068,10.4945 11.065,10.4949C11.0619,10.4945 11.0486,10.4923 11.0227,10.4819C10.9904,10.4689 10.9481,10.447 10.8956,10.4136C10.7901,10.3467 10.6612,10.2452 10.5125,10.1129C10.2161,9.8488 9.8646,9.4829 9.5012,9.0816C9.1388,8.6815 8.7692,8.2516 8.4366,7.8618C8.3773,7.7924 8.3192,7.7241 8.2624,7.6575C8.002,7.3518 7.7705,7.0802 7.5962,6.8858L7.5961,6.8857C7.1437,6.3819 6.915,5.8162 6.915,5.245C6.915,3.9678 7.9547,2.9283 9.2316,2.9283C9.8585,2.9283 10.451,3.1797 10.8858,3.6264Z"
      android:strokeWidth="0.5"
      android:fillColor="#C4C9CC"
      android:fillAlpha="0.7"
      android:strokeColor="#BFCCD4"/>
  <path
      android:pathData="M7.0611,4.3913C7.0611,4.3913 6.8225,3.7449 6.665,5.245C6.665,5.8867 6.9227,6.51 7.41,7.0527C8.2486,7.9877 10.4355,10.745 11.065,10.745C11.3673,10.745 11.9435,10.0117 11.9435,10.0117L7.0611,4.3913Z"
      android:fillColor="#B1BEC4"
      android:fillAlpha="0.39"/>
  <path
      android:pathData="M12.8163,3.2783C12.8163,3.2783 13.1312,3.4053 13.3462,3.6378C13.5611,3.8702 13.666,4.0728 13.666,4.0728"
      android:strokeLineJoin="round"
      android:strokeWidth="0.366667"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M13.8652,4.5132L13.8915,4.5865L13.8652,4.5132Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="4.36652"
          android:startX="13.695"
          android:endY="4.73318"
          android:endX="13.695"
          android:type="linear">
        <item android:offset="0" android:color="#7F007755"/>
        <item android:offset="1" android:color="#7F000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M13.8652,4.5132L13.8915,4.5865"
      android:strokeLineJoin="round"
      android:strokeWidth="0.366667"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
</vector>
