<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="332dp"
    android:height="319dp"
    android:viewportWidth="332"
    android:viewportHeight="319">
  <path
      android:pathData="M234.669,240.22L322.883,242.282L326.494,263.78H236.965L234.669,240.22Z"
      android:fillColor="#94EBAB"/>
  <path
      android:pathData="M28.673,259.22C27.566,259.22 26.519,259.08 25.533,258.8C24.559,258.52 23.746,258.14 23.093,257.66L23.993,255.22C24.619,255.66 25.333,256.007 26.133,256.26C26.946,256.513 27.793,256.64 28.673,256.64C29.633,256.64 30.319,256.493 30.733,256.2C31.159,255.893 31.373,255.513 31.373,255.06C31.373,254.673 31.226,254.367 30.933,254.14C30.639,253.913 30.126,253.72 29.393,253.56L27.133,253.08C24.613,252.547 23.353,251.24 23.353,249.16C23.353,248.267 23.593,247.487 24.073,246.82C24.553,246.14 25.219,245.613 26.073,245.24C26.939,244.867 27.939,244.68 29.073,244.68C30.046,244.68 30.959,244.82 31.813,245.1C32.666,245.38 33.373,245.773 33.933,246.28L33.033,248.56C31.939,247.693 30.613,247.26 29.053,247.26C28.213,247.26 27.559,247.427 27.093,247.76C26.639,248.08 26.413,248.5 26.413,249.02C26.413,249.407 26.553,249.72 26.833,249.96C27.113,250.2 27.599,250.393 28.293,250.54L30.553,251.02C33.139,251.58 34.433,252.847 34.433,254.82C34.433,255.7 34.193,256.473 33.713,257.14C33.246,257.793 32.579,258.307 31.713,258.68C30.859,259.04 29.846,259.22 28.673,259.22Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M36.185,259V244.9H39.205V250.52C39.552,250.013 39.999,249.633 40.545,249.38C41.092,249.113 41.699,248.98 42.365,248.98C44.672,248.98 45.825,250.353 45.825,253.1V259H42.805V253.24C42.805,252.56 42.679,252.073 42.425,251.78C42.172,251.473 41.799,251.32 41.305,251.32C40.665,251.32 40.152,251.52 39.765,251.92C39.392,252.32 39.205,252.853 39.205,253.52V259H36.185Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M52.8,259.22C51.746,259.22 50.826,259.013 50.04,258.6C49.266,258.173 48.667,257.58 48.24,256.82C47.813,256.047 47.6,255.133 47.6,254.08C47.6,253.04 47.813,252.14 48.24,251.38C48.667,250.607 49.266,250.013 50.04,249.6C50.826,249.187 51.746,248.98 52.8,248.98C53.853,248.98 54.766,249.187 55.54,249.6C56.326,250.013 56.933,250.607 57.36,251.38C57.8,252.14 58.02,253.04 58.02,254.08C58.02,255.133 57.8,256.047 57.36,256.82C56.933,257.58 56.326,258.173 55.54,258.6C54.766,259.013 53.853,259.22 52.8,259.22ZM52.8,256.96C53.44,256.96 53.96,256.727 54.36,256.26C54.773,255.793 54.98,255.067 54.98,254.08C54.98,253.107 54.773,252.393 54.36,251.94C53.96,251.473 53.44,251.24 52.8,251.24C52.16,251.24 51.64,251.473 51.24,251.94C50.84,252.393 50.64,253.107 50.64,254.08C50.64,255.067 50.84,255.793 51.24,256.26C51.64,256.727 52.16,256.96 52.8,256.96Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M59.857,259V249.18H62.817V250.88C63.284,249.733 64.284,249.1 65.817,248.98L66.697,248.92L66.877,251.46L65.177,251.64C63.684,251.787 62.937,252.547 62.937,253.92V259H59.857Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M73.505,259.22C70.745,259.22 69.365,257.88 69.365,255.2V251.44H67.505V249.18H69.365V246.3H72.385V249.18H75.285V251.44H72.385V255.08C72.385,255.64 72.512,256.06 72.765,256.34C73.032,256.62 73.452,256.76 74.025,256.76C74.199,256.76 74.379,256.74 74.565,256.7C74.765,256.66 74.979,256.607 75.205,256.54L75.645,258.74C75.365,258.887 75.032,259 74.645,259.08C74.259,259.173 73.879,259.22 73.505,259.22Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M81.774,259.22C80.614,259.22 79.614,259.013 78.774,258.6C77.947,258.173 77.307,257.58 76.854,256.82C76.413,256.047 76.194,255.14 76.194,254.1C76.194,253.087 76.407,252.2 76.834,251.44C77.274,250.667 77.867,250.067 78.614,249.64C79.374,249.2 80.247,248.98 81.234,248.98C82.66,248.98 83.794,249.433 84.634,250.34C85.474,251.233 85.894,252.447 85.894,253.98V254.74H79.074C79.18,255.513 79.46,256.08 79.914,256.44C80.38,256.787 81.02,256.96 81.834,256.96C82.367,256.96 82.907,256.88 83.454,256.72C84,256.56 84.494,256.313 84.934,255.98L85.734,258C85.24,258.373 84.634,258.673 83.914,258.9C83.207,259.113 82.494,259.22 81.774,259.22ZM81.354,250.98C80.714,250.98 80.194,251.173 79.794,251.56C79.407,251.947 79.167,252.487 79.074,253.18H83.394C83.314,251.713 82.634,250.98 81.354,250.98Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M87.65,259V249.18H90.61V250.88C91.077,249.733 92.077,249.1 93.61,248.98L94.49,248.92L94.67,251.46L92.97,251.64C91.477,251.787 90.73,252.547 90.73,253.92V259H87.65Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M101.459,262.6V249.18H104.419V250.64C104.685,250.133 105.092,249.733 105.639,249.44C106.199,249.133 106.825,248.98 107.519,248.98C108.372,248.98 109.119,249.187 109.759,249.6C110.412,250.013 110.919,250.6 111.279,251.36C111.639,252.12 111.819,253.027 111.819,254.08C111.819,255.133 111.639,256.047 111.279,256.82C110.919,257.58 110.412,258.173 109.759,258.6C109.119,259.013 108.372,259.22 107.519,259.22C106.865,259.22 106.265,259.08 105.719,258.8C105.185,258.52 104.772,258.147 104.479,257.68V262.6H101.459ZM106.619,256.96C107.259,256.96 107.779,256.727 108.179,256.26C108.579,255.793 108.779,255.067 108.779,254.08C108.779,253.107 108.579,252.393 108.179,251.94C107.779,251.473 107.259,251.24 106.619,251.24C105.965,251.24 105.439,251.473 105.039,251.94C104.639,252.393 104.439,253.107 104.439,254.08C104.439,255.067 104.639,255.793 105.039,256.26C105.439,256.727 105.965,256.96 106.619,256.96Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M118.766,259.22C117.606,259.22 116.606,259.013 115.766,258.6C114.939,258.173 114.299,257.58 113.846,256.82C113.406,256.047 113.186,255.14 113.186,254.1C113.186,253.087 113.399,252.2 113.826,251.44C114.266,250.667 114.859,250.067 115.606,249.64C116.366,249.2 117.239,248.98 118.226,248.98C119.652,248.98 120.786,249.433 121.626,250.34C122.466,251.233 122.886,252.447 122.886,253.98V254.74H116.066C116.172,255.513 116.452,256.08 116.906,256.44C117.372,256.787 118.012,256.96 118.826,256.96C119.359,256.96 119.899,256.88 120.446,256.72C120.992,256.56 121.486,256.313 121.926,255.98L122.726,258C122.232,258.373 121.626,258.673 120.906,258.9C120.199,259.113 119.486,259.22 118.766,259.22ZM118.346,250.98C117.706,250.98 117.186,251.173 116.786,251.56C116.399,251.947 116.159,252.487 116.066,253.18H120.386C120.306,251.713 119.626,250.98 118.346,250.98Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M129.362,259.22C128.309,259.22 127.389,259.013 126.602,258.6C125.829,258.173 125.229,257.58 124.802,256.82C124.376,256.047 124.162,255.133 124.162,254.08C124.162,253.04 124.376,252.14 124.802,251.38C125.229,250.607 125.829,250.013 126.602,249.6C127.389,249.187 128.309,248.98 129.362,248.98C130.416,248.98 131.329,249.187 132.102,249.6C132.889,250.013 133.496,250.607 133.922,251.38C134.362,252.14 134.582,253.04 134.582,254.08C134.582,255.133 134.362,256.047 133.922,256.82C133.496,257.58 132.889,258.173 132.102,258.6C131.329,259.013 130.416,259.22 129.362,259.22ZM129.362,256.96C130.002,256.96 130.522,256.727 130.922,256.26C131.336,255.793 131.542,255.067 131.542,254.08C131.542,253.107 131.336,252.393 130.922,251.94C130.522,251.473 130.002,251.24 129.362,251.24C128.722,251.24 128.202,251.473 127.802,251.94C127.402,252.393 127.202,253.107 127.202,254.08C127.202,255.067 127.402,255.793 127.802,256.26C128.202,256.727 128.722,256.96 129.362,256.96Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M136.42,262.6V249.18H139.38V250.64C139.646,250.133 140.053,249.733 140.6,249.44C141.16,249.133 141.786,248.98 142.48,248.98C143.333,248.98 144.08,249.187 144.72,249.6C145.373,250.013 145.88,250.6 146.24,251.36C146.6,252.12 146.78,253.027 146.78,254.08C146.78,255.133 146.6,256.047 146.24,256.82C145.88,257.58 145.373,258.173 144.72,258.6C144.08,259.013 143.333,259.22 142.48,259.22C141.826,259.22 141.226,259.08 140.68,258.8C140.146,258.52 139.733,258.147 139.44,257.68V262.6H136.42ZM141.58,256.96C142.22,256.96 142.74,256.727 143.14,256.26C143.54,255.793 143.74,255.067 143.74,254.08C143.74,253.107 143.54,252.393 143.14,251.94C142.74,251.473 142.22,251.24 141.58,251.24C140.926,251.24 140.4,251.473 140,251.94C139.6,252.393 139.4,253.107 139.4,254.08C139.4,255.067 139.6,255.793 140,256.26C140.4,256.727 140.926,256.96 141.58,256.96Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M152.287,259.22C151.02,259.22 150.093,258.893 149.507,258.24C148.92,257.573 148.627,256.587 148.627,255.28V244.9H151.647V255.16C151.647,256.227 152.12,256.76 153.067,256.76C153.213,256.76 153.353,256.753 153.487,256.74C153.633,256.727 153.773,256.7 153.907,256.66L153.867,259C153.36,259.147 152.833,259.22 152.287,259.22Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M160.289,259.22C159.129,259.22 158.129,259.013 157.289,258.6C156.463,258.173 155.823,257.58 155.369,256.82C154.929,256.047 154.709,255.14 154.709,254.1C154.709,253.087 154.923,252.2 155.349,251.44C155.789,250.667 156.383,250.067 157.129,249.64C157.889,249.2 158.763,248.98 159.749,248.98C161.176,248.98 162.309,249.433 163.149,250.34C163.989,251.233 164.409,252.447 164.409,253.98V254.74H157.589C157.696,255.513 157.976,256.08 158.429,256.44C158.896,256.787 159.536,256.96 160.349,256.96C160.883,256.96 161.423,256.88 161.969,256.72C162.516,256.56 163.009,256.313 163.449,255.98L164.249,258C163.756,258.373 163.149,258.673 162.429,258.9C161.723,259.113 161.009,259.22 160.289,259.22ZM159.869,250.98C159.229,250.98 158.709,251.173 158.309,251.56C157.923,251.947 157.683,252.487 157.589,253.18H161.909C161.829,251.713 161.149,250.98 159.869,250.98Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M171.692,247.28V244.46H174.952V247.28H171.692ZM169.732,262.86L169.532,260.7L170.472,260.6C171.379,260.493 171.832,259.973 171.832,259.04V249.18H174.852V258.64C174.852,259.56 174.712,260.313 174.432,260.9C174.165,261.487 173.719,261.933 173.092,262.24C172.479,262.547 171.645,262.733 170.592,262.8L169.732,262.86Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M181.921,259.22C180.868,259.22 179.948,259.013 179.161,258.6C178.388,258.173 177.788,257.58 177.361,256.82C176.934,256.047 176.721,255.133 176.721,254.08C176.721,253.04 176.934,252.14 177.361,251.38C177.788,250.607 178.388,250.013 179.161,249.6C179.948,249.187 180.868,248.98 181.921,248.98C182.974,248.98 183.888,249.187 184.661,249.6C185.448,250.013 186.054,250.607 186.481,251.38C186.921,252.14 187.141,253.04 187.141,254.08C187.141,255.133 186.921,256.047 186.481,256.82C186.054,257.58 185.448,258.173 184.661,258.6C183.888,259.013 182.974,259.22 181.921,259.22ZM181.921,256.96C182.561,256.96 183.081,256.727 183.481,256.26C183.894,255.793 184.101,255.067 184.101,254.08C184.101,253.107 183.894,252.393 183.481,251.94C183.081,251.473 182.561,251.24 181.921,251.24C181.281,251.24 180.761,251.473 180.361,251.94C179.961,252.393 179.761,253.107 179.761,254.08C179.761,255.067 179.961,255.793 180.361,256.26C180.761,256.727 181.281,256.96 181.921,256.96Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M188.858,247.28V244.46H192.118V247.28H188.858ZM188.978,259V249.18H191.998V259H188.978Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M194.33,259V249.18H197.29V250.62C197.623,250.087 198.07,249.68 198.63,249.4C199.19,249.12 199.816,248.98 200.51,248.98C201.67,248.98 202.536,249.32 203.11,250C203.683,250.667 203.97,251.7 203.97,253.1V259H200.95V253.24C200.95,252.56 200.823,252.073 200.57,251.78C200.316,251.473 199.943,251.32 199.45,251.32C198.81,251.32 198.296,251.52 197.91,251.92C197.536,252.32 197.35,252.853 197.35,253.52V259H194.33Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M215.111,259.22C214.377,259.22 213.731,259.08 213.171,258.8C212.611,258.52 212.164,258.14 211.831,257.66C211.511,257.18 211.351,256.64 211.351,256.04C211.351,255.333 211.537,254.767 211.911,254.34C212.284,253.913 212.891,253.613 213.731,253.44C214.571,253.253 215.684,253.16 217.071,253.16H217.791V252.82C217.791,252.233 217.657,251.82 217.391,251.58C217.124,251.327 216.671,251.2 216.031,251.2C215.497,251.2 214.931,251.287 214.331,251.46C213.744,251.62 213.157,251.867 212.571,252.2L211.751,250.18C212.097,249.953 212.517,249.747 213.011,249.56C213.517,249.373 214.044,249.233 214.591,249.14C215.137,249.033 215.657,248.98 216.151,248.98C217.671,248.98 218.804,249.327 219.551,250.02C220.297,250.7 220.671,251.76 220.671,253.2V259H217.851V257.52C217.651,258.04 217.311,258.453 216.831,258.76C216.364,259.067 215.791,259.22 215.111,259.22ZM215.791,257.2C216.351,257.2 216.824,257.007 217.211,256.62C217.597,256.233 217.791,255.733 217.791,255.12V254.72H217.091C216.064,254.72 215.337,254.813 214.911,255C214.484,255.173 214.271,255.48 214.271,255.92C214.271,256.293 214.397,256.6 214.651,256.84C214.917,257.08 215.297,257.2 215.791,257.2Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M226.684,259.22C225.817,259.22 225.01,259.12 224.264,258.92C223.517,258.72 222.897,258.447 222.404,258.1L223.124,256.14C223.617,256.447 224.177,256.693 224.804,256.88C225.444,257.053 226.077,257.14 226.704,257.14C227.264,257.14 227.677,257.053 227.944,256.88C228.21,256.693 228.344,256.453 228.344,256.16C228.344,255.693 228.004,255.4 227.324,255.28L225.224,254.9C224.384,254.753 223.744,254.453 223.304,254C222.864,253.547 222.644,252.953 222.644,252.22C222.644,251.553 222.83,250.98 223.204,250.5C223.577,250.02 224.09,249.647 224.744,249.38C225.397,249.113 226.15,248.98 227.004,248.98C227.71,248.98 228.397,249.073 229.064,249.26C229.73,249.433 230.304,249.713 230.784,250.1L230.024,252.04C229.624,251.747 229.144,251.507 228.584,251.32C228.037,251.133 227.524,251.04 227.044,251.04C226.444,251.04 226.01,251.14 225.744,251.34C225.477,251.527 225.344,251.767 225.344,252.06C225.344,252.527 225.657,252.82 226.284,252.94L228.384,253.32C229.25,253.467 229.91,253.76 230.364,254.2C230.817,254.627 231.044,255.213 231.044,255.96C231.044,256.987 230.644,257.787 229.844,258.36C229.044,258.933 227.99,259.22 226.684,259.22Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M238.154,250.82V248.76C238.154,248 238.287,247.307 238.554,246.68C238.82,246.04 239.287,245.387 239.954,244.72L241.134,245.68C240.467,246.347 240.06,246.987 239.914,247.6H241.394V250.82H238.154ZM242.394,250.82V248.76C242.394,248 242.527,247.307 242.794,246.68C243.06,246.04 243.527,245.387 244.194,244.72L245.374,245.68C244.707,246.347 244.3,246.987 244.154,247.6H245.634V250.82H242.394Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M255.007,259.22C253.421,259.22 252.074,258.92 250.967,258.32C249.874,257.72 249.041,256.88 248.467,255.8C247.894,254.72 247.607,253.453 247.607,252C247.607,250.507 247.901,249.213 248.487,248.12C249.087,247.027 249.947,246.18 251.067,245.58C252.187,244.98 253.534,244.68 255.107,244.68C256.094,244.68 257.027,244.82 257.907,245.1C258.801,245.38 259.527,245.753 260.087,246.22L259.087,248.64C258.474,248.187 257.847,247.867 257.207,247.68C256.567,247.48 255.874,247.38 255.127,247.38C253.701,247.38 252.627,247.78 251.907,248.58C251.201,249.367 250.847,250.507 250.847,252C250.847,253.507 251.207,254.647 251.927,255.42C252.661,256.193 253.741,256.58 255.167,256.58C255.927,256.58 256.727,256.46 257.567,256.22V253.46H254.527V251.22H260.127V258.14C259.434,258.487 258.634,258.753 257.727,258.94C256.821,259.127 255.914,259.22 255.007,259.22Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M266.231,259.22C264.978,259.22 264.051,258.873 263.451,258.18C262.864,257.487 262.571,256.447 262.571,255.06V249.18H265.591V255.14C265.591,256.3 266.091,256.88 267.091,256.88C267.664,256.88 268.131,256.68 268.491,256.28C268.864,255.867 269.051,255.327 269.051,254.66V249.18H272.071V259H269.131V257.66C268.478,258.7 267.511,259.22 266.231,259.22Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M279.508,259.22C278.348,259.22 277.348,259.013 276.508,258.6C275.681,258.173 275.041,257.58 274.588,256.82C274.148,256.047 273.928,255.14 273.928,254.1C273.928,253.087 274.141,252.2 274.568,251.44C275.008,250.667 275.601,250.067 276.348,249.64C277.108,249.2 277.981,248.98 278.968,248.98C280.395,248.98 281.528,249.433 282.368,250.34C283.208,251.233 283.628,252.447 283.628,253.98V254.74H276.808C276.915,255.513 277.195,256.08 277.648,256.44C278.115,256.787 278.755,256.96 279.568,256.96C280.101,256.96 280.641,256.88 281.188,256.72C281.735,256.56 282.228,256.313 282.668,255.98L283.468,258C282.975,258.373 282.368,258.673 281.648,258.9C280.941,259.113 280.228,259.22 279.508,259.22ZM279.088,250.98C278.448,250.98 277.928,251.173 277.528,251.56C277.141,251.947 276.901,252.487 276.808,253.18H281.128C281.048,251.713 280.368,250.98 279.088,250.98Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M289.145,259.22C288.278,259.22 287.471,259.12 286.724,258.92C285.978,258.72 285.358,258.447 284.865,258.1L285.584,256.14C286.078,256.447 286.638,256.693 287.264,256.88C287.904,257.053 288.538,257.14 289.164,257.14C289.724,257.14 290.138,257.053 290.404,256.88C290.671,256.693 290.804,256.453 290.804,256.16C290.804,255.693 290.464,255.4 289.784,255.28L287.684,254.9C286.844,254.753 286.204,254.453 285.764,254C285.324,253.547 285.104,252.953 285.104,252.22C285.104,251.553 285.291,250.98 285.664,250.5C286.038,250.02 286.551,249.647 287.204,249.38C287.858,249.113 288.611,248.98 289.464,248.98C290.171,248.98 290.858,249.073 291.524,249.26C292.191,249.433 292.764,249.713 293.244,250.1L292.484,252.04C292.084,251.747 291.604,251.507 291.044,251.32C290.498,251.133 289.984,251.04 289.504,251.04C288.904,251.04 288.471,251.14 288.204,251.34C287.938,251.527 287.804,251.767 287.804,252.06C287.804,252.527 288.118,252.82 288.744,252.94L290.844,253.32C291.711,253.467 292.371,253.76 292.824,254.2C293.278,254.627 293.504,255.213 293.504,255.96C293.504,256.987 293.104,257.787 292.304,258.36C291.504,258.933 290.451,259.22 289.145,259.22Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M299.951,259.22C297.191,259.22 295.811,257.88 295.811,255.2V251.44H293.951V249.18H295.811V246.3H298.831V249.18H301.731V251.44H298.831V255.08C298.831,255.64 298.957,256.06 299.211,256.34C299.477,256.62 299.897,256.76 300.471,256.76C300.644,256.76 300.824,256.74 301.011,256.7C301.211,256.66 301.424,256.607 301.651,256.54L302.091,258.74C301.811,258.887 301.477,259 301.091,259.08C300.704,259.173 300.324,259.22 299.951,259.22Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M306.977,259.22C306.11,259.22 305.303,259.12 304.557,258.92C303.81,258.72 303.19,258.447 302.697,258.1L303.417,256.14C303.91,256.447 304.47,256.693 305.097,256.88C305.737,257.053 306.37,257.14 306.997,257.14C307.557,257.14 307.97,257.053 308.237,256.88C308.503,256.693 308.637,256.453 308.637,256.16C308.637,255.693 308.297,255.4 307.617,255.28L305.517,254.9C304.677,254.753 304.037,254.453 303.597,254C303.157,253.547 302.937,252.953 302.937,252.22C302.937,251.553 303.123,250.98 303.497,250.5C303.87,250.02 304.383,249.647 305.037,249.38C305.69,249.113 306.443,248.98 307.297,248.98C308.003,248.98 308.69,249.073 309.357,249.26C310.023,249.433 310.597,249.713 311.077,250.1L310.317,252.04C309.917,251.747 309.437,251.507 308.877,251.32C308.33,251.133 307.817,251.04 307.337,251.04C306.737,251.04 306.303,251.14 306.037,251.34C305.77,251.527 305.637,251.767 305.637,252.06C305.637,252.527 305.95,252.82 306.577,252.94L308.677,253.32C309.543,253.467 310.203,253.76 310.657,254.2C311.11,254.627 311.337,255.213 311.337,255.96C311.337,256.987 310.937,257.787 310.137,258.36C309.337,258.933 308.283,259.22 306.977,259.22Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M313.773,250.98L312.593,250.06C313.26,249.38 313.667,248.727 313.813,248.1H312.333V244.9H315.573V246.96C315.573,247.72 315.44,248.42 315.173,249.06C314.907,249.687 314.44,250.327 313.773,250.98ZM318.013,250.98L316.833,250.06C317.5,249.38 317.907,248.727 318.053,248.1H316.573V244.9H319.813V246.96C319.813,247.72 319.68,248.42 319.413,249.06C319.147,249.687 318.68,250.327 318.013,250.98Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M26.869,291V279.72H27.973L32.309,287.864L36.613,279.72H37.717V291H36.501V282.184L32.741,289.256H31.877L28.069,282.2V291H26.869Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M43.853,291.144C42.626,291.144 41.655,290.787 40.941,290.072C40.226,289.347 39.869,288.366 39.869,287.128C39.869,286.328 40.029,285.624 40.349,285.016C40.669,284.398 41.106,283.923 41.661,283.592C42.226,283.251 42.877,283.08 43.613,283.08C44.669,283.08 45.495,283.422 46.093,284.104C46.69,284.776 46.989,285.704 46.989,286.888V287.384H41.133C41.175,288.27 41.431,288.947 41.901,289.416C42.37,289.875 43.021,290.104 43.853,290.104C44.322,290.104 44.77,290.035 45.197,289.896C45.623,289.747 46.029,289.507 46.413,289.176L46.861,290.088C46.509,290.419 46.061,290.68 45.517,290.872C44.973,291.054 44.418,291.144 43.853,291.144ZM43.645,284.04C42.909,284.04 42.327,284.27 41.901,284.728C41.474,285.187 41.223,285.79 41.149,286.536H45.869C45.837,285.747 45.629,285.134 45.245,284.696C44.871,284.259 44.338,284.04 43.645,284.04Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M48.896,291V283.224H50.16V284.536C50.395,284.067 50.72,283.71 51.136,283.464C51.552,283.208 52.038,283.08 52.592,283.08C53.808,283.08 54.587,283.608 54.928,284.664C55.163,284.174 55.51,283.79 55.968,283.512C56.427,283.224 56.955,283.08 57.552,283.08C59.28,283.08 60.144,284.104 60.144,286.152V291H58.848V286.216C58.848,285.502 58.72,284.979 58.464,284.648C58.219,284.307 57.808,284.136 57.232,284.136C56.603,284.136 56.102,284.36 55.728,284.808C55.355,285.256 55.168,285.854 55.168,286.6V291H53.872V286.216C53.872,285.502 53.744,284.979 53.488,284.648C53.243,284.307 52.832,284.136 52.256,284.136C51.616,284.136 51.11,284.36 50.736,284.808C50.374,285.256 50.192,285.854 50.192,286.6V291H48.896Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M66.503,291.144C65.885,291.144 65.341,291.006 64.871,290.728C64.413,290.451 64.066,290.072 63.831,289.592V291H62.567V279.72H63.863V284.568C64.087,284.11 64.434,283.747 64.903,283.48C65.373,283.214 65.906,283.08 66.503,283.08C67.197,283.08 67.799,283.246 68.311,283.576C68.834,283.896 69.234,284.36 69.511,284.968C69.799,285.566 69.943,286.28 69.943,287.112C69.943,287.934 69.799,288.648 69.511,289.256C69.234,289.854 68.834,290.318 68.311,290.648C67.799,290.979 67.197,291.144 66.503,291.144ZM66.231,290.104C66.957,290.104 67.538,289.848 67.975,289.336C68.413,288.814 68.631,288.072 68.631,287.112C68.631,286.142 68.413,285.4 67.975,284.888C67.538,284.376 66.957,284.12 66.231,284.12C65.506,284.12 64.925,284.376 64.487,284.888C64.05,285.4 63.831,286.142 63.831,287.112C63.831,288.072 64.05,288.814 64.487,289.336C64.925,289.848 65.506,290.104 66.231,290.104Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M75.399,291.144C74.173,291.144 73.202,290.787 72.488,290.072C71.773,289.347 71.415,288.366 71.415,287.128C71.415,286.328 71.576,285.624 71.896,285.016C72.215,284.398 72.653,283.923 73.207,283.592C73.773,283.251 74.424,283.08 75.16,283.08C76.215,283.08 77.042,283.422 77.64,284.104C78.237,284.776 78.535,285.704 78.535,286.888V287.384H72.679C72.722,288.27 72.978,288.947 73.448,289.416C73.917,289.875 74.567,290.104 75.399,290.104C75.869,290.104 76.317,290.035 76.743,289.896C77.17,289.747 77.576,289.507 77.96,289.176L78.408,290.088C78.055,290.419 77.607,290.68 77.063,290.872C76.52,291.054 75.965,291.144 75.399,291.144ZM75.191,284.04C74.455,284.04 73.874,284.27 73.448,284.728C73.021,285.187 72.77,285.79 72.696,286.536H77.415C77.383,285.747 77.175,285.134 76.791,284.696C76.418,284.259 75.885,284.04 75.191,284.04Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M80.411,291V283.224H81.675V284.616C82.091,283.678 82.945,283.16 84.235,283.064L84.699,283.016L84.795,284.136L83.979,284.232C83.243,284.296 82.683,284.531 82.299,284.936C81.915,285.331 81.723,285.875 81.723,286.568V291H80.411Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M88.691,291.144C88.083,291.144 87.507,291.059 86.963,290.888C86.419,290.718 85.971,290.478 85.619,290.168L86.035,289.256C86.419,289.566 86.841,289.795 87.299,289.944C87.769,290.083 88.243,290.152 88.723,290.152C89.331,290.152 89.79,290.04 90.099,289.816C90.409,289.592 90.563,289.288 90.563,288.904C90.563,288.606 90.462,288.371 90.259,288.2C90.057,288.019 89.737,287.88 89.299,287.784L87.843,287.464C86.521,287.176 85.859,286.494 85.859,285.416C85.859,284.712 86.137,284.147 86.691,283.72C87.257,283.294 87.993,283.08 88.899,283.08C89.433,283.08 89.939,283.166 90.419,283.336C90.91,283.496 91.315,283.736 91.635,284.056L91.203,284.968C90.894,284.68 90.531,284.462 90.115,284.312C89.71,284.152 89.305,284.072 88.899,284.072C88.302,284.072 87.849,284.19 87.539,284.424C87.23,284.659 87.075,284.968 87.075,285.352C87.075,285.95 87.47,286.334 88.259,286.504L89.715,286.808C90.398,286.958 90.915,287.198 91.267,287.528C91.619,287.848 91.795,288.286 91.795,288.84C91.795,289.555 91.513,290.12 90.947,290.536C90.382,290.942 89.63,291.144 88.691,291.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M101.176,291.144C100.397,291.144 99.725,290.979 99.16,290.648C98.595,290.307 98.157,289.832 97.848,289.224C97.549,288.606 97.4,287.886 97.4,287.064C97.4,285.827 97.736,284.856 98.408,284.152C99.08,283.438 100.003,283.08 101.176,283.08C101.656,283.08 102.131,283.166 102.6,283.336C103.069,283.507 103.459,283.747 103.768,284.056L103.32,285C103.011,284.702 102.669,284.483 102.296,284.344C101.933,284.206 101.587,284.136 101.256,284.136C100.445,284.136 99.821,284.392 99.384,284.904C98.947,285.406 98.728,286.131 98.728,287.08C98.728,288.008 98.947,288.744 99.384,289.288C99.821,289.822 100.445,290.088 101.256,290.088C101.587,290.088 101.933,290.019 102.296,289.88C102.669,289.742 103.011,289.518 103.32,289.208L103.768,290.152C103.459,290.462 103.064,290.707 102.584,290.888C102.115,291.059 101.645,291.144 101.176,291.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M107.672,291.144C107.138,291.144 106.658,291.043 106.232,290.84C105.816,290.627 105.485,290.339 105.24,289.976C104.994,289.614 104.872,289.208 104.872,288.76C104.872,288.195 105.016,287.747 105.304,287.416C105.602,287.086 106.088,286.851 106.76,286.712C107.442,286.563 108.37,286.488 109.544,286.488H110.056V285.992C110.056,285.331 109.917,284.856 109.64,284.568C109.373,284.27 108.941,284.12 108.344,284.12C107.874,284.12 107.421,284.19 106.984,284.328C106.546,284.456 106.098,284.67 105.64,284.968L105.192,284.024C105.597,283.736 106.088,283.507 106.664,283.336C107.25,283.166 107.81,283.08 108.344,283.08C109.346,283.08 110.088,283.326 110.568,283.816C111.058,284.307 111.304,285.07 111.304,286.104V291H110.088V289.656C109.885,290.115 109.57,290.478 109.144,290.744C108.728,291.011 108.237,291.144 107.672,291.144ZM107.88,290.168C108.52,290.168 109.042,289.95 109.448,289.512C109.853,289.064 110.056,288.499 110.056,287.816V287.336H109.56C108.696,287.336 108.013,287.379 107.512,287.464C107.021,287.539 106.674,287.678 106.472,287.88C106.28,288.072 106.184,288.339 106.184,288.68C106.184,289.118 106.333,289.475 106.632,289.752C106.941,290.03 107.357,290.168 107.88,290.168Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M113.708,291V283.224H114.972V284.552C115.228,284.062 115.591,283.694 116.06,283.448C116.529,283.203 117.057,283.08 117.644,283.08C119.489,283.08 120.412,284.104 120.412,286.152V291H119.116V286.232C119.116,285.507 118.972,284.979 118.684,284.648C118.407,284.307 117.959,284.136 117.34,284.136C116.625,284.136 116.055,284.36 115.628,284.808C115.212,285.246 115.004,285.832 115.004,286.568V291H113.708Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M129.535,291.144C128.927,291.144 128.351,291.059 127.807,290.888C127.263,290.718 126.815,290.478 126.463,290.168L126.879,289.256C127.263,289.566 127.684,289.795 128.143,289.944C128.612,290.083 129.087,290.152 129.567,290.152C130.175,290.152 130.634,290.04 130.943,289.816C131.252,289.592 131.407,289.288 131.407,288.904C131.407,288.606 131.306,288.371 131.103,288.2C130.9,288.019 130.58,287.88 130.143,287.784L128.687,287.464C127.364,287.176 126.703,286.494 126.703,285.416C126.703,284.712 126.98,284.147 127.535,283.72C128.1,283.294 128.836,283.08 129.743,283.08C130.276,283.08 130.783,283.166 131.263,283.336C131.754,283.496 132.159,283.736 132.479,284.056L132.047,284.968C131.738,284.68 131.375,284.462 130.959,284.312C130.554,284.152 130.148,284.072 129.743,284.072C129.146,284.072 128.692,284.19 128.383,284.424C128.074,284.659 127.919,284.968 127.919,285.352C127.919,285.95 128.314,286.334 129.103,286.504L130.559,286.808C131.242,286.958 131.759,287.198 132.111,287.528C132.463,287.848 132.639,288.286 132.639,288.84C132.639,289.555 132.356,290.12 131.791,290.536C131.226,290.942 130.474,291.144 129.535,291.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M138.118,291.144C136.892,291.144 135.921,290.787 135.206,290.072C134.492,289.347 134.134,288.366 134.134,287.128C134.134,286.328 134.294,285.624 134.614,285.016C134.934,284.398 135.372,283.923 135.926,283.592C136.492,283.251 137.142,283.08 137.878,283.08C138.934,283.08 139.761,283.422 140.358,284.104C140.956,284.776 141.254,285.704 141.254,286.888V287.384H135.398C135.441,288.27 135.697,288.947 136.166,289.416C136.636,289.875 137.286,290.104 138.118,290.104C138.588,290.104 139.036,290.035 139.462,289.896C139.889,289.747 140.294,289.507 140.678,289.176L141.126,290.088C140.774,290.419 140.326,290.68 139.782,290.872C139.238,291.054 138.684,291.144 138.118,291.144ZM137.91,284.04C137.174,284.04 136.593,284.27 136.166,284.728C135.74,285.187 135.489,285.79 135.414,286.536H140.134C140.102,285.747 139.894,285.134 139.51,284.696C139.137,284.259 138.604,284.04 137.91,284.04Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M146.634,291.144C145.407,291.144 144.437,290.787 143.722,290.072C143.007,289.347 142.65,288.366 142.65,287.128C142.65,286.328 142.81,285.624 143.13,285.016C143.45,284.398 143.887,283.923 144.442,283.592C145.007,283.251 145.658,283.08 146.394,283.08C147.45,283.08 148.277,283.422 148.874,284.104C149.471,284.776 149.77,285.704 149.77,286.888V287.384H143.914C143.957,288.27 144.213,288.947 144.682,289.416C145.151,289.875 145.802,290.104 146.634,290.104C147.103,290.104 147.551,290.035 147.978,289.896C148.405,289.747 148.81,289.507 149.194,289.176L149.642,290.088C149.29,290.419 148.842,290.68 148.298,290.872C147.754,291.054 147.199,291.144 146.634,291.144ZM146.426,284.04C145.69,284.04 145.109,284.27 144.682,284.728C144.255,285.187 144.005,285.79 143.93,286.536H148.65C148.618,285.747 148.41,285.134 148.026,284.696C147.653,284.259 147.119,284.04 146.426,284.04Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M161.083,291.144C159.27,291.144 157.888,290.632 156.939,289.608C155.99,288.584 155.515,287.171 155.515,285.368C155.515,284.174 155.728,283.144 156.155,282.28C156.582,281.416 157.195,280.75 157.995,280.28C158.795,279.811 159.76,279.576 160.891,279.576C161.691,279.576 162.427,279.699 163.099,279.944C163.771,280.179 164.342,280.526 164.811,280.984L164.299,282.04C163.787,281.592 163.259,281.267 162.715,281.064C162.171,280.862 161.558,280.76 160.875,280.76C159.595,280.76 158.614,281.166 157.931,281.976C157.248,282.776 156.907,283.907 156.907,285.368C156.907,286.862 157.259,288.003 157.963,288.792C158.667,289.582 159.718,289.976 161.115,289.976C162.022,289.976 162.891,289.822 163.723,289.512V286.168H160.859V285.128H164.859V290.344C164.56,290.504 164.198,290.643 163.771,290.76C163.355,290.878 162.912,290.968 162.443,291.032C161.974,291.107 161.52,291.144 161.083,291.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M170.116,291.144C168.25,291.144 167.316,290.115 167.316,288.056V283.224H168.612V288.04C168.612,288.734 168.751,289.246 169.028,289.576C169.316,289.907 169.764,290.072 170.372,290.072C171.034,290.072 171.572,289.854 171.988,289.416C172.404,288.968 172.612,288.376 172.612,287.64V283.224H173.908V291H172.644V289.688C172.399,290.158 172.052,290.52 171.604,290.776C171.167,291.022 170.671,291.144 170.116,291.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M179.837,291.144C178.61,291.144 177.64,290.787 176.925,290.072C176.21,289.347 175.853,288.366 175.853,287.128C175.853,286.328 176.013,285.624 176.333,285.016C176.653,284.398 177.09,283.923 177.645,283.592C178.21,283.251 178.861,283.08 179.597,283.08C180.653,283.08 181.48,283.422 182.077,284.104C182.674,284.776 182.973,285.704 182.973,286.888V287.384H177.117C177.16,288.27 177.416,288.947 177.885,289.416C178.354,289.875 179.005,290.104 179.837,290.104C180.306,290.104 180.754,290.035 181.181,289.896C181.608,289.747 182.013,289.507 182.397,289.176L182.845,290.088C182.493,290.419 182.045,290.68 181.501,290.872C180.957,291.054 180.402,291.144 179.837,291.144ZM179.629,284.04C178.893,284.04 178.312,284.27 177.885,284.728C177.458,285.187 177.208,285.79 177.133,286.536H181.853C181.821,285.747 181.613,285.134 181.229,284.696C180.856,284.259 180.322,284.04 179.629,284.04Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M187.473,291.144C186.865,291.144 186.289,291.059 185.745,290.888C185.201,290.718 184.753,290.478 184.401,290.168L184.817,289.256C185.201,289.566 185.622,289.795 186.081,289.944C186.55,290.083 187.025,290.152 187.505,290.152C188.113,290.152 188.571,290.04 188.881,289.816C189.19,289.592 189.345,289.288 189.345,288.904C189.345,288.606 189.243,288.371 189.041,288.2C188.838,288.019 188.518,287.88 188.081,287.784L186.625,287.464C185.302,287.176 184.641,286.494 184.641,285.416C184.641,284.712 184.918,284.147 185.473,283.72C186.038,283.294 186.774,283.08 187.681,283.08C188.214,283.08 188.721,283.166 189.201,283.336C189.691,283.496 190.097,283.736 190.417,284.056L189.985,284.968C189.675,284.68 189.313,284.462 188.897,284.312C188.491,284.152 188.086,284.072 187.681,284.072C187.083,284.072 186.63,284.19 186.321,284.424C186.011,284.659 185.857,284.968 185.857,285.352C185.857,285.95 186.251,286.334 187.041,286.504L188.497,286.808C189.179,286.958 189.697,287.198 190.049,287.528C190.401,287.848 190.577,288.286 190.577,288.84C190.577,289.555 190.294,290.12 189.729,290.536C189.163,290.942 188.411,291.144 187.473,291.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M195.594,291.144C194.676,291.144 193.988,290.904 193.53,290.424C193.071,289.934 192.842,289.23 192.842,288.312V284.232H191.322V283.224H192.842V280.84H194.138V283.224H196.602V284.232H194.138V288.184C194.138,288.792 194.266,289.256 194.522,289.576C194.778,289.886 195.194,290.04 195.77,290.04C195.94,290.04 196.111,290.019 196.282,289.976C196.452,289.934 196.607,289.891 196.746,289.848L196.97,290.84C196.831,290.915 196.628,290.984 196.362,291.048C196.095,291.112 195.839,291.144 195.594,291.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M200.691,291.144C200.083,291.144 199.507,291.059 198.963,290.888C198.419,290.718 197.971,290.478 197.619,290.168L198.035,289.256C198.419,289.566 198.841,289.795 199.299,289.944C199.769,290.083 200.243,290.152 200.723,290.152C201.331,290.152 201.79,290.04 202.099,289.816C202.409,289.592 202.563,289.288 202.563,288.904C202.563,288.606 202.462,288.371 202.259,288.2C202.057,288.019 201.737,287.88 201.299,287.784L199.843,287.464C198.521,287.176 197.859,286.494 197.859,285.416C197.859,284.712 198.137,284.147 198.691,283.72C199.257,283.294 199.993,283.08 200.899,283.08C201.433,283.08 201.939,283.166 202.419,283.336C202.91,283.496 203.315,283.736 203.635,284.056L203.203,284.968C202.894,284.68 202.531,284.462 202.115,284.312C201.71,284.152 201.305,284.072 200.899,284.072C200.302,284.072 199.849,284.19 199.539,284.424C199.23,284.659 199.075,284.968 199.075,285.352C199.075,285.95 199.47,286.334 200.259,286.504L201.715,286.808C202.398,286.958 202.915,287.198 203.267,287.528C203.619,287.848 203.795,288.286 203.795,288.84C203.795,289.555 203.513,290.12 202.947,290.536C202.382,290.942 201.63,291.144 200.691,291.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M209.88,291V283.224H211.144V284.552C211.4,284.062 211.763,283.694 212.232,283.448C212.701,283.203 213.229,283.08 213.816,283.08C215.661,283.08 216.584,284.104 216.584,286.152V291H215.288V286.232C215.288,285.507 215.144,284.979 214.856,284.648C214.579,284.307 214.131,284.136 213.512,284.136C212.797,284.136 212.227,284.36 211.8,284.808C211.384,285.246 211.176,285.832 211.176,286.568V291H209.88Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M222.478,291.144C221.251,291.144 220.28,290.787 219.566,290.072C218.851,289.347 218.494,288.366 218.494,287.128C218.494,286.328 218.654,285.624 218.974,285.016C219.294,284.398 219.731,283.923 220.286,283.592C220.851,283.251 221.502,283.08 222.238,283.08C223.294,283.08 224.12,283.422 224.718,284.104C225.315,284.776 225.614,285.704 225.614,286.888V287.384H219.758C219.8,288.27 220.056,288.947 220.526,289.416C220.995,289.875 221.646,290.104 222.478,290.104C222.947,290.104 223.395,290.035 223.822,289.896C224.248,289.747 224.654,289.507 225.038,289.176L225.486,290.088C225.134,290.419 224.686,290.68 224.142,290.872C223.598,291.054 223.043,291.144 222.478,291.144ZM222.27,284.04C221.534,284.04 220.952,284.27 220.526,284.728C220.099,285.187 219.848,285.79 219.774,286.536H224.494C224.462,285.747 224.254,285.134 223.87,284.696C223.496,284.259 222.963,284.04 222.27,284.04Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M229.937,291.144C229.404,291.144 228.924,291.043 228.497,290.84C228.081,290.627 227.751,290.339 227.505,289.976C227.26,289.614 227.137,289.208 227.137,288.76C227.137,288.195 227.281,287.747 227.569,287.416C227.868,287.086 228.353,286.851 229.025,286.712C229.708,286.563 230.636,286.488 231.809,286.488H232.321V285.992C232.321,285.331 232.183,284.856 231.905,284.568C231.639,284.27 231.207,284.12 230.609,284.12C230.14,284.12 229.687,284.19 229.249,284.328C228.812,284.456 228.364,284.67 227.905,284.968L227.457,284.024C227.863,283.736 228.353,283.507 228.929,283.336C229.516,283.166 230.076,283.08 230.609,283.08C231.612,283.08 232.353,283.326 232.833,283.816C233.324,284.307 233.569,285.07 233.569,286.104V291H232.353V289.656C232.151,290.115 231.836,290.478 231.409,290.744C230.993,291.011 230.503,291.144 229.937,291.144ZM230.145,290.168C230.785,290.168 231.308,289.95 231.713,289.512C232.119,289.064 232.321,288.499 232.321,287.816V287.336H231.825C230.961,287.336 230.279,287.379 229.777,287.464C229.287,287.539 228.94,287.678 228.737,287.88C228.545,288.072 228.449,288.339 228.449,288.68C228.449,289.118 228.599,289.475 228.897,289.752C229.207,290.03 229.623,290.168 230.145,290.168Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M235.974,291V283.224H237.238V284.616C237.654,283.678 238.507,283.16 239.798,283.064L240.262,283.016L240.358,284.136L239.542,284.232C238.806,284.296 238.246,284.531 237.862,284.936C237.478,285.331 237.286,285.875 237.286,286.568V291H235.974Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M245.644,291.144C245.025,291.144 244.481,291.006 244.012,290.728C243.553,290.451 243.207,290.072 242.972,289.592V291H241.708V279.72H243.004V284.568C243.228,284.11 243.575,283.747 244.044,283.48C244.513,283.214 245.047,283.08 245.644,283.08C246.337,283.08 246.94,283.246 247.452,283.576C247.975,283.896 248.375,284.36 248.652,284.968C248.94,285.566 249.084,286.28 249.084,287.112C249.084,287.934 248.94,288.648 248.652,289.256C248.375,289.854 247.975,290.318 247.452,290.648C246.94,290.979 246.337,291.144 245.644,291.144ZM245.372,290.104C246.097,290.104 246.679,289.848 247.116,289.336C247.553,288.814 247.772,288.072 247.772,287.112C247.772,286.142 247.553,285.4 247.116,284.888C246.679,284.376 246.097,284.12 245.372,284.12C244.647,284.12 244.065,284.376 243.628,284.888C243.191,285.4 242.972,286.142 242.972,287.112C242.972,288.072 243.191,288.814 243.628,289.336C244.065,289.848 244.647,290.104 245.372,290.104Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M251.823,293.88L253.151,290.872L249.871,283.224H251.279L253.855,289.528L256.447,283.224H257.807L253.183,293.88H251.823Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M266.375,291.144C265.458,291.144 264.77,290.904 264.311,290.424C263.852,289.934 263.623,289.23 263.623,288.312V284.232H262.103V283.224H263.623V280.84H264.919V283.224H267.383V284.232H264.919V288.184C264.919,288.792 265.047,289.256 265.303,289.576C265.559,289.886 265.975,290.04 266.551,290.04C266.722,290.04 266.892,290.019 267.063,289.976C267.234,289.934 267.388,289.891 267.527,289.848L267.751,290.84C267.612,290.915 267.41,290.984 267.143,291.048C266.876,291.112 266.62,291.144 266.375,291.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M268.849,291V279.72H270.145V284.504C270.401,284.035 270.758,283.683 271.217,283.448C271.686,283.203 272.209,283.08 272.785,283.08C274.63,283.08 275.553,284.104 275.553,286.152V291H274.257V286.232C274.257,285.507 274.113,284.979 273.825,284.648C273.547,284.307 273.099,284.136 272.481,284.136C271.766,284.136 271.195,284.36 270.769,284.808C270.353,285.246 270.145,285.832 270.145,286.568V291H268.849Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M280.39,291.144C279.857,291.144 279.377,291.043 278.95,290.84C278.534,290.627 278.204,290.339 277.958,289.976C277.713,289.614 277.59,289.208 277.59,288.76C277.59,288.195 277.734,287.747 278.022,287.416C278.321,287.086 278.806,286.851 279.478,286.712C280.161,286.563 281.089,286.488 282.262,286.488H282.774V285.992C282.774,285.331 282.636,284.856 282.358,284.568C282.092,284.27 281.66,284.12 281.062,284.12C280.593,284.12 280.14,284.19 279.702,284.328C279.265,284.456 278.817,284.67 278.358,284.968L277.91,284.024C278.316,283.736 278.806,283.507 279.382,283.336C279.969,283.166 280.529,283.08 281.062,283.08C282.065,283.08 282.806,283.326 283.286,283.816C283.777,284.307 284.022,285.07 284.022,286.104V291H282.806V289.656C282.604,290.115 282.289,290.478 281.862,290.744C281.446,291.011 280.956,291.144 280.39,291.144ZM280.598,290.168C281.238,290.168 281.761,289.95 282.166,289.512C282.572,289.064 282.774,288.499 282.774,287.816V287.336H282.278C281.414,287.336 280.732,287.379 280.23,287.464C279.74,287.539 279.393,287.678 279.19,287.88C278.998,288.072 278.902,288.339 278.902,288.68C278.902,289.118 279.052,289.475 279.35,289.752C279.66,290.03 280.076,290.168 280.598,290.168Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M289.547,291.144C288.629,291.144 287.941,290.904 287.483,290.424C287.024,289.934 286.795,289.23 286.795,288.312V284.232H285.275V283.224H286.795V280.84H288.091V283.224H290.555V284.232H288.091V288.184C288.091,288.792 288.219,289.256 288.475,289.576C288.731,289.886 289.147,290.04 289.723,290.04C289.893,290.04 290.064,290.019 290.235,289.976C290.405,289.934 290.56,289.891 290.699,289.848L290.923,290.84C290.784,290.915 290.581,290.984 290.315,291.048C290.048,291.112 289.792,291.144 289.547,291.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M298.578,291.144C298.045,291.144 297.565,291.043 297.138,290.84C296.722,290.627 296.391,290.339 296.146,289.976C295.901,289.614 295.778,289.208 295.778,288.76C295.778,288.195 295.922,287.747 296.21,287.416C296.509,287.086 296.994,286.851 297.666,286.712C298.349,286.563 299.277,286.488 300.45,286.488H300.962V285.992C300.962,285.331 300.823,284.856 300.546,284.568C300.279,284.27 299.847,284.12 299.25,284.12C298.781,284.12 298.327,284.19 297.89,284.328C297.453,284.456 297.005,284.67 296.546,284.968L296.098,284.024C296.503,283.736 296.994,283.507 297.57,283.336C298.157,283.166 298.717,283.08 299.25,283.08C300.253,283.08 300.994,283.326 301.474,283.816C301.965,284.307 302.21,285.07 302.21,286.104V291H300.994V289.656C300.791,290.115 300.477,290.478 300.05,290.744C299.634,291.011 299.143,291.144 298.578,291.144ZM298.786,290.168C299.426,290.168 299.949,289.95 300.354,289.512C300.759,289.064 300.962,288.499 300.962,287.816V287.336H300.466C299.602,287.336 298.919,287.379 298.418,287.464C297.927,287.539 297.581,287.678 297.378,287.88C297.186,288.072 297.09,288.339 297.09,288.68C297.09,289.118 297.239,289.475 297.538,289.752C297.847,290.03 298.263,290.168 298.786,290.168Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M304.614,291V283.224H305.878V284.616C306.294,283.678 307.148,283.16 308.438,283.064L308.902,283.016L308.998,284.136L308.182,284.232C307.446,284.296 306.886,284.531 306.502,284.936C306.118,285.331 305.926,285.875 305.926,286.568V291H304.614Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M313.759,291.144C312.532,291.144 311.562,290.787 310.847,290.072C310.132,289.347 309.775,288.366 309.775,287.128C309.775,286.328 309.935,285.624 310.255,285.016C310.575,284.398 311.012,283.923 311.567,283.592C312.132,283.251 312.783,283.08 313.519,283.08C314.575,283.08 315.402,283.422 315.999,284.104C316.596,284.776 316.895,285.704 316.895,286.888V287.384H311.039C311.082,288.27 311.338,288.947 311.807,289.416C312.276,289.875 312.927,290.104 313.759,290.104C314.228,290.104 314.676,290.035 315.103,289.896C315.53,289.747 315.935,289.507 316.319,289.176L316.767,290.088C316.415,290.419 315.967,290.68 315.423,290.872C314.879,291.054 314.324,291.144 313.759,291.144ZM313.551,284.04C312.815,284.04 312.234,284.27 311.807,284.728C311.38,285.187 311.13,285.79 311.055,286.536H315.775C315.743,285.747 315.535,285.134 315.151,284.696C314.778,284.259 314.244,284.04 313.551,284.04Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M75.707,315.144C74.928,315.144 74.257,314.979 73.691,314.648C73.126,314.307 72.688,313.832 72.379,313.224C72.08,312.606 71.931,311.886 71.931,311.064C71.931,309.827 72.267,308.856 72.939,308.152C73.611,307.438 74.534,307.08 75.707,307.08C76.187,307.08 76.662,307.166 77.131,307.336C77.601,307.507 77.99,307.747 78.299,308.056L77.851,309C77.542,308.702 77.201,308.483 76.827,308.344C76.465,308.206 76.118,308.136 75.787,308.136C74.977,308.136 74.353,308.392 73.915,308.904C73.478,309.406 73.259,310.131 73.259,311.08C73.259,312.008 73.478,312.744 73.915,313.288C74.353,313.822 74.977,314.088 75.787,314.088C76.118,314.088 76.465,314.019 76.827,313.88C77.201,313.742 77.542,313.518 77.851,313.208L78.299,314.152C77.99,314.462 77.595,314.707 77.115,314.888C76.646,315.059 76.176,315.144 75.707,315.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M82.893,315.144C82.136,315.144 81.48,314.979 80.925,314.648C80.37,314.318 79.938,313.854 79.629,313.256C79.33,312.648 79.181,311.934 79.181,311.112C79.181,310.291 79.33,309.582 79.629,308.984C79.938,308.376 80.37,307.907 80.925,307.576C81.48,307.246 82.136,307.08 82.893,307.08C83.64,307.08 84.29,307.246 84.845,307.576C85.41,307.907 85.842,308.376 86.141,308.984C86.451,309.582 86.605,310.291 86.605,311.112C86.605,311.934 86.451,312.648 86.141,313.256C85.842,313.854 85.41,314.318 84.845,314.648C84.29,314.979 83.64,315.144 82.893,315.144ZM82.893,314.104C83.618,314.104 84.2,313.848 84.637,313.336C85.075,312.814 85.293,312.072 85.293,311.112C85.293,310.142 85.075,309.4 84.637,308.888C84.2,308.376 83.618,308.12 82.893,308.12C82.157,308.12 81.571,308.376 81.133,308.888C80.706,309.4 80.493,310.142 80.493,311.112C80.493,312.072 80.706,312.814 81.133,313.336C81.571,313.848 82.157,314.104 82.893,314.104Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M88.584,315V307.224H89.848V308.536C90.082,308.067 90.408,307.71 90.824,307.464C91.24,307.208 91.725,307.08 92.28,307.08C93.496,307.08 94.274,307.608 94.616,308.664C94.85,308.174 95.197,307.79 95.656,307.512C96.114,307.224 96.642,307.08 97.24,307.08C98.968,307.08 99.832,308.104 99.832,310.152V315H98.536V310.216C98.536,309.502 98.408,308.979 98.152,308.648C97.906,308.307 97.496,308.136 96.92,308.136C96.29,308.136 95.789,308.36 95.416,308.808C95.042,309.256 94.856,309.854 94.856,310.6V315H93.56V310.216C93.56,309.502 93.432,308.979 93.176,308.648C92.93,308.307 92.52,308.136 91.944,308.136C91.304,308.136 90.797,308.36 90.424,308.808C90.061,309.256 89.88,309.854 89.88,310.6V315H88.584Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M102.671,315V308.232H101.135V307.224H102.671V306.952C102.671,305.918 102.927,305.134 103.439,304.6C103.962,304.067 104.762,303.768 105.839,303.704L106.463,303.672L106.575,304.664L105.839,304.712C105.178,304.766 104.698,304.963 104.399,305.304C104.111,305.646 103.967,306.142 103.967,306.792V307.224H106.207V308.232H103.967V315H102.671Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M110.596,315.144C109.839,315.144 109.183,314.979 108.628,314.648C108.074,314.318 107.642,313.854 107.332,313.256C107.034,312.648 106.884,311.934 106.884,311.112C106.884,310.291 107.034,309.582 107.332,308.984C107.642,308.376 108.074,307.907 108.628,307.576C109.183,307.246 109.839,307.08 110.596,307.08C111.343,307.08 111.994,307.246 112.548,307.576C113.114,307.907 113.546,308.376 113.844,308.984C114.154,309.582 114.308,310.291 114.308,311.112C114.308,311.934 114.154,312.648 113.844,313.256C113.546,313.854 113.114,314.318 112.548,314.648C111.994,314.979 111.343,315.144 110.596,315.144ZM110.596,314.104C111.322,314.104 111.903,313.848 112.34,313.336C112.778,312.814 112.996,312.072 112.996,311.112C112.996,310.142 112.778,309.4 112.34,308.888C111.903,308.376 111.322,308.12 110.596,308.12C109.86,308.12 109.274,308.376 108.836,308.888C108.41,309.4 108.196,310.142 108.196,311.112C108.196,312.072 108.41,312.814 108.836,313.336C109.274,313.848 109.86,314.104 110.596,314.104Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M116.255,315V307.224H117.519V308.616C117.935,307.678 118.788,307.16 120.079,307.064L120.543,307.016L120.639,308.136L119.823,308.232C119.087,308.296 118.527,308.531 118.143,308.936C117.759,309.331 117.567,309.875 117.567,310.568V315H116.255Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M125.531,315.144C124.614,315.144 123.926,314.904 123.467,314.424C123.008,313.934 122.779,313.23 122.779,312.312V308.232H121.259V307.224H122.779V304.84H124.075V307.224H126.539V308.232H124.075V312.184C124.075,312.792 124.203,313.256 124.459,313.576C124.715,313.886 125.131,314.04 125.707,314.04C125.878,314.04 126.048,314.019 126.219,313.976C126.39,313.934 126.544,313.891 126.683,313.848L126.907,314.84C126.768,314.915 126.566,314.984 126.299,315.048C126.032,315.112 125.776,315.144 125.531,315.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M130.453,315.144C129.92,315.144 129.44,315.043 129.013,314.84C128.597,314.627 128.266,314.339 128.021,313.976C127.776,313.614 127.653,313.208 127.653,312.76C127.653,312.195 127.797,311.747 128.085,311.416C128.384,311.086 128.869,310.851 129.541,310.712C130.224,310.563 131.152,310.488 132.325,310.488H132.837V309.992C132.837,309.331 132.698,308.856 132.421,308.568C132.154,308.27 131.722,308.12 131.125,308.12C130.656,308.12 130.202,308.19 129.765,308.328C129.328,308.456 128.88,308.67 128.421,308.968L127.973,308.024C128.378,307.736 128.869,307.507 129.445,307.336C130.032,307.166 130.592,307.08 131.125,307.08C132.128,307.08 132.869,307.326 133.349,307.816C133.84,308.307 134.085,309.07 134.085,310.104V315H132.869V313.656C132.666,314.115 132.352,314.478 131.925,314.744C131.509,315.011 131.018,315.144 130.453,315.144ZM130.661,314.168C131.301,314.168 131.824,313.95 132.229,313.512C132.634,313.064 132.837,312.499 132.837,311.816V311.336H132.341C131.477,311.336 130.794,311.379 130.293,311.464C129.802,311.539 129.456,311.678 129.253,311.88C129.061,312.072 128.965,312.339 128.965,312.68C128.965,313.118 129.114,313.475 129.413,313.752C129.722,314.03 130.138,314.168 130.661,314.168Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M140.425,315.144C139.807,315.144 139.263,315.006 138.793,314.728C138.335,314.451 137.988,314.072 137.753,313.592V315H136.489V303.72H137.785V308.568C138.009,308.11 138.356,307.747 138.825,307.48C139.295,307.214 139.828,307.08 140.425,307.08C141.119,307.08 141.721,307.246 142.233,307.576C142.756,307.896 143.156,308.36 143.433,308.968C143.721,309.566 143.865,310.28 143.865,311.112C143.865,311.934 143.721,312.648 143.433,313.256C143.156,313.854 142.756,314.318 142.233,314.648C141.721,314.979 141.119,315.144 140.425,315.144ZM140.153,314.104C140.879,314.104 141.46,313.848 141.897,313.336C142.335,312.814 142.553,312.072 142.553,311.112C142.553,310.142 142.335,309.4 141.897,308.888C141.46,308.376 140.879,308.12 140.153,308.12C139.428,308.12 138.847,308.376 138.409,308.888C137.972,309.4 137.753,310.142 137.753,311.112C137.753,312.072 137.972,312.814 138.409,313.336C138.847,313.848 139.428,314.104 140.153,314.104Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M148.201,315.144C147.412,315.144 146.815,314.915 146.409,314.456C146.015,313.987 145.817,313.31 145.817,312.424V303.72H147.113V312.328C147.113,313.47 147.567,314.04 148.473,314.04C148.74,314.04 148.98,314.008 149.193,313.944L149.161,315.032C148.831,315.107 148.511,315.144 148.201,315.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M153.978,315.144C152.751,315.144 151.78,314.787 151.066,314.072C150.351,313.347 149.994,312.366 149.994,311.128C149.994,310.328 150.154,309.624 150.474,309.016C150.794,308.398 151.231,307.923 151.786,307.592C152.351,307.251 153.002,307.08 153.738,307.08C154.794,307.08 155.62,307.422 156.218,308.104C156.815,308.776 157.114,309.704 157.114,310.888V311.384H151.258C151.3,312.27 151.556,312.947 152.026,313.416C152.495,313.875 153.146,314.104 153.978,314.104C154.447,314.104 154.895,314.035 155.322,313.896C155.748,313.747 156.154,313.507 156.538,313.176L156.986,314.088C156.634,314.419 156.186,314.68 155.642,314.872C155.098,315.054 154.543,315.144 153.978,315.144ZM153.77,308.04C153.034,308.04 152.452,308.27 152.026,308.728C151.599,309.187 151.348,309.79 151.274,310.536H155.994C155.962,309.747 155.754,309.134 155.37,308.696C154.996,308.259 154.463,308.04 153.77,308.04Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M165.163,315L162.267,307.224H163.627L165.819,313.48L168.091,307.224H169.195L171.435,313.496L173.659,307.224H174.955L172.043,315H170.811L168.619,308.968L166.411,315H165.163Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M176.392,305.288V303.832H177.992V305.288H176.392ZM176.552,315V307.224H177.848V315H176.552Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M183.391,315.144C182.473,315.144 181.785,314.904 181.327,314.424C180.868,313.934 180.639,313.23 180.639,312.312V308.232H179.119V307.224H180.639V304.84H181.935V307.224H184.399V308.232H181.935V312.184C181.935,312.792 182.063,313.256 182.319,313.576C182.575,313.886 182.991,314.04 183.567,314.04C183.737,314.04 183.908,314.019 184.079,313.976C184.249,313.934 184.404,313.891 184.543,313.848L184.767,314.84C184.628,314.915 184.425,314.984 184.159,315.048C183.892,315.112 183.636,315.144 183.391,315.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M185.864,315V303.72H187.16V308.504C187.416,308.035 187.774,307.683 188.232,307.448C188.702,307.203 189.224,307.08 189.8,307.08C191.646,307.08 192.568,308.104 192.568,310.152V315H191.272V310.232C191.272,309.507 191.128,308.979 190.84,308.648C190.563,308.307 190.115,308.136 189.496,308.136C188.782,308.136 188.211,308.36 187.784,308.808C187.368,309.246 187.16,309.832 187.16,310.568V315H185.864Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M202.027,315.144C201.334,315.144 200.726,314.979 200.203,314.648C199.691,314.318 199.291,313.854 199.003,313.256C198.726,312.648 198.587,311.934 198.587,311.112C198.587,310.28 198.726,309.566 199.003,308.968C199.291,308.36 199.691,307.896 200.203,307.576C200.726,307.246 201.334,307.08 202.027,307.08C202.635,307.08 203.169,307.214 203.627,307.48C204.097,307.747 204.443,308.115 204.667,308.584V303.72H205.963V315H204.699V313.576C204.475,314.067 204.129,314.451 203.659,314.728C203.19,315.006 202.646,315.144 202.027,315.144ZM202.299,314.104C203.025,314.104 203.606,313.848 204.043,313.336C204.481,312.814 204.699,312.072 204.699,311.112C204.699,310.142 204.481,309.4 204.043,308.888C203.606,308.376 203.025,308.12 202.299,308.12C201.563,308.12 200.977,308.376 200.539,308.888C200.113,309.4 199.899,310.142 199.899,311.112C199.899,312.072 200.113,312.814 200.539,313.336C200.977,313.848 201.563,314.104 202.299,314.104Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M210.844,315.144C210.31,315.144 209.83,315.043 209.404,314.84C208.988,314.627 208.657,314.339 208.412,313.976C208.166,313.614 208.044,313.208 208.044,312.76C208.044,312.195 208.188,311.747 208.476,311.416C208.774,311.086 209.26,310.851 209.932,310.712C210.614,310.563 211.542,310.488 212.716,310.488H213.228V309.992C213.228,309.331 213.089,308.856 212.812,308.568C212.545,308.27 212.113,308.12 211.516,308.12C211.046,308.12 210.593,308.19 210.156,308.328C209.718,308.456 209.27,308.67 208.812,308.968L208.364,308.024C208.769,307.736 209.26,307.507 209.836,307.336C210.422,307.166 210.982,307.08 211.516,307.08C212.518,307.08 213.26,307.326 213.74,307.816C214.23,308.307 214.476,309.07 214.476,310.104V315H213.26V313.656C213.057,314.115 212.742,314.478 212.316,314.744C211.9,315.011 211.409,315.144 210.844,315.144ZM211.052,314.168C211.692,314.168 212.214,313.95 212.62,313.512C213.025,313.064 213.228,312.499 213.228,311.816V311.336H212.732C211.868,311.336 211.185,311.379 210.684,311.464C210.193,311.539 209.846,311.678 209.644,311.88C209.452,312.072 209.356,312.339 209.356,312.68C209.356,313.118 209.505,313.475 209.804,313.752C210.113,314.03 210.529,314.168 211.052,314.168Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M220,315.144C219.083,315.144 218.395,314.904 217.936,314.424C217.477,313.934 217.248,313.23 217.248,312.312V308.232H215.728V307.224H217.248V304.84H218.544V307.224H221.008V308.232H218.544V312.184C218.544,312.792 218.672,313.256 218.928,313.576C219.184,313.886 219.6,314.04 220.176,314.04C220.347,314.04 220.517,314.019 220.688,313.976C220.859,313.934 221.013,313.891 221.152,313.848L221.376,314.84C221.237,314.915 221.035,314.984 220.768,315.048C220.501,315.112 220.245,315.144 220,315.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M222.314,305.288V303.832H223.914V305.288H222.314ZM222.474,315V307.224H223.77V315H222.474Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M226.192,315V307.224H227.456V308.552C227.712,308.062 228.075,307.694 228.544,307.448C229.014,307.203 229.542,307.08 230.128,307.08C231.974,307.08 232.896,308.104 232.896,310.152V315H231.6V310.232C231.6,309.507 231.456,308.979 231.168,308.648C230.891,308.307 230.443,308.136 229.824,308.136C229.11,308.136 228.539,308.36 228.112,308.808C227.696,309.246 227.488,309.832 227.488,310.568V315H226.192Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M238.726,318.024C238.054,318.024 237.419,317.934 236.822,317.752C236.235,317.582 235.713,317.32 235.254,316.968L235.686,316.024C236.166,316.366 236.641,316.611 237.11,316.76C237.59,316.91 238.107,316.984 238.662,316.984C240.198,316.984 240.966,316.195 240.966,314.616V313.24C240.742,313.731 240.39,314.12 239.91,314.408C239.441,314.686 238.902,314.824 238.294,314.824C237.579,314.824 236.961,314.659 236.438,314.328C235.915,313.998 235.51,313.544 235.222,312.968C234.945,312.382 234.806,311.704 234.806,310.936C234.806,310.168 234.945,309.496 235.222,308.92C235.51,308.334 235.915,307.88 236.438,307.56C236.961,307.24 237.579,307.08 238.294,307.08C238.902,307.08 239.441,307.219 239.91,307.496C240.39,307.774 240.742,308.152 240.966,308.632V307.224H242.23V314.44C242.23,315.635 241.931,316.531 241.334,317.128C240.747,317.726 239.878,318.024 238.726,318.024ZM238.534,313.768C239.281,313.768 239.873,313.512 240.31,313C240.747,312.488 240.966,311.8 240.966,310.936C240.966,310.072 240.747,309.39 240.31,308.888C239.873,308.376 239.281,308.12 238.534,308.12C237.798,308.12 237.211,308.376 236.774,308.888C236.337,309.39 236.118,310.072 236.118,310.936C236.118,311.8 236.337,312.488 236.774,313C237.211,313.512 237.798,313.768 238.534,313.768Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M244.597,311.272V310.2H249.077V311.272H244.597Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M254.194,315.144C252.328,315.144 251.394,314.115 251.394,312.056V307.224H252.69V312.04C252.69,312.734 252.829,313.246 253.106,313.576C253.394,313.907 253.842,314.072 254.45,314.072C255.112,314.072 255.65,313.854 256.066,313.416C256.482,312.968 256.69,312.376 256.69,311.64V307.224H257.986V315H256.722V313.688C256.477,314.158 256.13,314.52 255.682,314.776C255.245,315.022 254.749,315.144 254.194,315.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M260.411,317.88V307.224H261.675V308.616C261.91,308.136 262.256,307.763 262.715,307.496C263.184,307.219 263.728,307.08 264.347,307.08C265.04,307.08 265.643,307.246 266.155,307.576C266.678,307.896 267.078,308.36 267.355,308.968C267.643,309.566 267.787,310.28 267.787,311.112C267.787,311.934 267.643,312.648 267.355,313.256C267.078,313.854 266.678,314.318 266.155,314.648C265.643,314.979 265.04,315.144 264.347,315.144C263.75,315.144 263.216,315.011 262.747,314.744C262.278,314.478 261.931,314.11 261.707,313.64V317.88H260.411ZM264.075,314.104C264.8,314.104 265.382,313.848 265.819,313.336C266.256,312.814 266.475,312.072 266.475,311.112C266.475,310.142 266.256,309.4 265.819,308.888C265.382,308.376 264.8,308.12 264.075,308.12C263.35,308.12 262.768,308.376 262.331,308.888C261.894,309.4 261.675,310.142 261.675,311.112C261.675,312.072 261.894,312.814 262.331,313.336C262.768,313.848 263.35,314.104 264.075,314.104Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M269.172,315V313.368H270.804V315H269.172Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M185.027,122.982L312.018,122.982A8.924,8.924 89.948,0 0,320.942 114.058L320.942,114.058A8.924,8.924 89.948,0 0,312.018 105.135L185.027,105.135A8.924,8.924 89.948,0 0,176.103 114.058L176.103,114.058A8.924,8.924 89.948,0 0,185.027 122.982z"
      android:fillColor="#D6EEFF"/>
  <path
      android:pathData="M190.83,60.828L293.124,60.828A7.854,7.854 90.095,0 0,300.978 52.974L300.978,52.974A7.854,7.854 90.095,0 0,293.124 45.121L190.83,45.121A7.854,7.854 90.095,0 0,182.976 52.974L182.976,52.974A7.854,7.854 90.095,0 0,190.83 60.828z"
      android:fillColor="#D6EEFF"/>
  <path
      android:pathData="M202.806,91.461l120.751,-0l0,-16.885l-120.751,-0z"
      android:fillColor="#D6EEFF"/>
  <path
      android:pathData="M202.806,83.018m-8.443,-0a8.443,8.443 0,1 0,16.885 -0a8.443,8.443 0,1 0,-16.885 -0"
      android:fillColor="#D6EEFF"/>
  <path
      android:pathData="M323.557,83.018m-8.443,-0a8.443,8.443 0,1 0,16.885 -0a8.443,8.443 0,1 0,-16.885 -0"
      android:fillColor="#D6EEFF"/>
  <path
      android:pathData="M296.636,105.129H205.082V105.078C205.357,105.111 205.637,105.128 205.921,105.128C209.772,105.128 212.894,102.006 212.894,98.154C212.894,94.338 209.83,91.238 206.028,91.181H297.136V91.463C294.88,91.567 293.071,94.602 293.071,98.332C293.071,101.781 294.619,104.637 296.636,105.129ZM205.082,91.23C205.322,91.201 205.566,91.185 205.814,91.181H205.082V91.23Z"
      android:fillColor="#D6EEFF"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M208.302,74.57H267.598V74.57C263.804,74.569 260.728,71.492 260.728,67.698C260.728,63.934 263.754,60.876 267.506,60.826H208.399C212.151,60.876 215.177,63.934 215.177,67.698C215.177,71.493 212.1,74.57 208.305,74.57C208.304,74.57 208.303,74.57 208.302,74.57V74.57Z"
      android:fillColor="#D6EEFF"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M157.232,28.543L21.606,28.543A9.531,9.531 0,0 0,12.075 38.074L12.075,38.074A9.531,9.531 0,0 0,21.606 47.604L157.232,47.604A9.531,9.531 0,0 0,166.763 38.074L166.763,38.074A9.531,9.531 0,0 0,157.232 28.543z"
      android:fillColor="#D6EEFF"/>
  <path
      android:pathData="M151.036,94.924L41.786,94.924A8.388,8.388 89.136,0 0,33.398 103.312L33.398,103.312A8.388,8.388 89.136,0 0,41.786 111.699L151.036,111.699A8.388,8.388 89.136,0 0,159.424 103.312L159.424,103.312A8.388,8.388 89.136,0 0,151.036 94.924z"
      android:fillColor="#D6EEFF"/>
  <path
      android:pathData="M138.245,62.209l-128.962,0l-0,18.034l128.962,0z"
      android:fillColor="#D6EEFF"/>
  <path
      android:pathData="M138.245,71.226m9.017,0a9.017,9.017 0,1 0,-18.034 0a9.017,9.017 0,1 0,18.034 0"
      android:fillColor="#D6EEFF"/>
  <path
      android:pathData="M9.282,71.226m9.017,0a9.017,9.017 0,1 0,-18.034 0a9.017,9.017 0,1 0,18.034 0"
      android:fillColor="#D6EEFF"/>
  <path
      android:pathData="M38.034,47.611H135.816V47.665C135.523,47.63 135.224,47.612 134.921,47.612C130.807,47.612 127.473,50.946 127.473,55.06C127.473,59.146 130.764,62.464 134.84,62.507H37.502V62.207C39.911,62.096 41.843,58.854 41.843,54.871C41.843,51.186 40.19,48.136 38.034,47.611ZM135.816,62.455C135.549,62.487 135.277,62.505 135.001,62.507H135.816V62.455Z"
      android:fillColor="#D6EEFF"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M132.372,94.925C128.318,94.925 125.032,91.639 125.032,87.585C125.032,83.541 128.304,80.26 132.345,80.246H69.072C73.113,80.26 76.384,83.541 76.384,87.585C76.384,91.638 73.1,94.924 69.047,94.925V94.925H132.375V94.925C132.374,94.925 132.373,94.925 132.372,94.925Z"
      android:fillColor="#D6EEFF"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M279.244,115.195L280.868,118.215C284.918,125.746 285.93,134.542 283.695,142.795L282.315,147.894C280.388,155.01 282.018,162.619 286.692,168.321L295.281,178.798"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M228.593,75.573L208.086,89.947V65.734L228.593,46.333V75.573Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M252.236,192.269L245.887,191.84L246.325,169.408C246.365,167.318 246.706,165.244 247.336,163.245L248.224,160.43C246.678,156.356 245.887,152.048 245.887,147.706V108.463H265.171L263.694,118.032L252.236,192.269Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M290.33,117.032L283.444,116.335L265.66,74.069H277.066L290.33,117.032Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M283.935,114.108L289.32,113.757L290.189,116.569L283.935,114.108Z"
      android:fillColor="#B76452"/>
  <path
      android:pathData="M269.91,200.866H243.965L246.043,191.768L253.357,191.993L267.387,197.372C268.914,197.957 269.91,199.336 269.91,200.866Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M240.228,192.788L246.38,191.839L245.944,169.407C245.903,167.317 245.563,165.243 244.935,163.244L244.049,160.428C245.591,156.354 246.38,152.047 246.38,147.705V108.462H227.14L240.228,192.788Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M237.763,161.121C237.763,162.162 236.919,163.007 235.877,163.007C234.835,163.007 233.991,162.162 233.991,161.121C233.991,160.079 234.835,159.234 235.877,159.234C236.919,159.234 237.763,160.079 237.763,161.121Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M254.412,161.121C254.412,162.162 255.256,163.007 256.298,163.007C257.34,163.007 258.184,162.162 258.184,161.121C258.184,160.079 257.34,159.234 256.298,159.234C255.256,159.234 254.412,160.079 254.412,161.121Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M242.383,160.545L243.615,160.429"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M249.92,160.545L248.688,160.429"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M290.152,123.539L291.064,119.911C291.089,119.81 291.104,119.709 291.109,119.609C291.14,118.907 290.675,118.256 289.967,118.078C289.158,117.875 288.337,118.366 288.134,119.175L287.222,122.803C287.018,123.612 287.509,124.433 288.319,124.636C289.128,124.839 289.948,124.348 290.152,123.539Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M287.246,123.056L288.158,119.429C288.361,118.619 287.87,117.799 287.061,117.595C286.252,117.392 285.431,117.883 285.228,118.692L284.316,122.32C284.112,123.129 284.603,123.95 285.412,124.153C286.221,124.357 287.042,123.866 287.246,123.056Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M284.477,122.361L285.389,118.733C285.593,117.924 285.102,117.103 284.293,116.899C283.483,116.696 282.662,117.187 282.459,117.996L281.547,121.624C281.343,122.433 281.834,123.254 282.644,123.458C283.453,123.661 284.274,123.17 284.477,122.361Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M281.916,119.24L284.309,116.365C284.843,115.723 284.755,114.771 284.114,114.237C283.473,113.703 282.52,113.791 281.986,114.432L279.593,117.307C279.06,117.948 279.147,118.901 279.788,119.435C280.43,119.968 281.382,119.881 281.916,119.24Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M280.841,122.564L281.961,118.995C282.211,118.199 281.768,117.351 280.972,117.101C280.176,116.852 279.328,117.294 279.078,118.09L277.959,121.66C277.709,122.456 278.152,123.304 278.948,123.553C279.744,123.803 280.592,123.36 280.841,122.564Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M292.092,119.649C292.533,118.542 291.992,117.287 290.884,116.847L284.23,114.203C283.122,113.762 281.867,114.304 281.427,115.411C280.987,116.519 281.528,117.774 282.636,118.214L289.29,120.858C290.397,121.298 291.652,120.757 292.092,119.649Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M222.851,53.764C222.629,47.367 227.754,42.061 234.154,42.061H258.61C265.073,42.061 270.221,47.466 269.907,53.921L265.172,108.462L264.773,108.697C253.135,115.526 238.692,115.436 227.14,108.462L224.895,79.823"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M229.45,43.083C230.881,42.428 232.473,42.062 234.153,42.062H254.662"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M226.58,101.314L222.851,53.764C222.761,51.171 223.55,48.757 224.946,46.803"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M258.61,42.061C265.072,42.061 270.22,47.466 269.906,53.92L265.172,108.462L264.772,108.697C253.134,115.526 238.692,115.436 227.14,108.462H227.14"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M252.238,42.591L246.143,53.031L240.049,42.591V27.549H252.238V42.591Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M240.049,32.822L252.238,36.402V29.531L240.049,29.3V32.822Z"
      android:fillColor="#B76452"/>
  <path
      android:pathData="M244.925,41.027C243.805,41.027 242.898,40.12 242.898,39"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M249.535,47.222L246.143,53.033L240.049,42.592V27.55H252.238V42.592L251.373,44.074"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M282.854,88.061H276.373H269.92L252.238,46.333H264.453C266.954,46.333 269.178,47.925 269.985,50.293L282.854,88.061Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M253.341,7.667C253.341,9.326 253.037,10.67 252.662,10.67C252.288,10.67 251.984,9.326 251.984,7.667C251.984,6.009 252.288,4.665 252.662,4.665C253.037,4.665 253.341,6.009 253.341,7.667Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M254.206,9.011C253.271,10.381 252.263,11.32 251.953,11.109C251.644,10.898 252.15,9.616 253.085,8.247C254.02,6.877 255.028,5.938 255.338,6.149C255.647,6.36 255.141,7.642 254.206,9.011Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M236.937,13.437C236.937,14.497 236.077,15.357 235.017,15.357C233.956,15.357 233.096,14.497 233.096,13.437C233.096,12.376 233.956,11.517 235.017,11.517C236.077,11.517 236.937,12.376 236.937,13.437Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M236.387,19.677C236.387,21.495 234.914,22.968 233.096,22.968C231.278,22.968 229.805,21.495 229.805,19.677C229.805,17.859 231.278,16.386 233.096,16.386C234.914,16.386 236.387,17.859 236.387,19.677Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M236.387,19.677C236.387,21.495 234.914,22.968 233.096,22.968C231.278,22.968 229.805,21.495 229.805,19.677C229.805,17.859 231.278,16.386 233.096,16.386C234.914,16.386 236.387,17.859 236.387,19.677Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M258.268,21.229C257.538,28.161 251.327,33.188 244.396,32.458C237.464,31.728 232.437,25.517 233.167,18.586C233.897,11.654 240.108,6.627 247.039,7.357C253.971,8.087 258.998,14.298 258.268,21.229Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M222.4,200.866H248.345L246.267,191.768L238.953,191.993L224.922,197.372C223.396,197.957 222.4,199.336 222.4,200.866Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M243.2,199.383H229.482"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M284.113,114.236L289.142,116.154"
      android:strokeLineJoin="round"
      android:strokeWidth="0.347869"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M281.98,119.9L281.547,121.624C281.343,122.433 281.834,123.254 282.644,123.458C283.453,123.661 284.274,123.17 284.477,122.361L285.389,118.733"
      android:strokeLineJoin="round"
      android:strokeWidth="0.347869"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M285.413,124.153C286.222,124.357 287.043,123.866 287.247,123.056L288.011,119.659"
      android:strokeLineJoin="round"
      android:strokeWidth="0.347869"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M291.109,119.659L290.115,123.503"
      android:strokeLineJoin="round"
      android:strokeWidth="0.347869"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M250.39,199.383H262.827"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M252.854,10.689L242.674,12.517C239.851,13.023 236.943,11.887 235.332,9.514C234.619,8.465 234.233,7.293 234.415,6.195C235.088,2.131 239.477,-0.146 243.188,1.642L249.831,4.845C251.679,5.735 252.854,7.605 252.854,9.656V10.689Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M258.878,17.531C255.551,17.531 252.854,14.834 252.854,11.507V8.379H253.595C256.513,8.379 258.878,10.745 258.878,13.663V17.531Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M238.226,5.108C238.226,6.617 237.002,7.841 235.493,7.841C233.983,7.841 232.76,6.617 232.76,5.108C232.76,3.599 233.983,2.375 235.493,2.375C237.002,2.375 238.226,3.599 238.226,5.108Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M236.301,2.426C236.301,1.392 237.14,0.553 238.174,0.553C239.209,0.553 240.048,1.392 240.048,2.426C240.048,3.461 239.209,4.3 238.174,4.3C237.14,4.3 236.301,3.461 236.301,2.426Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M240.735,9.286C240.735,11.136 239.236,12.635 237.386,12.635C235.537,12.635 234.037,11.136 234.037,9.286C234.037,7.437 235.537,5.937 237.386,5.937C239.236,5.937 240.735,7.437 240.735,9.286Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M242.084,17.305C242.084,18.03 241.496,18.618 240.77,18.618C240.045,18.618 239.457,18.03 239.457,17.305"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M241.391,24.993C240.665,24.993 240.077,24.405 240.077,23.679"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M247.031,24.993C246.305,24.993 245.717,24.405 245.717,23.679"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M245.738,17.305C245.738,18.03 245.15,18.618 244.425,18.618C243.699,18.618 243.111,18.03 243.111,17.305"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M243.235,14.493C244.084,14.028 245.149,14.339 245.614,15.189"
      android:strokeLineJoin="round"
      android:strokeWidth="0.869673"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M239.684,14.909C239.955,13.979 240.929,13.447 241.858,13.718"
      android:strokeLineJoin="round"
      android:strokeWidth="0.869673"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M245.717,23.068C245.717,24.23 244.775,25.173 243.612,25.173C242.449,25.173 241.507,24.23 241.507,23.068V21.005"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M248.473,28.55C250.412,28.55 251.984,26.978 251.984,25.04"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M271.547,88.062H281.386L282.73,92.417L272.181,89.568L271.547,88.062Z"
      android:fillColor="#B76452"/>
  <path
      android:pathData="M265.172,108.463L264.772,108.697C253.672,115.211 240.022,115.425 228.76,109.38L228.767,109.473H228.768C245.886,120.992 264.855,110.565 264.855,110.565L265.172,108.463Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M279.243,115.194L279.018,110.961C278.992,110.467 278.583,110.079 278.088,110.079H277.178C276.403,110.079 275.967,110.969 276.441,111.582L279.243,115.194Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M244.395,32.46C238.392,31.828 233.817,27.085 233.173,21.323"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M233.167,18.587C233.897,11.655 240.108,6.627 247.04,7.358C253.971,8.088 258.999,14.299 258.269,21.23C257.754,26.113 254.52,30.051 250.23,31.697"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M262.168,19.677C262.168,21.495 260.695,22.968 258.877,22.968C257.059,22.968 255.586,21.495 255.586,19.677C255.586,17.859 257.059,16.386 258.877,16.386C260.695,16.386 262.168,17.859 262.168,19.677Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M258.022,20.63C258.022,19.684 258.789,18.917 259.735,18.917"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M255.586,19.677C255.586,17.859 257.059,16.386 258.877,16.386C260.695,16.386 262.168,17.859 262.168,19.677C262.168,21.495 260.695,22.968 258.877,22.968C258.209,22.968 257.587,22.769 257.068,22.427"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M257.088,16.367C257.088,17.173 256.436,17.825 255.63,17.825C254.825,17.825 254.172,17.173 254.172,16.367C254.172,15.562 254.825,14.91 255.63,14.91C256.436,14.91 257.088,15.562 257.088,16.367Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M260.413,14.661C260.413,15.603 259.649,16.367 258.707,16.367C257.765,16.367 257.001,15.603 257.001,14.661C257.001,13.719 257.765,12.955 258.707,12.955C259.649,12.955 260.413,13.719 260.413,14.661Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M259.225,12.455C259.225,12.974 258.805,13.394 258.287,13.394C257.769,13.394 257.348,12.974 257.348,12.455C257.348,11.937 257.769,11.517 258.287,11.517C258.805,11.517 259.225,11.937 259.225,12.455Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M246.38,192.98V182.073"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M246.849,192.379L249.109,201.012"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M242.383,122.688L244.519,124.142C245.342,124.703 246.426,124.696 247.242,124.126L249.303,122.688"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M245.843,124.681V150.151"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M266.821,88.498L264,74.09L267.546,82.458L266.821,88.498Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M263.264,74.646C263.872,74.303 264.248,73.66 264.248,72.962V71.586"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M270.215,84.943H281.565"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#0D9AFF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M274.987,86.059H283.669"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M274.986,64.971L270.633,52.195C270.203,50.931 269.63,49.721 268.925,48.587L267.817,46.804"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M264,74.09L269.921,88.061H276.374H282.854L277.241,71.586"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M241.615,63.656H228.594V65.735L241.615,63.656Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M240.049,62.155H241.613V63.656H228.91V62.155H236.686"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M265.17,108.078L260.93,104.537L265.48,105.268L265.17,108.078Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M264.526,112.638L257.689,156.933"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M255.983,167.991L253.809,182.073"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M236.307,167.99L240.227,192.788"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M236.341,196.317L234.453,193.325"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M237.665,194.992L235.777,192"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M256.326,196.317L258.214,193.325"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M255.002,194.992L256.89,192"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M269.882,179.081C266.746,177.242 264.695,173.787 264.825,169.908C264.403,170.901 264.148,171.984 264.1,173.126C263.931,177.139 266.354,180.66 269.882,182.074V179.081Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M264.825,169.908C264.403,170.9 264.148,171.984 264.1,173.125C263.931,177.139 266.354,180.66 269.882,182.074V179.081"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M281.689,197.716H281.39V188.862L277.876,185.013V199.303C277.876,200.212 278.613,200.949 279.523,200.949H279.633H281.39H283.652V199.679C283.652,198.595 282.773,197.716 281.689,197.716Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M301.684,197.716H301.385V185.013L297.872,188.862V199.303C297.872,200.212 298.609,200.949 299.518,200.949H299.629H301.385H303.647V199.679C303.647,198.595 302.768,197.716 301.684,197.716Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M290.375,192.788H280.77C273.882,192.788 268.298,187.204 268.298,180.316C268.298,179.211 269.194,178.314 270.3,178.314H302.847V180.316C302.847,187.204 297.263,192.788 290.375,192.788Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M275.735,178.316C275.409,178.837 275.218,179.452 275.218,180.112C275.218,181.99 276.74,183.512 278.617,183.512C280.495,183.512 282.017,181.99 282.017,180.112C282.017,180.091 282.014,180.071 282.014,180.05C282.098,180.059 282.184,180.065 282.27,180.065C283.365,180.065 284.285,179.323 284.56,178.316H275.735Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M287.573,179.426C287.573,180.606 288.529,181.562 289.709,181.562C290.889,181.562 291.846,180.606 291.846,179.426C291.846,179.019 291.73,178.639 291.532,178.315H287.887C287.689,178.639 287.573,179.019 287.573,179.426Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M270.3,178.314H302.847V180.316C302.847,181.977 302.522,183.563 301.933,185.012C300.542,188.433 297.676,191.096 294.125,192.214"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M294.723,197.716H294.425V188.862H290.911V190.531V199.303C290.911,200.212 291.648,200.949 292.557,200.949H292.668H294.425H296.687V199.679C296.687,198.595 295.808,197.716 294.723,197.716Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M274.582,197.716H274.284V188.862L270.77,185.013V199.303C270.77,200.212 271.507,200.949 272.416,200.949H272.527H274.284H276.545V199.679C276.545,198.595 275.666,197.716 274.582,197.716Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M290.911,191.251V195.186"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M290.374,192.788H280.769C279.44,192.788 278.16,192.58 276.96,192.196C275.771,191.815 274.66,191.26 273.656,190.562"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M270.77,183.974V193.535"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M301.927,185.021C302.519,183.568 302.848,181.981 302.848,180.316V178.314H293.102C294.605,181.896 297.936,184.518 301.927,185.021Z"
      android:fillColor="#E43C28"/>
  <path
      android:pathData="M301.927,185.021C302.519,183.568 302.848,181.981 302.848,180.316V178.314H293.102C294.605,181.896 297.936,184.518 301.927,185.021Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M298.462,174.114C296.743,175.833 293.835,175.303 292.833,173.088L289.031,164.683L297.436,168.485C299.651,169.487 300.181,172.395 298.462,174.114Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M294.464,168.668C295.201,168.668 295.882,168.433 296.441,168.037L291.057,165.601C291.237,167.324 292.693,168.668 294.464,168.668Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M298.462,174.114C296.743,175.833 293.835,175.303 292.833,173.088L289.031,164.683L297.436,168.485C299.651,169.487 300.181,172.395 298.462,174.114Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M301.704,172.44C300.008,171.423 299.852,169.024 301.402,167.796L307.284,163.135L305.944,170.519C305.59,172.465 303.401,173.458 301.704,172.44Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M301.704,172.44C300.008,171.423 299.852,169.024 301.402,167.796L307.284,163.135L305.944,170.519C305.59,172.465 303.401,173.458 301.704,172.44Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M305.658,170.916C305.658,168.561 303.749,166.652 301.394,166.652H301.328C297.17,166.652 293.799,170.023 293.799,174.181C293.799,179.59 298.184,183.974 303.593,183.974H303.926C311.254,183.974 317.224,178.175 317.504,170.916H305.658Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M302.217,171.969C302.217,172.556 301.741,173.032 301.153,173.032C300.566,173.032 300.09,172.556 300.09,171.969"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M302.566,179.855C301.647,179.855 300.902,179.11 300.902,178.191"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M305.368,171.969C305.368,172.556 304.892,173.032 304.305,173.032C303.718,173.032 303.242,172.556 303.242,171.969"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M314.098,172.391C314.673,173.428 315.508,174.582 316.479,174.853C316.909,174.972 317.358,174.684 317.473,174.253C317.734,173.28 317.914,172.274 318.005,171.242C318.046,170.778 317.676,170.378 317.21,170.378L315.135,170.5C314.198,170.554 313.643,171.569 314.098,172.391Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M312.788,174.614C312.788,174.752 312.676,174.864 312.538,174.864C312.399,174.864 312.287,174.752 312.287,174.614C312.287,174.476 312.399,174.364 312.538,174.364C312.676,174.364 312.788,174.476 312.788,174.614Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M314.424,174.335C314.424,174.425 314.351,174.499 314.26,174.499C314.17,174.499 314.096,174.425 314.096,174.335C314.096,174.245 314.17,174.172 314.26,174.172C314.351,174.172 314.424,174.245 314.424,174.335Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M314.096,175.924C314.096,176.062 313.984,176.174 313.846,176.174C313.708,176.174 313.596,176.062 313.596,175.924C313.596,175.786 313.708,175.674 313.846,175.674C313.984,175.674 314.096,175.786 314.096,175.924Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M299.005,182.835C300.374,183.562 301.936,183.974 303.594,183.974H303.927C306.393,183.974 308.705,183.317 310.699,182.169"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M294.127,171.978C293.914,172.674 293.799,173.414 293.799,174.18C293.799,176.388 294.53,178.426 295.763,180.064"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M303.995,167.535C303.276,166.981 302.374,166.651 301.395,166.651H301.329C299,166.651 296.918,167.709 295.537,169.37"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M317.504,170.915H305.658C305.658,170.37 305.556,169.849 305.37,169.37"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M294.714,178.316H293.101C294.604,181.898 297.935,184.52 301.926,185.022C302.073,184.662 302.204,184.294 302.317,183.919"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M211.321,191.573H207.111V112.303H210.634C220.673,112.303 226.857,126.899 224.466,136.852L211.321,191.573Z"
      android:fillColor="#63E685"/>
  <path
      android:pathData="M203.138,191.573H207.352V112.303H203.825C193.777,112.303 187.588,126.899 189.981,136.852L203.138,191.573Z"
      android:fillColor="#63E685"/>
  <path
      android:pathData="M217.635,53.533C220.84,53.632 223.323,56.35 223.157,59.553C223.05,61.618 223.418,63.699 224.254,64.841L225.499,66.544C227.565,69.37 226.366,73.387 223.089,74.618C220.253,75.684 219.003,79.001 220.429,81.674L222.636,85.81C224.022,88.408 223.02,91.637 220.407,92.995L216.813,94.863L198.958,86.663L210.626,53.318L217.635,53.533Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M203.145,48.081L198.883,51.3C195.95,52.474 193.947,56.718 195.447,59.499L194.699,62.348C196.193,65.118 194.796,68.563 191.796,69.511L189.785,70.147C186.447,71.202 185.035,75.149 186.947,78.081C188.603,80.619 187.647,84.033 184.916,85.343L180.689,87.371C178.034,88.644 176.93,91.84 178.232,94.48L180.024,98.114L199.268,102.073L209.601,68.289L203.145,48.081Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M211.204,76.975H203.467V72.317H211.204V76.975Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M211.204,75.783L203.467,75.859V72.318H211.204V75.783Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M212.39,80.155V76.668H202.073V80.155C202.073,80.955 201.56,81.672 200.795,81.907C197.669,82.867 195.459,85.876 195.68,89.321L197.124,111.818C197.332,115.041 200.006,117.549 203.236,117.549H211.227C214.457,117.549 217.132,115.041 217.339,111.818L218.783,89.321C219.005,85.876 216.794,82.867 213.668,81.907C212.903,81.672 212.39,80.955 212.39,80.155Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M212.39,80.155V76.668H202.073V80.155C202.073,80.955 201.56,81.672 200.795,81.907C197.669,82.867 195.459,85.876 195.68,89.321L197.124,111.818C197.332,115.041 200.006,117.549 203.236,117.549H211.227C214.457,117.549 217.132,115.041 217.339,111.818L218.783,89.321C219.005,85.876 216.794,82.867 213.668,81.907C212.903,81.672 212.39,80.955 212.39,80.155Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="117.549"
          android:startX="207.232"
          android:endY="76.6676"
          android:endX="207.232"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M222.841,60.072C222.841,67.802 216.301,74.068 208.234,74.068C200.166,74.068 193.626,67.802 193.626,60.072C193.626,52.343 200.166,46.077 208.234,46.077C216.301,46.077 222.841,52.343 222.841,60.072Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M222.148,66.373C222.148,67.234 221.45,67.932 220.589,67.932C219.728,67.932 219.03,67.234 219.03,66.373C219.03,65.512 219.728,64.814 220.589,64.814C221.45,64.814 222.148,65.512 222.148,66.373Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M222.148,66.373C222.148,67.234 221.45,67.932 220.589,67.932C219.728,67.932 219.03,67.234 219.03,66.373C219.03,65.512 219.728,64.814 220.589,64.814C221.45,64.814 222.148,65.512 222.148,66.373Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M220.59,63.696C220.59,70.042 215.446,75.186 209.1,75.186C202.754,75.186 197.609,70.042 197.609,63.696C197.609,57.35 202.754,52.205 209.1,52.205C215.446,52.205 220.59,57.35 220.59,63.696Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M199.212,69.513C198.194,67.798 197.609,65.795 197.609,63.656C197.609,57.31 202.754,52.165 209.1,52.165C215.446,52.165 220.59,57.31 220.59,63.656C220.59,64.339 220.531,65.009 220.416,65.66"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M206.164,74.768C203.953,74.186 202.002,72.961 200.524,71.305"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M220.194,66.656C218.875,71.548 214.407,75.147 209.099,75.147"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M212.037,91.257H205.513L204.894,85.743H212.701L212.037,91.257Z"
      android:fillColor="#E44734"/>
  <path
      android:pathData="M224.181,201.012H213.349C212.557,201.012 211.915,200.37 211.915,199.578V198.857C211.915,198.176 211.426,197.565 210.75,197.485C209.946,197.39 209.208,200.164 209.208,200.949H207.018L207.239,193.19L221.546,197.491C223.108,197.95 224.181,199.383 224.181,201.012Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M210.911,195.045L211.297,191.573H207.239L207.244,193.252L208.03,194.136C208.777,194.851 209.808,195.189 210.833,195.055L210.911,195.045Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M202.124,99.59L203.797,93.354L207.745,93.155L206.955,99.475L202.124,99.59Z"
      android:fillColor="#E8C9BF"/>
  <path
      android:pathData="M190.172,201.012H201.005C201.796,201.012 202.438,200.37 202.438,199.578V198.857C202.438,198.176 202.927,197.565 203.603,197.485C204.408,197.39 205.145,200.164 205.145,200.949H207.336L207.245,193.252L192.808,197.491C191.245,197.95 190.172,199.383 190.172,201.012Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M203.541,195.045L203.164,191.574H207.353L207.245,193.253L206.387,194.096C205.636,194.835 204.585,195.185 203.541,195.045Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M216.309,158.264C216.309,158.869 215.818,159.36 215.213,159.36C214.607,159.36 214.116,158.869 214.116,158.264"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M200.482,158.264C200.482,158.869 199.991,159.36 199.385,159.36C198.78,159.36 198.289,158.869 198.289,158.264"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M195.665,63.568C195.665,64.532 195.772,65.471 195.969,66.376C197.654,67.164 199.533,67.607 201.516,67.607C208.754,67.607 214.621,61.74 214.621,54.502C214.621,53.537 214.514,52.599 214.316,51.694C212.632,50.905 210.753,50.463 208.77,50.463C201.532,50.463 195.665,56.33 195.665,63.568Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M204.858,61.421C208.659,59.955 211.605,56.768 212.74,52.814"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M198.924,63.746C202.954,63.147 206.526,60.682 208.499,57.073"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M200.863,55.878C203.054,55.938 205.177,54.966 206.56,53.238"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M214.939,52.646C214.408,52.646 213.892,52.707 213.394,52.818C212.96,53.764 212.717,54.82 212.717,55.935C212.717,60.002 215.944,63.299 219.926,63.299C220.456,63.299 220.973,63.239 221.471,63.128C221.904,62.181 222.148,61.125 222.148,60.011C222.148,55.944 218.92,52.646 214.939,52.646Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M213.529,66.37C213.529,67.125 212.917,67.737 212.163,67.737C211.408,67.737 210.796,67.125 210.796,66.37"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M217.182,66.409C217.182,67.164 216.57,67.775 215.815,67.775C215.061,67.775 214.449,67.164 214.449,66.409"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M214.8,64.091L215.815,64.431"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M212.54,64.5L213.499,64.022"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M210.158,72.164C208.577,72.164 207.296,70.882 207.296,69.301"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M213.512,68.839C213.884,68.839 214.185,69.141 214.185,69.513C214.185,69.885 213.884,70.187 213.512,70.187"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M206.288,68.376C206.288,67.713 206.825,67.176 207.487,67.176"
      android:strokeLineJoin="round"
      android:strokeWidth="0.347869"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M198.923,74.742L197.291,75.258C194.581,76.114 193.435,79.318 194.988,81.699C196.331,83.758 195.556,86.53 193.338,87.593L189.907,89.239C187.752,90.273 186.856,92.867 187.913,95.011"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M189.666,77.902C188.984,75.825 190.106,73.587 192.178,72.89L192.451,72.798C193.746,72.363 194.726,71.295 195.049,69.968"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M216.439,78.613C215.757,76.536 216.879,74.298 218.951,73.601L219.224,73.51C220.518,73.075 221.499,72.006 221.822,70.68"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M180.759,94.954C180.077,92.877 181.199,90.639 183.271,89.942L183.544,89.851C184.838,89.415 185.819,88.347 186.142,87.021"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M213.05,90.987H206.852C206.278,90.987 205.813,90.521 205.813,89.948C205.813,89.373 206.278,88.908 206.852,88.908H213.05C213.624,88.908 214.089,89.373 214.089,89.948C214.089,90.521 213.624,90.987 213.05,90.987Z"
      android:fillColor="#E8C9BF"/>
  <path
      android:pathData="M213.05,93.066H206.852C206.278,93.066 205.813,92.601 205.813,92.027C205.813,91.453 206.278,90.987 206.852,90.987H213.05C213.624,90.987 214.089,91.453 214.089,92.027C214.089,92.601 213.624,93.066 213.05,93.066Z"
      android:fillColor="#E8C9BF"/>
  <path
      android:pathData="M213.05,95.116H206.852C206.278,95.116 205.813,94.651 205.813,94.077C205.813,93.503 206.278,93.038 206.852,93.038H213.05C213.624,93.038 214.089,93.503 214.089,94.077C214.089,94.651 213.624,95.116 213.05,95.116Z"
      android:fillColor="#E8C9BF"/>
  <path
      android:pathData="M266.601,99.411L260.478,98.457C259.911,98.369 259.522,97.837 259.611,97.27C259.699,96.703 260.231,96.315 260.798,96.403L266.922,97.357C267.489,97.446 267.877,97.977 267.788,98.545C267.7,99.112 267.169,99.5 266.601,99.411Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M266.281,101.466L260.158,100.512C259.59,100.423 259.202,99.892 259.291,99.325C259.379,98.758 259.91,98.369 260.477,98.458L266.601,99.412C267.168,99.501 267.557,100.032 267.468,100.599V100.599C267.38,101.166 266.848,101.554 266.281,101.466Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M265.965,103.492L260.312,102.611C259.744,102.522 259.356,101.991 259.445,101.424C259.533,100.857 260.065,100.469 260.632,100.557L266.285,101.438C266.852,101.526 267.241,102.058 267.152,102.625C267.064,103.192 266.532,103.58 265.965,103.492Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M265.688,105.278L260.928,104.537C260.361,104.448 259.973,103.917 260.061,103.35C260.15,102.783 260.681,102.395 261.248,102.483L266.008,103.225C266.575,103.313 266.963,103.844 266.874,104.412C266.786,104.979 266.255,105.367 265.688,105.278Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M208.083,95.117L203.71,96.373V91.056C203.71,89.87 204.672,88.908 205.858,88.908H208.083V95.117Z"
      android:fillColor="#E8C9BF"/>
  <path
      android:pathData="M213.696,85.742H203.903L204.128,84.01H213.412L213.696,85.742Z"
      android:fillColor="#E44734"/>
  <path
      android:pathData="M212.261,84.01H205.339V83.668C205.339,83.331 205.612,83.058 205.95,83.058H211.65C211.987,83.058 212.261,83.331 212.261,83.668V84.01Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M226.469,99.916L215.745,98.301V84.009L225.742,90.646L226.469,99.916Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M226.469,99.916L215.745,98.301V84.009L225.742,90.646L226.469,99.916Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="99.9165"
          android:startX="221.107"
          android:endY="84.0094"
          android:endX="221.107"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M265.661,105.275C264.64,105.116 263.942,104.16 264.101,103.139L264.76,98.912C264.919,97.892 265.875,97.194 266.895,97.353C267.916,97.512 268.614,98.468 268.455,99.488L267.796,103.715C267.637,104.736 266.681,105.434 265.661,105.275Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M202.84,96.926L207.254,97.139L207.232,97.45L202.448,98.386L202.84,96.926Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M200.523,120.037C198.44,119.748 196.987,117.824 197.277,115.741L202.023,98.471L207.551,97.167L204.819,116.791C204.53,118.874 202.606,120.327 200.523,120.037Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M200.523,120.037C198.44,119.748 196.987,117.824 197.277,115.741L202.023,98.471L207.551,97.167L204.819,116.791C204.53,118.874 202.606,120.327 200.523,120.037Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="120.074"
          android:startX="202.396"
          android:endY="97.167"
          android:endX="202.396"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M200.457,104.175L198.741,110.418"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M207.335,98.394L205.122,114.983"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M215.852,97.135L206.49,95.839C206.073,95.782 205.781,95.396 205.839,94.978C205.896,94.561 206.282,94.269 206.7,94.326L216.061,95.622C216.479,95.679 216.771,96.065 216.713,96.483C216.655,96.901 216.269,97.192 215.852,97.135Z"
      android:fillColor="#E8C9BF"/>
  <path
      android:pathData="M209.95,93.038H213.049C213.623,93.038 214.089,93.503 214.089,94.077C214.089,94.651 213.623,95.116 213.049,95.116L212.362,95.109"
      android:strokeLineJoin="round"
      android:strokeWidth="0.347869"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M209.74,90.987H212.26"
      android:strokeLineJoin="round"
      android:strokeWidth="0.347869"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M218.416,95.044L217.957,102.188"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M203.58,76.936V73.934"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M211.125,77.034V74.969"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M195.969,66.376C197.653,67.164 199.532,67.607 201.515,67.607C208.753,67.607 214.621,61.739 214.621,54.501"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M213.625,59.516C214.857,61.773 217.217,63.299 219.926,63.299C220.457,63.299 220.973,63.239 221.471,63.128"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M220.194,54.16C221.15,55.931 221.69,57.941 221.69,60.073"
      android:strokeAlpha="0.300003"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.300003"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M217.201,50.46C217.789,50.964 218.331,51.517 218.819,52.112"
      android:strokeAlpha="0.300003"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.300003"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M194.859,60.073C194.859,55.379 197.493,51.275 201.422,49.042"
      android:strokeAlpha="0.300003"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.300003"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M215.518,58.491C216.722,60.322 218.734,61.508 220.942,61.654"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M204.894,85.741H212.701L212.626,86.367L205.159,88.103L204.894,85.741Z"
      android:fillColor="#A42111"/>
  <path
      android:pathData="M213.81,84.01H203.58"
      android:strokeLineJoin="round"
      android:strokeWidth="0.347869"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M214.281,88.908H204.657"
      android:strokeLineJoin="round"
      android:strokeWidth="0.347869"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M207.746,81.794C207.746,81.968 207.887,82.109 208.061,82.109C208.236,82.109 208.377,81.968 208.377,81.794C208.377,81.619 208.236,81.478 208.061,81.478C207.887,81.478 207.746,81.619 207.746,81.794Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M207.746,81.794C207.746,81.968 207.887,82.109 208.061,82.109C208.236,82.109 208.377,81.968 208.377,81.794C208.377,81.619 208.236,81.478 208.061,81.478C207.887,81.478 207.746,81.619 207.746,81.794Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="82.1094"
          android:startX="208.061"
          android:endY="81.4779"
          android:endX="208.061"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M209.385,78.195C208.95,78.286 208.605,78.646 208.527,79.083C208.458,79.478 208.597,79.842 208.852,80.088C208.988,80.217 209.044,80.401 209.004,80.585C208.987,80.665 208.986,80.751 209.004,80.84C209.055,81.092 209.273,81.291 209.527,81.322C209.901,81.367 210.218,81.077 210.218,80.713C210.218,80.674 210.214,80.637 210.207,80.601C210.173,80.42 210.233,80.24 210.371,80.117C210.599,79.913 210.742,79.616 210.742,79.286C210.742,78.67 210.242,78.17 209.626,78.17C209.547,78.17 209.467,78.178 209.385,78.195"
      android:fillColor="#F9D45C"/>
  <path
      android:pathData="M252.279,18.918C252.855,18.918 253.323,19.386 253.323,19.962"
      android:strokeLineJoin="round"
      android:strokeWidth="0.347869"
      android:fillColor="#00000000"
      android:strokeColor="#B76452"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M233.845,19.962C233.845,19.386 234.313,18.918 234.889,18.918"
      android:strokeLineJoin="round"
      android:strokeWidth="0.347869"
      android:fillColor="#00000000"
      android:strokeColor="#B76452"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M265.688,105.28L260.928,104.539C260.361,104.45 259.973,103.919 260.061,103.352C260.15,102.784 260.681,102.396 261.248,102.485"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M262.168,100.574L265.478,101.036"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M262.868,98.831L260.478,98.459C259.911,98.37 259.522,97.839 259.611,97.272C259.699,96.705 260.231,96.316 260.798,96.405L266.921,97.359"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M265.168,99.19L265.901,99.304"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M207.245,200.715V193.689"
      android:strokeLineJoin="round"
      android:strokeWidth="0.347869"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M196.358,125.728C196.358,129.236 193.514,132.08 190.006,132.08"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M218.138,125.728C218.138,129.236 220.982,132.08 224.489,132.08"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M205.292,131.387L206.292,132.032C206.941,132.45 207.77,132.464 208.432,132.067L209.468,131.445"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M207.353,191.574H202.406L201.771,189.878H207.353V191.574Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.347869"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M207.245,191.574H212.191L212.827,189.878H207.245V191.574Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.347869"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M207.245,179.649V189.879H208.931L207.245,179.649Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M180.689,87.372C178.034,88.645 176.93,91.841 178.232,94.482L180.024,98.115"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M191.795,69.513L189.784,70.148C186.447,71.203 185.035,75.15 186.948,78.082C188.602,80.62 187.647,84.034 184.916,85.344"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M225.5,66.545L224.256,64.843C223.598,63.943 223.246,61.979 223.38,60.038C223.458,58.896 222.905,57.073 222.462,55.901"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M220.43,81.675C219.004,79.002 220.254,75.685 223.09,74.619H223.09C225.08,73.871 226.304,72.096 226.495,70.184"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M220.407,92.996C223.02,91.638 224.021,88.409 222.635,85.812"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M190.785,100.329L191.442,95.377L192.778,100.739L190.785,100.329Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M207.336,162.047V132.355"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M209.839,151.939L207.292,148.065"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M208.72,152.884L207.353,150.095"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M207.245,171.401V175.992"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M201.024,193.532C200.797,194.066 200.423,196.149 200.958,196.376C201.492,196.603 202.732,194.889 202.959,194.354C203.186,193.82 202.937,193.203 202.403,192.976C201.868,192.749 201.251,192.998 201.024,193.532Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.347869"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M197.883,196.213C198.417,196.44 200.5,196.814 200.727,196.28C200.954,195.745 199.239,194.505 198.705,194.278C198.171,194.051 197.553,194.3 197.326,194.835C197.099,195.369 197.349,195.986 197.883,196.213Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.347869"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M213.316,193.532C213.543,194.066 213.917,196.149 213.383,196.376C212.848,196.603 211.608,194.889 211.381,194.354C211.154,193.82 211.403,193.203 211.937,192.976C212.472,192.749 213.089,192.998 213.316,193.532Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.347869"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M216.458,196.213C215.924,196.44 213.841,196.814 213.614,196.28C213.387,195.745 215.101,194.505 215.636,194.278C216.17,194.051 216.787,194.3 217.014,194.835C217.241,195.369 216.992,195.986 216.458,196.213Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.347869"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M200.094,103.229L196.377,103.651C196.274,103.663 196.171,103.664 196.071,103.655C195.371,103.592 194.788,103.045 194.706,102.32C194.612,101.491 195.208,100.743 196.037,100.649L199.754,100.227C200.583,100.133 201.331,100.729 201.425,101.558C201.519,102.387 200.923,103.135 200.094,103.229Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M200.001,100.286L196.284,100.708C195.455,100.802 194.707,100.206 194.613,99.377C194.519,98.548 195.114,97.8 195.943,97.706L199.66,97.284C200.489,97.19 201.238,97.786 201.332,98.615V98.615C201.426,99.444 200.83,100.192 200.001,100.286Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M199.681,97.449L195.964,97.871C195.135,97.965 194.386,97.369 194.292,96.54C194.198,95.711 194.794,94.963 195.623,94.868L199.34,94.447C200.169,94.353 200.917,94.949 201.011,95.778C201.105,96.607 200.51,97.355 199.681,97.449Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M196.927,94.495L193.759,96.485C193.053,96.928 192.12,96.715 191.677,96.009C191.233,95.302 191.446,94.37 192.153,93.926L195.321,91.937C196.027,91.493 196.96,91.706 197.403,92.413C197.847,93.119 197.634,94.052 196.927,94.495Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M200.365,93.873L196.679,94.508C195.857,94.65 195.075,94.098 194.933,93.276C194.792,92.453 195.343,91.672 196.166,91.531L199.852,90.895C200.674,90.754 201.456,91.305 201.597,92.127C201.739,92.95 201.187,93.731 200.365,93.873Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M195.981,104.638C194.824,104.927 193.652,104.224 193.364,103.068L191.628,96.121C191.339,94.965 192.042,93.793 193.198,93.504C194.355,93.215 195.526,93.918 195.815,95.075L197.551,102.021C197.84,103.178 197.137,104.349 195.981,104.638Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M197.572,94.649L199.338,94.449C200.167,94.355 200.916,94.95 201.01,95.78C201.104,96.608 200.508,97.357 199.679,97.451L195.962,97.873"
      android:strokeLineJoin="round"
      android:strokeWidth="0.347869"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M201.334,98.617C201.428,99.446 200.832,100.194 200.003,100.288L196.534,100.594"
      android:strokeLineJoin="round"
      android:strokeWidth="0.347869"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M271.28,200.949H158.927"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M221.029,200.949H289.76"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M293.32,200.949H312.056"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M316.57,200.949H322.267"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M310.037,198.733L310.845,200.865L311.423,199.397"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M162.923,60.413C162.923,60.949 163.357,61.382 163.892,61.382C164.428,61.382 164.861,60.949 164.861,60.413C164.861,59.878 164.428,59.444 163.892,59.444C163.357,59.444 162.923,59.878 162.923,60.413Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M155.111,57.675C155.111,58.029 155.398,58.316 155.752,58.316C156.106,58.316 156.393,58.029 156.393,57.675C156.393,57.321 156.106,57.034 155.752,57.034C155.398,57.034 155.111,57.321 155.111,57.675Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M171.671,20.214C171.671,20.568 171.958,20.855 172.312,20.855C172.665,20.855 172.952,20.568 172.952,20.214C172.952,19.86 172.665,19.573 172.312,19.573C171.958,19.573 171.671,19.86 171.671,20.214Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M166.991,53.218H167.695V52.514C167.695,52.387 167.798,52.283 167.926,52.283C168.054,52.283 168.157,52.387 168.157,52.514V53.218H168.861C168.988,53.218 169.092,53.322 169.092,53.449C169.092,53.577 168.988,53.68 168.861,53.68H168.157V54.384C168.157,54.512 168.054,54.615 167.926,54.615C167.798,54.615 167.695,54.512 167.695,54.384V53.68H166.991C166.864,53.68 166.76,53.577 166.76,53.449C166.76,53.322 166.864,53.218 166.991,53.218"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M204.964,22.736H205.667V22.032C205.667,21.904 205.771,21.801 205.898,21.801C206.026,21.801 206.129,21.904 206.129,22.032V22.736H206.833C206.961,22.736 207.064,22.839 207.064,22.967C207.064,23.094 206.961,23.198 206.833,23.198H206.129V23.901C206.129,24.029 206.026,24.132 205.898,24.132C205.771,24.132 205.667,24.029 205.667,23.901V23.198H204.964C204.836,23.198 204.733,23.094 204.733,22.967C204.733,22.839 204.836,22.736 204.964,22.736"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M276.683,25.38H277.612V24.451C277.612,24.282 277.749,24.145 277.918,24.145C278.086,24.145 278.223,24.282 278.223,24.451V25.38H279.152C279.321,25.38 279.457,25.517 279.457,25.685C279.457,25.854 279.321,25.99 279.152,25.99H278.223V26.92C278.223,27.089 278.086,27.225 277.918,27.225C277.749,27.225 277.612,27.089 277.612,26.92V25.99H276.683C276.514,25.99 276.378,25.854 276.378,25.685C276.378,25.517 276.514,25.38 276.683,25.38Z"
      android:fillColor="#529ED6"/>
  <path
      android:pathData="M281.86,18.706C281.86,19.063 282.149,19.352 282.506,19.352C282.863,19.352 283.152,19.063 283.152,18.706C283.152,18.349 282.863,18.06 282.506,18.06C282.149,18.06 281.86,18.349 281.86,18.706Z"
      android:fillColor="#529ED6"/>
  <path
      android:pathData="M275.333,18.411C275.333,18.881 275.714,19.263 276.185,19.263C276.655,19.263 277.036,18.881 277.036,18.411C277.036,17.94 276.655,17.559 276.185,17.559C275.714,17.559 275.333,17.94 275.333,18.411Z"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#E4F4FC"/>
  <path
      android:pathData="M310.089,77.443C310.089,77.797 310.376,78.084 310.73,78.084C311.084,78.084 311.371,77.797 311.371,77.443C311.371,77.089 311.084,76.802 310.73,76.802C310.376,76.802 310.089,77.089 310.089,77.443Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.521804"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M106.916,112.76L114.445,104.043"
      android:strokeLineJoin="round"
      android:strokeWidth="0.731304"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M70.511,84.059L84.823,77.195L96.667,64.256L97.895,85.362L71.238,96.279L70.511,84.059Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M80.484,101.959L78.963,96.278L76.889,97.517C73.889,99.308 70.677,99.902 67.514,99.253L65.111,98.759L64.167,101.959C61.891,109.676 61.215,118.483 62.25,126.941L62.328,127.578L63.759,146.482C63.937,148.829 64.431,151.074 65.204,153.04C65.433,153.625 65.595,154.28 65.679,154.969L70.582,195.688H73.955L78.972,154.969C79.056,154.28 79.218,153.625 79.448,153.04C80.22,151.074 80.715,148.829 80.892,146.482L82.323,127.578L82.401,126.941C83.436,118.483 82.76,109.676 80.484,101.959Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M85.127,140.125H59.125L55.641,112.259C54.583,103.799 60.73,96.278 68.703,96.278H75.19C83.091,96.278 89.217,103.671 88.274,112.07L85.127,140.125Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M83.576,99.747C76.705,104.076 67.876,104.185 60.893,100.027L60.69,99.906L61.613,62.041C61.968,57.018 66.276,53.119 71.471,53.119H78.573C84.295,53.119 88.821,57.814 88.431,63.346L83.576,99.747Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M83.576,99.747C76.705,104.076 67.876,104.185 60.893,100.027L60.69,99.906L61.613,62.041C61.968,57.018 66.276,53.119 71.471,53.119H78.573C84.295,53.119 88.821,57.814 88.431,63.346L83.576,99.747Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="103.071"
          android:startX="74.572"
          android:endY="53.119"
          android:endX="74.572"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M83.808,97.998L82.963,92.221L84.611,91.97L83.808,97.998Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M90.208,40.948C91.821,44.627 90.146,48.916 86.468,50.529C82.789,52.141 78.499,50.467 76.887,46.788C75.274,43.109 76.949,38.82 80.627,37.207C84.306,35.595 88.596,37.269 90.208,40.948Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M71.664,46.637C71.664,51.117 68.032,54.75 63.551,54.75C59.07,54.75 55.438,51.117 55.438,46.637C55.438,42.156 59.07,38.523 63.551,38.523C68.032,38.523 71.664,42.156 71.664,46.637Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M78.3,55.173H71.238V45.161H78.3V55.173Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M78.3,48.636L71.238,50.593V45.161H78.3V48.636Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M88.29,39.735C88.29,40.521 87.653,41.158 86.867,41.158C86.081,41.158 85.444,40.521 85.444,39.735C85.444,38.949 86.081,38.312 86.867,38.312C87.653,38.312 88.29,38.949 88.29,39.735Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M86.868,37.29C86.868,43.082 82.172,47.778 76.379,47.778C70.586,47.778 65.89,43.082 65.89,37.29C65.89,31.497 70.586,26.801 76.379,26.801C82.172,26.801 86.868,31.497 86.868,37.29Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M67.354,42.601C66.424,41.035 65.89,39.207 65.89,37.254C65.89,31.461 70.586,26.765 76.379,26.765C82.172,26.765 86.868,31.461 86.868,37.254C86.868,37.878 86.813,38.489 86.709,39.083"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M73.701,47.399C71.683,46.867 69.902,45.749 68.552,44.238"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M86.506,39.993C85.302,44.458 81.224,47.744 76.378,47.744"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M77.06,38.451C77.06,38.828 76.755,39.134 76.378,39.134C76.001,39.134 75.695,38.828 75.695,38.451"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M81.048,38.472C81.048,38.849 80.742,39.154 80.365,39.154C79.988,39.154 79.682,38.849 79.682,38.472"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M78.755,35.472L79.682,35.782"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M76.597,35.782L77.525,35.475"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M74.659,44.421C73.216,44.421 72.046,43.251 72.046,41.808"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M78.105,39.992C78.445,39.992 78.72,40.268 78.72,40.607C78.72,40.947 78.445,41.222 78.105,41.222"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M71.126,40.962C71.126,40.357 71.616,39.867 72.221,39.867"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M71.341,55.09V48.637"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M60.063,51.52C57.611,51.52 55.57,49.59 55.458,47.116C55.106,39.376 59.403,32.232 66.406,28.916C66.458,28.892 66.509,28.866 66.559,28.84C67.912,28.143 68.834,26.708 68.967,25.097L69.035,24.278C69.245,21.739 71.476,19.851 74.014,20.06C76.553,20.27 78.441,22.499 78.231,25.039L78.163,25.857C77.771,30.612 74.945,34.898 70.789,37.041C70.645,37.115 70.5,37.187 70.353,37.257C66.722,38.976 64.493,42.682 64.676,46.697C64.791,49.242 62.822,51.399 60.276,51.515C60.205,51.518 60.134,51.52 60.063,51.52Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M67.966,39.735C67.966,40.803 67.101,41.668 66.034,41.668C64.966,41.668 64.101,40.803 64.101,39.735C64.101,38.668 64.966,37.803 66.034,37.803C67.101,37.803 67.966,38.668 67.966,39.735Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M67.512,40.98C67.157,41.401 66.627,41.668 66.034,41.668C64.966,41.668 64.101,40.803 64.101,39.735C64.101,38.668 64.966,37.803 66.034,37.803C66.901,37.803 67.636,38.375 67.88,39.163"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M70.812,29.325C70.812,31.518 69.035,33.295 66.842,33.295C64.649,33.295 62.871,31.518 62.871,29.325C62.871,27.132 64.649,25.354 66.842,25.354C69.035,25.354 70.812,27.132 70.812,29.325Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M66.842,24.982C66.842,23.634 67.935,22.54 69.284,22.54C70.632,22.54 71.726,23.634 71.726,24.982C71.726,26.331 70.632,27.424 69.284,27.424C67.935,27.424 66.842,26.331 66.842,24.982Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M87.953,39.101C87.486,39.101 87.012,39.011 86.554,38.823L84.576,38.009C80.411,36.294 77.538,32.282 77.256,27.788L77.172,26.451C77.044,24.423 78.585,22.675 80.613,22.548C82.641,22.42 84.389,23.961 84.517,25.989L84.601,27.326C84.708,29.031 85.798,30.553 87.377,31.203L89.355,32.017C91.234,32.79 92.131,34.941 91.357,36.82C90.772,38.241 89.4,39.101 87.953,39.101Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M87.122,29.324C87.122,30.937 85.814,32.245 84.201,32.245C82.587,32.245 81.279,30.937 81.279,29.324C81.279,27.71 82.587,26.403 84.201,26.403C85.814,26.403 87.122,27.71 87.122,29.324Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M86.025,27.244C86.025,28.612 84.916,29.721 83.548,29.721C82.18,29.721 81.071,28.612 81.071,27.244C81.071,25.876 82.18,24.768 83.548,24.768C84.916,24.768 86.025,25.876 86.025,27.244Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M77.904,38.498C77.904,39.576 77.03,40.45 75.952,40.45C74.875,40.45 74.001,39.576 74.001,38.498C74.001,37.42 74.875,36.547 75.952,36.547C77.03,36.547 77.904,37.42 77.904,38.498Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M82.316,38.498C82.316,39.576 81.442,40.45 80.365,40.45C79.287,40.45 78.413,39.576 78.413,38.498C78.413,37.42 79.287,36.547 80.365,36.547C81.442,36.547 82.316,37.42 82.316,38.498Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M77.92,38.391H78.391"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M78.384,150.25C78.384,151.425 77.487,152.377 76.38,152.377"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M68.171,152.377C67.064,152.377 66.167,151.425 66.167,150.25"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M88.53,200.414H72.645V195.686H84.463C86.709,195.686 88.53,197.508 88.53,199.754V200.414Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M56.119,200.414H72.646V195.686H60.186C57.94,195.686 56.119,197.508 56.119,199.754V200.414Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M76.521,193.531C76.521,194.085 75.518,195.688 75.518,195.688C75.518,195.688 74.515,194.085 74.515,193.531C74.515,192.977 74.964,192.527 75.518,192.527C76.072,192.527 76.521,192.977 76.521,193.531Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M77.675,196.691C77.121,196.691 75.518,195.688 75.518,195.688C75.518,195.688 77.121,194.685 77.675,194.685C78.229,194.685 78.678,195.134 78.678,195.688C78.678,196.242 78.229,196.691 77.675,196.691Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M68.984,193.531C68.984,194.085 69.987,195.688 69.987,195.688C69.987,195.688 70.99,194.085 70.99,193.531C70.99,192.977 70.541,192.527 69.987,192.527C69.433,192.527 68.984,192.977 68.984,193.531Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M67.83,196.691C68.385,196.691 69.988,195.688 69.988,195.688C69.988,195.688 68.385,194.685 67.83,194.685C67.276,194.685 66.827,195.134 66.827,195.688C66.827,196.242 67.276,196.691 67.83,196.691Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M88.53,199.754V200.414H72.645V195.686"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M81.75,195.688H84.463C86.1,195.688 87.512,196.656 88.157,198.051"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M66.242,195.686H60.186C57.94,195.686 56.119,197.508 56.119,199.754V200.414H68.407"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M70.581,200.414H72.645V195.686H70.581"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M74.184,195.688H72.645"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M75.943,200.414H72.645V195.686H73.194L75.943,200.414Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M72.325,195.688V186.552L73.195,195.688H72.325Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M78.571,53.117H71.47C70.094,53.117 68.781,53.393 67.587,53.889C68.247,57.505 71.412,60.248 75.219,60.248C78.97,60.248 82.1,57.586 82.823,54.049C81.537,53.454 80.098,53.117 78.571,53.117Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M78.631,54.229H76.31"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M74.661,54.229H73.06"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M74.898,54.773C74.898,55.046 75.12,55.268 75.393,55.268C75.666,55.268 75.888,55.046 75.888,54.773"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M81.372,140.123H63.276L63.555,143.817L81.284,141.275L81.372,140.123Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M93.573,84.058L94.263,84.265"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M59.11,36.667C59.11,37.521 58.418,38.213 57.564,38.213C56.71,38.213 56.018,37.521 56.018,36.667C56.018,35.813 56.71,35.121 57.564,35.121"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M89.447,48.007C90.013,48.646 89.955,49.623 89.316,50.189C88.678,50.756 87.7,50.698 87.134,50.059C86.567,49.42 86.625,48.443 87.264,47.876"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M86.601,48.882C87.168,49.521 87.109,50.498 86.47,51.064C85.832,51.631 84.854,51.573 84.288,50.934C83.721,50.295 83.78,49.318 84.418,48.751"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M58.749,40.962C58.749,41.816 58.057,42.508 57.203,42.508C56.349,42.508 55.657,41.816 55.657,40.962C55.657,40.108 56.349,39.416 57.203,39.416"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M81.229,23.437C81.229,24.291 80.536,24.983 79.683,24.983C78.829,24.983 78.136,24.291 78.136,23.437C78.136,22.583 78.829,21.891 79.683,21.891"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M74.969,21.559C74.388,22.338 74.549,23.44 75.328,24.021C76.107,24.602 77.209,24.441 77.79,23.662C78.371,22.883 78.21,21.781 77.431,21.2"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M58.121,49.029C58.121,50.19 57.18,51.132 56.018,51.132C54.857,51.132 53.915,50.19 53.915,49.029C53.915,47.867 54.857,46.925 56.018,46.925"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M72.325,179.197V140.124"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M83.211,92.447C81.267,92.598 79.74,90.81 80.194,88.914L85.469,66.831L89.683,68.144L87.191,88.596C86.766,90.567 85.195,92.087 83.211,92.447Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M87.191,88.596C86.929,89.809 86.233,90.852 85.273,91.558C84.675,91.999 83.974,92.308 83.212,92.447C81.267,92.598 79.741,90.81 80.194,88.913"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M78.908,75.7L67.703,74.929C66.635,74.856 65.764,74.047 65.613,72.987L65.521,72.344"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#333333"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M81.193,75.857L80.12,75.784"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#333333"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M65.56,55.02C67.213,53.824 69.262,53.119 71.471,53.119H71.952"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M87.138,57.92C88.081,59.505 88.568,61.372 88.429,63.345"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M77.678,53.117H78.571C81.578,53.117 84.256,54.414 86.062,56.448"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M78.145,52.96V47.595"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M59.713,51.755C58.158,50.588 57.153,48.729 57.153,46.636C57.153,45.855 57.292,45.107 57.549,44.415"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M66.248,52.439C65.428,52.821 64.514,53.035 63.55,53.035"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M85.058,36.827C83.698,36.267 82.496,35.426 81.513,34.387"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M65.554,39.042C66.16,39.042 66.672,39.441 66.842,39.99"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M73.701,37.978H69.487"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M82.747,38.183H84.399"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M80.113,97.293H83.652"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M82.703,98.453H83.653"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M62.883,85.41L60.688,83.142"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M62.089,85.794L60.69,84.924"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M79.597,58.896C81.229,57.778 82.413,56.054 82.823,54.049"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M68.521,56.408C69.869,58.705 72.363,60.248 75.218,60.248"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M43.86,200.674H106.6"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M109.851,200.674H126.954"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M131.074,200.674H136.275"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M125.108,198.651L125.846,200.598L126.373,199.258"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M49.572,85.086C50.866,85.866 52.415,85.717 53.108,84.746L64.328,69.039L69.021,62.47C70.059,61.018 69.3,58.805 67.365,57.638C65.51,56.519 63.291,56.799 62.409,58.262L48.288,81.679C47.645,82.745 48.22,84.271 49.572,85.086Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M49.572,85.086C50.866,85.866 52.415,85.717 53.108,84.746L64.328,69.039L69.021,62.47C70.059,61.018 69.3,58.805 67.365,57.638C65.51,56.519 63.291,56.799 62.409,58.262L48.288,81.679C47.645,82.745 48.22,84.271 49.572,85.086Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="85.5854"
          android:startX="58.7558"
          android:endY="56.9522"
          android:endX="58.7558"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M63.939,100.399L60.547,100.784C60.452,100.795 60.359,100.796 60.267,100.788C59.628,100.731 59.096,100.231 59.021,99.569C58.935,98.813 59.479,98.129 60.236,98.044L63.629,97.659C64.385,97.573 65.068,98.117 65.154,98.874C65.24,99.63 64.696,100.313 63.939,100.399Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M63.855,97.712L60.462,98.097C59.705,98.183 59.022,97.639 58.936,96.882C58.851,96.126 59.395,95.442 60.151,95.357L63.544,94.972C64.301,94.886 64.984,95.43 65.07,96.187C65.156,96.943 64.612,97.626 63.855,97.712Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M63.562,95.122L60.169,95.507C59.412,95.593 58.729,95.049 58.643,94.292C58.558,93.536 59.102,92.853 59.858,92.767L63.251,92.382C64.008,92.296 64.691,92.84 64.777,93.597V93.597C64.863,94.354 64.319,95.037 63.562,95.122Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M61.049,92.426L58.157,94.242C57.512,94.647 56.661,94.452 56.256,93.807C55.851,93.162 56.045,92.311 56.69,91.906L59.582,90.09C60.227,89.685 61.078,89.88 61.483,90.525C61.888,91.17 61.694,92.021 61.049,92.426Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M64.187,91.857L60.822,92.437C60.071,92.567 59.358,92.063 59.229,91.313C59.099,90.562 59.603,89.849 60.353,89.719L63.718,89.14C64.469,89.01 65.182,89.514 65.312,90.264C65.441,91.015 64.937,91.728 64.187,91.857Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M60.185,101.683C59.129,101.947 58.059,101.305 57.796,100.249L56.211,93.908C55.947,92.853 56.589,91.783 57.645,91.519C58.7,91.256 59.77,91.898 60.034,92.953L61.618,99.294C61.882,100.35 61.24,101.419 60.185,101.683Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M60.168,92.732L63.25,92.382C64.007,92.297 64.69,92.84 64.776,93.597C64.861,94.354 64.317,95.037 63.561,95.123L60.168,95.508"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M65.069,96.185C65.155,96.942 64.611,97.625 63.854,97.711L60.688,97.99"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M49.668,67.192L48.141,61.5L44.537,61.317L45.258,67.087L49.668,67.192Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M48.665,63.454L48.141,61.499L44.537,61.317L44.989,64.932L48.665,63.454Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M51.13,85.857C53.031,85.593 54.358,83.837 54.093,81.936L49.761,66.171L44.715,64.981L47.208,82.894C47.473,84.795 49.229,86.122 51.13,85.857Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M51.13,85.857C53.031,85.593 54.358,83.837 54.093,81.936L49.761,66.171L44.715,64.981L47.208,82.894C47.473,84.795 49.229,86.122 51.13,85.857Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="85.8911"
          android:startX="49.4208"
          android:endY="64.9809"
          android:endX="49.4208"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M50.959,70.528L52.96,77.81"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M43.31,60.377L43.479,55.281C43.497,54.743 43.948,54.321 44.486,54.339C45.025,54.356 45.446,54.807 45.429,55.346L45.26,60.442C45.242,60.98 44.791,61.402 44.252,61.384C43.714,61.366 43.292,60.915 43.31,60.377Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M43.31,60.377L43.479,55.281C43.497,54.743 43.948,54.321 44.486,54.339C45.025,54.356 45.446,54.807 45.429,55.346L45.26,60.442C45.242,60.98 44.791,61.402 44.252,61.384C43.714,61.366 43.292,60.915 43.31,60.377Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M45.237,57.64L47.208,56.211C48.624,55.185 49.629,53.685 50.04,51.986C50.204,51.307 50.888,50.89 51.567,51.054C52.246,51.218 52.664,51.902 52.499,52.581C51.947,54.864 50.595,56.881 48.693,58.26L46.722,59.689C46.498,59.851 46.238,59.93 45.981,59.93C45.589,59.93 45.203,59.748 44.956,59.407C44.546,58.841 44.672,58.051 45.237,57.64Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M45.237,57.64L47.208,56.211C48.623,55.185 49.629,53.685 50.039,51.986C50.203,51.307 50.887,50.89 51.566,51.054C52.245,51.218 52.663,51.902 52.498,52.581C51.946,54.864 50.595,56.881 48.693,58.26L46.722,59.689C46.497,59.851 46.238,59.93 45.98,59.93"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M54.016,56.773L48.369,59.514C47.711,59.833 46.919,59.558 46.6,58.901C46.281,58.243 46.555,57.451 47.213,57.132L52.86,54.391C53.518,54.072 54.31,54.346 54.629,55.004C54.949,55.662 54.674,56.453 54.016,56.773Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M47.214,57.131L52.861,54.39C53.519,54.071 54.311,54.346 54.63,55.003"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M54.297,59.344L48.142,60.578C47.425,60.722 46.728,60.258 46.584,59.541C46.44,58.824 46.905,58.127 47.621,57.983L53.776,56.748C54.493,56.604 55.191,57.069 55.334,57.785C55.478,58.502 55.014,59.2 54.297,59.344Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M48.196,62.249L46.761,62.946C45.584,63.517 44.168,63.026 43.597,61.85C43.026,60.673 43.517,59.257 44.693,58.686L46.129,57.989C47.305,57.418 48.722,57.909 49.293,59.085C49.864,60.262 49.373,61.678 48.196,62.249Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M55.333,57.785C55.477,58.501 55.013,59.199 54.296,59.343L48.141,60.578"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M52.758,56.952L48.025,58.01"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M46.761,62.946C45.585,63.517 44.168,63.026 43.597,61.85C43.153,60.935 43.351,59.874 44.015,59.18"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M49.249,84.471C49.407,85.129 50.069,85.534 50.728,85.376C51.386,85.217 51.791,84.555 51.633,83.897"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M50.391,67.192L46.05,66.015"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M61.406,77.192V70.526"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M58.502,77.192L61.146,71.518L61.37,73.293L58.502,77.192Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M58.205,138.404H81.504"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M81.006,103.862L80.483,101.958"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M82.159,102.91L81.473,101.601"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M64.159,104.606L65.521,102.909"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M56.209,93.908L57.794,100.249C58.058,101.305 59.127,101.947 60.183,101.683C60.718,101.549 61.146,101.209 61.405,100.768L63.429,100.456"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.15,195.688H106.719L91.447,111.314H117.633V116.13C117.633,119.254 116.347,122.202 114.15,124.113V195.688Z"
      android:fillColor="#63E585"/>
  <path
      android:pathData="M114.149,167.499V195.688H106.719L94.611,128.794"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M93.337,121.75L91.447,111.312H117.633V116.128C117.633,119.252 116.347,122.2 114.15,124.111V163.983"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M92.441,116.805C100.917,118.279 109.654,117.322 117.633,114.024V111.313H91.447L92.441,116.805Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M88.899,113.082C98.175,116.328 108.278,116.328 117.554,113.082V69.702C117.554,66.445 116.475,63.438 114.655,61.022C112.019,57.523 107.83,55.262 103.113,55.262H98.494C92.189,67.522 88.899,81.109 88.899,94.896V113.082Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M88.899,113.082C98.175,116.328 108.278,116.328 117.554,113.082V69.702C117.554,66.445 116.475,63.438 114.655,61.022C112.019,57.523 107.83,55.262 103.113,55.262H98.494C92.189,67.522 88.899,81.109 88.899,94.896V113.082Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="115.596"
          android:startX="103.227"
          android:endY="55.1822"
          android:endX="103.227"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M88.899,113.082C98.175,116.328 108.278,116.328 117.554,113.082V69.702C117.554,66.445 116.475,63.438 114.655,61.022C112.019,57.523 107.83,55.262 103.113,55.262H98.494C92.189,67.522 88.899,81.109 88.899,94.896V113.082Z"
      android:strokeWidth="0.158771"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"/>
  <path
      android:pathData="M117.633,93.619V113.138C108.308,116.415 98.145,116.415 88.82,113.138V108.711"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M116.366,63.763C117.18,65.576 117.633,67.587 117.633,69.703V76.63V88.597"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M88.82,103.862V94.896C88.82,81.08 92.12,67.465 98.445,55.182H103.113C106.481,55.182 109.581,56.329 112.044,58.253"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M111.878,112.732C104.226,114.287 96.256,113.758 88.82,111.144"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M117.634,111.145C116.267,111.625 114.883,112.035 113.485,112.375"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.819,65.908C99.086,65.093 99.365,64.281 99.657,63.473"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#94EBAB"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M95.244,81.65C95.877,76.811 96.917,72.036 98.352,67.377"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#94EBAB"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M94.512,111.145V95.899"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#94EBAB"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.39,63.333C100.749,63.333 98.608,61.192 98.608,58.551V45.151H108.171V58.551C108.171,61.192 106.031,63.333 103.39,63.333Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M108.171,51.527L98.608,57.076V46.947H108.171V51.527Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M93.479,38.834C93.479,39.679 92.794,40.364 91.949,40.364C91.104,40.364 90.419,39.679 90.419,38.834C90.419,37.989 91.104,37.304 91.949,37.304C92.794,37.304 93.479,37.989 93.479,38.834Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M91.563,36.195C91.563,35.245 92.333,34.475 93.283,34.475C94.233,34.475 95.004,35.245 95.004,36.195C95.004,37.146 94.233,37.916 93.283,37.916C92.333,37.916 91.563,37.146 91.563,36.195Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M92.714,42.465C92.714,43.626 91.772,44.567 90.611,44.567C89.45,44.567 88.508,43.626 88.508,42.465C88.508,41.304 89.45,40.362 90.611,40.362C91.772,40.362 92.714,41.304 92.714,42.465Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M92.714,42.465C92.714,43.626 91.772,44.567 90.611,44.567C89.45,44.567 88.508,43.626 88.508,42.465C88.508,41.304 89.45,40.362 90.611,40.362C91.772,40.362 92.714,41.304 92.714,42.465Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M111.53,43.757C110.923,49.517 105.762,53.694 100.001,53.088C94.241,52.481 90.063,47.32 90.67,41.56C91.277,35.799 96.438,31.622 102.198,32.228C107.958,32.835 112.136,37.996 111.53,43.757Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M98.08,40.494C98.08,41.097 97.591,41.585 96.988,41.585C96.385,41.585 95.896,41.097 95.896,40.494"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.645,59.346C102.042,59.346 101.553,58.857 101.553,58.254C101.553,57.651 102.042,57.162 102.645,57.162"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M101.116,40.494C101.116,41.097 100.627,41.585 100.024,41.585C99.421,41.585 98.932,41.097 98.932,40.494"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.035,38.157C99.741,37.771 100.626,38.03 101.013,38.735"
      android:strokeLineJoin="round"
      android:strokeWidth="0.793856"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M96.085,38.502C96.31,37.73 97.119,37.287 97.892,37.513"
      android:strokeLineJoin="round"
      android:strokeWidth="0.793856"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M90.669,41.558C91.276,35.798 96.437,31.62 102.197,32.227C107.958,32.834 112.136,37.995 111.529,43.755C111.162,47.234 109.134,50.136 106.316,51.755"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.113,52.953C102.113,53.149 101.067,53.2 100.001,53.088C94.757,52.536 90.825,48.209 90.62,43.091"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.152,42.465C114.152,43.633 113.205,44.58 112.036,44.58C110.868,44.58 109.921,43.633 109.921,42.465C109.921,41.297 110.868,40.35 112.036,40.35C113.205,40.35 114.152,41.297 114.152,42.465Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M111.486,43.078C111.486,42.47 111.979,41.977 112.587,41.977"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M109.921,42.465C109.921,41.297 110.868,40.35 112.037,40.35C113.205,40.35 114.152,41.297 114.152,42.465C114.152,43.633 113.205,44.58 112.037,44.58C111.607,44.58 111.207,44.452 110.874,44.232"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.39,35.544C103.39,34.593 104.16,33.823 105.11,33.823C106.061,33.823 106.831,34.593 106.831,35.544C106.831,36.494 106.061,37.264 105.11,37.264C104.16,37.264 103.39,36.494 103.39,35.544Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M107.307,37.917C107.307,36.966 108.077,36.196 109.027,36.196C109.977,36.196 110.748,36.966 110.748,37.917C110.748,38.867 109.977,39.637 109.027,39.637C108.077,39.637 107.307,38.867 107.307,37.917Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M105.949,35.806C105.949,35.134 106.494,34.589 107.165,34.589C107.837,34.589 108.382,35.134 108.382,35.806C108.382,36.478 107.837,37.022 107.165,37.022C106.494,37.022 105.949,36.478 105.949,35.806Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M110.511,36.418C108.931,33.438 105.945,31.272 102.338,30.892C98.78,30.517 95.453,31.969 93.284,34.491L110.511,36.418Z"
      android:fillColor="#9D9D9D"/>
  <path
      android:pathData="M93.369,33.99L93.284,34.475L110.511,37.022L110.613,36.243C110.676,35.765 110.343,35.325 109.867,35.254L94.737,32.999C94.089,32.906 93.483,33.345 93.369,33.99Z"
      android:fillColor="#9D9D9D"/>
  <path
      android:pathData="M98.081,35.091C98.081,36.081 97.278,36.884 96.287,36.884C95.296,36.884 94.493,36.081 94.493,35.091C94.493,34.1 95.296,33.297 96.287,33.297C97.278,33.297 98.081,34.1 98.081,35.091Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M97.731,35.446C97.731,34.909 98.166,34.474 98.703,34.474C99.24,34.474 99.675,34.909 99.675,35.446C99.675,35.983 99.24,36.418 98.703,36.418C98.166,36.418 97.731,35.983 97.731,35.446Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M97.535,33.416L109.417,34.758L109.866,35.254L97.535,33.416Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M98.375,42.465V44.46C98.375,44.935 98.76,45.321 99.236,45.321H100.87"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M111.961,39.316C111.961,40.176 111.264,40.874 110.404,40.874C109.544,40.874 108.846,40.176 108.846,39.316C108.846,38.456 109.544,37.758 110.404,37.758C111.264,37.758 111.961,38.456 111.961,39.316Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M110.405,41.458C110.405,42.016 109.953,42.469 109.395,42.469C108.836,42.469 108.384,42.016 108.384,41.458C108.384,40.9 108.836,40.448 109.395,40.448C109.953,40.448 110.405,40.9 110.405,41.458Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M104.944,52.438H98.516C97.359,52.438 96.421,51.5 96.421,50.343C96.421,48.606 97.829,47.198 99.566,47.198H103.894C105.631,47.198 107.039,48.606 107.039,50.343C107.039,51.5 106.101,52.438 104.944,52.438Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M101.481,52.421C101.481,53.905 100.278,55.109 98.793,55.109C97.308,55.109 96.105,53.905 96.105,52.421C96.105,50.936 97.308,49.733 98.793,49.733C100.278,49.733 101.481,50.936 101.481,52.421Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M99.82,54.247C99.82,52.988 100.84,51.968 102.099,51.968C103.358,51.968 104.378,52.988 104.378,54.247C104.378,55.506 103.358,56.526 102.099,56.526C100.84,56.526 99.82,55.506 99.82,54.247Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M102.647,52.419C102.647,51.096 103.719,50.024 105.042,50.024C106.364,50.024 107.436,51.096 107.436,52.419C107.436,53.741 106.364,54.813 105.042,54.813C103.719,54.813 102.647,53.741 102.647,52.419Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M102.201,49.526C103.183,49.526 104.051,49.042 104.58,48.299"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M107.792,185.138L102.849,140.124"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M108.95,195.688L108.221,189.048"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.227,126.965L98.515,123.961"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M111.781,200.674H93.104C92.058,198.223 93.574,195.438 96.2,194.988L105.052,193.469C106.044,194.977 107.73,195.881 109.534,195.874L110.341,195.871L111.781,200.674Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M109.534,195.874L110.341,195.871L111.781,200.674H93.104C92.301,198.794 93.007,196.718 94.564,195.649"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.642,193.471L100.4,196.507"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M101.409,193.064L102.167,196.101"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M115.522,200.674H96.845C95.799,198.223 97.316,195.438 99.942,194.988L108.793,193.469C109.785,194.977 111.471,195.881 113.275,195.874L114.083,195.871L115.522,200.674Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M113.276,195.874L114.083,195.871L115.522,200.674H96.845C96.043,198.794 96.748,196.718 98.305,195.649"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.386,193.471L104.144,196.507"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M105.152,193.064L105.911,196.101"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M111.878,195.694C110.627,195.351 109.522,194.545 108.793,193.403L99.941,194.967"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M110.427,126.596L110.442,127.334"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M110.472,128.779L111.792,191.604"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M111.808,192.325L111.823,193.063"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M106.781,163.174C105.908,163.174 105.2,162.466 105.2,161.593"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M101.95,163.174C101.077,163.174 100.369,162.466 100.369,161.593"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.608,58.551V49.732"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M108.171,50.506V58.551C108.171,61.192 106.031,63.333 103.39,63.333C102.077,63.333 100.888,62.804 100.024,61.947"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M113.008,124.065C109.729,124.065 107.072,121.407 107.072,118.129"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M101.291,125.693L102.486,126.597L102.343,135.511L101.291,125.93"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M109.453,120.007V119.233H112.771V122.695"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M125.961,70.566H125.112V69.717C125.112,69.563 124.988,69.439 124.834,69.439C124.68,69.439 124.555,69.563 124.555,69.717V70.566H123.707C123.553,70.566 123.428,70.69 123.428,70.844C123.428,70.998 123.553,71.123 123.707,71.123H124.555V71.971C124.555,72.125 124.68,72.25 124.834,72.25C124.988,72.25 125.112,72.125 125.112,71.971V71.123H125.961C126.115,71.123 126.239,70.998 126.239,70.844C126.239,70.69 126.115,70.566 125.961,70.566Z"
      android:fillColor="#529ED6"/>
  <path
      android:pathData="M35.735,49.23C35.735,49.659 35.387,50.007 34.957,50.007C34.528,50.007 34.18,49.659 34.18,49.23C34.18,48.8 34.528,48.452 34.957,48.452C35.387,48.452 35.735,48.8 35.735,49.23Z"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#E4F4FC"/>
  <path
      android:pathData="M38.458,34.101C38.458,34.59 38.062,34.986 37.574,34.986C37.085,34.986 36.689,34.59 36.689,34.101C36.689,33.613 37.085,33.217 37.574,33.217C38.062,33.217 38.458,33.613 38.458,34.101Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M143.857,38.243C143.857,38.731 143.462,39.127 142.973,39.127C142.484,39.127 142.088,38.731 142.088,38.243C142.088,37.755 142.484,37.359 142.973,37.359C143.462,37.359 143.857,37.755 143.857,38.243Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M45.59,31.603C45.59,31.926 45.328,32.188 45.005,32.188C44.682,32.188 44.42,31.926 44.42,31.603C44.42,31.279 44.682,31.017 45.005,31.017C45.328,31.017 45.59,31.279 45.59,31.603Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M137.416,53.195C137.416,53.518 137.154,53.78 136.831,53.78C136.508,53.78 136.246,53.518 136.246,53.195C136.246,52.871 136.508,52.61 136.831,52.61C137.154,52.61 137.416,52.871 137.416,53.195Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M34.745,41.635H34.103V40.993C34.103,40.876 34.008,40.782 33.892,40.782C33.775,40.782 33.681,40.876 33.681,40.993V41.635H33.039C32.922,41.635 32.828,41.73 32.828,41.846C32.828,41.963 32.922,42.057 33.039,42.057H33.681V42.699C33.681,42.816 33.775,42.91 33.892,42.91C34.008,42.91 34.103,42.816 34.103,42.699V42.057H34.745C34.862,42.057 34.956,41.963 34.956,41.846C34.956,41.73 34.862,41.635 34.745,41.635"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M119.551,19.834H118.908V19.192C118.908,19.076 118.814,18.981 118.698,18.981C118.581,18.981 118.487,19.076 118.487,19.192V19.834H117.844C117.728,19.834 117.634,19.929 117.634,20.045C117.634,20.162 117.728,20.256 117.844,20.256H118.487V20.899C118.487,21.015 118.581,21.109 118.698,21.109C118.814,21.109 118.908,21.015 118.908,20.899V20.256H119.551C119.667,20.256 119.762,20.162 119.762,20.045C119.762,19.929 119.667,19.834 119.551,19.834"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M111.975,69.233C112.782,68.557 113.712,68.021 114.73,67.666"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M110.572,93.095L108.847,76.765C108.662,74.64 109.243,72.637 110.353,71.017"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M117.365,99.199C116.725,101.058 114.699,102.047 112.84,101.408L88.29,90.464L88.883,85.638L112.093,93.621"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M89.795,63.999C89.265,64.726 88.554,65.425 87.726,66.026C85.104,67.928 82.291,68.21 81.183,66.683C80.274,65.431 80.848,63.467 82.641,61.682C83.009,61.317 83.604,61.318 83.97,61.685C84.336,62.053 84.333,62.648 83.967,63.014C82.799,64.177 82.479,65.269 82.704,65.58C83,65.988 84.713,65.89 86.622,64.505C87.519,63.855 88.257,63.051 88.647,62.301C88.956,61.706 88.977,61.275 88.859,61.114C88.628,60.794 87.352,60.729 85.634,61.734C85.187,61.996 84.611,61.846 84.349,61.398C84.087,60.95 84.238,60.375 84.685,60.113C87.107,58.695 89.396,58.653 90.38,60.01C90.972,60.826 90.948,61.947 90.314,63.168C90.169,63.446 89.995,63.725 89.795,63.999Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M92.046,67.425L90.215,68.839L89.561,69.704C88.834,70.668 87.58,71.073 86.426,70.717C85.525,70.439 84.817,69.738 84.529,68.839L84.086,67.454L90.161,61.03L92.046,67.425Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M117.365,99.198L117.634,97.678"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M115.141,98.498C115.627,98.984 115.627,99.771 115.141,100.257C114.656,100.743 113.868,100.743 113.382,100.257"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M107.718,62.582C106.712,63.866 105.148,64.69 103.39,64.69C100.355,64.69 97.894,62.23 97.894,59.195"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#94EBAB"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M108.886,57.603V59.196C108.886,59.954 108.732,60.677 108.454,61.335"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M91.597,64.643H89.199C88.66,64.643 88.224,64.206 88.224,63.668C88.224,63.13 88.66,62.693 89.199,62.693H91.597C92.135,62.693 92.572,63.13 92.572,63.668C92.572,64.206 92.135,64.643 91.597,64.643Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M91.597,66.593H89.199C88.66,66.593 88.224,66.157 88.224,65.618C88.224,65.08 88.66,64.643 89.199,64.643H91.597C92.135,64.643 92.572,65.08 92.572,65.618C92.572,66.157 92.135,66.593 91.597,66.593Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M91.597,68.544H89.199C88.66,68.544 88.224,68.107 88.224,67.568C88.224,67.03 88.66,66.593 89.199,66.593H91.597C92.135,66.593 92.572,67.03 92.572,67.568C92.572,68.107 92.135,68.544 91.597,68.544Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M89.199,64.643C88.66,64.643 88.224,64.206 88.224,63.668C88.224,63.13 88.66,62.693 89.199,62.693H91.597"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M91.596,64.643H90.807"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M91.597,66.593H89.199C88.66,66.593 88.224,66.157 88.224,65.618C88.224,65.08 88.66,64.643 89.199,64.643"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M91.597,68.544H89.199C88.66,68.544 88.224,68.107 88.224,67.568C88.224,67.03 88.66,66.593 89.199,66.593"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M88.971,70.285L90.399,68.542"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M105.266,115.548V113.771"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.921,115.548V113.771"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.691,115.548V113.771"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M92.506,115.548V112.428"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M95.81,115.548V113.053"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.227,115.548V113.771"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M106.913,115.548V113.771"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M110.848,115.548V113.069"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.994,114.261V112.12"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M116.575,113.359V112.541"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M108.636,115.548V114.123"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M84.739,69.779L82.544,78.528"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M106.415,45.321C107.135,45.321 107.719,45.905 107.719,46.625"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M109.583,35.805V36.884"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M107.167,34.904V36.511"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M105.111,34.588V36.195"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.91,34.588V36.195"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M101.099,33.936V35.543"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.932,33.627V35.233"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M95.005,34.474L109.867,36.926Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M95.005,34.474L109.867,36.926"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M88.82,89.879L90.556,86.692L95.552,87.932L89.684,91.085L88.82,90.527V89.879Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M85.493,84.138C86.207,84.911 87.129,85.628 88.173,86.214C91.478,88.069 94.785,88.016 95.866,86.09C96.753,84.512 95.82,82.308 93.494,80.476C93.017,80.1 92.325,80.183 91.95,80.66C91.574,81.137 91.658,81.828 92.133,82.203C93.649,83.396 94.169,84.623 93.949,85.014C93.66,85.529 91.656,85.648 89.249,84.296C88.119,83.662 87.152,82.828 86.597,82.009C86.156,81.358 86.074,80.861 86.189,80.658C86.415,80.255 87.889,80.006 90.022,80.942C90.578,81.186 91.226,80.933 91.47,80.377C91.714,79.821 91.461,79.172 90.905,78.928C87.898,77.608 85.232,77.871 84.272,79.581C83.694,80.609 83.874,81.91 84.777,83.242C84.983,83.546 85.223,83.846 85.493,84.138Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M87.036,87.522C87.133,86.951 86.749,86.408 86.177,86.31L78.495,84.999C77.923,84.902 77.38,85.286 77.283,85.858C77.185,86.43 77.57,86.973 78.142,87.071L85.823,88.381C86.395,88.479 86.938,88.094 87.036,87.522Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M86.442,85.43C86.614,84.876 86.304,84.287 85.75,84.115L80.323,82.429L78.308,81.803C77.754,81.631 77.165,81.941 76.993,82.495C76.821,83.049 77.131,83.637 77.685,83.81L85.127,86.122C85.681,86.294 86.269,85.984 86.442,85.43Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M87.265,84.523C87.623,84.066 87.543,83.406 87.087,83.048L80.954,78.24C80.497,77.882 79.837,77.962 79.479,78.419V78.419C79.121,78.875 79.201,79.536 79.657,79.894L85.79,84.701C86.247,85.059 86.907,84.979 87.265,84.523Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M83.178,87.931L85.987,88.84L86.737,89.625C87.571,90.498 88.863,90.755 89.967,90.268C90.83,89.887 91.453,89.108 91.635,88.183L91.914,86.756L85.136,81.078L83.178,87.931Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M84.107,87.348L84.296,86.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M84.658,85.099L84.822,84.386"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M85.306,83.142L85.47,82.429"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M85.249,79.246L84.881,78.847"
      android:strokeLineJoin="round"
      android:strokeWidth="0.317543"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M83.178,87.931L85.987,88.84L86.737,89.625C87.571,90.498 88.863,90.755 89.967,90.268C90.83,89.887 91.453,89.108 91.635,88.183L91.914,87.186"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M105.877,20.041H104.607V18.77C104.607,18.54 104.42,18.353 104.19,18.353C103.959,18.353 103.773,18.54 103.773,18.77V20.041H102.502C102.272,20.041 102.086,20.227 102.086,20.458C102.086,20.688 102.272,20.875 102.502,20.875H103.773V22.145C103.773,22.375 103.959,22.562 104.19,22.562C104.42,22.562 104.607,22.375 104.607,22.145V20.875H105.877C106.107,20.875 106.294,20.688 106.294,20.458C106.294,20.227 106.107,20.041 105.877,20.041Z"
      android:fillColor="#E43C28"/>
  <path
      android:pathData="M110.701,17.341C110.701,17.829 110.306,18.224 109.818,18.224C109.331,18.224 108.935,17.829 108.935,17.341C108.935,16.854 109.331,16.458 109.818,16.458C110.306,16.458 110.701,16.854 110.701,17.341Z"
      android:fillColor="#E43C28"/>
  <path
      android:pathData="M98.424,9.739C98.542,6.9 96.121,4.594 93.279,4.594C91.816,4.594 90.499,5.208 89.562,6.188C88.625,5.208 87.308,4.594 85.845,4.594C83.003,4.594 80.809,6.9 80.7,9.739C80.423,16.943 89.554,19.908 89.554,19.908C89.554,19.908 98.098,17.653 98.424,9.739"
      android:fillColor="#E43C28"/>
  <path
      android:pathData="M88.248,22.989L89.562,20.195L90.875,22.989H88.248Z"
      android:fillColor="#E43C28"/>
  <path
      android:pathData="M96.333,8.1C95.648,7.162 94.513,6.552 93.267,6.552"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M93.228,16.456C94.944,15.368 96.877,13.45 97.008,10.29"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M84.835,17.385C87.142,19.125 89.555,19.908 89.555,19.908C89.555,19.908 98.098,17.653 98.424,9.739C98.542,6.9 96.121,4.594 93.28,4.594C91.816,4.594 90.499,5.208 89.562,6.188C88.625,5.208 87.308,4.594 85.844,4.594C83.003,4.594 80.809,6.9 80.7,9.739C80.622,11.767 81.289,13.458 82.288,14.843"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M83.182,15.92C83.46,16.215 83.751,16.491 84.049,16.751"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M88.905,7.12L89.562,6.189"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M88.248,22.989L89.562,20.195L90.875,22.989H88.248Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M88.143,20.928L90.668,20.195"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M88.038,19.909L90.875,21.165"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M112.396,161.946H114.151"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M112.957,160.962H114.15"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M88.289,67.066C83.652,62.442 81.46,54.894 83.528,48.581L88.251,34.16C89.286,31 89.54,26.765 89.465,23.24"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M89.547,83.961C89.763,83.321 90.109,82.716 90.582,82.188C93.881,78.504 93.899,73.102 90.994,69.414L90.306,68.543"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M87.369,94.053L87.983,90.389"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476314"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M86.056,60.959C86.056,61.471 85.641,61.886 85.129,61.886C84.617,61.886 84.202,61.471 84.202,60.959C84.202,60.447 84.617,60.032 85.129,60.032C85.641,60.032 86.056,60.447 86.056,60.959Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M93.823,81.276C93.823,81.879 93.334,82.368 92.731,82.368C92.128,82.368 91.639,81.879 91.639,81.276C91.639,80.673 92.128,80.184 92.731,80.184C93.334,80.184 93.823,80.673 93.823,81.276Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M58.882,138.249L55.562,111.27"
      android:strokeLineJoin="round"
      android:strokeWidth="0.488485"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M44.996,65.357L46.874,81.124"
      android:strokeLineJoin="round"
      android:strokeWidth="0.488485"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M61.998,58.889L52.381,74.874"
      android:strokeLineJoin="round"
      android:strokeWidth="0.488485"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M59.479,75.436L54.125,83.204"
      android:strokeLineJoin="round"
      android:strokeWidth="0.488485"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M194.856,116.195C190.066,120.388 187.473,131.075 190.924,139.901"
      android:strokeLineJoin="round"
      android:strokeWidth="0.487425"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M208.724,123.596C209.936,125.342 211.736,126.312 214.37,124.725"
      android:strokeLineJoin="round"
      android:strokeWidth="0.487425"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M207.982,112.273L208.421,119.187"
      android:strokeLineJoin="round"
      android:strokeWidth="0.487425"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M191.496,143.531L200.733,182.326"
      android:strokeLineJoin="round"
      android:strokeWidth="0.487425"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M213.291,105.403L217.918,104.182V105.403"
      android:strokeLineJoin="round"
      android:strokeWidth="0.487425"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
</vector>
