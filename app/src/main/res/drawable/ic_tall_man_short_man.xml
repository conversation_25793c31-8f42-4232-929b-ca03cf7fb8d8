<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="1194dp"
    android:height="1194dp"
    android:viewportWidth="1194"
    android:viewportHeight="1194">
  <group>
    <clip-path android:pathData="M0,0h1194v1194h-1194z M 0,0"/>
    <path
        android:pathData="M0,0h1194v1194h-1194z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M597,597m-597,0a597,597 0,1 1,1194 0a597,597 0,1 1,-1194 0"
        android:fillColor="#F3F3F3"/>
    <path
        android:pathData="M667.06,831.55L690.79,976.95C686.07,978.66 682.64,983.24 682.64,988.67C682.64,993.53 685.35,997.67 689.36,999.68C692.79,1001.54 695.36,1004.82 695.93,1008.68L710.23,1117.48C711.8,1129.63 722.24,1138.78 734.39,1138.78C747.83,1138.78 758.84,1127.91 758.84,1114.33V831.55H667.06Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M705.23,1028.41C707.8,1028.41 709.8,1030.41 709.8,1032.99"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M737.68,1028.41C740.25,1028.41 742.25,1030.41 742.25,1032.99"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M723.67,1017.98C724.1,1015.55 726.53,1013.83 729.1,1014.26"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M737.25,999.82C737.68,997.39 740.11,995.67 742.68,996.1"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M715.38,998.82C713.38,1000.39 710.37,999.96 708.94,997.82"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M693.22,993.24C691.22,994.82 688.21,994.39 686.78,992.24"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#E5B6AA"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M730.53,984.24C728.53,985.81 725.53,985.38 724.1,983.24"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M711.8,1088.31C714.38,1088.31 716.38,1090.31 716.38,1092.89"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M744.26,1088.31C746.83,1088.31 748.83,1090.31 748.83,1092.89"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M730.39,1077.88C730.82,1075.45 733.25,1073.73 735.82,1074.16"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M743.83,1059.72C744.26,1057.29 746.69,1055.58 749.26,1056"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M721.95,1058.86C719.95,1060.44 716.95,1060.01 715.52,1057.86"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M737.25,1044.14C735.25,1045.71 732.25,1045.28 730.82,1043.14"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M780.14,1088.31C782.71,1088.31 784.71,1090.31 784.71,1092.89"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M812.59,1088.31C815.16,1088.31 817.16,1090.31 817.16,1092.89"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M798.72,1077.88C799.15,1075.45 801.58,1073.73 804.16,1074.16"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M812.3,1059.72C812.73,1057.29 815.16,1055.58 817.74,1056"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M790.29,1058.86C788.29,1060.44 785.29,1060.01 783.86,1057.86"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M805.59,1044.14C803.58,1045.71 800.58,1045.28 799.15,1043.14"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M768.27,1192.68H629.6L630.31,1189.67C636.18,1167.09 654.19,1149.79 676.92,1144.78L693.08,1141.21C693.5,1141.07 694.08,1140.92 694.5,1140.92C703.08,1138.49 708.94,1130.34 709.09,1121.34L709.23,1115.33H758.69L768.27,1192.68Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M718.38,1163.08C719.09,1166.8 716.81,1170.37 713.09,1171.09C709.37,1171.8 705.8,1169.52 705.08,1165.8C704.37,1162.08 705.51,1134.49 705.51,1134.49C705.51,1134.49 717.66,1159.37 718.38,1163.08Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M693.79,1163.8C691.79,1166.94 687.5,1167.8 684.35,1165.8C681.21,1163.8 680.35,1159.51 682.35,1156.36C684.35,1153.22 704.8,1134.49 704.8,1134.49C704.8,1134.49 695.79,1160.65 693.79,1163.8Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M491.5,483.73L453.33,607.82L452.18,619.97C447.9,666.86 454.76,714.18 472.2,757.93L487.92,797.53C498.36,823.69 500.79,852.43 494.36,879.73C494.36,879.88 494.21,880.16 494.21,880.3C486.49,912.61 479.49,940.21 486.92,956.93C488.5,960.65 495.22,968.65 499.93,972.37C513.37,983.09 521.95,998.82 524.24,1015.69L524.38,1016.55C526.38,1031.27 523.38,1046.14 516.23,1059.15C509.23,1071.87 504.8,1084.74 511.37,1096.03C518.66,1108.76 553.54,1105.33 553.54,1105.33H603.29L629.46,659.43L603.29,374.36L491.5,483.73Z"
        android:fillColor="#222222"/>
    <path
        android:pathData="M533.82,374.5L421.88,483.87L383.71,607.96L382.56,620.11C380.27,644.84 381.13,669.58 384.99,694.02C386.28,702.17 390.28,709.46 396.29,714.9C404.58,722.47 408.3,733.91 405.29,744.78C403.15,752.5 403.58,760.64 406.58,768.08L418.45,797.81C428.88,823.98 431.31,852.71 424.88,880.02C424.88,880.16 424.74,880.45 424.74,880.59C417.02,912.9 410.01,940.49 417.45,957.22C419.02,960.93 425.74,968.94 430.45,972.66C443.89,983.38 452.47,999.1 454.76,1015.97L454.9,1016.83C456.9,1031.56 453.9,1046.43 446.75,1059.43C439.75,1072.16 435.32,1085.02 441.89,1096.32C449.18,1109.04 484.07,1105.61 484.07,1105.61H533.82L559.98,659.71L533.82,374.5Z"
        android:fillColor="#222222"/>
    <path
        android:pathData="M627.03,630.84L603.58,374.36L539.68,436.84L533.96,374.36L422.02,483.73L383.85,607.82L382.7,619.97C382.13,625.69 381.85,631.55 381.7,637.27C460.19,673.58 549.83,671.44 627.03,630.84Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M691.65,412.67C691.65,354.49 663.91,300.59 618.3,266.57C598.15,239.98 566.41,222.96 530.53,222.96H486.35C425.59,222.96 376.27,272.29 376.27,333.05V619.68L378.27,620.68C461.05,664.57 558.26,664.29 640.75,619.68V508.75L691.79,518.18V412.67H691.65Z"
        android:fillColor="#C1C1C1"/>
    <path
        android:pathData="M509.8,276.58C535.96,276.58 557.55,254.7 559.84,226.82C550.54,224.25 540.68,222.82 530.53,222.82H486.35C477.2,222.82 468.2,223.96 459.76,226.11C461.62,254.42 483.35,276.58 509.8,276.58Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M599.86,143.05C608.47,143.05 615.45,136.07 615.45,127.47C615.45,118.86 608.47,111.88 599.86,111.88C591.26,111.88 584.28,118.86 584.28,127.47C584.28,136.07 591.26,143.05 599.86,143.05Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M508.37,92.3C518.55,92.3 526.81,84.04 526.81,73.86C526.81,63.67 518.55,55.41 508.37,55.41C498.18,55.41 489.93,63.67 489.93,73.86C489.93,84.04 498.18,92.3 508.37,92.3Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M683.64,1193.68C683.64,1184.96 677.92,1177.09 669.2,1173.66L609.59,1149.93C597.01,1144.93 588.86,1132.77 588.86,1119.34V1083.6H543.68L535.1,1193.68H683.64Z"
        android:fillColor="#C1C1C1"/>
    <path
        android:pathData="M650.9,1192.82C650.9,1184.1 654.33,1176.09 659.91,1170.09"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M599.86,1131.06C602.72,1134.2 606.87,1150.07 606.87,1150.07C606.87,1150.07 591.43,1144.36 588.57,1141.21C585.71,1138.06 586,1133.35 589.14,1130.49C592.15,1127.77 597.01,1128.06 599.86,1131.06Z"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M625.88,1143.21C622.74,1146.07 606.87,1150.22 606.87,1150.22C606.87,1150.22 612.59,1134.78 615.73,1131.92C618.88,1129.06 623.6,1129.34 626.46,1132.49C629.31,1135.63 629.03,1140.35 625.88,1143.21Z"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M606.16,1193.68C606.16,1184.96 600.44,1177.09 591.72,1173.66L532.1,1149.93C519.52,1144.93 511.37,1132.77 511.37,1119.34V1083.6H466.2L457.62,1193.68H606.16Z"
        android:fillColor="#C1C1C1"/>
    <path
        android:pathData="M573.42,1192.82C573.42,1184.1 576.85,1176.09 582.42,1170.09"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M522.38,1131.06C525.24,1134.2 529.38,1150.07 529.38,1150.07C529.38,1150.07 513.94,1144.36 511.08,1141.21C508.23,1138.06 508.51,1133.35 511.66,1130.49C514.8,1127.77 519.52,1128.06 522.38,1131.06Z"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M548.4,1143.21C545.25,1146.07 529.38,1150.22 529.38,1150.22C529.38,1150.22 535.1,1134.78 538.25,1131.92C541.39,1129.06 546.11,1129.34 548.97,1132.49C551.83,1135.49 551.54,1140.35 548.4,1143.21Z"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M603.29,1183.38C600.72,1179.09 596.72,1175.66 591.72,1173.66L532.1,1149.93"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M200,1193.1H870.21"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M609.3,1107.9H509.08L512.09,1083.6H612.3L609.3,1107.9Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M509.8,250.41C492.07,250.41 477.63,235.98 477.63,218.25V139.9H541.97V218.25C541.82,235.98 527.53,250.41 509.8,250.41Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M541.82,208.81V139.9H477.49V167.64C483.06,180.51 492.64,191.66 505.65,199.23C517.09,206.1 529.67,209.1 541.82,208.81Z"
        android:fillColor="#969696"/>
    <path
        android:pathData="M477.63,227.11V139.9H541.82V227.11"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M600.06,158.17C619.32,125.34 608.32,83.12 575.49,63.87C542.67,44.61 500.45,55.61 481.19,88.43C461.94,121.26 472.94,163.48 505.76,182.73C538.58,201.99 580.8,190.99 600.06,158.17Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M512.8,186.37C512.8,186.22 512.66,186.08 512.51,185.94C543.54,197.95 579.42,186.08 596.86,156.63C615.88,124.18 605.3,82.58 573.42,62.99C573.84,63.13 574.13,63.42 574.56,63.56C607.44,82.86 618.31,125.04 599.15,157.77C581.57,187.8 544.68,199.52 513.37,186.65C512.94,186.65 512.8,186.51 512.8,186.37C512.8,186.51 512.8,186.51 512.8,186.37Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M512.8,186.51C512.66,186.51 512.51,186.37 512.8,186.51V186.51Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M486.21,165.64C469.34,144.05 466.48,113.46 481.21,88.44C500.51,55.56 542.68,44.69 575.42,63.85C608.16,83 619.16,125.32 600.01,158.06C580.85,190.8 538.53,201.81 505.8,182.65"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M539.25,147.77C537.1,151.48 538.96,156.49 543.25,159.06C547.68,161.63 552.97,160.78 555.12,157.06L563.41,142.91"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M568.98,111.31C565.7,113.89 561.12,114.46 557.12,112.31"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M583.85,128.04C580.42,125.75 578.42,121.46 579.28,117.17"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M518.38,163.06C508.65,157.35 505.51,144.91 511.08,135.33"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M539.25,147.77C537.96,150.05 534.96,150.77 532.67,149.48C530.39,148.2 529.67,145.19 530.96,142.91"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M563.41,161.92C562.12,164.21 559.12,164.92 556.83,163.64C554.54,162.35 553.83,159.35 555.12,157.06"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M493.11,91.73C496.79,85.47 494.69,77.41 488.42,73.73C482.16,70.06 474.1,72.16 470.43,78.42C466.75,84.69 468.85,92.75 475.11,96.42C481.38,100.1 489.44,98 493.11,91.73Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M477.63,80.57C480.64,81 482.64,83.86 482.21,86.72"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M483.21,98.16C480.49,98.45 477.63,98.02 475.06,96.44C468.77,92.73 466.77,84.72 470.34,78.43C473.91,72.14 482.06,70.14 488.35,73.71C494.64,77.43 496.64,85.44 493.07,91.73"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M518.66,78C528.85,78 537.1,69.74 537.1,59.56C537.1,49.37 528.85,41.12 518.66,41.12C508.48,41.12 500.22,49.37 500.22,59.56C500.22,69.74 508.48,78 518.66,78Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M518.66,90.87C525.14,90.87 530.39,85.62 530.39,79.14C530.39,72.67 525.14,67.42 518.66,67.42C512.19,67.42 506.94,72.67 506.94,79.14C506.94,85.62 512.19,90.87 518.66,90.87Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M541.82,71.43C550.27,71.43 557.12,64.58 557.12,56.13C557.12,47.68 550.27,40.83 541.82,40.83C533.38,40.83 526.53,47.68 526.53,56.13C526.53,64.58 533.38,71.43 541.82,71.43Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M541.82,90.01C550.27,90.01 557.12,82.78 557.12,73.86C557.12,64.93 550.27,57.7 541.82,57.7C533.38,57.7 526.53,64.93 526.53,73.86C526.53,82.78 533.38,90.01 541.82,90.01Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M588.21,62.25C588.62,52.07 580.7,43.49 570.52,43.09C560.34,42.68 551.76,50.61 551.36,60.78C550.95,70.96 558.87,79.54 569.05,79.94C579.23,80.35 587.81,72.43 588.21,62.25Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M560.55,86.72C565.68,86.72 569.84,82.56 569.84,77.43C569.84,72.3 565.68,68.14 560.55,68.14C555.42,68.14 551.26,72.3 551.26,77.43C551.26,82.56 555.42,86.72 560.55,86.72Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M581.57,98.45C591.75,98.45 600.01,90.19 600.01,80C600.01,69.82 591.75,61.56 581.57,61.56C571.38,61.56 563.12,69.82 563.12,80C563.12,90.19 571.38,98.45 581.57,98.45Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M599.86,88.58C604.6,88.58 608.44,84.74 608.44,80C608.44,75.27 604.6,71.43 599.86,71.43C595.13,71.43 591.29,75.27 591.29,80C591.29,84.74 595.13,88.58 599.86,88.58Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M594.43,107.88C600.2,107.88 604.87,103.21 604.87,97.44C604.87,91.68 600.2,87.01 594.43,87.01C588.67,87.01 583.99,91.68 583.99,97.44C583.99,103.21 588.67,107.88 594.43,107.88Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M610.59,104.59C616.51,104.59 621.31,99.79 621.31,93.87C621.31,87.95 616.51,83.15 610.59,83.15C604.66,83.15 599.86,87.95 599.86,93.87C599.86,99.79 604.66,104.59 610.59,104.59Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M609.59,128.04C618.19,128.04 625.17,121.06 625.17,112.46C625.17,103.85 618.19,96.87 609.59,96.87C600.98,96.87 594,103.85 594,112.46C594,121.06 600.98,128.04 609.59,128.04Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M563.27,130.61C561.55,133.76 557.69,135.04 554.55,133.33C551.4,131.61 550.11,127.75 551.83,124.61"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M582.85,141.19C581.14,144.34 577.28,145.62 574.13,143.91C570.99,142.19 569.7,138.33 571.41,135.19"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M431.31,606.24C379.7,601.81 340.53,557.78 340.53,506.03V477.72H390.57V506.32C390.57,531.76 409.44,553.78 434.89,556.35C454.76,558.35 473.63,548.2 483.07,531.05L534.82,436.41L571.13,458.42L530.1,548.34C525.1,559.5 518.23,569.65 509.51,578.22C489.07,598.1 460.76,608.82 431.31,606.24Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M583.85,461.28L580.99,468.72C574.27,486.3 554.55,495.16 536.96,488.45C519.38,481.73 510.51,462 517.23,444.41L520.09,436.98C526.81,419.39 546.54,410.53 564.12,417.25C581.71,423.97 590.57,443.7 583.85,461.28Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M573.7,381.22L541.54,438.55L525.24,427.69L561.41,373.65C563.7,370.36 568.13,369.36 571.56,371.65C574.56,373.65 575.56,377.94 573.7,381.22Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M595.72,393.38L557.55,446.99L542.54,434.41L584.28,384.65C586.85,381.51 591.43,381.22 594.57,383.8C597.43,386.08 598.01,390.37 595.72,393.38Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M611.87,409.82L567.7,458.57L554.26,444.27L601.44,399.52C604.29,396.81 609.01,396.95 611.73,399.81C614.45,402.67 614.45,406.96 611.87,409.82Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M621.59,429.69L571.41,472.15L559.98,456.28L612.73,418.25C616.02,415.96 620.59,416.68 622.88,419.82C625.03,422.97 624.45,427.26 621.59,429.69Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M536.1,454.56L521.24,472.15L493.5,429.97C491.21,426.4 492.21,421.68 495.65,419.39C498.79,417.25 503.08,417.82 505.65,420.68L536.1,454.56Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M548.97,415.11C553.97,414.68 559.12,415.39 564.12,417.25C576.13,421.83 584.14,432.55 585.85,444.41"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M585.71,457.42L581.71,467"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M546.25,488.73C548.4,489.88 549.26,492.59 548.26,494.74C547.11,496.88 544.4,497.74 542.25,496.74"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M556.26,722.9L538.25,1030.7"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M541.82,722.9C551.26,722.9 560.26,719.76 567.7,713.9L584.14,700.74"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M530.81,907.04C525.1,912.76 515.66,912.76 509.94,907.04"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M605.01,907.04C599.29,912.76 589.86,912.76 584.14,907.04"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M531.96,1107.9H431.74L434.6,1083.6H534.82L531.96,1107.9Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M540.39,601.39V613.54"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M443.75,542.34V554.64"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M601.29,505.74V513.46"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M602.15,359.35V367.07"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M472.06,298.59V306.31"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M527.1,363.21V370.93"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M440.6,423.4V431.12"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M440.6,256.85V264.57"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M636.32,298.59V306.31"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M449.75,1089.89L447.61,1107.9"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M510.23,1089.89L508.08,1107.9"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M463.91,1095.32L461.76,1107.9"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M599.01,1089.89L596.86,1107.9"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M552.83,1095.32L550.68,1107.9"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M539.96,1107.9H531.96L532.1,1083.6H550.68L539.96,1107.9Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M531.96,1107.9H431.74L434.6,1083.6H534.82L531.96,1107.9Z"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M588.43,634.27C521.81,654.42 450.33,648.99 386.42,617.68"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M636.89,614.39C628.74,618.68 620.45,622.4 612.16,625.83"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M434.37,609.01C437.84,606.14 438.32,601 435.45,597.53C432.58,594.07 427.45,593.58 423.98,596.45C420.51,599.32 420.03,604.46 422.89,607.92C425.76,611.39 430.9,611.88 434.37,609.01Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M437.32,604.1C437.03,605.96 436.03,607.67 434.46,608.96C431.02,611.82 425.88,611.39 423.02,607.82C421.3,605.67 420.73,603.1 421.45,600.67"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M540.39,1107.9H609.3"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M535.1,888.74C530.96,894.89 522.66,896.6 516.52,892.6"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M610.44,888.74C606.3,894.89 598.01,896.6 591.86,892.6"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M467.2,225.82C467.2,249.27 486.21,268.28 509.65,268.28C533.1,268.28 552.11,249.27 552.11,225.82V225.11C545.11,223.68 537.82,222.96 530.38,222.96H486.21C479.63,222.96 473.34,223.54 467.05,224.68V225.82H467.2Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M542.39,263.42C550.26,255.99 555.97,246.12 558.55,234.69"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M459.62,226.11C461.62,254.27 483.21,276.58 509.65,276.58C515.95,276.58 521.95,275.29 527.38,273"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M550.68,1083.6H537.82V1037.99L550.68,1083.6Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M913.67,1193.68H940.4"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M967.99,1193.68H994.72"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M525.53,200.66C527.67,200.66 529.39,202.38 529.39,204.52C529.39,206.67 527.67,208.38 525.53,208.38"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M415.3,499.74L315.94,518.18V412.67C315.94,344.2 354.4,281.58 415.3,250.41V499.74Z"
        android:fillColor="#C1C1C1"/>
    <path
        android:pathData="M407.01,501.31V410.53L421.02,402.95L407.01,501.31Z"
        android:fillColor="#E4E4E4"/>
    <path
        android:pathData="M381.7,473.86V481.58"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M365.55,342.05V349.63"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M307.65,509.46C311.08,513.03 316.51,514.46 321.52,512.75C325.23,511.46 328.09,508.6 329.52,505.17L346.11,501.88C351.11,500.88 354.4,495.88 353.4,490.88C352.54,486.73 349.11,483.87 345.25,483.44C349.54,481.87 352.25,477.44 351.25,472.86C350.4,468.86 347.11,466 343.25,465.43C346.54,463.43 348.54,459.57 347.68,455.56C346.68,450.56 341.67,447.27 336.67,448.27L319.23,451.85L321.8,449.85C321.95,449.7 322.09,449.7 322.09,449.56L342.68,444.13C347.68,442.84 350.68,437.69 349.39,432.69C348.11,427.69 342.96,424.68 337.96,425.97L315.51,431.83C313.8,432.26 312.37,433.12 311.22,434.26C310.94,434.4 310.65,434.55 310.51,434.83L307.51,437.12V509.46H307.65Z"
        android:strokeAlpha="0.5"
        android:fillColor="#161E24"
        android:fillAlpha="0.5"/>
    <path
        android:pathData="M402.01,410.53L424.74,399.52"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M315.94,511.32C308.94,513.75 301.36,510.03 298.93,503.03L284.49,461.14C282.06,454.13 285.78,446.56 292.78,444.13C299.79,441.7 307.36,445.41 309.8,452.42L324.23,494.31C326.67,501.31 322.95,508.89 315.94,511.32Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M340.53,500.45L317.8,505.03C312.8,506.03 307.79,502.74 306.79,497.74C305.79,492.73 309.08,487.73 314.08,486.73L336.81,482.15C341.82,481.15 346.82,484.44 347.82,489.45C348.96,494.45 345.68,499.45 340.53,500.45Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M338.39,482.3L315.66,487.02C310.65,488.02 305.65,484.73 304.65,479.72C303.65,474.72 306.94,469.72 311.94,468.72L334.67,464.14C339.67,463.14 344.68,466.43 345.68,471.43C346.82,476.44 343.53,481.3 338.39,482.3Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M334.96,465L312.23,469.57C307.22,470.58 302.22,467.29 301.22,462.28C300.22,457.28 303.51,452.28 308.51,451.27L331.24,446.7C336.24,445.7 341.25,448.99 342.25,453.99C343.25,459.14 339.96,464 334.96,465Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M316.37,448.27L297.93,462.28C293.78,465.43 287.92,464.57 284.78,460.42C281.63,456.28 282.49,450.42 286.64,447.27L305.08,433.26C309.22,430.12 315.08,430.98 318.23,435.12C321.37,439.27 320.52,445.13 316.37,448.27Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M337.24,442.55L314.8,448.42C309.79,449.7 304.65,446.7 303.36,441.7C302.07,436.69 305.08,431.55 310.08,430.26L332.52,424.4C337.53,423.11 342.67,426.11 343.96,431.12C345.25,436.27 342.25,441.27 337.24,442.55Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M304.08,443.27L306.65,450.7"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#B76452"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M310.51,462L313.08,469.43"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#B76452"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M316.8,480.01L319.37,487.44"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#B76452"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M317.8,505.03L336.53,501.31"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M326.66,484.73L338.39,482.3"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M324.38,467.14L337.82,464"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M338.39,513.46L407.01,500.74"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#A6A6A6"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M913.81,190.51H909.52V186.23C909.52,185.37 908.95,184.79 908.09,184.79C907.23,184.79 906.66,185.37 906.66,186.23V190.51H902.37C901.51,190.51 900.94,191.09 900.94,191.94C900.94,192.8 901.51,193.37 902.37,193.37H906.66V197.66C906.66,198.52 907.23,199.09 908.09,199.09C908.95,199.09 909.52,198.52 909.52,197.66V193.37H913.81C914.66,193.37 915.24,192.8 915.24,191.94C915.24,191.09 914.66,190.51 913.81,190.51Z"
        android:fillColor="#C1C1C1"/>
    <path
        android:pathData="M853.19,241.26H844.61V232.54C844.61,230.97 843.33,229.68 841.75,229.68C840.18,229.68 838.9,230.97 838.9,232.54V241.26H830.17C828.6,241.26 827.32,242.55 827.32,244.12C827.32,245.7 828.6,246.98 830.17,246.98H838.9V255.7C838.9,257.28 840.18,258.56 841.75,258.56C843.33,258.56 844.61,257.28 844.61,255.7V246.98H853.33C854.91,246.98 856.19,245.7 856.19,244.12C856.19,242.55 854.76,241.26 853.19,241.26Z"
        android:fillColor="#C1C1C1"/>
    <path
        android:pathData="M718.52,76.29C718.52,71 714.23,66.71 708.95,66.71C703.66,66.71 699.37,71 699.37,76.29C699.37,77 699.37,77 699.37,76.29C699.37,71 695.08,66.71 689.79,66.71C684.5,66.71 680.21,71 680.21,76.29C680.21,80.29 685.78,85.29 685.78,85.29L699.37,98.87L712.95,85.29C713.09,85.29 718.52,80.29 718.52,76.29Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#BDBDBD"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M715.09,63.28V57.42"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M725.96,61.28L721.67,65.42"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M733.39,55.27L731.39,57.42"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M722.95,73.43H728.82"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M337.53,204.95H331.81V199.23C331.81,198.23 330.95,197.38 329.95,197.38C328.95,197.38 328.09,198.23 328.09,199.23V204.95H322.37C321.37,204.95 320.52,205.81 320.52,206.81C320.52,207.81 321.37,208.67 322.37,208.67H328.09V214.39C328.09,215.39 328.95,216.25 329.95,216.25C330.95,216.25 331.81,215.39 331.81,214.39V208.67H337.53C338.53,208.67 339.39,207.81 339.39,206.81C339.39,205.81 338.67,204.95 337.53,204.95Z"
        android:fillColor="#C1C1C1"/>
    <path
        android:pathData="M301.5,167.64C303.71,167.64 305.51,165.85 305.51,163.64C305.51,161.43 303.71,159.63 301.5,159.63C299.29,159.63 297.5,161.43 297.5,163.64C297.5,165.85 299.29,167.64 301.5,167.64Z"
        android:fillColor="#C1C1C1"/>
    <path
        android:pathData="M340.67,167.07C343.6,167.07 345.96,164.7 345.96,161.78C345.96,158.86 343.6,156.49 340.67,156.49C337.75,156.49 335.38,158.86 335.38,161.78C335.38,164.7 337.75,167.07 340.67,167.07Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M906.94,337.48C909.87,337.48 912.23,335.11 912.23,332.19C912.23,329.27 909.87,326.9 906.94,326.9C904.02,326.9 901.66,329.27 901.66,332.19C901.66,335.11 904.02,337.48 906.94,337.48Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M736.39,831.55L760.13,976.95C755.41,978.66 751.98,983.24 751.98,988.67C751.98,993.53 754.69,997.67 758.7,999.68C762.13,1001.54 764.7,1004.82 765.27,1008.68L779.42,1117.48C781,1129.63 791.43,1138.78 803.59,1138.78C817.02,1138.78 828.03,1127.91 828.03,1114.33V831.55H736.39Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M852.34,723.76C852.34,686.02 825.17,655.57 791.72,655.57C777.28,655.57 764.13,661.29 753.69,670.72L635.03,657.57L635.18,660.71C637.89,729.91 646.04,798.82 659.77,866.72C680.07,876.16 703.37,876.3 723.81,867.29L724.96,866.72L734.53,871.72C763.55,887.02 798.01,886.59 826.74,870.72L834.04,866.72L837.47,768.51C846.76,756.35 852.34,740.77 852.34,723.76Z"
        android:fillColor="#969696"/>
    <path
        android:pathData="M845.9,693.17C835.89,670.86 815.45,655.57 791.72,655.57C777.28,655.57 764.13,661.29 753.69,670.72L635.03,657.57L635.18,660.71C635.32,664.86 635.46,668.86 635.75,673.01C702.94,701.32 778.42,719.18 845.9,693.17Z"
        android:fillColor="#6B6B6B"/>
    <path
        android:pathData="M846.62,682.3C779.42,711.32 703.65,695.03 635.6,667C629.89,664.57 626.17,659 626.17,652.85V422.25C626.17,401.95 629.6,381.8 636.46,362.64C646.61,334.05 673.78,314.89 704.08,314.89H718.09C789,314.89 846.62,372.36 846.62,443.41V682.3Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M780,1028.41C782.57,1028.41 784.57,1030.41 784.57,1032.99"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M812.45,1028.41C815.02,1028.41 817.03,1030.41 817.03,1032.99"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M798.58,1017.98C799.01,1015.54 801.44,1013.83 804.02,1014.26"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M812.02,999.82C812.45,997.39 814.88,995.67 817.45,996.1"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M790.15,998.82C788.15,1000.39 785.14,999.96 783.71,997.82"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M805.3,984.24C803.3,985.81 800.3,985.38 798.87,983.23"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M780,1088.88C782.57,1088.88 784.57,1090.89 784.57,1093.46"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M812.45,1088.88C815.02,1088.88 817.03,1090.89 817.03,1093.46"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M798.58,1078.45C799.01,1076.02 801.44,1074.3 804.02,1074.73"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M812.02,1060.15C812.45,1057.72 814.88,1056 817.45,1056.43"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M790.15,1059.29C788.15,1060.86 785.14,1060.43 783.71,1058.29"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M805.3,1044.57C803.3,1046.14 800.3,1045.71 798.87,1043.57"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M837.61,1192.67H698.94L699.65,1189.67C705.51,1167.08 723.53,1149.79 746.26,1144.78L762.41,1141.21C762.84,1141.06 763.41,1140.92 763.84,1140.92C772.42,1138.49 778.14,1130.49 778.57,1121.62L778.85,1116.62L828.17,1115.76L837.61,1192.67Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M740.68,888.74L760.13,976.8C755.41,978.52 751.98,983.09 751.98,988.53C751.98,993.39 754.69,997.53 758.7,999.53C762.13,1001.39 764.7,1004.68 765.27,1008.54"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M787.72,1163.08C788.43,1166.8 786.14,1170.37 782.43,1171.09C778.71,1171.8 775.14,1169.51 774.42,1165.8C773.71,1162.08 774.85,1134.49 774.85,1134.49C774.85,1134.49 787,1159.36 787.72,1163.08Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M763.13,1163.8C761.12,1166.94 756.84,1167.8 753.69,1165.8C750.54,1163.8 749.69,1159.51 751.69,1156.36C753.69,1153.22 774.13,1134.49 774.13,1134.49C774.13,1134.49 765.13,1160.65 763.13,1163.8Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M698.94,1192.67L699.65,1189.67C705.51,1167.08 723.53,1149.79 746.26,1144.78"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M711.66,1187.24H828.03"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#C1C1C1"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M720.81,841.42L681.35,759.21L698.51,770.51L720.81,841.42Z"
        android:fillColor="#6B6B6B"/>
    <path
        android:pathData="M828.03,781.95L837.61,768.36"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M669.92,743.2L674.78,749.78C682.21,760.07 692.5,768.08 704.23,772.65"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M718.24,833.41L698.51,770.51"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M682.5,323.18L704.37,352.06L673.21,370.21L682.5,323.18Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M742.54,324.47L706.23,352.06L736.82,375.08L742.54,324.47Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M712.95,353.35C699.37,353.35 687.64,344.2 684.35,331.04L659.62,232.26H720.38L741.54,316.75C746.26,335.19 732.25,353.35 712.95,353.35Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M726.53,256.56L720.38,232.11H659.62L679.64,311.74C704.23,304.17 722.96,282.86 726.53,256.56Z"
        android:fillColor="#C0C0C0"/>
    <path
        android:pathData="M704.37,357.35V392.8"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M712.95,353.35C699.37,353.35 687.64,344.2 684.35,331.04L659.62,232.26H720.38L741.54,316.75C746.26,335.19 732.25,353.35 712.95,353.35Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M690.22,214.67C704.74,214.67 716.52,201.81 716.52,185.94C716.52,170.07 704.74,157.2 690.22,157.2C675.69,157.2 663.91,170.07 663.91,185.94C663.91,201.81 675.69,214.67 690.22,214.67Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M614.16,214.39C626.08,214.39 635.75,203.76 635.75,190.65C635.75,177.55 626.08,166.92 614.16,166.92C602.24,166.92 592.57,177.55 592.57,190.65C592.57,203.76 602.24,214.39 614.16,214.39Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M600.44,237.26C612.36,237.26 622.02,226.64 622.02,213.53C622.02,200.42 612.36,189.8 600.44,189.8C588.52,189.8 578.85,200.42 578.85,213.53C578.85,226.64 588.52,237.26 600.44,237.26Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M663.2,197.09C675.12,197.09 684.78,186.46 684.78,173.36C684.78,160.25 675.12,149.63 663.2,149.63C651.28,149.63 641.61,160.25 641.61,173.36C641.61,186.46 651.28,197.09 663.2,197.09Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M657.48,290.73C694.82,290.73 725.1,260.45 725.1,223.11C725.1,185.76 694.82,155.49 657.48,155.49C620.13,155.49 589.86,185.76 589.86,223.11C589.86,260.45 620.13,290.73 657.48,290.73Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M682.62,198.09C688.26,192.45 691.11,186.16 688.99,184.04C686.87,181.91 680.58,184.76 674.94,190.4C669.3,196.04 666.45,202.33 668.57,204.46C670.69,206.58 676.98,203.73 682.62,198.09Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M718.87,197.47C721.26,195.66 719.28,189.04 714.45,182.69C709.62,176.35 703.77,172.68 701.38,174.49C698.99,176.31 700.97,182.93 705.8,189.27C710.63,195.62 716.48,199.29 718.87,197.47Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M928.39,1191.82C926.96,1188.67 908.81,1150.5 918.81,1142.92C928.82,1135.35 933.11,1177.95 933.11,1177.95C933.11,1177.95 932.68,1154.5 939.26,1155.08C945.98,1155.79 942.12,1188.1 942.12,1188.1C942.12,1188.1 944.98,1173.95 950.27,1174.66C955.55,1175.38 950.27,1191.82 950.27,1191.82"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M316.8,1174.23C316.8,1174.23 316.23,1144.35 324.66,1145.21C333.1,1146.07 328.24,1187.24 328.24,1187.24C328.24,1187.24 331.81,1169.23 338.53,1170.09C345.25,1170.94 338.53,1191.96 338.53,1191.96"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M657.48,237.12C654.33,240.98 648.76,241.55 644.9,238.55"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M652.19,278.86C649.04,282.72 643.47,283.29 639.61,280.29"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M626.74,237.12C623.6,240.98 618.02,241.55 614.16,238.55"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M660.05,290.01C622.74,290.01 592.43,259.7 592.43,222.39C592.43,190.65 614.16,164.06 643.61,156.77C612.16,162.49 588.28,190.08 588.28,223.25C588.28,260.56 618.59,290.87 655.91,290.87C660.62,290.87 665.34,290.3 669.92,289.44C666.63,289.73 663.34,290.01 660.05,290.01Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M691.36,280.72C710.66,268.86 723.53,247.41 723.53,223.11C723.53,185.79 693.22,155.49 655.91,155.49C618.59,155.49 588.28,185.79 588.28,223.11C588.28,260.42 618.59,290.73 655.91,290.73C660.77,290.73 665.63,290.16 670.2,289.16"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M724.4,214.9C730.26,212.76 733.28,206.28 731.14,200.42C729.01,194.56 722.52,191.54 716.66,193.68C710.8,195.81 707.78,202.3 709.92,208.16C712.06,214.02 718.54,217.03 724.4,214.9Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M724.67,201.09C722.1,200.95 719.95,203.09 719.81,205.67"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M715.81,214.53C718.38,215.82 721.53,215.96 724.39,214.96C730.25,212.81 733.25,206.38 731.1,200.52C728.96,194.66 722.53,191.65 716.66,193.8C713.52,194.94 711.23,197.37 710.09,200.23"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M634.75,231.26L630.32,259.71C630.17,261.14 630.46,262.42 631.03,263.42C632.75,266.14 636.61,266.57 639.18,264.42L643.33,260.85C644.61,262.57 647.04,262.85 648.61,261.42L648.76,261.28"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M658.66,191.02C665.44,182.59 667.29,172.83 662.79,169.22C658.3,165.61 649.16,169.52 642.39,177.96C635.62,186.39 633.77,196.15 638.26,199.76C642.76,203.37 651.89,199.46 658.66,191.02Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M691.22,217.39C694.38,217.39 696.94,210.54 696.94,202.09C696.94,193.64 694.38,186.79 691.22,186.79C688.06,186.79 685.5,193.64 685.5,202.09C685.5,210.54 688.06,217.39 691.22,217.39Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M597.13,209.51C600.15,200.03 600.17,191.57 597.16,190.61C594.15,189.65 589.26,196.55 586.23,206.02C583.2,215.5 583.19,223.96 586.2,224.92C589.21,225.89 594.1,218.98 597.13,209.51Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M688.67,198.43C691.41,191.96 692.21,186.11 690.47,185.37C688.72,184.64 685.09,189.28 682.35,195.76C679.61,202.23 678.81,208.07 680.55,208.81C682.3,209.55 685.93,204.9 688.67,198.43Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M681.15,165.71C683.66,165.48 685.2,159.94 684.59,153.34C683.98,146.73 681.44,141.57 678.93,141.8C676.41,142.03 674.87,147.58 675.48,154.18C676.09,160.78 678.63,165.95 681.15,165.71Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M687.36,158.18C689.04,152.91 689.23,148.27 687.81,147.82C686.38,147.36 683.86,151.26 682.19,156.53C680.51,161.8 680.32,166.44 681.74,166.89C683.17,167.35 685.69,163.44 687.36,158.18Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M705.23,223.54C709.81,223.54 713.52,213.1 713.52,200.23C713.52,187.36 709.81,176.93 705.23,176.93C700.65,176.93 696.93,187.36 696.93,200.23C696.93,213.1 700.65,223.54 705.23,223.54Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M657.22,178.59C672.2,176.85 683.75,170.35 683.02,164.07C682.29,157.8 669.55,154.12 654.57,155.87C639.59,157.61 628.04,164.11 628.77,170.38C629.5,176.65 642.24,180.33 657.22,178.59Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M685.63,187.44C697.27,177.85 703.44,166.12 699.43,161.25C695.41,156.37 682.72,160.2 671.08,169.79C659.44,179.38 653.27,191.1 657.28,195.98C661.3,200.85 673.99,197.03 685.63,187.44Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M706.59,183.88C707.09,172.36 702.38,162.8 696.07,162.53C689.76,162.26 684.24,171.37 683.74,182.89C683.24,194.4 687.95,203.96 694.26,204.24C700.57,204.51 706.09,195.4 706.59,183.88Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M609.14,203.34C612.45,189.93 612.02,178.28 608.19,177.34C604.35,176.39 598.57,186.5 595.26,199.92C591.95,213.33 592.38,224.97 596.21,225.92C600.04,226.87 605.83,216.76 609.14,203.34Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M640.22,190.44C652.24,175.48 655.94,158.5 648.49,152.52C641.04,146.54 625.26,153.82 613.25,168.77C601.23,183.73 597.53,200.71 604.98,206.69C612.43,212.68 628.21,205.4 640.22,190.44Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M723.1,854.14L725.1,866.58"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M688.79,231.11C689.66,231.11 690.36,230.41 690.36,229.54C690.36,228.67 689.66,227.97 688.79,227.97C687.92,227.97 687.21,228.67 687.21,229.54C687.21,230.41 687.92,231.11 688.79,231.11Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M631.46,219.82C631.89,221.1 631.32,222.54 630.03,222.96C628.74,223.39 621.45,224.96 621.02,223.68C620.59,222.39 627.03,218.68 628.31,218.25C629.6,217.96 631.03,218.53 631.46,219.82Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M641.47,219.82C641.04,221.1 641.61,222.54 642.9,222.96C644.18,223.39 651.47,224.96 651.9,223.68C652.33,222.39 645.9,218.68 644.61,218.25C643.33,217.96 641.9,218.53 641.47,219.82Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M763.41,993.24C761.41,994.82 758.41,994.39 756.98,992.24"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#E5B6AA"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M684.5,695.02V731.05C684.5,735.05 682.35,738.77 678.92,740.77L674.92,743.2V692.74"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M784.14,713.18C784.14,734.62 801.58,752.07 823.03,752.07"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M734.68,871.58C763.7,886.88 798.15,886.45 826.89,870.58"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M659.48,866.72L659.77,866.86C664.77,869.87 670.49,871.72 676.21,872.01"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M856.48,647.7C854.05,652.14 849.34,654.99 844.05,654.71C840.04,654.57 836.61,652.57 834.32,649.71L817.46,650.99C812.31,651.42 807.88,647.56 807.45,642.41C807.16,638.27 809.59,634.55 813.31,632.98C808.73,632.69 805.02,629.12 804.59,624.4C804.3,620.26 806.59,616.68 810.16,615.11C806.45,614.11 803.45,610.82 803.16,606.82C802.73,601.67 806.59,597.24 811.74,596.81L829.46,595.38L826.46,594.24C826.32,594.24 826.18,594.09 826.03,594.09L804.73,594.52C799.59,594.66 795.3,590.52 795.15,585.37C795.01,580.23 799.16,575.94 804.3,575.79L827.46,575.36C829.18,575.36 830.75,575.79 832.18,576.51C832.47,576.65 832.75,576.65 833.04,576.79L836.61,578.22L856.48,647.7Z"
        android:strokeAlpha="0.5"
        android:fillColor="#161E24"
        android:fillAlpha="0.5"/>
    <path
        android:pathData="M849.05,651.85C856.48,652.28 862.77,646.56 863.06,639.27L865.49,594.95C865.92,587.52 860.2,581.23 852.91,580.94C845.47,580.51 839.18,586.23 838.9,593.52L836.47,637.84C836.04,645.27 841.61,651.56 849.05,651.85Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M822.32,648.13L845.33,646.27C850.48,645.84 854.34,641.41 853.91,636.27C853.48,631.12 849.05,627.26 843.9,627.69L820.89,629.55C815.74,629.98 811.88,634.41 812.31,639.55C812.74,644.7 817.17,648.56 822.32,648.13Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M819.46,630.12L842.47,628.26C847.62,627.83 851.48,623.4 851.05,618.26C850.62,613.11 846.19,609.25 841.04,609.68L818.03,611.53C812.88,611.96 809.02,616.4 809.45,621.54C809.73,626.69 814.31,630.55 819.46,630.12Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M818.03,612.53L841.04,610.68C846.19,610.25 850.05,605.82 849.62,600.67C849.19,595.52 844.76,591.66 839.61,592.09L816.6,593.95C811.45,594.38 807.59,598.81 808.02,603.96C808.45,609.1 812.88,612.96 818.03,612.53Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M831.32,591.38L852.91,599.81C857.77,601.67 863.2,599.24 865.06,594.52C866.92,589.66 864.49,584.23 859.77,582.37L838.18,573.93C833.32,572.08 827.89,574.51 826.03,579.22C824.17,584.09 826.46,589.52 831.32,591.38Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M809.74,591.66L832.9,591.23C838.04,591.09 842.19,586.8 842.04,581.66C841.9,576.51 837.61,572.36 832.47,572.51L809.31,572.93C804.16,573.08 800.01,577.37 800.16,582.51C800.3,587.66 804.59,591.81 809.74,591.66Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M841.76,583.23L841.33,591.09"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#969696"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M840.62,602.96L840.19,610.82"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#969696"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M839.61,621.97L839.19,629.83"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#969696"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M845.47,646.42L826.46,647.85"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M831.47,629.12L819.46,630.12"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M828.75,611.68L815.03,612.25"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M680.64,254.99C680.35,253.7 679.92,252.41 679.35,251.27C676.49,254.85 672.78,257.85 668.49,259.99C666.2,261.14 664.2,262.71 662.63,264.71L661.77,265.71V262.57C658.77,263.28 655.76,263.71 652.76,263.57C644.04,263.28 638.9,273.43 645.33,278.15C649.33,281.01 654.91,282.58 662.48,280.72C671.63,278.58 678.49,271.29 680.5,263.28L677.78,263.85L678.07,263.57C680.21,261.28 681.21,258.28 680.5,255.27C680.64,255.13 680.64,255.13 680.64,254.99Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M633.46,269.28C631.32,270.43 629.03,271.14 626.46,271.57C625.31,271.71 624.17,271.86 623.02,271.86L625.17,273.43L622.88,272.43C621.88,272 620.88,271.86 619.74,271.71C616.59,271.43 613.45,270.57 610.73,269.28C610.73,270.29 610.87,271.29 611.02,272.29C612.87,280.72 622.59,286.44 632.17,284.44C638.18,283.15 641.47,280.43 643.33,277C646.33,271.71 639.47,266.28 633.46,269.28Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M846.62,661V682.3"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M750.69,319.04C805.87,333.47 846.76,383.65 846.76,443.41V563.93"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M689.65,302.74C687.5,303.31 685.5,302.02 684.93,300.02C684.36,297.88 685.64,295.88 687.64,295.3"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M676.78,320.32L676.35,320.46C670.06,322.32 664.2,325.75 659.62,330.47"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M718.52,564.07C698.08,566.07 678.78,555.78 669.2,538.05L626.31,509.03V565.5C627.46,566.65 635.18,578.8 642.32,586.52C657.19,602.53 678.07,611.82 699.94,614.68C700.22,615.97 700.65,617.25 701.65,618.4C704.51,621.83 709.66,622.4 713.09,619.54C714.38,618.54 715.23,617.11 715.66,615.68C717.81,615.68 720.1,615.54 722.24,615.4C774.99,610.82 824.32,560.93 824.32,490.16L773.13,490.45C776.14,534.91 744.4,561.5 718.52,564.07Z"
        android:strokeAlpha="0.5"
        android:fillColor="#161E24"
        android:fillAlpha="0.5"/>
    <path
        android:pathData="M722.24,604.96C774.99,600.39 824.32,550.49 824.32,479.73L773.14,480.01C776.28,524.76 744.54,551.21 718.67,553.92C698.22,555.92 678.92,545.63 669.34,527.9L616.3,431.12L579.14,453.71L621.17,545.77C626.31,557.07 633.32,567.65 642.32,576.37C662.91,596.67 691.93,607.68 722.24,604.96Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M556.98,447.56L559.84,455.28C566.7,473.29 586.85,482.44 604.87,475.58C622.88,468.72 632.03,448.56 625.17,430.55L622.31,422.83C615.45,404.81 595.29,395.66 577.28,402.53C559.12,409.24 550.11,429.4 556.98,447.56Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M567.27,365.5L600.29,424.26L617.02,413.11L579.99,357.78C577.71,354.35 573.13,353.49 569.7,355.78C566.41,357.78 565.41,362.07 567.27,365.5Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M544.82,378.08L583.99,432.83L599.43,419.97L556.69,368.93C554.12,365.78 549.4,365.36 546.25,368.07C543.11,370.5 542.54,374.79 544.82,378.08Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M528.1,394.81L573.27,444.7L587.14,430.12L538.82,384.37C535.82,381.51 531.1,381.65 528.24,384.66C525.67,387.52 525.53,391.95 528.1,394.81Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M518.23,415.11L569.7,458.57L581.42,442.27L527.38,403.38C524.09,400.95 519.38,401.67 516.95,405.1C514.8,408.24 515.23,412.68 518.23,415.11Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M605.87,440.55L621.02,458.57L649.47,415.39C651.9,411.82 650.9,406.96 647.18,404.53C643.9,402.38 639.61,402.95 637.03,405.81L605.87,440.55Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M619.31,416.68L624.31,425.97"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M600.58,483.58C598.29,484.73 597.43,487.45 598.58,489.73C599.72,492.02 602.44,492.88 604.72,491.73"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M833.61,491.02H753.12L762.7,414.11C765.41,391.66 782.28,374.93 802.16,374.93C826.46,374.93 845.04,399.38 841.61,426.83L833.61,491.02Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M523.09,419.11L556.97,447.56"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M556.98,447.56L559.84,455.28C565.7,470.58 580.99,479.44 596.58,477.58"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M577.28,402.38C565.98,406.67 558.26,416.11 555.69,426.97"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M624.31,454.99L631.6,442.55"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M713.03,609.28C716.5,606.41 716.99,601.27 714.12,597.8C711.25,594.34 706.11,593.85 702.64,596.72C699.17,599.59 698.69,604.72 701.56,608.19C704.43,611.66 709.57,612.15 713.03,609.28Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M715.95,604.39C715.66,606.25 714.66,607.96 713.09,609.25C709.66,612.11 704.51,611.68 701.65,608.1C699.94,605.96 699.36,603.39 700.08,600.96"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M833.61,491.02H753.12L762.7,414.11C765.41,391.66 782.28,374.93 802.16,374.93C826.46,374.93 845.04,399.38 841.61,426.83L833.61,491.02Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M809.3,385.94C810.16,386.8 811.02,387.66 811.73,388.52"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M816.59,395.52C821.74,404.39 824.17,415.39 822.74,426.83L817.74,466.72"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M817.17,470.86L816.74,474.43"
        android:strokeLineJoin="round"
        android:strokeWidth="4.28886"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M626.31,589.38V639.41"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M573.13,375.79L588.71,403.67"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M624.31,531.62C626.6,530.48 629.31,531.33 630.46,533.62"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M653.33,517.04C655.62,515.9 658.34,516.75 659.48,519.04"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M636.18,513.89C635.46,511.46 636.89,508.89 639.32,508.17"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M640.04,491.59C639.32,489.16 640.75,486.59 643.18,485.87"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M620.02,500.6C618.88,502.89 616.16,503.74 613.88,502.6"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M627.03,480.58C625.89,482.87 623.17,483.73 620.88,482.58"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M617.02,468.43C615.88,470.72 613.16,471.58 610.87,470.43"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M654.33,534.62C653.19,536.91 650.47,537.77 648.19,536.62"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2.85924"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M808.88,508.03L806.02,507.46C805.3,507.32 804.59,507.75 804.44,508.46C803.73,512.32 800.3,515.04 796.44,514.89L798.87,502.17L801.44,502.6C802.16,502.74 802.87,502.31 803.02,501.6C803.16,500.88 802.73,500.17 802.01,500.03L799.44,499.6L800.44,494.31C802.16,494.02 803.73,492.73 804.16,490.88C804.59,488.59 803.02,486.3 800.73,485.87C798.44,485.44 796.15,487.02 795.72,489.3C795.44,491.16 796.29,492.88 797.87,493.88L796.87,499.17L794.29,498.74C793.58,498.6 792.86,499.03 792.72,499.74C792.58,500.45 793.01,501.17 793.72,501.31L796.29,501.74L793.86,514.47C790.29,513.18 788,509.32 788.86,505.46C789,504.74 788.58,504.03 787.86,503.89L784.57,503.31C783.86,503.17 783.14,503.6 783,504.32C782.86,505.03 783.29,505.74 784,505.89L786,506.32C785.72,511.18 788.72,515.61 793.29,517.04L793.01,518.75C792.86,519.47 793.29,520.18 794.01,520.33C794.72,520.47 795.44,520.04 795.58,519.33L795.87,517.61C800.73,517.9 805.16,514.89 806.59,510.32L808.16,510.61C808.88,510.75 809.59,510.32 809.73,509.61C809.88,508.89 809.59,508.17 808.88,508.03ZM800.16,488.59C801.01,488.73 801.59,489.59 801.44,490.45C801.3,491.31 800.44,491.88 799.58,491.73C798.73,491.59 798.15,490.73 798.3,489.88C798.44,489.02 799.3,488.45 800.16,488.59Z"
        android:fillColor="#161E24"/>
  </group>
</vector>
