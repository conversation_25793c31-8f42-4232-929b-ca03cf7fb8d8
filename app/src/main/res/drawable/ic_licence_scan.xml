<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="326dp"
    android:height="290dp"
    android:viewportWidth="326"
    android:viewportHeight="290">
  <group>
    <clip-path
        android:pathData="M0,0h326v290h-326z"/>
    <path
        android:pathData="M237.65,39.978C236.936,39.89 236.208,39.844 235.47,39.844C225.681,39.844 217.745,47.772 217.745,57.552C217.745,58.122 217.772,58.686 217.826,59.243C218.063,61.727 216.101,63.873 213.603,63.873C212.159,63.873 210.988,65.043 210.988,66.486C210.988,67.929 212.159,69.099 213.603,69.099H316.807C318.252,69.099 319.423,67.929 319.423,66.486C319.423,65.043 318.252,63.873 316.807,63.873H312.861C309.075,63.873 305.985,60.872 305.852,57.092C305.851,57.039 305.848,56.986 305.846,56.932C305.542,50.513 300.437,45.25 294.024,44.753C292.157,44.609 290.369,44.868 288.732,45.445C286.331,33.075 275.432,23.733 262.348,23.733C251.275,23.733 241.767,30.424 237.65,39.978Z"
        android:fillColor="#E3F3FF"/>
    <path
        android:pathData="M42.953,131.27C39.812,131.27 37.266,133.813 37.266,136.951C37.266,140.089 39.812,142.633 42.953,142.633H52.702C55.842,142.633 58.388,140.089 58.388,136.951C58.388,133.813 55.842,131.27 52.702,131.27H42.953Z"
        android:fillColor="#E3F3FF"/>
    <path
        android:pathData="M276.152,177.993C272.502,177.993 269.543,180.949 269.543,184.596C269.543,188.243 272.502,191.199 276.152,191.199H307.074C310.724,191.199 313.684,188.243 313.684,184.596C313.684,180.949 310.724,177.993 307.074,177.993H276.152Z"
        android:fillColor="#E3F3FF"/>
    <path
        android:pathData="M24.792,243.403C24.367,243.351 23.934,243.324 23.495,243.324C17.669,243.324 12.947,248.041 12.947,253.861C12.947,254.201 12.963,254.536 12.995,254.867C13.136,256.346 11.969,257.623 10.482,257.623C9.623,257.623 8.926,258.319 8.926,259.178C8.926,260.037 9.623,260.733 10.482,260.733H71.897C72.757,260.733 73.453,260.037 73.453,259.178C73.453,258.319 72.757,257.623 71.897,257.623H69.548C67.296,257.623 65.457,255.837 65.378,253.588C65.377,253.556 65.376,253.524 65.374,253.493C65.193,249.673 62.155,246.54 58.339,246.245C57.228,246.159 56.164,246.313 55.19,246.657C53.761,239.295 47.276,233.737 39.489,233.737C32.9,233.737 27.242,237.718 24.792,243.403Z"
        android:fillColor="#E3F3FF"/>
    <path
        android:pathData="M94.55,50.806L236.321,50.806A21.91,21.91 0,0 1,258.232 72.717L258.232,370.601A21.91,21.91 0,0 1,236.321 392.511L94.55,392.511A21.91,21.91 0,0 1,72.64 370.601L72.64,72.717A21.91,21.91 0,0 1,94.55 50.806z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M94.55,50.806L236.321,50.806A21.91,21.91 0,0 1,258.232 72.717L258.232,370.601A21.91,21.91 0,0 1,236.321 392.511L94.55,392.511A21.91,21.91 0,0 1,72.64 370.601L72.64,72.717A21.91,21.91 0,0 1,94.55 50.806z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="50.8064"
            android:startX="165.435"
            android:endY="392.511"
            android:endX="165.435"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M122.841,117.827h35.778v24.237h-35.778z"
        android:fillColor="#ECECEC"/>
    <path
        android:pathData="M90.837,98.873h70.584v10.343h-70.584z"
        android:fillColor="#ECECEC"/>
    <path
        android:pathData="M90.837,285.295h99.462v11.5h-99.462z"
        android:fillColor="#ECECEC"/>
    <path
        android:pathData="M83.968,152.451h163.127v115.414h-163.127z"
        android:fillColor="#DBE1E4"/>
    <path
        android:pathData="M105.108,130.342m-14.271,0a14.271,14.271 0,1 1,28.541 0a14.271,14.271 0,1 1,-28.541 0"
        android:fillColor="#0D9AFF"/>
    <path
        android:pathData="M103.351,137.383C103.224,137.504 103.023,137.5 102.902,137.374L97.231,131.468C97.108,131.341 97.114,131.137 97.244,131.017L99.581,128.839C99.706,128.723 99.901,128.726 100.021,128.847L103.122,131.947L110.634,124.435C110.758,124.31 110.962,124.312 111.085,124.438L113.627,127.047C113.749,127.173 113.746,127.375 113.62,127.497L103.351,137.383Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M103.351,137.383C103.224,137.504 103.023,137.5 102.902,137.374L97.231,131.468C97.108,131.341 97.114,131.137 97.244,131.017L99.581,128.839C99.706,128.723 99.901,128.726 100.021,128.847L103.122,131.947L110.634,124.435C110.758,124.31 110.962,124.312 111.085,124.438L113.627,127.047C113.749,127.173 113.746,127.375 113.62,127.497L103.351,137.383Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="124.208"
            android:startX="105.429"
            android:endY="137.603"
            android:endX="105.429"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M143.615,195.652a21.929,22.506 0,1 0,43.857 0a21.929,22.506 0,1 0,-43.857 0z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M122.841,265.478C122.841,245.08 139.376,228.545 159.773,228.545H171.314C191.712,228.545 208.247,245.08 208.247,265.478H122.841Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M122.841,265.478C122.841,245.08 139.376,228.545 159.773,228.545H171.314C191.712,228.545 208.247,245.08 208.247,265.478H122.841Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="228.545"
            android:startX="165.544"
            android:endY="265.478"
            android:endX="165.544"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M72.64,72.717C72.64,60.616 82.449,50.806 94.55,50.806H236.321C248.422,50.806 258.231,60.616 258.231,72.717V370.601C258.231,382.702 248.422,392.511 236.321,392.511H94.55C82.449,392.511 72.64,382.702 72.64,370.601V72.717ZM80,77.935C80,69.134 87.134,62 95.935,62H127.178C127.115,62.422 127.083,62.853 127.083,63.292C127.083,68.14 131.013,72.07 135.86,72.07H195.769C200.617,72.07 204.547,68.14 204.547,63.292C204.547,62.853 204.514,62.422 204.452,62H234.487C243.288,62 250.422,69.134 250.422,77.935V364.68C250.422,373.48 243.288,380.615 234.487,380.615H95.935C87.134,380.615 80,373.48 80,364.68V77.935Z"
        android:fillColor="#F5F5F5"
        android:fillType="evenOdd"/>
    <group>
      <clip-path
          android:pathData="M72.64,72.717C72.64,60.616 82.449,50.806 94.55,50.806H236.321C248.422,50.806 258.231,60.616 258.231,72.717V370.601C258.231,382.702 248.422,392.511 236.321,392.511H94.55C82.449,392.511 72.64,382.702 72.64,370.601V72.717ZM80,77.935C80,69.134 87.134,62 95.935,62H127.178C127.115,62.422 127.083,62.853 127.083,63.292C127.083,68.14 131.013,72.07 135.86,72.07H195.769C200.617,72.07 204.547,68.14 204.547,63.292C204.547,62.853 204.514,62.422 204.452,62H234.487C243.288,62 250.422,69.134 250.422,77.935V364.68C250.422,373.48 243.288,380.615 234.487,380.615H95.935C87.134,380.615 80,373.48 80,364.68V77.935Z"
          android:fillType="evenOdd"/>
      <path
          android:pathData="M127.178,62L128.163,62.145L128.331,61.004H127.178V62ZM204.452,62V61.004H203.298L203.467,62.145L204.452,62ZM94.55,49.811C81.899,49.811 71.644,60.066 71.644,72.717H73.635C73.635,61.166 82.999,51.802 94.55,51.802V49.811ZM236.321,49.811H94.55V51.802H236.321V49.811ZM259.227,72.717C259.227,60.066 248.972,49.811 236.321,49.811V51.802C247.872,51.802 257.235,61.166 257.235,72.717H259.227ZM259.227,370.601V72.717H257.235V370.601H259.227ZM236.321,393.507C248.972,393.507 259.227,383.252 259.227,370.601H257.235C257.235,382.152 247.872,391.515 236.321,391.515V393.507ZM94.55,393.507H236.321V391.515H94.55V393.507ZM71.644,370.601C71.644,383.252 81.899,393.507 94.55,393.507V391.515C82.999,391.515 73.635,382.152 73.635,370.601H71.644ZM71.644,72.717V370.601H73.635V72.717H71.644ZM95.935,61.004C86.584,61.004 79.004,68.584 79.004,77.935H80.996C80.996,69.684 87.684,62.996 95.935,62.996V61.004ZM127.178,61.004H95.935V62.996H127.178V61.004ZM128.079,63.292C128.079,62.902 128.108,62.519 128.163,62.145L126.192,61.855C126.123,62.324 126.087,62.805 126.087,63.292H128.079ZM135.86,71.074C131.563,71.074 128.079,67.59 128.079,63.292H126.087C126.087,68.69 130.463,73.066 135.86,73.066V71.074ZM195.769,71.074H135.86V73.066H195.769V71.074ZM203.551,63.292C203.551,67.59 200.067,71.074 195.769,71.074V73.066C201.167,73.066 205.542,68.69 205.542,63.292H203.551ZM203.467,62.145C203.522,62.519 203.551,62.902 203.551,63.292H205.542C205.542,62.805 205.507,62.324 205.437,61.855L203.467,62.145ZM234.487,61.004H204.452V62.996H234.487V61.004ZM251.418,77.935C251.418,68.584 243.838,61.004 234.487,61.004V62.996C242.738,62.996 249.426,69.684 249.426,77.935H251.418ZM251.418,364.68V77.935H249.426V364.68H251.418ZM234.487,381.611C243.838,381.611 251.418,374.03 251.418,364.68H249.426C249.426,372.93 242.738,379.619 234.487,379.619V381.611ZM95.935,381.611H234.487V379.619H95.935V381.611ZM79.004,364.68C79.004,374.03 86.584,381.611 95.935,381.611V379.619C87.684,379.619 80.996,372.93 80.996,364.68H79.004ZM79.004,77.935V364.68H80.996V77.935H79.004Z"
          android:fillColor="#C4C9CC"/>
    </group>
    <path
        android:pathData="M153.796,63.867m-2.006,0a2.006,2.006 0,1 1,4.012 0a2.006,2.006 0,1 1,-4.012 0"
        android:fillColor="#838C91"/>
    <path
        android:pathData="M171.128,64.335m-1.537,0a1.537,1.537 0,1 1,3.073 0a1.537,1.537 0,1 1,-3.073 0"
        android:fillColor="#838C91"/>
    <path
        android:pathData="M143.785,192.698h43.687v14.354h-43.687z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M49,286L277,286A2,2 0,0 1,279 288L279,288A2,2 0,0 1,277 290L49,290A2,2 0,0 1,47 288L47,288A2,2 0,0 1,49 286z"
        android:fillColor="#C4C9CC"/>
  </group>
</vector>
