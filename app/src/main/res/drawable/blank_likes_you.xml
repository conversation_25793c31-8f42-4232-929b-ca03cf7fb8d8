<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="220dp"
    android:height="220dp"
    android:viewportWidth="220"
    android:viewportHeight="220">
  <path
      android:pathData="M0,0h220v220h-220z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M71.078,138.041C69.992,137.923 68.888,137.861 67.766,137.861C52.897,137.861 40.844,148.584 40.844,161.812C40.844,162.583 40.885,163.346 40.966,164.099C41.327,167.46 38.347,170.362 34.553,170.362C32.359,170.362 30.58,171.945 30.58,173.896C30.58,175.848 32.359,177.43 34.553,177.43H191.309C193.503,177.43 195.281,175.848 195.281,173.896C195.281,171.945 193.503,170.362 191.309,170.362H185.314C179.564,170.362 174.871,166.303 174.669,161.19C174.667,161.118 174.663,161.046 174.66,160.974C174.198,152.291 166.445,145.173 156.703,144.501C153.868,144.305 151.152,144.656 148.665,145.437C145.019,128.704 128.465,116.07 108.591,116.07C91.772,116.07 77.331,125.119 71.078,138.041Z"
      android:strokeAlpha="0.9"
      android:fillColor="#E6E6E6"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M89.909,77.201C84.276,77.201 79.709,81.264 79.709,86.276C79.709,91.287 84.276,95.35 89.909,95.35H131.026C136.66,95.35 141.227,91.287 141.227,86.276C141.227,81.264 136.66,77.201 131.026,77.201H89.909Z"
      android:strokeAlpha="0.9"
      android:fillColor="#F4F4F4"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M150.593,122.66C146.937,122.66 143.973,125.297 143.973,128.549C143.973,131.802 146.937,134.439 150.593,134.439H181.567C185.223,134.439 188.187,131.802 188.187,128.549C188.187,125.297 185.223,122.66 181.567,122.66H150.593Z"
      android:strokeAlpha="0.9"
      android:fillColor="#F4F4F4"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M121.158,67.706C117.501,67.706 114.537,70.343 114.537,73.596C114.537,76.849 117.501,79.486 121.158,79.486H152.131C155.788,79.486 158.752,76.849 158.752,73.596C158.752,70.343 155.788,67.706 152.131,67.706H121.158Z"
      android:strokeAlpha="0.9"
      android:fillColor="#F4F4F4"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M83.07,151.077L97.216,147.847"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="0.662052"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M78.79,158.925L84.694,170.809"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="0.662052"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M79.224,152.344C78.741,152.136 78.16,152.315 77.926,152.744C77.692,153.174 77.893,153.691 78.376,153.899C78.44,153.927 78.44,153.927 78.376,153.899C77.893,153.691 77.312,153.87 77.078,154.3C76.844,154.729 77.045,155.246 77.528,155.455C77.892,155.612 78.598,155.361 78.598,155.361L80.443,154.792L79.804,153.15C79.804,153.15 79.588,152.501 79.224,152.344Z"
      android:strokeAlpha="0.9"
      android:fillColor="#ffffff"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M92.407,153.633L94.095,150.538"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.441368"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M91.297,155.668L90.498,157.134"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.441368"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M68.344,150.599C72.276,150.926 71.293,152.91 80.591,150.207C89.643,146.065 87.105,144.927 89.86,141.764L106.283,154.449L88.484,174.381L53.905,172.097L68.344,150.599Z"
      android:strokeAlpha="0.9"
      android:fillColor="#E6E6E6"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M117.166,134.304C124.066,146.575 125.721,144.611 129.803,148.832C133.886,153.053 147.789,155.017 147.789,155.017L156.993,172.619L121.116,174.844L86.073,171.321C86.073,171.321 110.266,122.034 117.166,134.304Z"
      android:strokeAlpha="0.9"
      android:fillColor="#E6E6E6"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M11.601,185.265C9.57,185.265 7.923,186.73 7.923,188.537C7.923,190.344 9.57,191.809 11.601,191.809H26.902C28.933,191.809 30.58,190.344 30.58,188.537C30.58,186.73 28.933,185.265 26.902,185.265H11.601Z"
      android:strokeAlpha="0.9"
      android:fillColor="#F4F4F4"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M182.63,44.928C182.63,45.758 181.957,46.431 181.127,46.431C180.297,46.431 179.624,45.758 179.624,44.928C179.624,44.098 180.297,43.425 181.127,43.425C181.957,43.425 182.63,44.098 182.63,44.928Z"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="0.556234"
      android:fillColor="#00000000"
      android:strokeColor="#3E3E3E"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M181.128,92.237C181.128,93.067 180.455,93.74 179.625,93.74C178.794,93.74 178.122,93.067 178.122,92.237C178.122,91.407 178.794,90.734 179.625,90.734C180.455,90.734 181.128,91.407 181.128,92.237Z"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="0.556234"
      android:fillColor="#00000000"
      android:strokeColor="#3E3E3E"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M194.749,40.681C194.749,41.231 194.304,41.675 193.755,41.675C193.206,41.675 192.761,41.231 192.761,40.681C192.761,40.132 193.206,39.687 193.755,39.687C194.304,39.687 194.749,40.132 194.749,40.681Z"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="0.556234"
      android:fillColor="#00000000"
      android:strokeColor="#3E3E3E"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M154.463,40.096H153.371V39.005C153.371,38.806 153.211,38.646 153.013,38.646C152.815,38.646 152.655,38.806 152.655,39.005V40.096H151.563C151.365,40.096 151.205,40.257 151.205,40.454C151.205,40.652 151.365,40.813 151.563,40.813H152.655V41.904C152.655,42.103 152.815,42.263 153.013,42.263C153.211,42.263 153.371,42.103 153.371,41.904V40.813H154.463C154.661,40.813 154.821,40.652 154.821,40.454C154.821,40.257 154.661,40.096 154.463,40.096Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C7C7C7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M37.557,42.536H35.394V40.371C35.394,39.979 35.076,39.661 34.683,39.661C34.29,39.661 33.973,39.979 33.973,40.371V42.536H31.809C31.416,42.536 31.099,42.854 31.099,43.246C31.099,43.638 31.416,43.956 31.809,43.956H33.973V46.12C33.973,46.513 34.29,46.831 34.683,46.831C35.076,46.831 35.394,46.513 35.394,46.12V43.956H37.557C37.95,43.956 38.268,43.638 38.268,43.246C38.268,42.854 37.95,42.536 37.557,42.536Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C7C7C7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M53.122,23.75H51.923V22.551C51.923,22.334 51.747,22.158 51.53,22.158C51.313,22.158 51.137,22.334 51.137,22.551V23.75H49.937C49.72,23.75 49.544,23.927 49.544,24.144C49.544,24.361 49.72,24.538 49.937,24.538H51.137V25.737C51.137,25.954 51.313,26.13 51.53,26.13C51.747,26.13 51.923,25.954 51.923,25.737V24.538H53.122C53.34,24.538 53.516,24.361 53.516,24.144C53.516,23.927 53.34,23.75 53.122,23.75Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C7C7C7"
      android:fillAlpha="0.9"/>
  <path
      android:strokeWidth="1"
      android:pathData="M107.705,156.169L107.975,156.568L108.383,156.314C111.633,154.291 115.629,153.608 119.531,154.437C127.441,156.119 131.639,163.503 130.059,170.936C129.35,174.271 127.229,177.234 123.866,179.52L123.866,179.521C122.37,180.539 120.318,182.072 118.037,183.775L118.006,183.798C115.729,185.499 113.218,187.375 110.778,189.101C108.326,190.836 105.96,192.411 103.986,193.502C102.999,194.048 102.123,194.464 101.394,194.721C100.645,194.983 100.124,195.05 99.809,194.983C99.493,194.915 99.03,194.636 98.425,194.078C97.837,193.534 97.171,192.78 96.448,191.86C95.002,190.021 93.367,187.568 91.719,184.935C90.072,182.304 88.421,179.506 86.942,176.979C86.678,176.527 86.419,176.084 86.166,175.651C85.009,173.671 83.989,171.925 83.21,170.667L83.21,170.666C81.067,167.21 80.333,163.639 81.042,160.305C82.611,152.923 90.323,148.229 98.284,149.921C102.186,150.75 105.559,153 107.705,156.169Z"
      android:fillColor="#ffffff"
      android:strokeColor="#4E4E4E"/>
  <path
      android:pathData="M109.111,155.675C107.134,152.447 103.981,150.196 100.308,149.415C92.824,147.824 85.405,152.778 83.772,160.46C83.032,163.943 83.676,167.616 85.627,171.109C88.983,177.126 97.369,194.55 100.698,195.258C104.027,195.965 118.776,183.459 124.29,179.327C127.493,176.929 129.573,173.836 130.314,170.353C131.946,162.671 127.185,155.128 119.7,153.537C116.027,152.756 112.23,153.53 109.111,155.675Z"
      android:fillColor="#C7C7C7"/>
  <path
      android:strokeWidth="1"
      android:pathData="M107.705,156.169L107.975,156.568L108.383,156.314C111.633,154.291 115.629,153.608 119.531,154.437C127.441,156.119 131.639,163.503 130.059,170.936C129.35,174.271 127.229,177.234 123.866,179.52L123.866,179.521C122.37,180.539 120.318,182.072 118.037,183.775L118.006,183.798C115.729,185.499 113.218,187.375 110.778,189.101C108.326,190.836 105.96,192.411 103.986,193.502C102.999,194.048 102.123,194.464 101.394,194.721C100.645,194.983 100.124,195.05 99.809,194.983C99.493,194.915 99.03,194.636 98.425,194.078C97.837,193.534 97.171,192.78 96.448,191.86C95.002,190.021 93.367,187.568 91.719,184.935C90.072,182.304 88.421,179.506 86.942,176.979C86.678,176.527 86.419,176.084 86.166,175.651C85.009,173.671 83.989,171.925 83.21,170.667L83.21,170.666C81.067,167.21 80.333,163.639 81.042,160.305C82.611,152.923 90.323,148.229 98.284,149.921C102.186,150.75 105.559,153 107.705,156.169Z"
      android:fillColor="#00000000"
      android:strokeColor="#4E4E4E"/>
  <path
      android:pathData="M119.971,159.342C119.971,159.342 121.533,160.394 122.432,161.904C123.33,163.414 123.666,164.634 123.666,164.634"
      android:strokeLineJoin="round"
      android:strokeWidth="2.03453"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M124.239,167.254L124.297,167.682L124.239,167.254Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="166.262"
          android:startX="123.484"
          android:endY="168.252"
          android:endX="123.061"
          android:type="linear">
        <item android:offset="0" android:color="#7F007755"/>
        <item android:offset="1" android:color="#7F000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M124.239,167.254L124.297,167.682"
      android:strokeLineJoin="round"
      android:strokeWidth="2.03453"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M122.779,105.9L123.055,106.056L123.222,105.787C124.552,103.641 126.715,102.07 129.265,101.42C134.433,100.102 139.072,103.242 140.31,108.098C140.866,110.277 140.478,112.649 139.157,114.984L139.157,114.984C138.57,116.023 137.805,117.529 136.954,119.203L136.943,119.225C136.093,120.896 135.157,122.739 134.219,124.472C133.276,126.215 132.337,127.837 131.489,129.059C131.065,129.67 130.669,130.172 130.312,130.537C129.947,130.911 129.658,131.103 129.452,131.155C129.245,131.208 128.89,131.178 128.369,131.024C127.863,130.875 127.248,130.624 126.552,130.291C125.16,129.626 123.477,128.654 121.733,127.578C119.99,126.504 118.196,125.332 116.584,124.271C116.295,124.08 116.012,123.894 115.737,123.712C114.474,122.879 113.361,122.145 112.533,121.629L112.532,121.628C110.255,120.211 108.778,118.314 108.223,116.136C106.994,111.313 110.182,106.284 115.383,104.958C117.932,104.308 120.584,104.653 122.779,105.9Z"
      android:strokeWidth="0.659514"
      android:fillColor="#ffffff"
      android:strokeColor="#4E4E4E"/>
  <path
      android:pathData="M123.709,105.954C121.615,104.673 119.108,104.292 116.715,104.901C111.841,106.144 108.883,111.116 110.125,115.986C110.687,118.194 112.122,120.112 114.277,121.551C117.985,124.032 127.937,131.598 130.105,131.046C132.273,130.493 137.39,119.086 139.458,115.132C140.661,112.837 141.001,110.467 140.438,108.259C139.197,103.39 134.221,100.439 129.346,101.682C126.954,102.292 124.935,103.827 123.709,105.954Z"
      android:fillColor="#C7C7C7"/>
  <path
      android:strokeWidth="1"
      android:pathData="M122.695,106.048L123.113,106.285L123.367,105.876C124.673,103.77 126.799,102.224 129.307,101.585C134.377,100.292 138.929,103.367 140.145,108.14C140.688,110.269 140.312,112.596 139.009,114.9L139.009,114.9C138.42,115.943 137.652,117.453 136.802,119.125L136.792,119.146C135.942,120.819 135.006,122.66 134.069,124.391C133.127,126.132 132.192,127.748 131.349,128.962C130.927,129.569 130.538,130.063 130.191,130.418C129.829,130.787 129.568,130.95 129.41,130.99C129.251,131.031 128.933,131.013 128.418,130.861C127.923,130.715 127.318,130.468 126.625,130.137C125.242,129.476 123.564,128.508 121.822,127.433C120.082,126.361 118.29,125.19 116.678,124.128C116.389,123.938 116.107,123.752 115.831,123.57C114.569,122.738 113.453,122.002 112.623,121.484L112.622,121.484C110.376,120.085 108.93,118.222 108.388,116.094C107.184,111.372 110.305,106.428 115.425,105.123C117.933,104.484 120.539,104.823 122.695,106.048Z"
      android:fillColor="#00000000"
      android:strokeColor="#4E4E4E"/>
  <path
      android:pathData="M130.958,104.191C130.958,104.191 132.189,104.357 133.162,104.987C134.134,105.617 134.689,106.24 134.689,106.24"
      android:strokeLineJoin="round"
      android:strokeWidth="1.3418"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M135.794,107.622L135.953,107.858L135.794,107.622Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="107.256"
          android:startX="135.057"
          android:endY="108.556"
          android:endX="135.389"
          android:type="linear">
        <item android:offset="0" android:color="#7F007755"/>
        <item android:offset="1" android:color="#7F000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M135.794,107.622L135.953,107.858"
      android:strokeLineJoin="round"
      android:strokeWidth="1.3418"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.411,61.349L99.504,61.495L99.654,61.407C100.844,60.708 102.29,60.499 103.689,60.834C106.524,61.514 107.968,64.213 107.33,66.878C107.043,68.073 106.251,69.121 105.018,69.914L105.018,69.914C104.469,70.267 103.716,70.8 102.878,71.393L102.867,71.401C102.03,71.993 101.108,72.646 100.213,73.245C99.313,73.848 98.446,74.393 97.724,74.768C97.363,74.955 97.044,75.098 96.779,75.183C96.506,75.271 96.318,75.29 96.205,75.263C96.092,75.236 95.927,75.131 95.715,74.924C95.508,74.723 95.275,74.445 95.022,74.106C94.519,73.43 93.952,72.531 93.383,71.567C92.814,70.604 92.244,69.58 91.735,68.656C91.644,68.49 91.555,68.328 91.468,68.17C91.069,67.445 90.718,66.807 90.449,66.346L90.449,66.346C89.709,65.08 89.477,63.787 89.763,62.592C90.397,59.946 93.22,58.326 96.073,59.009C97.472,59.345 98.667,60.187 99.411,61.349Z"
      android:strokeWidth="0.3605"
      android:fillColor="#ffffff"
      android:strokeColor="#4E4E4E"/>
  <path
      android:pathData="M100.15,61.679C99.496,60.534 98.426,59.722 97.161,59.419C94.584,58.801 91.966,60.472 91.326,63.145C91.036,64.356 91.223,65.645 91.865,66.883C92.969,69.014 95.704,75.174 96.85,75.449C97.997,75.723 103.227,71.472 105.177,70.072C106.31,69.26 107.061,68.196 107.351,66.984C107.992,64.312 106.416,61.636 103.838,61.019C102.574,60.715 101.251,60.955 100.15,61.679Z"
      android:fillColor="#C7C7C7"/>
  <path
      android:strokeWidth="1"
      android:pathData="M99.141,61.521L99.401,61.926L99.816,61.682C100.932,61.027 102.293,60.828 103.614,61.145C106.264,61.78 107.619,64.297 107.019,66.803C106.755,67.904 106.022,68.888 104.845,69.645L104.845,69.645C104.289,70.003 103.528,70.541 102.693,71.132L102.684,71.139C101.846,71.731 100.926,72.382 100.035,72.979C99.137,73.58 98.282,74.118 97.577,74.484C97.223,74.668 96.922,74.801 96.68,74.879C96.56,74.918 96.464,74.939 96.391,74.949C96.355,74.953 96.328,74.955 96.308,74.954C96.299,74.954 96.291,74.953 96.287,74.953C96.282,74.952 96.28,74.952 96.28,74.952C96.28,74.952 96.271,74.95 96.252,74.941C96.233,74.932 96.208,74.917 96.176,74.896C96.112,74.852 96.032,74.787 95.938,74.695C95.748,74.51 95.526,74.247 95.279,73.915C94.786,73.253 94.226,72.366 93.658,71.404C93.092,70.445 92.524,69.425 92.015,68.501C91.924,68.337 91.836,68.175 91.749,68.017C91.35,67.293 90.996,66.649 90.725,66.185L90.725,66.184C90.018,64.976 89.811,63.767 90.074,62.666C90.663,60.211 93.298,58.673 95.999,59.32C97.32,59.637 98.443,60.431 99.141,61.521Z"
      android:fillColor="#00000000"
      android:strokeColor="#4E4E4E"/>
  <path
      android:pathData="M103.802,62.606C103.802,62.606 104.355,62.999 104.665,63.552C104.975,64.104 105.084,64.547 105.084,64.547"
      android:strokeLineJoin="round"
      android:strokeWidth="0.733449"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M105.266,65.497L105.283,65.651L105.266,65.497Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="65.1321"
          android:startX="105.004"
          android:endY="65.8453"
          android:endX="104.833"
          android:type="linear">
        <item android:offset="0" android:color="#7F007755"/>
        <item android:offset="1" android:color="#7F000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M105.266,65.497L105.283,65.651"
      android:strokeLineJoin="round"
      android:strokeWidth="0.733449"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
</vector>
