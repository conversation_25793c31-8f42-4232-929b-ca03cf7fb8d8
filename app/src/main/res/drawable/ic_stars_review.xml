<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="131dp"
    android:height="24dp"
    android:viewportWidth="131"
    android:viewportHeight="24">
  <path
      android:pathData="M15.262,7.944L15.419,8.258L15.767,8.302L22.753,9.175C22.891,9.192 22.946,9.364 22.843,9.459L17.592,14.272L17.304,14.536L17.399,14.915L19.167,21.99C19.201,22.124 19.064,22.236 18.939,22.178L12.301,19.08L12.013,18.946L11.73,19.088L5.673,22.116C5.548,22.179 5.407,22.066 5.441,21.93L7.194,14.915L7.289,14.536L7.001,14.272L1.736,9.446L1.293,9.929L1.736,9.446C1.633,9.352 1.687,9.181 1.825,9.162L8.284,8.301L8.629,8.255L8.784,7.944L11.877,1.76C11.937,1.639 12.109,1.639 12.17,1.76L15.262,7.944Z"
      android:strokeWidth="1.31163">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="0"
          android:startX="12.2965"
          android:endY="23.5"
          android:endX="12.2965"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFDDE"/>
        <item android:offset="0.366232" android:color="#FFFFFEDD"/>
        <item android:offset="0.67702" android:color="#FFFBE357"/>
        <item android:offset="1" android:color="#FFC78127"/>
      </gradient>
    </aapt:attr>
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:gradientRadius="21.314"
          android:centerX="12.0233"
          android:centerY="13.1163"
          android:type="radial">
        <item android:offset="0" android:color="#2BCC8627"/>
        <item android:offset="1" android:color="#FFCC8627"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M41.855,7.944L42.012,8.258L42.361,8.302L49.346,9.175C49.484,9.192 49.539,9.364 49.436,9.459L44.185,14.272L43.897,14.536L43.992,14.915L45.76,21.99C45.794,22.124 45.657,22.236 45.532,22.178L38.894,19.08L38.606,18.946L38.323,19.088L32.266,22.116C32.141,22.179 32,22.066 32.034,21.93L33.787,14.915L33.882,14.536L33.594,14.272L28.329,9.446L27.886,9.929L28.329,9.446C28.226,9.352 28.28,9.181 28.418,9.162L34.877,8.301L35.222,8.255L35.377,7.944L38.47,1.76C38.53,1.639 38.702,1.639 38.763,1.76L41.855,7.944Z"
      android:strokeWidth="1.31163">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="0"
          android:startX="38.8895"
          android:endY="23.5"
          android:endX="38.8895"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFDDE"/>
        <item android:offset="0.366232" android:color="#FFFFFEDD"/>
        <item android:offset="0.67702" android:color="#FFFBE357"/>
        <item android:offset="1" android:color="#FFC78127"/>
      </gradient>
    </aapt:attr>
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:gradientRadius="21.314"
          android:centerX="38.6163"
          android:centerY="13.1163"
          android:type="radial">
        <item android:offset="0" android:color="#2BCC8627"/>
        <item android:offset="1" android:color="#FFCC8627"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M68.448,7.944L68.605,8.258L68.953,8.302L75.939,9.175C76.077,9.192 76.132,9.364 76.029,9.459L70.778,14.272L70.49,14.536L70.585,14.915L72.353,21.99C72.387,22.124 72.25,22.236 72.125,22.178L65.487,19.08L65.2,18.946L64.916,19.088L58.859,22.116C58.734,22.179 58.593,22.066 58.627,21.93L60.38,14.915L60.475,14.536L60.187,14.272L54.922,9.446L54.479,9.929L54.922,9.446C54.819,9.352 54.873,9.181 55.011,9.162L61.47,8.301L61.815,8.255L61.97,7.944L65.063,1.76C65.123,1.639 65.296,1.639 65.356,1.76L68.448,7.944Z"
      android:strokeWidth="1.31163">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="0"
          android:startX="65.4825"
          android:endY="23.5"
          android:endX="65.4825"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFDDE"/>
        <item android:offset="0.366232" android:color="#FFFFFEDD"/>
        <item android:offset="0.67702" android:color="#FFFBE357"/>
        <item android:offset="1" android:color="#FFC78127"/>
      </gradient>
    </aapt:attr>
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:gradientRadius="21.314"
          android:centerX="65.2093"
          android:centerY="13.1163"
          android:type="radial">
        <item android:offset="0" android:color="#2BCC8627"/>
        <item android:offset="1" android:color="#FFCC8627"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M95.041,7.944L95.198,8.258L95.547,8.302L102.532,9.175C102.67,9.192 102.725,9.364 102.622,9.459L97.371,14.272L97.083,14.536L97.178,14.915L98.946,21.99C98.98,22.124 98.843,22.236 98.718,22.178L92.08,19.08L91.793,18.946L91.509,19.088L85.452,22.116C85.327,22.179 85.186,22.066 85.22,21.93L86.973,14.915L87.068,14.536L86.78,14.272L81.515,9.446L81.072,9.929L81.515,9.446C81.412,9.352 81.466,9.181 81.604,9.162L88.063,8.301L88.408,8.255L88.563,7.944L91.656,1.76C91.716,1.639 91.888,1.639 91.949,1.76L95.041,7.944Z"
      android:strokeWidth="1.31163">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="0"
          android:startX="92.0756"
          android:endY="23.5"
          android:endX="92.0756"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFDDE"/>
        <item android:offset="0.366232" android:color="#FFFFFEDD"/>
        <item android:offset="0.67702" android:color="#FFFBE357"/>
        <item android:offset="1" android:color="#FFC78127"/>
      </gradient>
    </aapt:attr>
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:gradientRadius="21.314"
          android:centerX="91.8023"
          android:centerY="13.1163"
          android:type="radial">
        <item android:offset="0" android:color="#2BCC8627"/>
        <item android:offset="1" android:color="#FFCC8627"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M121.634,7.944L121.791,8.258L122.14,8.302L129.125,9.175C129.263,9.192 129.318,9.364 129.215,9.459L123.964,14.272L123.676,14.536L123.771,14.915L125.539,21.99C125.573,22.124 125.436,22.236 125.311,22.178L118.673,19.08L118.385,18.946L118.102,19.088L112.045,22.116C111.92,22.179 111.779,22.066 111.813,21.93L113.566,14.915L113.661,14.536L113.373,14.272L108.108,9.446L107.665,9.929L108.108,9.446C108.005,9.352 108.059,9.181 108.197,9.162L114.656,8.301L115.001,8.255L115.156,7.944L118.249,1.76C118.309,1.639 118.482,1.639 118.542,1.76L121.634,7.944Z"
      android:strokeWidth="1.31163">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="0"
          android:startX="118.669"
          android:endY="23.5"
          android:endX="118.669"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFDDE"/>
        <item android:offset="0.366232" android:color="#FFFFFEDD"/>
        <item android:offset="0.67702" android:color="#FFFBE357"/>
        <item android:offset="1" android:color="#FFC78127"/>
      </gradient>
    </aapt:attr>
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:gradientRadius="21.314"
          android:centerX="118.395"
          android:centerY="13.1163"
          android:type="radial">
        <item android:offset="0" android:color="#2BCC8627"/>
        <item android:offset="1" android:color="#FFCC8627"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
