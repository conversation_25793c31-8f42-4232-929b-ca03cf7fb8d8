<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="200dp"
    android:height="200dp"
    android:viewportWidth="200"
    android:viewportHeight="200">
  <path
      android:pathData="M0,0H200V200H0V0Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M100,197.487C150.418,197.487 191.29,156.728 191.29,106.449C191.29,56.17 150.418,15.41 100,15.41C49.582,15.41 8.71,56.17 8.71,106.449C8.71,156.728 49.582,197.487 100,197.487Z"
      android:fillColor="#E3F3FF"/>
  <path
      android:pathData="M117.644,113.503L124.799,105.219"
      android:strokeLineJoin="round"
      android:strokeWidth="0.692121"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M83.046,86.225L96.647,79.702L107.902,67.406L109.069,87.463L83.737,97.838L83.046,86.225Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M92.523,103.237L91.078,97.838L89.107,99.015C86.256,100.717 83.204,101.282 80.198,100.665L77.915,100.195L77.017,103.237C74.854,110.57 74.212,118.94 75.195,126.977L75.27,127.583L76.629,145.547C76.798,147.778 77.268,149.911 78.002,151.779C78.221,152.335 78.374,152.958 78.454,153.613L83.114,192.309H86.318L91.087,153.613C91.167,152.958 91.32,152.335 91.539,151.779C92.273,149.911 92.743,147.778 92.911,145.547L94.271,127.583L94.345,126.977C95.329,118.94 94.687,110.57 92.523,103.237Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M96.937,139.506H72.227L68.916,113.025C67.911,104.985 73.753,97.838 81.329,97.838H87.494C95.003,97.838 100.824,104.863 99.928,112.845L96.937,139.506Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M95.462,101.135C88.932,105.248 80.542,105.351 73.906,101.4L73.713,101.285L74.59,65.301C74.927,60.528 79.021,56.823 83.959,56.823H90.707C96.145,56.823 100.446,61.285 100.075,66.541L95.462,101.135Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M95.683,99.474L94.881,93.984L96.447,93.745L95.683,99.474Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M101.765,45.257C103.297,48.753 101.706,52.829 98.21,54.362C94.714,55.894 90.638,54.303 89.105,50.807C87.573,47.311 89.164,43.235 92.66,41.702C96.156,40.169 100.232,41.761 101.765,45.257Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M84.142,50.663C84.142,54.921 80.69,58.373 76.432,58.373C72.173,58.373 68.721,54.921 68.721,50.663C68.721,46.405 72.173,42.953 76.432,42.953C80.69,42.953 84.142,46.405 84.142,50.663Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M90.449,58.775H83.737V49.261H90.449V58.775Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M90.449,52.563L83.737,54.423V49.261H90.449V52.563Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M99.942,44.104C99.942,44.851 99.337,45.457 98.59,45.457C97.843,45.457 97.238,44.851 97.238,44.104C97.238,43.357 97.843,42.752 98.59,42.752C99.337,42.752 99.942,43.357 99.942,44.104Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.59,41.781C98.59,47.286 94.128,51.749 88.623,51.749C83.118,51.749 78.655,47.286 78.655,41.781C78.655,36.276 83.118,31.813 88.623,31.813C94.128,31.813 98.59,36.276 98.59,41.781Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M80.045,46.829C79.162,45.341 78.655,43.603 78.655,41.748C78.655,36.243 83.117,31.78 88.622,31.78C94.127,31.78 98.59,36.243 98.59,41.748C98.59,42.34 98.538,42.921 98.439,43.486"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M86.078,51.387C84.159,50.882 82.467,49.819 81.185,48.383"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.247,44.349C97.103,48.592 93.228,51.715 88.623,51.715"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M89.271,42.885C89.271,43.243 88.981,43.534 88.623,43.534C88.264,43.534 87.974,43.243 87.974,42.885"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M93.06,42.903C93.06,43.262 92.769,43.552 92.411,43.552C92.052,43.552 91.762,43.262 91.762,42.903"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M90.881,40.054L91.762,40.349"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M88.83,40.348L89.712,40.056"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M86.989,48.558C85.617,48.558 84.506,47.447 84.506,46.075"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M90.264,44.349C90.587,44.349 90.848,44.611 90.848,44.934C90.848,45.256 90.587,45.518 90.264,45.518"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M83.631,45.271C83.631,44.696 84.097,44.23 84.672,44.23"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M83.835,58.696V52.563"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M73.117,55.305C70.787,55.305 68.847,53.471 68.741,51.12C68.406,43.764 72.49,36.975 79.145,33.824C79.194,33.801 79.243,33.777 79.29,33.752C80.576,33.089 81.452,31.726 81.579,30.194L81.643,29.416C81.843,27.003 83.962,25.209 86.374,25.408C88.788,25.608 90.582,27.726 90.382,30.139L90.318,30.917C89.945,35.436 87.259,39.509 83.31,41.546C83.174,41.616 83.036,41.684 82.896,41.75C79.445,43.384 77.327,46.906 77.5,50.721C77.61,53.14 75.739,55.19 73.32,55.3C73.252,55.303 73.184,55.305 73.117,55.305Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M80.628,44.104C80.628,45.119 79.806,45.941 78.791,45.941C77.777,45.941 76.955,45.119 76.955,44.104C76.955,43.09 77.777,42.268 78.791,42.268C79.806,42.268 80.628,43.09 80.628,44.104Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M80.196,45.287C79.86,45.687 79.355,45.941 78.791,45.941C77.777,45.941 76.955,45.119 76.955,44.104C76.955,43.09 77.777,42.268 78.791,42.268C79.616,42.268 80.314,42.812 80.546,43.56"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M83.333,34.212C83.333,36.296 81.644,37.985 79.56,37.985C77.476,37.985 75.786,36.296 75.786,34.212C75.786,32.128 77.476,30.439 79.56,30.439C81.644,30.439 83.333,32.128 83.333,34.212Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M79.56,30.086C79.56,28.804 80.599,27.765 81.881,27.765C83.162,27.765 84.201,28.804 84.201,30.086C84.201,31.368 83.162,32.407 81.881,32.407C80.599,32.407 79.56,31.368 79.56,30.086Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M99.623,43.503C99.179,43.503 98.729,43.418 98.293,43.239L96.414,42.465C92.456,40.836 89.725,37.023 89.457,32.752L89.377,31.482C89.256,29.554 90.72,27.893 92.648,27.772C94.575,27.651 96.236,29.115 96.357,31.043L96.437,32.313C96.539,33.934 97.575,35.38 99.076,35.998L100.955,36.771C102.741,37.506 103.593,39.55 102.858,41.336C102.302,42.686 100.998,43.503 99.623,43.503Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M98.833,34.212C98.833,35.745 97.59,36.988 96.057,36.988C94.523,36.988 93.281,35.745 93.281,34.212C93.281,32.679 94.523,31.436 96.057,31.436C97.59,31.436 98.833,32.679 98.833,34.212Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M97.789,32.234C97.789,33.534 96.735,34.588 95.435,34.588C94.135,34.588 93.081,33.534 93.081,32.234C93.081,30.934 94.135,29.88 95.435,29.88C96.735,29.88 97.789,30.934 97.789,32.234Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M90.072,42.93C90.072,43.954 89.241,44.784 88.217,44.784C87.193,44.784 86.362,43.954 86.362,42.93C86.362,41.905 87.193,41.075 88.217,41.075C89.241,41.075 90.072,41.905 90.072,42.93Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M94.265,42.93C94.265,43.954 93.435,44.784 92.411,44.784C91.386,44.784 90.556,43.954 90.556,42.93C90.556,41.905 91.386,41.075 92.411,41.075C93.435,41.075 94.265,41.905 94.265,42.93Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M90.087,42.828H90.535"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M90.529,149.129C90.529,150.246 89.676,151.151 88.624,151.151"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M80.823,151.15C79.771,151.15 78.919,150.245 78.919,149.129"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M100.171,196.801H85.075V192.308H96.306C98.44,192.308 100.171,194.039 100.171,196.174V196.801Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M69.37,196.801H85.075V192.308H73.235C71.1,192.308 69.37,194.039 69.37,196.174V196.801Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M88.758,190.258C88.758,190.785 87.805,192.308 87.805,192.308C87.805,192.308 86.852,190.785 86.852,190.258C86.852,189.732 87.278,189.305 87.805,189.305C88.331,189.305 88.758,189.732 88.758,190.258Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M89.855,193.262C89.329,193.262 87.805,192.308 87.805,192.308C87.805,192.308 89.329,191.355 89.855,191.355C90.382,191.355 90.808,191.782 90.808,192.308C90.808,192.835 90.382,193.262 89.855,193.262Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M81.596,190.258C81.596,190.785 82.549,192.308 82.549,192.308C82.549,192.308 83.502,190.785 83.502,190.258C83.502,189.732 83.076,189.305 82.549,189.305C82.022,189.305 81.596,189.732 81.596,190.258Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M80.499,193.262C81.025,193.262 82.549,192.308 82.549,192.308C82.549,192.308 81.025,191.355 80.499,191.355C79.972,191.355 79.546,191.782 79.546,192.308C79.546,192.835 79.972,193.262 80.499,193.262Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M100.171,196.174V196.801H85.075V192.308"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M93.728,192.308H96.305C97.862,192.308 99.203,193.228 99.816,194.554"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M78.99,192.308H73.235C71.1,192.308 69.37,194.039 69.37,196.174V196.801H81.047"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M83.114,196.801H85.075V192.308H83.114"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M86.537,192.308H85.075"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M88.209,196.801H85.075V192.308H85.597L88.209,196.801Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M84.77,192.308V183.626L85.597,192.308H84.77Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M90.707,56.823H83.959C82.651,56.823 81.403,57.085 80.268,57.556C80.895,60.993 83.903,63.599 87.521,63.599C91.086,63.599 94.06,61.07 94.747,57.708C93.525,57.143 92.157,56.823 90.707,56.823Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M90.762,57.88H88.557"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M86.991,57.88H85.469"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M87.217,58.396C87.217,58.656 87.427,58.867 87.687,58.867C87.947,58.867 88.157,58.656 88.157,58.396"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M93.368,139.506H76.172L76.437,143.016L93.285,140.601L93.368,139.506Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M104.963,86.224L105.619,86.421"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M72.213,41.189C72.213,42 71.555,42.658 70.743,42.658C69.932,42.658 69.274,42 69.274,41.189C69.274,40.378 69.932,39.72 70.743,39.72"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M101.042,51.966C101.58,52.573 101.525,53.502 100.918,54.04C100.311,54.578 99.382,54.523 98.844,53.916C98.305,53.309 98.361,52.38 98.968,51.842"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.337,52.798C98.875,53.405 98.82,54.334 98.213,54.872C97.606,55.411 96.677,55.355 96.139,54.748C95.6,54.141 95.656,53.213 96.263,52.674"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M71.87,45.271C71.87,46.083 71.212,46.74 70.4,46.74C69.589,46.74 68.931,46.083 68.931,45.271C68.931,44.46 69.589,43.802 70.4,43.802"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M93.231,28.617C93.231,29.428 92.574,30.086 91.762,30.086C90.951,30.086 90.293,29.428 90.293,28.617C90.293,27.805 90.951,27.147 91.762,27.147"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M87.282,26.832C86.73,27.573 86.883,28.62 87.623,29.172C88.364,29.724 89.411,29.571 89.963,28.831C90.515,28.091 90.362,27.043 89.622,26.491"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M71.273,52.938C71.273,54.042 70.378,54.936 69.274,54.936C68.17,54.936 67.275,54.042 67.275,52.938C67.275,51.834 68.17,50.939 69.274,50.939"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M84.77,176.638V139.506"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M95.117,94.198C93.269,94.341 91.818,92.643 92.249,90.84L97.262,69.855L101.267,71.103L98.898,90.538C98.494,92.411 97.002,93.856 95.117,94.198Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M98.898,90.539C98.649,91.692 97.988,92.683 97.076,93.354C96.507,93.772 95.841,94.067 95.117,94.198C93.269,94.341 91.818,92.643 92.249,90.84"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M91.026,78.284L80.378,77.551C79.363,77.481 78.536,76.712 78.392,75.706L78.304,75.094"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M93.197,78.433L92.177,78.363"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M78.342,58.63C79.912,57.493 81.859,56.823 83.959,56.823H84.415"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.848,61.385C99.744,62.892 100.207,64.666 100.074,66.541"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M89.858,56.823H90.706C93.565,56.823 96.109,58.056 97.826,59.988"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M90.301,56.672V51.574"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M72.785,55.529C71.307,54.419 70.352,52.653 70.352,50.663C70.352,49.921 70.484,49.21 70.728,48.553"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M78.996,56.178C78.217,56.541 77.348,56.743 76.432,56.743"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M96.871,41.34C95.579,40.808 94.437,40.009 93.502,39.022"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M78.336,43.446C78.912,43.446 79.398,43.825 79.56,44.347"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M86.077,42.436H82.073"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M94.675,42.629H96.246"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M92.172,98.802H95.535"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M94.633,99.904H95.535"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M75.798,87.51L73.712,85.355"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M75.042,87.876L73.713,87.05"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M91.681,62.314C93.232,61.253 94.357,59.614 94.747,57.708"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M81.157,59.95C82.437,62.133 84.807,63.599 87.521,63.599"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M57.72,197.047H117.342"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M120.432,197.047H136.684"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M140.601,197.047H145.543"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M134.931,195.124L135.632,196.974L136.133,195.701"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M63.147,87.201C64.377,87.943 65.849,87.801 66.508,86.879L77.171,71.952L81.63,65.71C82.616,64.329 81.895,62.226 80.056,61.117C78.294,60.054 76.185,60.32 75.347,61.71L61.928,83.964C61.317,84.977 61.863,86.427 63.147,87.201Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M76.802,101.754L73.578,102.12C73.488,102.13 73.399,102.131 73.312,102.123C72.705,102.069 72.199,101.594 72.128,100.965C72.046,100.246 72.563,99.597 73.282,99.515L76.507,99.15C77.226,99.068 77.875,99.585 77.956,100.304C78.038,101.023 77.521,101.672 76.802,101.754Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M76.721,99.201L73.497,99.567C72.778,99.648 72.129,99.132 72.047,98.412C71.966,97.693 72.483,97.044 73.202,96.963L76.426,96.597C77.145,96.515 77.794,97.032 77.876,97.751C77.957,98.47 77.441,99.119 76.721,99.201Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M76.442,96.74L73.218,97.105C72.499,97.187 71.85,96.67 71.768,95.951C71.687,95.232 72.204,94.583 72.923,94.501L76.147,94.135C76.866,94.054 77.515,94.571 77.597,95.29V95.29C77.678,96.009 77.161,96.658 76.442,96.74Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M74.055,94.177L71.307,95.902C70.694,96.287 69.885,96.103 69.5,95.49C69.115,94.877 69.3,94.068 69.913,93.683L72.661,91.957C73.274,91.572 74.083,91.757 74.468,92.37C74.853,92.983 74.668,93.792 74.055,94.177Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M77.037,93.637L73.839,94.188C73.126,94.311 72.448,93.832 72.325,93.119C72.202,92.406 72.681,91.728 73.394,91.605L76.592,91.054C77.305,90.931 77.983,91.41 78.106,92.123C78.229,92.836 77.75,93.514 77.037,93.637Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M73.233,102.975C72.229,103.226 71.213,102.616 70.963,101.613L69.457,95.587C69.206,94.584 69.816,93.567 70.819,93.317C71.822,93.066 72.839,93.676 73.089,94.679L74.595,100.705C74.846,101.708 74.236,102.725 73.233,102.975Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M73.218,94.468L76.147,94.135C76.867,94.054 77.515,94.571 77.597,95.29C77.679,96.009 77.162,96.658 76.442,96.74L73.218,97.105"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M77.876,97.751C77.957,98.47 77.44,99.119 76.721,99.201L73.712,99.467"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M63.24,70.198L61.789,64.788L58.364,64.615L59.049,70.097L63.24,70.198Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M62.287,66.646L61.789,64.788L58.364,64.615L58.794,68.05L62.287,66.646Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M64.629,87.935C66.435,87.683 67.696,86.015 67.445,84.208L63.328,69.227L58.532,68.095L60.902,85.119C61.153,86.925 62.822,88.186 64.629,87.935Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M64.466,73.367L66.367,80.287"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M57.198,63.721L57.358,58.878C57.375,58.367 57.804,57.966 58.316,57.983C58.827,58 59.228,58.428 59.211,58.94L59.051,63.782C59.034,64.294 58.605,64.695 58.094,64.678C57.582,64.661 57.181,64.233 57.198,63.721Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M57.198,63.721L57.358,58.878C57.375,58.367 57.804,57.966 58.316,57.983C58.827,58 59.228,58.428 59.211,58.94L59.051,63.782C59.034,64.294 58.605,64.695 58.094,64.678C57.582,64.661 57.181,64.233 57.198,63.721Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M59.029,61.121L60.902,59.763C62.247,58.788 63.202,57.362 63.593,55.748C63.749,55.102 64.398,54.706 65.044,54.861C65.689,55.018 66.086,55.667 65.93,56.313C65.405,58.482 64.12,60.399 62.313,61.709L60.44,63.067C60.227,63.222 59.98,63.296 59.735,63.296C59.363,63.296 58.996,63.124 58.761,62.799C58.372,62.262 58.491,61.51 59.029,61.121Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M59.029,61.121L60.902,59.763C62.247,58.788 63.202,57.362 63.593,55.748C63.749,55.102 64.398,54.706 65.044,54.861C65.689,55.018 66.086,55.667 65.929,56.313C65.405,58.482 64.12,60.399 62.313,61.709L60.44,63.067C60.227,63.222 59.98,63.296 59.735,63.296"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M67.372,60.295L62.005,62.9C61.38,63.203 60.627,62.943 60.324,62.318C60.021,61.693 60.282,60.94 60.906,60.637L66.273,58.032C66.899,57.728 67.651,57.989 67.954,58.614C68.258,59.239 67.997,59.992 67.372,60.295Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M60.907,60.637L66.274,58.032C66.899,57.728 67.651,57.989 67.955,58.614"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M67.638,62.738L61.789,63.912C61.108,64.048 60.445,63.607 60.308,62.926C60.171,62.245 60.613,61.582 61.294,61.445L67.143,60.272C67.824,60.135 68.487,60.576 68.624,61.257C68.76,61.939 68.319,62.602 67.638,62.738Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M61.84,65.5L60.476,66.162C59.358,66.704 58.012,66.238 57.47,65.12C56.927,64.002 57.393,62.656 58.511,62.113L59.875,61.451C60.993,60.909 62.339,61.375 62.882,62.493C63.425,63.611 62.958,64.957 61.84,65.5Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M68.623,61.258C68.76,61.939 68.319,62.602 67.637,62.738L61.788,63.912"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M66.175,60.466L61.677,61.471"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M60.476,66.162C59.358,66.704 58.012,66.238 57.47,65.12C57.047,64.251 57.236,63.243 57.866,62.583"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M62.84,86.617C62.991,87.243 63.62,87.628 64.246,87.477C64.871,87.327 65.256,86.698 65.106,86.072"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M63.926,70.198L59.8,69.079"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M74.393,79.702V73.367"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M71.635,79.702L74.147,74.309L74.36,75.997L71.635,79.702Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M71.352,137.871H93.493"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M93.02,105.046L92.523,103.236"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M94.116,104.141L93.464,102.897"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M77.01,105.754L78.304,104.141"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M69.457,95.587L70.963,101.613C71.213,102.616 72.23,103.226 73.233,102.975C73.741,102.848 74.148,102.525 74.394,102.106L76.317,101.809"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M124.517,192.308H117.456L102.943,112.126H127.828V116.703C127.828,119.672 126.605,122.473 124.517,124.289V192.308Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M124.517,165.52V192.309H117.456L105.949,128.738"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M104.738,122.046L102.943,112.127H127.828V116.703C127.828,119.672 126.605,122.473 124.517,124.29V162.181"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.888,117.346C111.942,118.747 120.245,117.838 127.828,114.703V112.126H102.943L103.888,117.346Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M100.546,113.79C109.346,116.865 118.928,116.865 127.728,113.79V72.584C127.728,69.493 126.704,66.642 124.978,64.349C122.478,61.03 118.504,58.885 114.029,58.885H109.654C103.669,70.529 100.546,83.433 100.546,96.525V113.79Z"
      android:strokeWidth="0.150264"
      android:fillColor="#9D9D9D"
      android:strokeColor="#161E24"/>
  <path
      android:pathData="M127.828,95.312V113.861C118.966,116.975 109.308,116.975 100.446,113.861V109.654"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M126.624,66.938C127.397,68.661 127.828,70.572 127.828,72.584V79.167V90.539"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M100.446,105.046V96.525C100.446,83.396 103.582,70.457 109.593,58.785H114.029C117.229,58.785 120.176,59.874 122.517,61.703"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M122.359,113.475C115.087,114.952 107.513,114.449 100.446,111.966"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M127.828,111.966C126.529,112.422 125.213,112.812 123.885,113.135"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M109.947,68.978C110.201,68.203 110.466,67.432 110.743,66.664"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M106.551,83.938C107.152,79.34 108.141,74.802 109.504,70.374"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M105.855,111.966V97.478"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.292,66.531C111.783,66.531 109.748,64.496 109.748,61.986V49.252H118.836V61.986C118.836,64.496 116.802,66.531 114.292,66.531Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M118.836,55.312L109.748,60.584V50.959H118.836V55.312Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M104.874,43.248C104.874,44.05 104.223,44.701 103.42,44.701C102.618,44.701 101.967,44.05 101.967,43.248C101.967,42.445 102.618,41.794 103.42,41.794C104.223,41.794 104.874,42.445 104.874,43.248Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M103.053,40.741C103.053,39.838 103.785,39.106 104.688,39.106C105.591,39.106 106.323,39.838 106.323,40.741C106.323,41.644 105.591,42.376 104.688,42.376C103.785,42.376 103.053,41.644 103.053,40.741Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M104.146,46.699C104.146,47.803 103.252,48.697 102.148,48.697C101.044,48.697 100.15,47.803 100.15,46.699C100.15,45.596 101.044,44.701 102.148,44.701C103.252,44.701 104.146,45.596 104.146,46.699Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M104.146,46.699C104.146,47.803 103.252,48.697 102.148,48.697C101.044,48.697 100.15,47.803 100.15,46.699C100.15,45.596 101.044,44.701 102.148,44.701C103.252,44.701 104.146,45.596 104.146,46.699Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M122.027,47.926C121.45,53.4 116.546,57.37 111.072,56.794C105.598,56.217 101.627,51.312 102.204,45.838C102.78,40.364 107.685,36.394 113.159,36.971C118.633,37.547 122.604,42.452 122.027,47.926Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M109.246,44.826C109.246,45.399 108.781,45.863 108.208,45.863C107.636,45.863 107.171,45.399 107.171,44.826"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M113.584,62.742C113.011,62.742 112.547,62.277 112.547,61.704C112.547,61.131 113.011,60.667 113.584,60.667"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M112.131,44.826C112.131,45.399 111.667,45.863 111.094,45.863C110.521,45.863 110.057,45.399 110.057,44.826"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M110.155,42.606C110.825,42.239 111.666,42.485 112.033,43.155"
      android:strokeLineJoin="round"
      android:strokeWidth="0.751321"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M107.35,42.934C107.565,42.2 108.333,41.779 109.067,41.993"
      android:strokeLineJoin="round"
      android:strokeWidth="0.751321"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.204,45.838C102.78,40.364 107.685,36.394 113.159,36.971C118.633,37.547 122.603,42.452 122.027,47.926C121.679,51.232 119.751,53.99 117.073,55.529"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.029,56.666C113.078,56.851 112.085,56.901 111.072,56.794C106.088,56.269 102.351,52.157 102.156,47.294"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M124.519,46.7C124.519,47.81 123.619,48.71 122.508,48.71C121.399,48.71 120.499,47.81 120.499,46.7C120.499,45.589 121.399,44.689 122.508,44.689C123.619,44.689 124.519,45.589 124.519,46.7Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M121.986,47.282C121.986,46.704 122.455,46.236 123.032,46.236"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M120.499,46.7C120.499,45.589 121.399,44.689 122.509,44.689C123.619,44.689 124.519,45.589 124.519,46.7C124.519,47.81 123.619,48.71 122.509,48.71C122.1,48.71 121.721,48.588 121.404,48.379"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.292,40.121C114.292,39.218 115.025,38.486 115.928,38.486C116.831,38.486 117.563,39.218 117.563,40.121C117.563,41.024 116.831,41.756 115.928,41.756C115.025,41.756 114.292,41.024 114.292,40.121Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M118.014,42.376C118.014,41.473 118.746,40.741 119.649,40.741C120.552,40.741 121.285,41.473 121.285,42.376C121.285,43.279 120.552,44.011 119.649,44.011C118.746,44.011 118.014,43.279 118.014,42.376Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M116.725,40.37C116.725,39.732 117.242,39.214 117.881,39.214C118.519,39.214 119.037,39.732 119.037,40.37C119.037,41.009 118.519,41.526 117.881,41.526C117.242,41.526 116.725,41.009 116.725,40.37Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M121.059,40.953C119.558,38.121 116.72,36.063 113.293,35.702C109.911,35.346 106.749,36.725 104.688,39.121L121.059,40.953Z"
      android:fillColor="#9D9D9D"/>
  <path
      android:pathData="M104.769,38.645L104.688,39.106L121.059,41.526L121.156,40.786C121.215,40.332 120.9,39.914 120.447,39.846L106.069,37.703C105.453,37.614 104.877,38.031 104.769,38.645Z"
      android:fillColor="#9D9D9D"/>
  <path
      android:pathData="M109.246,39.691C109.246,40.633 108.483,41.396 107.541,41.396C106.6,41.396 105.837,40.633 105.837,39.691C105.837,38.75 106.6,37.987 107.541,37.987C108.483,37.987 109.246,38.75 109.246,39.691Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M108.914,40.029C108.914,39.519 109.327,39.106 109.837,39.106C110.348,39.106 110.761,39.519 110.761,40.029C110.761,40.539 110.348,40.953 109.837,40.953C109.327,40.953 108.914,40.539 108.914,40.029Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M108.729,38.099L120.02,39.375L120.447,39.846L108.729,38.099Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M109.526,46.7V48.595C109.526,49.047 109.892,49.413 110.344,49.413H111.897"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M122.438,43.706C122.438,44.523 121.775,45.186 120.957,45.186C120.14,45.186 119.477,44.523 119.477,43.706C119.477,42.888 120.14,42.226 120.957,42.226C121.775,42.226 122.438,42.888 122.438,43.706Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M120.958,45.743C120.958,46.273 120.528,46.703 119.997,46.703C119.467,46.703 119.037,46.273 119.037,45.743C119.037,45.213 119.467,44.783 119.997,44.783C120.528,44.783 120.958,45.213 120.958,45.743Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M115.769,56.176H109.66C108.56,56.176 107.669,55.285 107.669,54.185C107.669,52.535 109.007,51.197 110.658,51.197H114.771C116.421,51.197 117.76,52.535 117.76,54.185C117.76,55.285 116.868,56.176 115.769,56.176Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M112.479,56.16C112.479,57.57 111.335,58.714 109.924,58.714C108.513,58.714 107.37,57.57 107.37,56.16C107.37,54.749 108.513,53.605 109.924,53.605C111.335,53.605 112.479,54.749 112.479,56.16Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M110.899,57.896C110.899,56.7 111.869,55.73 113.065,55.73C114.261,55.73 115.231,56.7 115.231,57.896C115.231,59.092 114.261,60.062 113.065,60.062C111.869,60.062 110.899,59.092 110.899,57.896Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M113.585,56.16C113.585,54.903 114.604,53.884 115.861,53.884C117.118,53.884 118.137,54.903 118.137,56.16C118.137,57.417 117.118,58.435 115.861,58.435C114.604,58.435 113.585,57.417 113.585,56.16Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M113.162,53.411C114.095,53.411 114.92,52.95 115.422,52.244"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M118.476,182.284L113.779,139.506"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M119.577,192.308L118.884,185.998"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.137,127.001L109.659,124.146"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M122.265,197.047H104.517C103.522,194.718 104.963,192.072 107.459,191.644L115.871,190.201C116.814,191.633 118.416,192.493 120.13,192.486L120.898,192.483L122.265,197.047Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M120.13,192.486L120.898,192.483L122.265,197.047H104.517C103.754,195.261 104.424,193.288 105.904,192.272"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M110.731,190.201L111.451,193.087"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M112.41,189.815L113.131,192.701"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M125.821,197.047H108.073C107.078,194.718 108.52,192.072 111.015,191.644L119.427,190.201C120.37,191.633 121.972,192.493 123.686,192.486L124.454,192.483L125.821,197.047Z"
      android:fillColor="#BEE3F7"/>
  <path
      android:pathData="M123.687,192.486L124.454,192.483L125.821,197.047H108.073C107.31,195.261 107.98,193.288 109.46,192.272"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.287,190.201L115.008,193.087"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M115.966,189.815L116.687,192.701"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M122.359,192.315C121.17,191.988 120.12,191.223 119.427,190.138L111.015,191.624"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M120.979,126.65L120.994,127.351"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M121.023,128.724L122.277,188.427"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M122.292,189.114L122.307,189.815"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M117.514,161.411C116.685,161.411 116.012,160.739 116.012,159.909"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M112.924,161.411C112.094,161.411 111.422,160.739 111.422,159.909"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M109.748,61.986V53.605"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M118.836,54.341V61.986C118.836,64.496 116.802,66.531 114.292,66.531C113.045,66.531 111.915,66.028 111.094,65.214"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M123.432,124.244C120.316,124.244 117.79,121.718 117.79,118.602"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M112.298,125.791L113.433,126.65L113.297,135.121L112.298,126.016"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M120.055,120.389V119.654H123.208V122.944"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M135.741,73.404H134.934V72.598C134.934,72.451 134.816,72.333 134.67,72.333C134.524,72.333 134.405,72.451 134.405,72.598V73.404H133.599C133.452,73.404 133.334,73.523 133.334,73.669C133.334,73.815 133.452,73.934 133.599,73.934H134.405V74.74C134.405,74.886 134.524,75.005 134.67,75.005C134.816,75.005 134.934,74.886 134.934,74.74V73.934H135.741C135.887,73.934 136.006,73.815 136.006,73.669C136.006,73.523 135.887,73.404 135.741,73.404Z"
      android:fillColor="#529ED6"/>
  <path
      android:pathData="M49.997,53.129C49.997,53.537 49.666,53.867 49.258,53.867C48.85,53.867 48.52,53.537 48.52,53.129C48.52,52.72 48.85,52.39 49.258,52.39C49.666,52.39 49.997,52.72 49.997,53.129Z"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#E4F4FC"/>
  <path
      android:pathData="M52.587,38.752C52.587,39.216 52.211,39.592 51.747,39.592C51.282,39.592 50.906,39.216 50.906,38.752C50.906,38.287 51.282,37.911 51.747,37.911C52.211,37.911 52.587,38.287 52.587,38.752Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M152.748,42.687C152.748,43.152 152.372,43.528 151.908,43.528C151.443,43.528 151.067,43.152 151.067,42.687C151.067,42.223 151.443,41.847 151.908,41.847C152.372,41.847 152.748,42.223 152.748,42.687Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M59.364,36.377C59.364,36.684 59.116,36.933 58.808,36.933C58.501,36.933 58.252,36.684 58.252,36.377C58.252,36.069 58.501,35.821 58.808,35.821C59.116,35.821 59.364,36.069 59.364,36.377Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M146.627,56.896C146.627,57.203 146.378,57.452 146.071,57.452C145.764,57.452 145.515,57.203 145.515,56.896C145.515,56.589 145.764,56.34 146.071,56.34C146.378,56.34 146.627,56.589 146.627,56.896Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M49.058,45.911H48.447V45.301C48.447,45.19 48.358,45.1 48.247,45.1C48.136,45.1 48.047,45.19 48.047,45.301V45.911H47.436C47.326,45.911 47.236,46.001 47.236,46.112C47.236,46.222 47.326,46.312 47.436,46.312H48.047V46.923C48.047,47.033 48.136,47.123 48.247,47.123C48.358,47.123 48.447,47.033 48.447,46.923V46.312H49.058C49.169,46.312 49.258,46.222 49.258,46.112C49.258,46.001 49.169,45.911 49.058,45.911Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M129.65,25.193H129.039V24.583C129.039,24.472 128.95,24.382 128.839,24.382C128.728,24.382 128.639,24.472 128.639,24.583V25.193H128.028C127.918,25.193 127.828,25.283 127.828,25.393C127.828,25.504 127.918,25.594 128.028,25.594H128.639V26.204C128.639,26.315 128.728,26.404 128.839,26.404C128.95,26.404 129.039,26.315 129.039,26.204V25.594H129.65C129.761,25.594 129.85,25.504 129.85,25.393C129.85,25.283 129.761,25.193 129.65,25.193Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M122.449,72.137C123.216,71.494 124.101,70.985 125.068,70.648"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M121.118,94.815L119.479,79.296C119.302,77.276 119.855,75.373 120.91,73.834"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M127.573,100.613C126.965,102.38 125.04,103.32 123.273,102.712L99.943,92.313L100.507,87.727L122.564,95.312"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M101.373,67.163C100.869,67.854 100.194,68.519 99.407,69.09C96.915,70.897 94.242,71.165 93.189,69.714C92.326,68.524 92.871,66.658 94.575,64.962C94.924,64.614 95.49,64.615 95.838,64.965C96.185,65.314 96.183,65.88 95.835,66.227C94.724,67.332 94.42,68.37 94.634,68.665C94.916,69.053 96.544,68.961 98.358,67.644C99.21,67.026 99.911,66.263 100.282,65.55C100.577,64.984 100.596,64.575 100.484,64.421C100.264,64.118 99.052,64.055 97.419,65.011C96.994,65.26 96.447,65.117 96.198,64.692C95.949,64.266 96.092,63.719 96.517,63.47C98.819,62.123 100.994,62.083 101.929,63.373C102.492,64.148 102.469,65.214 101.866,66.374C101.729,66.638 101.563,66.903 101.373,67.163Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M103.511,70.419L101.771,71.762L101.15,72.585C100.458,73.501 99.267,73.886 98.171,73.548C97.314,73.283 96.641,72.617 96.368,71.763L95.947,70.447L101.72,64.342L103.511,70.419Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M127.573,100.613L127.828,99.169"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M125.46,99.948C125.922,100.409 125.922,101.158 125.46,101.619C124.999,102.081 124.25,102.081 123.789,101.619"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M118.405,65.817C117.449,67.037 115.962,67.821 114.292,67.821C111.408,67.821 109.069,65.483 109.069,62.598"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M119.516,61.085V62.598C119.516,63.319 119.369,64.006 119.105,64.631"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.085,67.775H100.806C100.294,67.775 99.879,67.36 99.879,66.848C99.879,66.337 100.294,65.922 100.806,65.922H103.085C103.597,65.922 104.012,66.337 104.012,66.848C104.012,67.36 103.597,67.775 103.085,67.775Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M103.085,69.628H100.806C100.294,69.628 99.879,69.213 99.879,68.701C99.879,68.19 100.294,67.775 100.806,67.775H103.085C103.597,67.775 104.012,68.19 104.012,68.701C104.012,69.213 103.597,69.628 103.085,69.628Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M103.085,71.481H100.806C100.294,71.481 99.879,71.066 99.879,70.555C99.879,70.043 100.294,69.628 100.806,69.628H103.085C103.597,69.628 104.012,70.043 104.012,70.555C104.012,71.066 103.597,71.481 103.085,71.481Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M100.806,67.775C100.294,67.775 99.879,67.36 99.879,66.848C99.879,66.337 100.294,65.922 100.806,65.922H103.085"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.085,67.775H102.335"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.085,69.628H100.806C100.294,69.628 99.879,69.213 99.879,68.701C99.879,68.19 100.294,67.775 100.806,67.775"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.085,71.481H100.806C100.294,71.481 99.879,71.066 99.879,70.555C99.879,70.043 100.294,69.628 100.806,69.628"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M100.589,73.138L101.946,71.481"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M116.075,116.151V114.462"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M110.995,116.151V114.462"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M109.827,116.151V114.462"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.949,116.151V113.185"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M107.088,116.151V113.779"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.137,116.151V114.462"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M117.64,116.151V114.462"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M121.38,116.151V113.794"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M125.32,114.927V112.893"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M126.823,114.071V113.294"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M119.277,116.151V114.796"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M96.568,72.655L94.483,80.97"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M117.167,49.413C117.851,49.413 118.406,49.968 118.406,50.652"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M120.178,40.37V41.396"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M117.881,39.515V41.042"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M115.927,39.214V40.741"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.785,39.214V40.741"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M112.115,38.595V40.121"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M110.057,38.299V39.826"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M106.323,39.106L120.447,41.436L106.323,39.106Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M106.323,39.106L120.447,41.436"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M100.446,91.757L102.096,88.728L106.844,89.906L101.267,92.903L100.446,92.373V91.757Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M97.284,86.302C97.962,87.036 98.838,87.717 99.83,88.274C102.971,90.037 106.114,89.987 107.142,88.157C107.984,86.657 107.098,84.562 104.887,82.822C104.433,82.465 103.777,82.543 103.42,82.996C103.063,83.45 103.143,84.106 103.594,84.463C105.034,85.597 105.529,86.762 105.32,87.134C105.045,87.624 103.14,87.737 100.853,86.452C99.779,85.85 98.86,85.057 98.333,84.278C97.914,83.66 97.836,83.188 97.945,82.994C98.16,82.612 99.561,82.375 101.587,83.264C102.116,83.496 102.732,83.256 102.964,82.728C103.196,82.199 102.955,81.583 102.427,81.351C99.569,80.096 97.036,80.346 96.123,81.971C95.574,82.948 95.745,84.184 96.603,85.451C96.799,85.74 97.027,86.025 97.284,86.302Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M98.75,89.519C98.843,88.975 98.478,88.459 97.934,88.366L90.634,87.121C90.091,87.028 89.575,87.394 89.482,87.937C89.389,88.481 89.755,88.997 90.298,89.089L97.598,90.335C98.142,90.427 98.658,90.062 98.75,89.519Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M98.186,87.529C98.35,87.002 98.055,86.443 97.529,86.279L92.372,84.677L90.457,84.082C89.93,83.918 89.371,84.213 89.207,84.739C89.043,85.266 89.338,85.825 89.864,85.989L96.936,88.186C97.463,88.35 98.022,88.055 98.186,87.529Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M98.968,86.667C99.308,86.233 99.232,85.606 98.798,85.266L92.97,80.697C92.536,80.356 91.908,80.432 91.568,80.866V80.867C91.228,81.3 91.304,81.928 91.738,82.268L97.566,86.837C98,87.177 98.628,87.101 98.968,86.667Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M95.085,89.906L97.755,90.771L98.467,91.516C99.26,92.346 100.487,92.59 101.537,92.127C102.357,91.765 102.949,91.025 103.122,90.145L103.388,88.79L96.946,83.394L95.085,89.906Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M95.967,89.353L96.146,88.547"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M96.491,87.214L96.647,86.536"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M97.106,85.354L97.262,84.677"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M97.052,81.652L96.703,81.273"
      android:strokeLineJoin="round"
      android:strokeWidth="0.300529"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M95.085,89.906L97.755,90.771L98.467,91.516C99.26,92.346 100.487,92.59 101.537,92.127C102.357,91.765 102.949,91.025 103.122,90.145L103.388,89.198"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M116.655,25.389H115.448V24.182C115.448,23.963 115.27,23.785 115.051,23.785C114.832,23.785 114.655,23.963 114.655,24.182V25.389H113.448C113.229,25.389 113.052,25.566 113.052,25.785C113.052,26.004 113.229,26.181 113.448,26.181H114.655V27.389C114.655,27.608 114.832,27.785 115.051,27.785C115.27,27.785 115.448,27.608 115.448,27.389V26.181H116.655C116.874,26.181 117.051,26.004 117.051,25.785C117.051,25.566 116.874,25.389 116.655,25.389Z"
      android:fillColor="#E43C28"/>
  <path
      android:pathData="M121.24,22.824C121.24,23.287 120.864,23.663 120.401,23.663C119.938,23.663 119.562,23.287 119.562,22.824C119.562,22.36 119.938,21.985 120.401,21.985C120.864,21.985 121.24,22.36 121.24,22.824Z"
      android:fillColor="#E43C28"/>
  <path
      android:pathData="M109.574,15.599C109.685,12.901 107.385,10.71 104.684,10.71C103.294,10.71 102.042,11.293 101.152,12.225C100.261,11.293 99.009,10.71 97.619,10.71C94.919,10.71 92.833,12.901 92.729,15.599C92.467,22.445 101.144,25.263 101.144,25.263C101.144,25.263 109.263,23.12 109.574,15.599Z"
      android:fillColor="#E43C28"/>
  <path
      android:pathData="M99.902,28.191L101.15,25.536L102.398,28.191H99.902Z"
      android:fillColor="#E43C28"/>
  <path
      android:pathData="M107.587,14.042C106.935,13.151 105.857,12.571 104.673,12.571"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M104.635,21.983C106.265,20.95 108.102,19.127 108.226,16.124"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M96.659,22.865C98.852,24.519 101.145,25.263 101.145,25.263C101.145,25.263 109.263,23.12 109.574,15.599C109.685,12.901 107.385,10.71 104.685,10.71C103.294,10.71 102.042,11.293 101.152,12.225C100.261,11.293 99.009,10.71 97.619,10.71C94.919,10.71 92.833,12.901 92.729,15.599C92.656,17.526 93.29,19.134 94.24,20.449"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M95.089,21.472C95.354,21.752 95.63,22.015 95.913,22.262"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M100.527,13.11L101.151,12.225"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.902,28.191L101.15,25.536L102.398,28.191H99.902Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.803,26.232L102.202,25.536"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.702,25.263L102.398,26.457"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M122.85,160.244H124.517"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M123.384,159.309H124.517"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.942,70.077C95.536,65.682 93.452,58.51 95.418,52.51L99.906,38.806C100.89,35.802 101.131,31.778 101.06,28.429"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M101.137,86.134C101.342,85.526 101.671,84.951 102.121,84.448C105.255,80.948 105.273,75.814 102.513,72.31L101.858,71.481"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.068,95.724L99.652,92.242"
      android:strokeLineJoin="round"
      android:strokeWidth="0.450793"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M97.819,64.274C97.819,64.76 97.424,65.155 96.938,65.155C96.451,65.155 96.056,64.76 96.056,64.274C96.056,63.787 96.451,63.393 96.938,63.393C97.424,63.393 97.819,63.787 97.819,64.274Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M105.201,83.582C105.201,84.155 104.737,84.619 104.164,84.619C103.591,84.619 103.126,84.155 103.126,83.582C103.126,83.009 103.591,82.544 104.164,82.544C104.737,82.544 105.201,83.009 105.201,83.582Z"
      android:fillColor="#F3D5CB"/>
</vector>
