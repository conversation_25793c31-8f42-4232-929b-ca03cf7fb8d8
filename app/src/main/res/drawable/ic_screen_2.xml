<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="295dp"
    android:height="334dp"
    android:viewportWidth="295"
    android:viewportHeight="334">
  <path
      android:pathData="M181.669,256.22L289.269,258.282L293.673,279.78H184.469L181.669,256.22Z"
      android:fillColor="#BDE3FF"/>
  <path
      android:pathData="M10.425,275V263.46H5.765V260.9H18.185V263.46H13.525V275H10.425Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M21.179,275.22C20.446,275.22 19.799,275.08 19.239,274.8C18.679,274.52 18.233,274.14 17.899,273.66C17.579,273.18 17.419,272.64 17.419,272.04C17.419,271.333 17.606,270.767 17.979,270.34C18.353,269.913 18.959,269.613 19.799,269.44C20.639,269.253 21.753,269.16 23.139,269.16H23.859V268.82C23.859,268.233 23.726,267.82 23.459,267.58C23.193,267.327 22.739,267.2 22.099,267.2C21.566,267.2 20.999,267.287 20.399,267.46C19.813,267.62 19.226,267.867 18.639,268.2L17.819,266.18C18.166,265.953 18.586,265.747 19.079,265.56C19.586,265.373 20.113,265.233 20.659,265.14C21.206,265.033 21.726,264.98 22.219,264.98C23.739,264.98 24.873,265.327 25.619,266.02C26.366,266.7 26.739,267.76 26.739,269.2V275H23.919V273.52C23.719,274.04 23.379,274.453 22.899,274.76C22.433,275.067 21.859,275.22 21.179,275.22ZM21.859,273.2C22.419,273.2 22.893,273.007 23.279,272.62C23.666,272.233 23.859,271.733 23.859,271.12V270.72H23.159C22.133,270.72 21.406,270.813 20.979,271C20.553,271.173 20.339,271.48 20.339,271.92C20.339,272.293 20.466,272.6 20.719,272.84C20.986,273.08 21.366,273.2 21.859,273.2Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M32.652,275.22C31.385,275.22 30.459,274.893 29.872,274.24C29.285,273.573 28.992,272.587 28.992,271.28V260.9H32.012V271.16C32.012,272.227 32.486,272.76 33.432,272.76C33.579,272.76 33.719,272.753 33.852,272.74C33.999,272.727 34.139,272.7 34.272,272.66L34.232,275C33.725,275.147 33.199,275.22 32.652,275.22Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M39.312,275.22C38.046,275.22 37.119,274.893 36.532,274.24C35.946,273.573 35.652,272.587 35.652,271.28V260.9H38.672V271.16C38.672,272.227 39.146,272.76 40.092,272.76C40.239,272.76 40.379,272.753 40.512,272.74C40.659,272.727 40.799,272.7 40.932,272.66L40.892,275C40.386,275.147 39.859,275.22 39.312,275.22Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M47.879,278.6V265.18H50.839V266.64C51.105,266.133 51.512,265.733 52.059,265.44C52.619,265.133 53.245,264.98 53.939,264.98C54.792,264.98 55.539,265.187 56.179,265.6C56.832,266.013 57.339,266.6 57.699,267.36C58.059,268.12 58.239,269.027 58.239,270.08C58.239,271.133 58.059,272.047 57.699,272.82C57.339,273.58 56.832,274.173 56.179,274.6C55.539,275.013 54.792,275.22 53.939,275.22C53.285,275.22 52.686,275.08 52.139,274.8C51.605,274.52 51.192,274.147 50.899,273.68V278.6H47.879ZM53.039,272.96C53.679,272.96 54.199,272.727 54.599,272.26C54.999,271.793 55.199,271.067 55.199,270.08C55.199,269.107 54.999,268.393 54.599,267.94C54.199,267.473 53.679,267.24 53.039,267.24C52.386,267.24 51.859,267.473 51.459,267.94C51.059,268.393 50.859,269.107 50.859,270.08C50.859,271.067 51.059,271.793 51.459,272.26C51.859,272.727 52.386,272.96 53.039,272.96Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M65.186,275.22C64.026,275.22 63.026,275.013 62.186,274.6C61.359,274.173 60.719,273.58 60.266,272.82C59.826,272.047 59.606,271.14 59.606,270.1C59.606,269.087 59.819,268.2 60.246,267.44C60.686,266.667 61.279,266.067 62.026,265.64C62.786,265.2 63.659,264.98 64.646,264.98C66.073,264.98 67.206,265.433 68.046,266.34C68.886,267.233 69.306,268.447 69.306,269.98V270.74H62.486C62.593,271.513 62.873,272.08 63.326,272.44C63.793,272.787 64.433,272.96 65.246,272.96C65.779,272.96 66.319,272.88 66.866,272.72C67.413,272.56 67.906,272.313 68.346,271.98L69.146,274C68.653,274.373 68.046,274.673 67.326,274.9C66.619,275.113 65.906,275.22 65.186,275.22ZM64.766,266.98C64.126,266.98 63.606,267.173 63.206,267.56C62.819,267.947 62.579,268.487 62.486,269.18H66.806C66.726,267.713 66.046,266.98 64.766,266.98Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M75.783,275.22C74.729,275.22 73.809,275.013 73.022,274.6C72.249,274.173 71.649,273.58 71.223,272.82C70.796,272.047 70.582,271.133 70.582,270.08C70.582,269.04 70.796,268.14 71.223,267.38C71.649,266.607 72.249,266.013 73.022,265.6C73.809,265.187 74.729,264.98 75.783,264.98C76.836,264.98 77.749,265.187 78.522,265.6C79.309,266.013 79.916,266.607 80.342,267.38C80.783,268.14 81.003,269.04 81.003,270.08C81.003,271.133 80.783,272.047 80.342,272.82C79.916,273.58 79.309,274.173 78.522,274.6C77.749,275.013 76.836,275.22 75.783,275.22ZM75.783,272.96C76.423,272.96 76.942,272.727 77.342,272.26C77.756,271.793 77.963,271.067 77.963,270.08C77.963,269.107 77.756,268.393 77.342,267.94C76.942,267.473 76.423,267.24 75.783,267.24C75.143,267.24 74.622,267.473 74.223,267.94C73.823,268.393 73.622,269.107 73.622,270.08C73.622,271.067 73.823,271.793 74.223,272.26C74.622,272.727 75.143,272.96 75.783,272.96Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M82.84,278.6V265.18H85.8V266.64C86.066,266.133 86.473,265.733 87.02,265.44C87.58,265.133 88.206,264.98 88.9,264.98C89.753,264.98 90.5,265.187 91.14,265.6C91.793,266.013 92.3,266.6 92.66,267.36C93.02,268.12 93.2,269.027 93.2,270.08C93.2,271.133 93.02,272.047 92.66,272.82C92.3,273.58 91.793,274.173 91.14,274.6C90.5,275.013 89.753,275.22 88.9,275.22C88.246,275.22 87.646,275.08 87.1,274.8C86.566,274.52 86.153,274.147 85.86,273.68V278.6H82.84ZM88,272.96C88.64,272.96 89.16,272.727 89.56,272.26C89.96,271.793 90.16,271.067 90.16,270.08C90.16,269.107 89.96,268.393 89.56,267.94C89.16,267.473 88.64,267.24 88,267.24C87.346,267.24 86.82,267.473 86.42,267.94C86.02,268.393 85.82,269.107 85.82,270.08C85.82,271.067 86.02,271.793 86.42,272.26C86.82,272.727 87.346,272.96 88,272.96Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M98.707,275.22C97.44,275.22 96.513,274.893 95.927,274.24C95.34,273.573 95.047,272.587 95.047,271.28V260.9H98.067V271.16C98.067,272.227 98.54,272.76 99.487,272.76C99.633,272.76 99.773,272.753 99.907,272.74C100.054,272.727 100.194,272.7 100.327,272.66L100.287,275C99.78,275.147 99.254,275.22 98.707,275.22Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M106.709,275.22C105.549,275.22 104.549,275.013 103.709,274.6C102.883,274.173 102.243,273.58 101.789,272.82C101.349,272.047 101.129,271.14 101.129,270.1C101.129,269.087 101.343,268.2 101.769,267.44C102.209,266.667 102.803,266.067 103.549,265.64C104.309,265.2 105.183,264.98 106.169,264.98C107.596,264.98 108.729,265.433 109.569,266.34C110.409,267.233 110.829,268.447 110.829,269.98V270.74H104.009C104.116,271.513 104.396,272.08 104.849,272.44C105.316,272.787 105.956,272.96 106.769,272.96C107.303,272.96 107.843,272.88 108.389,272.72C108.936,272.56 109.429,272.313 109.869,271.98L110.669,274C110.176,274.373 109.569,274.673 108.849,274.9C108.143,275.113 107.429,275.22 106.709,275.22ZM106.289,266.98C105.649,266.98 105.129,267.173 104.729,267.56C104.343,267.947 104.103,268.487 104.009,269.18H108.329C108.249,267.713 107.569,266.98 106.289,266.98Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M118.112,263.28V260.46H121.372V263.28H118.112ZM116.152,278.86L115.952,276.7L116.892,276.6C117.799,276.493 118.252,275.973 118.252,275.04V265.18H121.272V274.64C121.272,275.56 121.132,276.313 120.852,276.9C120.586,277.487 120.139,277.933 119.512,278.24C118.899,278.547 118.066,278.733 117.012,278.8L116.152,278.86Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M128.341,275.22C127.288,275.22 126.368,275.013 125.581,274.6C124.808,274.173 124.208,273.58 123.781,272.82C123.354,272.047 123.141,271.133 123.141,270.08C123.141,269.04 123.354,268.14 123.781,267.38C124.208,266.607 124.808,266.013 125.581,265.6C126.368,265.187 127.288,264.98 128.341,264.98C129.394,264.98 130.308,265.187 131.081,265.6C131.868,266.013 132.474,266.607 132.901,267.38C133.341,268.14 133.561,269.04 133.561,270.08C133.561,271.133 133.341,272.047 132.901,272.82C132.474,273.58 131.868,274.173 131.081,274.6C130.308,275.013 129.394,275.22 128.341,275.22ZM128.341,272.96C128.981,272.96 129.501,272.727 129.901,272.26C130.314,271.793 130.521,271.067 130.521,270.08C130.521,269.107 130.314,268.393 129.901,267.94C129.501,267.473 128.981,267.24 128.341,267.24C127.701,267.24 127.181,267.473 126.781,267.94C126.381,268.393 126.181,269.107 126.181,270.08C126.181,271.067 126.381,271.793 126.781,272.26C127.181,272.727 127.701,272.96 128.341,272.96Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M135.278,263.28V260.46H138.538V263.28H135.278ZM135.398,275V265.18H138.418V275H135.398Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M140.75,275V265.18H143.71V266.62C144.043,266.087 144.49,265.68 145.05,265.4C145.61,265.12 146.237,264.98 146.93,264.98C148.09,264.98 148.957,265.32 149.53,266C150.103,266.667 150.39,267.7 150.39,269.1V275H147.37V269.24C147.37,268.56 147.243,268.073 146.99,267.78C146.737,267.473 146.363,267.32 145.87,267.32C145.23,267.32 144.717,267.52 144.33,267.92C143.957,268.32 143.77,268.853 143.77,269.52V275H140.75Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M161.531,275.22C160.798,275.22 160.151,275.08 159.591,274.8C159.031,274.52 158.584,274.14 158.251,273.66C157.931,273.18 157.771,272.64 157.771,272.04C157.771,271.333 157.958,270.767 158.331,270.34C158.704,269.913 159.311,269.613 160.151,269.44C160.991,269.253 162.104,269.16 163.491,269.16H164.211V268.82C164.211,268.233 164.078,267.82 163.811,267.58C163.544,267.327 163.091,267.2 162.451,267.2C161.918,267.2 161.351,267.287 160.751,267.46C160.164,267.62 159.578,267.867 158.991,268.2L158.171,266.18C158.518,265.953 158.938,265.747 159.431,265.56C159.938,265.373 160.464,265.233 161.011,265.14C161.558,265.033 162.078,264.98 162.571,264.98C164.091,264.98 165.224,265.327 165.971,266.02C166.718,266.7 167.091,267.76 167.091,269.2V275H164.271V273.52C164.071,274.04 163.731,274.453 163.251,274.76C162.784,275.067 162.211,275.22 161.531,275.22ZM162.211,273.2C162.771,273.2 163.244,273.007 163.631,272.62C164.018,272.233 164.211,271.733 164.211,271.12V270.72H163.511C162.484,270.72 161.758,270.813 161.331,271C160.904,271.173 160.691,271.48 160.691,271.92C160.691,272.293 160.818,272.6 161.071,272.84C161.338,273.08 161.718,273.2 162.211,273.2Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M173.104,275.22C172.237,275.22 171.43,275.12 170.684,274.92C169.937,274.72 169.317,274.447 168.824,274.1L169.544,272.14C170.037,272.447 170.597,272.693 171.224,272.88C171.864,273.053 172.497,273.14 173.124,273.14C173.684,273.14 174.097,273.053 174.364,272.88C174.63,272.693 174.764,272.453 174.764,272.16C174.764,271.693 174.424,271.4 173.744,271.28L171.644,270.9C170.804,270.753 170.164,270.453 169.724,270C169.284,269.547 169.064,268.953 169.064,268.22C169.064,267.553 169.25,266.98 169.624,266.5C169.997,266.02 170.51,265.647 171.164,265.38C171.817,265.113 172.57,264.98 173.424,264.98C174.13,264.98 174.817,265.073 175.484,265.26C176.15,265.433 176.724,265.713 177.204,266.1L176.444,268.04C176.044,267.747 175.564,267.507 175.004,267.32C174.457,267.133 173.944,267.04 173.464,267.04C172.864,267.04 172.43,267.14 172.164,267.34C171.897,267.527 171.764,267.767 171.764,268.06C171.764,268.527 172.077,268.82 172.704,268.94L174.804,269.32C175.67,269.467 176.33,269.76 176.784,270.2C177.237,270.627 177.464,271.213 177.464,271.96C177.464,272.987 177.064,273.787 176.264,274.36C175.464,274.933 174.41,275.22 173.104,275.22Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M184.574,266.82V264.76C184.574,264 184.707,263.307 184.974,262.68C185.241,262.04 185.707,261.387 186.374,260.72L187.554,261.68C186.887,262.347 186.481,262.987 186.334,263.6H187.814V266.82H184.574ZM188.814,266.82V264.76C188.814,264 188.947,263.307 189.214,262.68C189.481,262.04 189.947,261.387 190.614,260.72L191.794,261.68C191.127,262.347 190.721,262.987 190.574,263.6H192.054V266.82H188.814Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M194.487,275V260.9H197.067L201.827,269.56L206.567,260.9H209.107V275H206.287V266.4L202.647,272.88H200.907L197.287,266.46V275H194.487Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M216.807,275.22C215.647,275.22 214.647,275.013 213.807,274.6C212.98,274.173 212.34,273.58 211.887,272.82C211.447,272.047 211.227,271.14 211.227,270.1C211.227,269.087 211.44,268.2 211.867,267.44C212.307,266.667 212.9,266.067 213.647,265.64C214.407,265.2 215.28,264.98 216.267,264.98C217.694,264.98 218.827,265.433 219.667,266.34C220.507,267.233 220.927,268.447 220.927,269.98V270.74H214.107C214.214,271.513 214.494,272.08 214.947,272.44C215.414,272.787 216.054,272.96 216.867,272.96C217.4,272.96 217.94,272.88 218.487,272.72C219.034,272.56 219.527,272.313 219.967,271.98L220.767,274C220.274,274.373 219.667,274.673 218.947,274.9C218.24,275.113 217.527,275.22 216.807,275.22ZM216.387,266.98C215.747,266.98 215.227,267.173 214.827,267.56C214.44,267.947 214.2,268.487 214.107,269.18H218.427C218.347,267.713 217.667,266.98 216.387,266.98Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M222.724,275V265.18H225.684V266.6C225.977,266.093 226.377,265.7 226.884,265.42C227.404,265.127 228.004,264.98 228.684,264.98C229.377,264.98 229.964,265.133 230.444,265.44C230.937,265.747 231.31,266.213 231.564,266.84C231.87,266.253 232.31,265.8 232.884,265.48C233.47,265.147 234.117,264.98 234.824,264.98C235.944,264.98 236.777,265.32 237.324,266C237.87,266.667 238.144,267.7 238.144,269.1V275H235.124V269.2C235.124,268.547 235.017,268.073 234.804,267.78C234.604,267.473 234.257,267.32 233.764,267.32C233.19,267.32 232.744,267.52 232.424,267.92C232.104,268.32 231.944,268.887 231.944,269.62V275H228.924V269.2C228.924,268.547 228.817,268.073 228.604,267.78C228.39,267.473 228.044,267.32 227.564,267.32C226.99,267.32 226.544,267.52 226.224,267.92C225.904,268.32 225.744,268.887 225.744,269.62V275H222.724Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M246.517,275.22C245.824,275.22 245.197,275.067 244.637,274.76C244.09,274.453 243.684,274.047 243.417,273.54V275H240.457V260.9H243.477V266.5C243.77,266.033 244.184,265.667 244.717,265.4C245.264,265.12 245.864,264.98 246.517,264.98C247.37,264.98 248.117,265.187 248.757,265.6C249.41,266.013 249.917,266.6 250.277,267.36C250.637,268.12 250.817,269.027 250.817,270.08C250.817,271.133 250.637,272.047 250.277,272.82C249.917,273.58 249.41,274.173 248.757,274.6C248.117,275.013 247.37,275.22 246.517,275.22ZM245.617,272.96C246.257,272.96 246.777,272.727 247.177,272.26C247.577,271.793 247.777,271.067 247.777,270.08C247.777,269.107 247.577,268.393 247.177,267.94C246.777,267.473 246.257,267.24 245.617,267.24C244.964,267.24 244.437,267.473 244.037,267.94C243.637,268.393 243.437,269.107 243.437,270.08C243.437,271.067 243.637,271.793 244.037,272.26C244.437,272.727 244.964,272.96 245.617,272.96Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M257.764,275.22C256.604,275.22 255.604,275.013 254.764,274.6C253.937,274.173 253.297,273.58 252.844,272.82C252.404,272.047 252.184,271.14 252.184,270.1C252.184,269.087 252.397,268.2 252.824,267.44C253.264,266.667 253.857,266.067 254.604,265.64C255.364,265.2 256.237,264.98 257.224,264.98C258.651,264.98 259.784,265.433 260.624,266.34C261.464,267.233 261.884,268.447 261.884,269.98V270.74H255.064C255.171,271.513 255.451,272.08 255.904,272.44C256.371,272.787 257.011,272.96 257.824,272.96C258.357,272.96 258.897,272.88 259.444,272.72C259.991,272.56 260.484,272.313 260.924,271.98L261.724,274C261.231,274.373 260.624,274.673 259.904,274.9C259.197,275.113 258.484,275.22 257.764,275.22ZM257.344,266.98C256.704,266.98 256.184,267.173 255.784,267.56C255.397,267.947 255.157,268.487 255.064,269.18H259.384C259.304,267.713 258.624,266.98 257.344,266.98Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M263.641,275V265.18H266.601V266.88C267.067,265.733 268.067,265.1 269.601,264.98L270.481,264.92L270.661,267.46L268.961,267.64C267.467,267.787 266.721,268.547 266.721,269.92V275H263.641Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M275.545,275.22C274.678,275.22 273.872,275.12 273.125,274.92C272.378,274.72 271.758,274.447 271.265,274.1L271.985,272.14C272.478,272.447 273.038,272.693 273.665,272.88C274.305,273.053 274.938,273.14 275.565,273.14C276.125,273.14 276.538,273.053 276.805,272.88C277.072,272.693 277.205,272.453 277.205,272.16C277.205,271.693 276.865,271.4 276.185,271.28L274.085,270.9C273.245,270.753 272.605,270.453 272.165,270C271.725,269.547 271.505,268.953 271.505,268.22C271.505,267.553 271.692,266.98 272.065,266.5C272.438,266.02 272.952,265.647 273.605,265.38C274.258,265.113 275.012,264.98 275.865,264.98C276.572,264.98 277.258,265.073 277.925,265.26C278.592,265.433 279.165,265.713 279.645,266.1L278.885,268.04C278.485,267.747 278.005,267.507 277.445,267.32C276.898,267.133 276.385,267.04 275.905,267.04C275.305,267.04 274.872,267.14 274.605,267.34C274.338,267.527 274.205,267.767 274.205,268.06C274.205,268.527 274.518,268.82 275.145,268.94L277.245,269.32C278.112,269.467 278.772,269.76 279.225,270.2C279.678,270.627 279.905,271.213 279.905,271.96C279.905,272.987 279.505,273.787 278.705,274.36C277.905,274.933 276.852,275.22 275.545,275.22Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M282.342,266.98L281.162,266.06C281.829,265.38 282.235,264.727 282.382,264.1H280.902V260.9H284.142V262.96C284.142,263.72 284.009,264.42 283.742,265.06C283.475,265.687 283.009,266.327 282.342,266.98ZM286.582,266.98L285.402,266.06C286.069,265.38 286.475,264.727 286.622,264.1H285.142V260.9H288.382V262.96C288.382,263.72 288.249,264.42 287.982,265.06C287.715,265.687 287.249,266.327 286.582,266.98Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M0.822,307V295.72H4.63C6.454,295.72 7.862,296.205 8.854,297.176C9.846,298.136 10.342,299.528 10.342,301.352C10.342,303.176 9.846,304.573 8.854,305.544C7.862,306.515 6.454,307 4.63,307H0.822ZM2.134,305.88H4.534C7.478,305.88 8.95,304.371 8.95,301.352C8.95,298.344 7.478,296.84 4.534,296.84H2.134V305.88Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M14.969,307.144C14.435,307.144 13.955,307.043 13.529,306.84C13.113,306.627 12.782,306.339 12.537,305.976C12.291,305.613 12.169,305.208 12.169,304.76C12.169,304.195 12.313,303.747 12.601,303.416C12.899,303.085 13.385,302.851 14.057,302.712C14.739,302.563 15.667,302.488 16.841,302.488H17.353V301.992C17.353,301.331 17.214,300.856 16.937,300.568C16.67,300.269 16.238,300.12 15.641,300.12C15.171,300.12 14.718,300.189 14.281,300.328C13.843,300.456 13.395,300.669 12.937,300.968L12.489,300.024C12.894,299.736 13.385,299.507 13.961,299.336C14.547,299.165 15.107,299.08 15.641,299.08C16.643,299.08 17.385,299.325 17.865,299.816C18.355,300.307 18.601,301.069 18.601,302.104V307H17.385V305.656C17.182,306.115 16.867,306.477 16.441,306.744C16.025,307.011 15.534,307.144 14.969,307.144ZM15.177,306.168C15.817,306.168 16.339,305.949 16.745,305.512C17.15,305.064 17.353,304.499 17.353,303.816V303.336H16.857C15.993,303.336 15.31,303.379 14.809,303.464C14.318,303.539 13.971,303.677 13.769,303.88C13.577,304.072 13.481,304.339 13.481,304.68C13.481,305.117 13.63,305.475 13.929,305.752C14.238,306.029 14.654,306.168 15.177,306.168Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M24.125,307.144C23.208,307.144 22.52,306.904 22.061,306.424C21.603,305.933 21.373,305.229 21.373,304.312V300.232H19.853V299.224H21.373V296.84H22.669V299.224H25.133V300.232H22.669V304.184C22.669,304.792 22.797,305.256 23.053,305.576C23.309,305.885 23.725,306.04 24.301,306.04C24.472,306.04 24.642,306.019 24.813,305.976C24.984,305.933 25.139,305.891 25.277,305.848L25.501,306.84C25.362,306.915 25.16,306.984 24.893,307.048C24.626,307.112 24.371,307.144 24.125,307.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M30.025,307.144C28.798,307.144 27.827,306.787 27.113,306.072C26.398,305.347 26.041,304.365 26.041,303.128C26.041,302.328 26.201,301.624 26.521,301.016C26.841,300.397 27.278,299.923 27.833,299.592C28.398,299.251 29.049,299.08 29.785,299.08C30.841,299.08 31.667,299.421 32.265,300.104C32.862,300.776 33.161,301.704 33.161,302.888V303.384H27.305C27.347,304.269 27.603,304.947 28.073,305.416C28.542,305.875 29.193,306.104 30.025,306.104C30.494,306.104 30.942,306.035 31.369,305.896C31.795,305.747 32.201,305.507 32.585,305.176L33.033,306.088C32.681,306.419 32.233,306.68 31.689,306.872C31.145,307.053 30.59,307.144 30.025,307.144ZM29.817,300.04C29.081,300.04 28.499,300.269 28.073,300.728C27.646,301.187 27.395,301.789 27.321,302.536H32.041C32.009,301.747 31.801,301.133 31.417,300.696C31.043,300.259 30.51,300.04 29.817,300.04Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M39.66,307.144C38.178,307.144 37.052,306.755 36.284,305.976C35.516,305.187 35.132,304.04 35.132,302.536V295.72H36.444V302.472C36.444,304.808 37.516,305.976 39.66,305.976C41.783,305.976 42.844,304.808 42.844,302.472V295.72H44.156V302.536C44.156,304.04 43.772,305.187 43.004,305.976C42.247,306.755 41.132,307.144 39.66,307.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M46.677,309.88V299.224H47.941V300.616C48.176,300.136 48.522,299.763 48.981,299.496C49.45,299.219 49.994,299.08 50.613,299.08C51.306,299.08 51.909,299.245 52.421,299.576C52.944,299.896 53.344,300.36 53.621,300.968C53.909,301.565 54.053,302.28 54.053,303.112C54.053,303.933 53.909,304.648 53.621,305.256C53.344,305.853 52.944,306.317 52.421,306.648C51.909,306.979 51.306,307.144 50.613,307.144C50.016,307.144 49.482,307.011 49.013,306.744C48.544,306.477 48.197,306.109 47.973,305.64V309.88H46.677ZM50.341,306.104C51.066,306.104 51.648,305.848 52.085,305.336C52.522,304.813 52.741,304.072 52.741,303.112C52.741,302.141 52.522,301.4 52.085,300.888C51.648,300.376 51.066,300.12 50.341,300.12C49.616,300.12 49.034,300.376 48.597,300.888C48.16,301.4 47.941,302.141 47.941,303.112C47.941,304.072 48.16,304.813 48.597,305.336C49.034,305.848 49.616,306.104 50.341,306.104Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M60.146,307V299.224H61.41V300.536C61.645,300.067 61.971,299.709 62.387,299.464C62.803,299.208 63.288,299.08 63.842,299.08C65.059,299.08 65.837,299.608 66.178,300.664C66.413,300.173 66.76,299.789 67.218,299.512C67.677,299.224 68.205,299.08 68.802,299.08C70.531,299.08 71.395,300.104 71.395,302.152V307H70.099V302.216C70.099,301.501 69.97,300.979 69.715,300.648C69.469,300.307 69.059,300.136 68.482,300.136C67.853,300.136 67.352,300.36 66.979,300.808C66.605,301.256 66.419,301.853 66.419,302.6V307H65.122V302.216C65.122,301.501 64.994,300.979 64.739,300.648C64.493,300.307 64.082,300.136 63.507,300.136C62.867,300.136 62.36,300.36 61.986,300.808C61.624,301.256 61.443,301.853 61.443,302.6V307H60.146Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M76.266,307.144C75.732,307.144 75.252,307.043 74.826,306.84C74.41,306.627 74.079,306.339 73.834,305.976C73.588,305.613 73.466,305.208 73.466,304.76C73.466,304.195 73.61,303.747 73.898,303.416C74.196,303.085 74.682,302.851 75.354,302.712C76.036,302.563 76.964,302.488 78.138,302.488H78.65V301.992C78.65,301.331 78.511,300.856 78.234,300.568C77.967,300.269 77.535,300.12 76.938,300.12C76.468,300.12 76.015,300.189 75.578,300.328C75.14,300.456 74.692,300.669 74.234,300.968L73.786,300.024C74.191,299.736 74.682,299.507 75.258,299.336C75.844,299.165 76.404,299.08 76.938,299.08C77.94,299.08 78.682,299.325 79.162,299.816C79.652,300.307 79.898,301.069 79.898,302.104V307H78.682V305.656C78.479,306.115 78.164,306.477 77.738,306.744C77.322,307.011 76.831,307.144 76.266,307.144ZM76.474,306.168C77.114,306.168 77.636,305.949 78.042,305.512C78.447,305.064 78.65,304.499 78.65,303.816V303.336H78.154C77.29,303.336 76.607,303.379 76.106,303.464C75.615,303.539 75.268,303.677 75.066,303.88C74.874,304.072 74.778,304.339 74.778,304.68C74.778,305.117 74.927,305.475 75.226,305.752C75.535,306.029 75.951,306.168 76.474,306.168Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M82.302,307V295.72H83.598V302.68H83.63L87.23,299.224H88.878L84.974,302.968L89.214,307H87.566L83.63,303.352H83.598V307H82.302Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M93.603,307.144C92.376,307.144 91.406,306.787 90.691,306.072C89.976,305.347 89.619,304.365 89.619,303.128C89.619,302.328 89.779,301.624 90.099,301.016C90.419,300.397 90.856,299.923 91.411,299.592C91.976,299.251 92.627,299.08 93.363,299.08C94.419,299.08 95.246,299.421 95.843,300.104C96.44,300.776 96.739,301.704 96.739,302.888V303.384H90.883C90.926,304.269 91.182,304.947 91.651,305.416C92.12,305.875 92.771,306.104 93.603,306.104C94.072,306.104 94.52,306.035 94.947,305.896C95.374,305.747 95.779,305.507 96.163,305.176L96.611,306.088C96.259,306.419 95.811,306.68 95.267,306.872C94.723,307.053 94.168,307.144 93.603,307.144ZM93.395,300.04C92.659,300.04 92.078,300.269 91.651,300.728C91.224,301.187 90.974,301.789 90.899,302.536H95.619C95.587,301.747 95.379,301.133 94.995,300.696C94.622,300.259 94.088,300.04 93.395,300.04Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M101.239,307.144C100.631,307.144 100.055,307.059 99.51,306.888C98.966,306.717 98.519,306.477 98.166,306.168L98.582,305.256C98.966,305.565 99.388,305.795 99.846,305.944C100.316,306.083 100.791,306.152 101.271,306.152C101.879,306.152 102.337,306.04 102.647,305.816C102.956,305.592 103.111,305.288 103.111,304.904C103.111,304.605 103.009,304.371 102.807,304.2C102.604,304.019 102.284,303.88 101.847,303.784L100.391,303.464C99.068,303.176 98.407,302.493 98.407,301.416C98.407,300.712 98.684,300.147 99.239,299.72C99.804,299.293 100.54,299.08 101.447,299.08C101.98,299.08 102.487,299.165 102.967,299.336C103.457,299.496 103.863,299.736 104.183,300.056L103.751,300.968C103.441,300.68 103.079,300.461 102.663,300.312C102.257,300.152 101.852,300.072 101.447,300.072C100.849,300.072 100.396,300.189 100.087,300.424C99.777,300.659 99.622,300.968 99.622,301.352C99.622,301.949 100.017,302.333 100.807,302.504L102.263,302.808C102.945,302.957 103.463,303.197 103.815,303.528C104.167,303.848 104.343,304.285 104.343,304.84C104.343,305.555 104.06,306.12 103.495,306.536C102.929,306.941 102.177,307.144 101.239,307.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M110.267,297.288V295.832H111.867V297.288H110.267ZM110.427,307V299.224H111.723V307H110.427Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M117.266,307.144C116.348,307.144 115.66,306.904 115.202,306.424C114.743,305.933 114.514,305.229 114.514,304.312V300.232H112.994V299.224H114.514V296.84H115.81V299.224H118.274V300.232H115.81V304.184C115.81,304.792 115.938,305.256 116.194,305.576C116.45,305.885 116.866,306.04 117.442,306.04C117.612,306.04 117.783,306.019 117.954,305.976C118.124,305.933 118.279,305.891 118.418,305.848L118.642,306.84C118.503,306.915 118.3,306.984 118.034,307.048C117.767,307.112 117.511,307.144 117.266,307.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M127.353,307.144C126.126,307.144 125.156,306.787 124.441,306.072C123.726,305.347 123.369,304.365 123.369,303.128C123.369,302.328 123.529,301.624 123.849,301.016C124.169,300.397 124.606,299.923 125.161,299.592C125.726,299.251 126.377,299.08 127.113,299.08C128.169,299.08 128.996,299.421 129.593,300.104C130.19,300.776 130.489,301.704 130.489,302.888V303.384H124.633C124.676,304.269 124.932,304.947 125.401,305.416C125.87,305.875 126.521,306.104 127.353,306.104C127.822,306.104 128.27,306.035 128.697,305.896C129.124,305.747 129.529,305.507 129.913,305.176L130.361,306.088C130.009,306.419 129.561,306.68 129.017,306.872C128.473,307.053 127.918,307.144 127.353,307.144ZM127.145,300.04C126.409,300.04 125.828,300.269 125.401,300.728C124.974,301.187 124.724,301.789 124.649,302.536H129.369C129.337,301.747 129.129,301.133 128.745,300.696C128.372,300.259 127.838,300.04 127.145,300.04Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M134.813,307.144C134.279,307.144 133.799,307.043 133.373,306.84C132.957,306.627 132.626,306.339 132.381,305.976C132.135,305.613 132.013,305.208 132.013,304.76C132.013,304.195 132.157,303.747 132.445,303.416C132.743,303.085 133.229,302.851 133.901,302.712C134.583,302.563 135.511,302.488 136.685,302.488H137.197V301.992C137.197,301.331 137.058,300.856 136.781,300.568C136.514,300.269 136.082,300.12 135.485,300.12C135.015,300.12 134.562,300.189 134.125,300.328C133.687,300.456 133.239,300.669 132.781,300.968L132.333,300.024C132.738,299.736 133.229,299.507 133.805,299.336C134.391,299.165 134.951,299.08 135.485,299.08C136.487,299.08 137.229,299.325 137.709,299.816C138.199,300.307 138.445,301.069 138.445,302.104V307H137.229V305.656C137.026,306.115 136.711,306.477 136.285,306.744C135.869,307.011 135.378,307.144 134.813,307.144ZM135.021,306.168C135.661,306.168 136.183,305.949 136.589,305.512C136.994,305.064 137.197,304.499 137.197,303.816V303.336H136.701C135.837,303.336 135.154,303.379 134.653,303.464C134.162,303.539 133.815,303.677 133.613,303.88C133.421,304.072 133.325,304.339 133.325,304.68C133.325,305.117 133.474,305.475 133.773,305.752C134.082,306.029 134.498,306.168 135.021,306.168Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M143.473,307.144C142.865,307.144 142.289,307.059 141.745,306.888C141.201,306.717 140.753,306.477 140.401,306.168L140.817,305.256C141.201,305.565 141.622,305.795 142.081,305.944C142.55,306.083 143.025,306.152 143.505,306.152C144.113,306.152 144.572,306.04 144.881,305.816C145.19,305.592 145.345,305.288 145.345,304.904C145.345,304.605 145.244,304.371 145.041,304.2C144.838,304.019 144.518,303.88 144.081,303.784L142.625,303.464C141.302,303.176 140.641,302.493 140.641,301.416C140.641,300.712 140.918,300.147 141.473,299.72C142.038,299.293 142.774,299.08 143.681,299.08C144.214,299.08 144.721,299.165 145.201,299.336C145.692,299.496 146.097,299.736 146.417,300.056L145.985,300.968C145.676,300.68 145.313,300.461 144.897,300.312C144.492,300.152 144.086,300.072 143.681,300.072C143.084,300.072 142.63,300.189 142.321,300.424C142.012,300.659 141.857,300.968 141.857,301.352C141.857,301.949 142.252,302.333 143.041,302.504L144.497,302.808C145.18,302.957 145.697,303.197 146.049,303.528C146.401,303.848 146.577,304.285 146.577,304.84C146.577,305.555 146.294,306.12 145.729,306.536C145.164,306.941 144.412,307.144 143.473,307.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M149.292,309.88L150.62,306.872L147.34,299.224H148.748L151.324,305.528L153.916,299.224H155.276L150.652,309.88H149.292Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M161.14,307V300.232H159.604V299.224H161.14V298.952C161.14,297.917 161.396,297.133 161.908,296.6C162.431,296.067 163.231,295.768 164.308,295.704L164.932,295.672L165.044,296.664L164.308,296.712C163.647,296.765 163.167,296.963 162.868,297.304C162.58,297.645 162.436,298.141 162.436,298.792V299.224H164.676V300.232H162.436V307H161.14Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M169.065,307.144C168.308,307.144 167.652,306.979 167.097,306.648C166.543,306.317 166.111,305.853 165.801,305.256C165.503,304.648 165.353,303.933 165.353,303.112C165.353,302.291 165.503,301.581 165.801,300.984C166.111,300.376 166.543,299.907 167.097,299.576C167.652,299.245 168.308,299.08 169.065,299.08C169.812,299.08 170.463,299.245 171.017,299.576C171.583,299.907 172.015,300.376 172.313,300.984C172.623,301.581 172.777,302.291 172.777,303.112C172.777,303.933 172.623,304.648 172.313,305.256C172.015,305.853 171.583,306.317 171.017,306.648C170.463,306.979 169.812,307.144 169.065,307.144ZM169.065,306.104C169.791,306.104 170.372,305.848 170.809,305.336C171.247,304.813 171.465,304.072 171.465,303.112C171.465,302.141 171.247,301.4 170.809,300.888C170.372,300.376 169.791,300.12 169.065,300.12C168.329,300.12 167.743,300.376 167.305,300.888C166.879,301.4 166.665,302.141 166.665,303.112C166.665,304.072 166.879,304.813 167.305,305.336C167.743,305.848 168.329,306.104 169.065,306.104Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M174.724,307V299.224H175.988V300.616C176.404,299.677 177.257,299.16 178.548,299.064L179.012,299.016L179.108,300.136L178.292,300.232C177.556,300.296 176.996,300.531 176.612,300.936C176.228,301.331 176.036,301.875 176.036,302.568V307H174.724Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M187.688,307.144C186.77,307.144 186.082,306.904 185.624,306.424C185.165,305.933 184.936,305.229 184.936,304.312V300.232H183.416V299.224H184.936V296.84H186.232V299.224H188.696V300.232H186.232V304.184C186.232,304.792 186.36,305.256 186.616,305.576C186.872,305.885 187.288,306.04 187.864,306.04C188.034,306.04 188.205,306.019 188.376,305.976C188.546,305.933 188.701,305.891 188.84,305.848L189.064,306.84C188.925,306.915 188.722,306.984 188.456,307.048C188.189,307.112 187.933,307.144 187.688,307.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M192.609,307.144C192.076,307.144 191.596,307.043 191.169,306.84C190.753,306.627 190.423,306.339 190.177,305.976C189.932,305.613 189.809,305.208 189.809,304.76C189.809,304.195 189.953,303.747 190.241,303.416C190.54,303.085 191.025,302.851 191.697,302.712C192.38,302.563 193.308,302.488 194.481,302.488H194.993V301.992C194.993,301.331 194.855,300.856 194.577,300.568C194.311,300.269 193.879,300.12 193.281,300.12C192.812,300.12 192.359,300.189 191.921,300.328C191.484,300.456 191.036,300.669 190.577,300.968L190.129,300.024C190.535,299.736 191.025,299.507 191.601,299.336C192.188,299.165 192.748,299.08 193.281,299.08C194.284,299.08 195.025,299.325 195.505,299.816C195.996,300.307 196.241,301.069 196.241,302.104V307H195.025V305.656C194.823,306.115 194.508,306.477 194.081,306.744C193.665,307.011 193.175,307.144 192.609,307.144ZM192.817,306.168C193.457,306.168 193.98,305.949 194.385,305.512C194.791,305.064 194.993,304.499 194.993,303.816V303.336H194.497C193.633,303.336 192.951,303.379 192.449,303.464C191.959,303.539 191.612,303.677 191.409,303.88C191.217,304.072 191.121,304.339 191.121,304.68C191.121,305.117 191.271,305.475 191.569,305.752C191.879,306.029 192.295,306.168 192.817,306.168Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M201.03,307.144C200.24,307.144 199.643,306.915 199.238,306.456C198.843,305.987 198.646,305.309 198.646,304.424V295.72H199.942V304.328C199.942,305.469 200.395,306.04 201.302,306.04C201.568,306.04 201.808,306.008 202.022,305.944L201.99,307.032C201.659,307.107 201.339,307.144 201.03,307.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M205.764,307.144C204.975,307.144 204.377,306.915 203.972,306.456C203.577,305.987 203.38,305.309 203.38,304.424V295.72H204.676V304.328C204.676,305.469 205.129,306.04 206.036,306.04C206.303,306.04 206.543,306.008 206.756,305.944L206.724,307.032C206.393,307.107 206.073,307.144 205.764,307.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M214.288,307L211.392,299.224H212.752L214.944,305.48L217.216,299.224H218.32L220.56,305.496L222.784,299.224H224.08L221.168,307H219.936L217.744,300.968L215.536,307H214.288Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M228.8,307.144C228.042,307.144 227.386,306.979 226.832,306.648C226.277,306.317 225.845,305.853 225.536,305.256C225.237,304.648 225.088,303.933 225.088,303.112C225.088,302.291 225.237,301.581 225.536,300.984C225.845,300.376 226.277,299.907 226.832,299.576C227.386,299.245 228.042,299.08 228.8,299.08C229.546,299.08 230.197,299.245 230.752,299.576C231.317,299.907 231.749,300.376 232.048,300.984C232.357,301.581 232.512,302.291 232.512,303.112C232.512,303.933 232.357,304.648 232.048,305.256C231.749,305.853 231.317,306.317 230.752,306.648C230.197,306.979 229.546,307.144 228.8,307.144ZM228.8,306.104C229.525,306.104 230.106,305.848 230.544,305.336C230.981,304.813 231.2,304.072 231.2,303.112C231.2,302.141 230.981,301.4 230.544,300.888C230.106,300.376 229.525,300.12 228.8,300.12C228.064,300.12 227.477,300.376 227.04,300.888C226.613,301.4 226.4,302.141 226.4,303.112C226.4,304.072 226.613,304.813 227.04,305.336C227.477,305.848 228.064,306.104 228.8,306.104Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M234.49,307V299.224H235.754V300.536C235.989,300.067 236.314,299.709 236.73,299.464C237.146,299.208 237.632,299.08 238.186,299.08C239.402,299.08 240.181,299.608 240.522,300.664C240.757,300.173 241.104,299.789 241.562,299.512C242.021,299.224 242.549,299.08 243.146,299.08C244.874,299.08 245.738,300.104 245.738,302.152V307H244.442V302.216C244.442,301.501 244.314,300.979 244.058,300.648C243.813,300.307 243.402,300.136 242.826,300.136C242.197,300.136 241.696,300.36 241.322,300.808C240.949,301.256 240.762,301.853 240.762,302.6V307H239.466V302.216C239.466,301.501 239.338,300.979 239.082,300.648C238.837,300.307 238.426,300.136 237.85,300.136C237.21,300.136 236.704,300.36 236.33,300.808C235.968,301.256 235.786,301.853 235.786,302.6V307H234.49Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M251.665,307.144C250.439,307.144 249.468,306.787 248.753,306.072C248.039,305.347 247.681,304.365 247.681,303.128C247.681,302.328 247.841,301.624 248.161,301.016C248.481,300.397 248.919,299.923 249.473,299.592C250.039,299.251 250.689,299.08 251.425,299.08C252.481,299.08 253.308,299.421 253.905,300.104C254.503,300.776 254.801,301.704 254.801,302.888V303.384H248.945C248.988,304.269 249.244,304.947 249.713,305.416C250.183,305.875 250.833,306.104 251.665,306.104C252.135,306.104 252.583,306.035 253.009,305.896C253.436,305.747 253.841,305.507 254.225,305.176L254.673,306.088C254.321,306.419 253.873,306.68 253.329,306.872C252.785,307.053 252.231,307.144 251.665,307.144ZM251.457,300.04C250.721,300.04 250.14,300.269 249.713,300.728C249.287,301.187 249.036,301.789 248.961,302.536H253.681C253.649,301.747 253.441,301.133 253.057,300.696C252.684,300.259 252.151,300.04 251.457,300.04Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M256.677,307V299.224H257.941V300.552C258.197,300.061 258.56,299.693 259.029,299.448C259.498,299.203 260.026,299.08 260.613,299.08C262.458,299.08 263.381,300.104 263.381,302.152V307H262.085V302.232C262.085,301.507 261.941,300.979 261.653,300.648C261.376,300.307 260.928,300.136 260.309,300.136C259.594,300.136 259.024,300.36 258.597,300.808C258.181,301.245 257.973,301.832 257.973,302.568V307H256.677Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M272.328,307.144C271.795,307.144 271.315,307.043 270.888,306.84C270.472,306.627 270.141,306.339 269.896,305.976C269.651,305.613 269.528,305.208 269.528,304.76C269.528,304.195 269.672,303.747 269.96,303.416C270.259,303.085 270.744,302.851 271.416,302.712C272.099,302.563 273.027,302.488 274.2,302.488H274.712V301.992C274.712,301.331 274.573,300.856 274.296,300.568C274.029,300.269 273.597,300.12 273,300.12C272.531,300.12 272.078,300.189 271.64,300.328C271.203,300.456 270.755,300.669 270.296,300.968L269.848,300.024C270.253,299.736 270.744,299.507 271.32,299.336C271.907,299.165 272.467,299.08 273,299.08C274.003,299.08 274.744,299.325 275.224,299.816C275.715,300.307 275.96,301.069 275.96,302.104V307H274.744V305.656C274.541,306.115 274.227,306.477 273.8,306.744C273.384,307.011 272.893,307.144 272.328,307.144ZM272.536,306.168C273.176,306.168 273.699,305.949 274.104,305.512C274.509,305.064 274.712,304.499 274.712,303.816V303.336H274.216C273.352,303.336 272.669,303.379 272.168,303.464C271.677,303.539 271.331,303.677 271.128,303.88C270.936,304.072 270.84,304.339 270.84,304.68C270.84,305.117 270.989,305.475 271.288,305.752C271.597,306.029 272.013,306.168 272.536,306.168Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M278.365,307V299.224H279.629V300.552C279.885,300.061 280.247,299.693 280.717,299.448C281.186,299.203 281.714,299.08 282.301,299.08C284.146,299.08 285.069,300.104 285.069,302.152V307H283.773V302.232C283.773,301.507 283.629,300.979 283.341,300.648C283.063,300.307 282.615,300.136 281.997,300.136C281.282,300.136 280.711,300.36 280.285,300.808C279.869,301.245 279.661,301.832 279.661,302.568V307H278.365Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M290.418,307.144C289.725,307.144 289.117,306.979 288.594,306.648C288.082,306.317 287.682,305.853 287.394,305.256C287.117,304.648 286.978,303.933 286.978,303.112C286.978,302.28 287.117,301.565 287.394,300.968C287.682,300.36 288.082,299.896 288.594,299.576C289.117,299.245 289.725,299.08 290.418,299.08C291.026,299.08 291.56,299.213 292.018,299.48C292.488,299.747 292.834,300.115 293.058,300.584V295.72H294.354V307H293.09V305.576C292.866,306.067 292.52,306.451 292.05,306.728C291.581,307.005 291.037,307.144 290.418,307.144ZM290.69,306.104C291.416,306.104 291.997,305.848 292.434,305.336C292.872,304.813 293.09,304.072 293.09,303.112C293.09,302.141 292.872,301.4 292.434,300.888C291.997,300.376 291.416,300.12 290.69,300.12C289.954,300.12 289.368,300.376 288.93,300.888C288.504,301.4 288.29,302.141 288.29,303.112C288.29,304.072 288.504,304.813 288.93,305.336C289.368,305.848 289.954,306.104 290.69,306.104Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M23.443,331V323.224H24.707V324.536C24.942,324.067 25.267,323.709 25.683,323.464C26.099,323.208 26.585,323.08 27.139,323.08C28.355,323.08 29.134,323.608 29.475,324.664C29.71,324.173 30.057,323.789 30.515,323.512C30.974,323.224 31.502,323.08 32.099,323.08C33.827,323.08 34.691,324.104 34.691,326.152V331H33.395V326.216C33.395,325.501 33.267,324.979 33.011,324.648C32.766,324.307 32.355,324.136 31.779,324.136C31.15,324.136 30.649,324.36 30.275,324.808C29.902,325.256 29.715,325.853 29.715,326.6V331H28.419V326.216C28.419,325.501 28.291,324.979 28.035,324.648C27.79,324.307 27.379,324.136 26.803,324.136C26.163,324.136 25.657,324.36 25.283,324.808C24.921,325.256 24.739,325.853 24.739,326.6V331H23.443Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M40.618,331.144C39.392,331.144 38.421,330.787 37.707,330.072C36.992,329.347 36.634,328.365 36.634,327.128C36.634,326.328 36.794,325.624 37.114,325.016C37.435,324.397 37.872,323.923 38.426,323.592C38.992,323.251 39.643,323.08 40.379,323.08C41.435,323.08 42.261,323.421 42.859,324.104C43.456,324.776 43.755,325.704 43.755,326.888V327.384H37.898C37.941,328.269 38.197,328.947 38.667,329.416C39.136,329.875 39.786,330.104 40.618,330.104C41.088,330.104 41.536,330.035 41.963,329.896C42.389,329.747 42.794,329.507 43.179,329.176L43.626,330.088C43.275,330.419 42.826,330.68 42.283,330.872C41.738,331.053 41.184,331.144 40.618,331.144ZM40.41,324.04C39.674,324.04 39.093,324.269 38.667,324.728C38.24,325.187 37.989,325.789 37.915,326.536H42.634C42.603,325.747 42.395,325.133 42.011,324.696C41.637,324.259 41.104,324.04 40.41,324.04Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M45.63,331V323.224H46.894V324.552C47.15,324.061 47.513,323.693 47.982,323.448C48.451,323.203 48.979,323.08 49.566,323.08C51.411,323.08 52.334,324.104 52.334,326.152V331H51.038V326.232C51.038,325.507 50.894,324.979 50.606,324.648C50.329,324.307 49.881,324.136 49.262,324.136C48.548,324.136 47.977,324.36 47.55,324.808C47.134,325.245 46.926,325.832 46.926,326.568V331H45.63Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M61.953,331.144C61.036,331.144 60.348,330.904 59.889,330.424C59.431,329.933 59.201,329.229 59.201,328.312V324.232H57.681V323.224H59.201V320.84H60.497V323.224H62.961V324.232H60.497V328.184C60.497,328.792 60.625,329.256 60.881,329.576C61.137,329.885 61.553,330.04 62.129,330.04C62.3,330.04 62.471,330.019 62.641,329.976C62.812,329.933 62.967,329.891 63.105,329.848L63.329,330.84C63.191,330.915 62.988,330.984 62.721,331.048C62.455,331.112 62.199,331.144 61.953,331.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M67.581,331.144C66.824,331.144 66.168,330.979 65.613,330.648C65.058,330.317 64.626,329.853 64.317,329.256C64.018,328.648 63.869,327.933 63.869,327.112C63.869,326.291 64.018,325.581 64.317,324.984C64.626,324.376 65.058,323.907 65.613,323.576C66.168,323.245 66.824,323.08 67.581,323.08C68.328,323.08 68.978,323.245 69.533,323.576C70.098,323.907 70.53,324.376 70.829,324.984C71.138,325.581 71.293,326.291 71.293,327.112C71.293,327.933 71.138,328.648 70.829,329.256C70.53,329.853 70.098,330.317 69.533,330.648C68.978,330.979 68.328,331.144 67.581,331.144ZM67.581,330.104C68.306,330.104 68.888,329.848 69.325,329.336C69.762,328.813 69.981,328.072 69.981,327.112C69.981,326.141 69.762,325.4 69.325,324.888C68.888,324.376 68.306,324.12 67.581,324.12C66.845,324.12 66.258,324.376 65.821,324.888C65.394,325.4 65.181,326.141 65.181,327.112C65.181,328.072 65.394,328.813 65.821,329.336C66.258,329.848 66.845,330.104 67.581,330.104Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M77.765,331V324.232H76.229V323.224H77.765V322.952C77.765,321.917 78.021,321.133 78.533,320.6C79.056,320.067 79.856,319.768 80.933,319.704L81.525,319.672L81.605,320.68L80.933,320.712C80.272,320.755 79.792,320.952 79.493,321.304C79.205,321.645 79.061,322.141 79.061,322.792V323.224H83.861V331H82.565V324.232H79.061V331H77.765ZM82.421,321.288V319.832H84.005V321.288H82.421Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M86.286,331V323.224H87.55V324.552C87.806,324.061 88.169,323.693 88.638,323.448C89.108,323.203 89.636,323.08 90.222,323.08C92.068,323.08 92.99,324.104 92.99,326.152V331H91.694V326.232C91.694,325.507 91.55,324.979 91.262,324.648C90.985,324.307 90.537,324.136 89.918,324.136C89.204,324.136 88.633,324.36 88.206,324.808C87.79,325.245 87.582,325.832 87.582,326.568V331H86.286Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M98.34,331.144C97.647,331.144 97.039,330.979 96.516,330.648C96.004,330.317 95.604,329.853 95.316,329.256C95.039,328.648 94.9,327.933 94.9,327.112C94.9,326.28 95.039,325.565 95.316,324.968C95.604,324.36 96.004,323.896 96.516,323.576C97.039,323.245 97.647,323.08 98.34,323.08C98.948,323.08 99.481,323.213 99.94,323.48C100.409,323.747 100.756,324.115 100.98,324.584V319.72H102.276V331H101.012V329.576C100.788,330.067 100.441,330.451 99.972,330.728C99.503,331.005 98.959,331.144 98.34,331.144ZM98.612,330.104C99.338,330.104 99.919,329.848 100.356,329.336C100.793,328.813 101.012,328.072 101.012,327.112C101.012,326.141 100.793,325.4 100.356,324.888C99.919,324.376 99.338,324.12 98.612,324.12C97.876,324.12 97.289,324.376 96.852,324.888C96.425,325.4 96.212,326.141 96.212,327.112C96.212,328.072 96.425,328.813 96.852,329.336C97.289,329.848 97.876,330.104 98.612,330.104Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M112.05,331.144C111.292,331.144 110.636,330.979 110.082,330.648C109.527,330.317 109.095,329.853 108.786,329.256C108.487,328.648 108.338,327.933 108.338,327.112C108.338,326.291 108.487,325.581 108.786,324.984C109.095,324.376 109.527,323.907 110.082,323.576C110.636,323.245 111.292,323.08 112.05,323.08C112.796,323.08 113.447,323.245 114.002,323.576C114.567,323.907 114.999,324.376 115.298,324.984C115.607,325.581 115.762,326.291 115.762,327.112C115.762,327.933 115.607,328.648 115.298,329.256C114.999,329.853 114.567,330.317 114.002,330.648C113.447,330.979 112.796,331.144 112.05,331.144ZM112.05,330.104C112.775,330.104 113.356,329.848 113.794,329.336C114.231,328.813 114.45,328.072 114.45,327.112C114.45,326.141 114.231,325.4 113.794,324.888C113.356,324.376 112.775,324.12 112.05,324.12C111.314,324.12 110.727,324.376 110.29,324.888C109.863,325.4 109.65,326.141 109.65,327.112C109.65,328.072 109.863,328.813 110.29,329.336C110.727,329.848 111.314,330.104 112.05,330.104Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M120.75,331.144C119.833,331.144 119.145,330.904 118.686,330.424C118.227,329.933 117.998,329.229 117.998,328.312V324.232H116.478V323.224H117.998V320.84H119.294V323.224H121.758V324.232H119.294V328.184C119.294,328.792 119.422,329.256 119.678,329.576C119.934,329.885 120.35,330.04 120.926,330.04C121.097,330.04 121.267,330.019 121.438,329.976C121.609,329.933 121.763,329.891 121.902,329.848L122.126,330.84C121.987,330.915 121.785,330.984 121.518,331.048C121.251,331.112 120.995,331.144 120.75,331.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M123.224,331V319.72H124.52V324.504C124.776,324.035 125.133,323.683 125.592,323.448C126.061,323.203 126.584,323.08 127.16,323.08C129.005,323.08 129.928,324.104 129.928,326.152V331H128.632V326.232C128.632,325.507 128.488,324.979 128.2,324.648C127.923,324.307 127.475,324.136 126.856,324.136C126.141,324.136 125.571,324.36 125.144,324.808C124.728,325.245 124.52,325.832 124.52,326.568V331H123.224Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M135.822,331.144C134.595,331.144 133.624,330.787 132.91,330.072C132.195,329.347 131.838,328.365 131.838,327.128C131.838,326.328 131.998,325.624 132.318,325.016C132.638,324.397 133.075,323.923 133.63,323.592C134.195,323.251 134.846,323.08 135.582,323.08C136.638,323.08 137.464,323.421 138.062,324.104C138.659,324.776 138.958,325.704 138.958,326.888V327.384H133.102C133.144,328.269 133.4,328.947 133.87,329.416C134.339,329.875 134.99,330.104 135.822,330.104C136.291,330.104 136.739,330.035 137.166,329.896C137.592,329.747 137.998,329.507 138.382,329.176L138.83,330.088C138.478,330.419 138.03,330.68 137.486,330.872C136.942,331.053 136.387,331.144 135.822,331.144ZM135.614,324.04C134.878,324.04 134.296,324.269 133.87,324.728C133.443,325.187 133.192,325.789 133.118,326.536H137.838C137.806,325.747 137.598,325.133 137.214,324.696C136.84,324.259 136.307,324.04 135.614,324.04Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M140.833,331V323.224H142.097V324.616C142.513,323.677 143.367,323.16 144.657,323.064L145.121,323.016L145.217,324.136L144.401,324.232C143.665,324.296 143.105,324.531 142.721,324.936C142.337,325.331 142.145,325.875 142.145,326.568V331H140.833Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M150.885,331V319.72H151.989L156.325,327.864L160.629,319.72H161.733V331H160.517V322.184L156.757,329.256H155.893L152.085,322.2V331H150.885Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M167.869,331.144C166.642,331.144 165.671,330.787 164.957,330.072C164.242,329.347 163.885,328.365 163.885,327.128C163.885,326.328 164.045,325.624 164.365,325.016C164.685,324.397 165.122,323.923 165.677,323.592C166.242,323.251 166.893,323.08 167.629,323.08C168.685,323.08 169.511,323.421 170.109,324.104C170.706,324.776 171.005,325.704 171.005,326.888V327.384H165.149C165.191,328.269 165.447,328.947 165.917,329.416C166.386,329.875 167.037,330.104 167.869,330.104C168.338,330.104 168.786,330.035 169.213,329.896C169.639,329.747 170.045,329.507 170.429,329.176L170.877,330.088C170.525,330.419 170.077,330.68 169.533,330.872C168.989,331.053 168.434,331.144 167.869,331.144ZM167.661,324.04C166.925,324.04 166.343,324.269 165.917,324.728C165.49,325.187 165.239,325.789 165.165,326.536H169.885C169.853,325.747 169.645,325.133 169.261,324.696C168.887,324.259 168.354,324.04 167.661,324.04Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M172.912,331V323.224H174.176V324.536C174.411,324.067 174.736,323.709 175.152,323.464C175.568,323.208 176.053,323.08 176.608,323.08C177.824,323.08 178.603,323.608 178.944,324.664C179.179,324.173 179.525,323.789 179.984,323.512C180.443,323.224 180.971,323.08 181.568,323.08C183.296,323.08 184.16,324.104 184.16,326.152V331H182.864V326.216C182.864,325.501 182.736,324.979 182.48,324.648C182.235,324.307 181.824,324.136 181.248,324.136C180.619,324.136 180.117,324.36 179.744,324.808C179.371,325.256 179.184,325.853 179.184,326.6V331H177.888V326.216C177.888,325.501 177.76,324.979 177.504,324.648C177.259,324.307 176.848,324.136 176.272,324.136C175.632,324.136 175.125,324.36 174.752,324.808C174.389,325.256 174.208,325.853 174.208,326.6V331H172.912Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M190.519,331.144C189.901,331.144 189.357,331.005 188.887,330.728C188.429,330.451 188.082,330.072 187.847,329.592V331H186.583V319.72H187.879V324.568C188.103,324.109 188.45,323.747 188.919,323.48C189.389,323.213 189.922,323.08 190.519,323.08C191.213,323.08 191.815,323.245 192.327,323.576C192.85,323.896 193.25,324.36 193.527,324.968C193.815,325.565 193.959,326.28 193.959,327.112C193.959,327.933 193.815,328.648 193.527,329.256C193.25,329.853 192.85,330.317 192.327,330.648C191.815,330.979 191.213,331.144 190.519,331.144ZM190.247,330.104C190.973,330.104 191.554,329.848 191.991,329.336C192.429,328.813 192.647,328.072 192.647,327.112C192.647,326.141 192.429,325.4 191.991,324.888C191.554,324.376 190.973,324.12 190.247,324.12C189.522,324.12 188.941,324.376 188.503,324.888C188.066,325.4 187.847,326.141 187.847,327.112C187.847,328.072 188.066,328.813 188.503,329.336C188.941,329.848 189.522,330.104 190.247,330.104Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M199.415,331.144C198.189,331.144 197.218,330.787 196.503,330.072C195.789,329.347 195.431,328.365 195.431,327.128C195.431,326.328 195.591,325.624 195.911,325.016C196.231,324.397 196.669,323.923 197.223,323.592C197.789,323.251 198.439,323.08 199.175,323.08C200.231,323.08 201.058,323.421 201.655,324.104C202.253,324.776 202.551,325.704 202.551,326.888V327.384H196.695C196.738,328.269 196.994,328.947 197.463,329.416C197.933,329.875 198.583,330.104 199.415,330.104C199.885,330.104 200.333,330.035 200.759,329.896C201.186,329.747 201.591,329.507 201.975,329.176L202.423,330.088C202.071,330.419 201.623,330.68 201.079,330.872C200.535,331.053 199.981,331.144 199.415,331.144ZM199.207,324.04C198.471,324.04 197.89,324.269 197.463,324.728C197.037,325.187 196.786,325.789 196.711,326.536H201.431C201.399,325.747 201.191,325.133 200.807,324.696C200.434,324.259 199.901,324.04 199.207,324.04Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M204.427,331V323.224H205.691V324.616C206.107,323.677 206.96,323.16 208.251,323.064L208.715,323.016L208.811,324.136L207.995,324.232C207.259,324.296 206.699,324.531 206.315,324.936C205.931,325.331 205.739,325.875 205.739,326.568V331H204.427Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M212.707,331.144C212.099,331.144 211.523,331.059 210.979,330.888C210.435,330.717 209.987,330.477 209.635,330.168L210.051,329.256C210.435,329.565 210.857,329.795 211.315,329.944C211.785,330.083 212.259,330.152 212.739,330.152C213.347,330.152 213.806,330.04 214.115,329.816C214.425,329.592 214.579,329.288 214.579,328.904C214.579,328.605 214.478,328.371 214.275,328.2C214.073,328.019 213.753,327.88 213.315,327.784L211.859,327.464C210.537,327.176 209.875,326.493 209.875,325.416C209.875,324.712 210.153,324.147 210.707,323.72C211.273,323.293 212.009,323.08 212.915,323.08C213.449,323.08 213.955,323.165 214.435,323.336C214.926,323.496 215.331,323.736 215.651,324.056L215.219,324.968C214.91,324.68 214.547,324.461 214.131,324.312C213.726,324.152 213.321,324.072 212.915,324.072C212.318,324.072 211.865,324.189 211.555,324.424C211.246,324.659 211.091,324.968 211.091,325.352C211.091,325.949 211.486,326.333 212.275,326.504L213.731,326.808C214.414,326.957 214.931,327.197 215.283,327.528C215.635,327.848 215.811,328.285 215.811,328.84C215.811,329.555 215.529,330.12 214.963,330.536C214.398,330.941 213.646,331.144 212.707,331.144Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M221.896,331V323.224H223.16V324.552C223.416,324.061 223.778,323.693 224.248,323.448C224.717,323.203 225.245,323.08 225.832,323.08C227.677,323.08 228.6,324.104 228.6,326.152V331H227.304V326.232C227.304,325.507 227.16,324.979 226.872,324.648C226.594,324.307 226.146,324.136 225.528,324.136C224.813,324.136 224.242,324.36 223.816,324.808C223.4,325.245 223.192,325.832 223.192,326.568V331H221.896Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M234.494,331.144C233.267,331.144 232.296,330.787 231.582,330.072C230.867,329.347 230.51,328.365 230.51,327.128C230.51,326.328 230.67,325.624 230.99,325.016C231.31,324.397 231.747,323.923 232.302,323.592C232.867,323.251 233.518,323.08 234.254,323.08C235.31,323.08 236.136,323.421 236.734,324.104C237.331,324.776 237.63,325.704 237.63,326.888V327.384H231.774C231.816,328.269 232.072,328.947 232.542,329.416C233.011,329.875 233.662,330.104 234.494,330.104C234.963,330.104 235.411,330.035 235.838,329.896C236.264,329.747 236.67,329.507 237.054,329.176L237.502,330.088C237.15,330.419 236.702,330.68 236.158,330.872C235.614,331.053 235.059,331.144 234.494,331.144ZM234.286,324.04C233.55,324.04 232.968,324.269 232.542,324.728C232.115,325.187 231.864,325.789 231.79,326.536H236.51C236.478,325.747 236.27,325.133 235.886,324.696C235.512,324.259 234.979,324.04 234.286,324.04Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M241.953,331.144C241.42,331.144 240.94,331.043 240.513,330.84C240.097,330.627 239.766,330.339 239.521,329.976C239.276,329.613 239.153,329.208 239.153,328.76C239.153,328.195 239.297,327.747 239.585,327.416C239.884,327.085 240.369,326.851 241.041,326.712C241.724,326.563 242.652,326.488 243.825,326.488H244.337V325.992C244.337,325.331 244.198,324.856 243.921,324.568C243.654,324.269 243.222,324.12 242.625,324.12C242.156,324.12 241.703,324.189 241.265,324.328C240.828,324.456 240.38,324.669 239.921,324.968L239.473,324.024C239.878,323.736 240.369,323.507 240.945,323.336C241.532,323.165 242.092,323.08 242.625,323.08C243.628,323.08 244.369,323.325 244.849,323.816C245.34,324.307 245.585,325.069 245.585,326.104V331H244.369V329.656C244.166,330.115 243.852,330.477 243.425,330.744C243.009,331.011 242.518,331.144 241.953,331.144ZM242.161,330.168C242.801,330.168 243.324,329.949 243.729,329.512C244.134,329.064 244.337,328.499 244.337,327.816V327.336H243.841C242.977,327.336 242.294,327.379 241.793,327.464C241.302,327.539 240.956,327.677 240.753,327.88C240.561,328.072 240.465,328.339 240.465,328.68C240.465,329.117 240.614,329.475 240.913,329.752C241.222,330.029 241.638,330.168 242.161,330.168Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M247.99,331V323.224H249.254V324.616C249.67,323.677 250.523,323.16 251.814,323.064L252.278,323.016L252.374,324.136L251.558,324.232C250.822,324.296 250.262,324.531 249.878,324.936C249.494,325.331 249.302,325.875 249.302,326.568V331H247.99Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M257.66,331.144C257.041,331.144 256.497,331.005 256.028,330.728C255.569,330.451 255.223,330.072 254.988,329.592V331H253.724V319.72H255.02V324.568C255.244,324.109 255.591,323.747 256.06,323.48C256.529,323.213 257.063,323.08 257.66,323.08C258.353,323.08 258.956,323.245 259.468,323.576C259.991,323.896 260.391,324.36 260.668,324.968C260.956,325.565 261.1,326.28 261.1,327.112C261.1,327.933 260.956,328.648 260.668,329.256C260.391,329.853 259.991,330.317 259.468,330.648C258.956,330.979 258.353,331.144 257.66,331.144ZM257.388,330.104C258.113,330.104 258.695,329.848 259.132,329.336C259.569,328.813 259.788,328.072 259.788,327.112C259.788,326.141 259.569,325.4 259.132,324.888C258.695,324.376 258.113,324.12 257.388,324.12C256.663,324.12 256.081,324.376 255.644,324.888C255.207,325.4 254.988,326.141 254.988,327.112C254.988,328.072 255.207,328.813 255.644,329.336C256.081,329.848 256.663,330.104 257.388,330.104Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M263.839,333.88L265.167,330.872L261.887,323.224H263.295L265.871,329.528L268.463,323.224H269.823L265.199,333.88H263.839Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M270.172,331V329.368H271.804V331H270.172Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M221.064,38.818L57.934,38.818A11.463,11.463 0,0 0,46.471 50.282L46.471,50.282A11.463,11.463 0,0 0,57.934 61.745L221.064,61.745A11.463,11.463 0,0 0,232.527 50.282L232.527,50.282A11.463,11.463 0,0 0,221.064 38.818z"
      android:fillColor="#D6EEFF"/>
  <path
      android:pathData="M213.61,118.661L82.205,118.661A10.089,10.089 0,0 0,72.116 128.75L72.116,128.75A10.089,10.089 0,0 0,82.205 138.838L213.61,138.838A10.089,10.089 0,0 0,223.699 128.75L223.699,128.75A10.089,10.089 0,0 0,213.61 118.661z"
      android:fillColor="#D6EEFF"/>
  <path
      android:pathData="M198.225,79.313l-155.114,0l-0,21.691l155.114,0z"
      android:fillColor="#D6EEFF"/>
  <path
      android:pathData="M198.225,90.157m10.845,0a10.845,10.845 0,1 0,-21.691 0a10.845,10.845 0,1 0,21.691 0"
      android:fillColor="#D6EEFF"/>
  <path
      android:pathData="M43.111,90.158m10.845,0a10.845,10.845 0,1 0,-21.691 0a10.845,10.845 0,1 0,21.691 0"
      android:fillColor="#D6EEFF"/>
  <path
      android:pathData="M77.053,79.309C79.951,79.174 82.275,75.275 82.275,70.484C82.275,66.054 80.288,62.386 77.697,61.753H194.159C189.243,61.79 185.269,65.787 185.269,70.712C185.269,75.659 189.28,79.67 194.227,79.67C194.592,79.67 194.951,79.648 195.304,79.606V79.67H77.053V79.309ZM195.304,61.753H194.296C194.637,61.756 194.973,61.778 195.304,61.817V61.753Z"
      android:fillColor="#D6EEFF"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M191.165,101.006H114.995V101.007C119.87,101.008 123.822,104.959 123.822,109.834C123.822,114.68 119.918,118.613 115.084,118.662H191.072C186.238,118.613 182.334,114.68 182.334,109.834C182.334,104.959 186.287,101.007 191.162,101.007C191.163,101.007 191.164,101.007 191.165,101.007V101.006Z"
      android:fillColor="#D6EEFF"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M142.166,69.719L166.353,90.052L164.257,100.816L134.931,87.443L142.166,69.719V69.719Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M153.345,95.673L163.223,100.346L164.578,99.167L166.353,90.052L161.204,85.725L153.345,95.673V95.673Z"
      android:fillColor="#ECECEC"/>
  <path
      android:pathData="M140.365,34.747C142.08,34.747 143.469,33.354 143.469,31.636C143.469,29.919 142.08,28.526 140.365,28.526C138.651,28.526 137.261,29.919 137.261,31.636C137.261,33.354 138.651,34.747 140.365,34.747Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M141.684,42.452C143.547,42.452 145.058,40.939 145.058,39.072C145.058,37.205 143.547,35.692 141.684,35.692C139.821,35.692 138.31,37.205 138.31,39.072C138.31,40.939 139.821,42.452 141.684,42.452Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M142.635,42.317L144.19,36.813C143.458,35.996 142.375,35.586 141.287,35.715C140.199,35.843 139.241,36.494 138.719,37.459V37.459L140.393,42.197C141.104,42.49 141.892,42.532 142.63,42.317H142.635Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M141.684,42.452C143.547,42.452 145.058,40.939 145.058,39.072C145.058,37.205 143.547,35.692 141.684,35.692C139.821,35.692 138.31,37.205 138.31,39.072C138.31,40.939 139.821,42.452 141.684,42.452Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M118.264,44.363h12.671v18.347h-12.671z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M130.935,52.798L118.264,55.329V44.363H130.935V52.798V52.798Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M128.422,51.692C136.241,51.692 142.58,45.341 142.58,37.507C142.58,29.672 136.241,23.321 128.422,23.321C120.602,23.321 114.263,29.672 114.263,37.507C114.263,45.341 120.602,51.692 128.422,51.692Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M115.688,36.883C116.207,31.906 118.503,25.787 122.654,23.687C124.986,22.501 128.445,23.965 131.252,24.261C133.164,24.461 135.016,25.049 136.693,25.99C132.431,22.924 126.825,22.471 122.127,24.813C117.429,27.156 114.41,31.909 114.281,37.165C114.153,42.421 116.936,47.317 121.514,49.887C117.38,46.916 115.157,41.952 115.688,36.883V36.883Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M116.479,20.658C117.263,20.674 118.008,21.002 118.55,21.569C119.093,22.137 119.388,22.897 119.37,23.682"
      android:strokeLineJoin="round"
      android:strokeWidth="0.985523"
      android:fillColor="#00000000"
      android:strokeColor="#EDA257"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M120.515,18.973C119.839,19.373 119.35,20.027 119.156,20.789C118.961,21.551 119.078,22.359 119.479,23.036"
      android:strokeLineJoin="round"
      android:strokeWidth="0.985523"
      android:fillColor="#00000000"
      android:strokeColor="#EDA257"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M96.585,223.338H125.693L123.36,213.112L115.154,213.371L99.414,219.418C97.747,220.01 96.621,221.574 96.585,223.346L96.585,223.338Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M125.693,223.335L123.625,214.267L120.267,223.335H125.693V223.335Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M151.313,223.338H122.205L124.537,213.112L132.742,213.371L148.484,219.418C150.15,220.01 151.277,221.574 151.313,223.346V223.338Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M128.707,216.707V216.707C127.601,216.707 126.54,216.267 125.758,215.483C124.976,214.699 124.537,213.636 124.538,212.528V205.978L133.203,205.02L132.879,212.704C132.783,214.943 130.944,216.708 128.707,216.707V216.707Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M133.112,207.125L124.538,209.804V205.978L133.203,205.02L133.112,207.125V207.125Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M135.027,211.479C135.027,212.195 134.447,214.742 133.731,214.742C133.016,214.742 132.436,212.195 132.436,211.479C132.436,210.762 133.016,210.181 133.731,210.181C134.447,210.181 135.027,210.762 135.027,211.479V211.479Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M136.976,216.04C136.26,216.04 133.718,215.458 133.718,214.742C133.718,214.025 136.26,213.444 136.976,213.444C137.691,213.444 138.271,214.025 138.271,214.742C138.271,215.459 137.691,216.04 136.976,216.04V216.04Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M144.895,223.166C144.893,221.508 145.53,219.913 146.673,218.714"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M124.061,221.671H143.368"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M119.189,216.707V216.707C120.296,216.707 121.357,216.267 122.139,215.483C122.922,214.699 123.361,213.636 123.361,212.528V205.978L114.696,205.02L115.023,212.704C115.117,214.941 116.954,216.707 119.189,216.707V216.707Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M114.898,209.804L123.361,207.852V205.978L114.696,205.02L114.898,209.804V209.804Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M112.871,211.479C112.871,212.195 113.449,214.742 114.167,214.742C114.885,214.742 115.463,212.195 115.463,211.479C115.463,210.762 114.883,210.181 114.167,210.181C113.451,210.181 112.871,210.762 112.871,211.479H112.871Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M110.923,216.04C111.639,216.04 114.181,215.458 114.181,214.742C114.181,214.025 111.639,213.444 110.923,213.444C110.208,213.444 109.628,214.025 109.628,214.742C109.628,215.459 110.208,216.04 110.923,216.04V216.04Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.004,223.166C103.006,221.508 102.369,219.913 101.226,218.714"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M119.922,221.671H104.53"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M134.996,176.452L141.734,124.324H106.168L112.701,176.452C112.992,178.552 112.886,180.689 112.39,182.75C111.571,186.159 110.835,189.061 111.612,190.832C111.972,191.45 112.436,192.001 112.983,192.46C114.393,193.599 115.311,195.238 115.548,197.037L115.561,197.12C115.767,198.672 115.467,200.249 114.706,201.616C113.97,202.964 113.507,204.309 114.188,205.511C114.965,206.85 118.637,206.489 118.637,206.489H128.855C128.855,206.489 132.651,206.85 133.441,205.511C134.149,204.314 133.667,202.969 132.908,201.616C132.126,200.256 131.817,198.675 132.029,197.12L132.042,197.037C132.295,195.223 133.246,193.58 134.693,192.46C135.256,192.004 135.734,191.453 136.108,190.832C136.909,189.061 136.157,186.159 135.315,182.75C134.804,180.691 134.696,178.552 134.996,176.452V176.452Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M123.464,124.324H106.17L106.704,128.582L141.552,125.69L141.731,124.324H123.887H123.464V124.324Z"
      android:fillColor="#529ED6"/>
  <path
      android:pathData="M129.251,206.484H124.011"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M140.963,130.438L135.21,176.452C134.919,178.552 135.025,180.689 135.521,182.75C136.338,186.159 137.076,189.061 136.299,190.832C135.939,191.45 135.474,192.001 134.925,192.46C133.518,193.601 132.6,195.238 132.36,197.037V197.12C132.155,198.671 132.454,200.248 133.213,201.616C133.951,202.964 134.415,204.308 133.731,205.51"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M133.661,173.801L136.258,172.768"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M124.011,137.266V192.213"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M120.7,131.679L124.011,133.426L127.782,131.679"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M108.772,145.104L108.119,139.885L106.17,124.324"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M118.647,206.484C118.647,206.484 114.965,206.845 114.198,205.505C113.514,204.308 113.978,202.964 114.716,201.611C115.477,200.243 115.777,198.666 115.572,197.114L115.559,197.031C115.321,195.232 114.403,193.594 112.993,192.454C112.446,191.995 111.982,191.444 111.622,190.827C110.845,189.056 111.573,186.154 112.4,182.745C112.896,180.683 113.002,178.547 112.711,176.447L109.422,150.291"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.236,173.801L111.64,172.768"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M143.244,124.324H105.248L102.177,80.478C101.894,76.444 103.296,72.474 106.047,69.515C108.798,66.556 112.653,64.875 116.689,64.873H131.793C135.829,64.876 139.683,66.557 142.434,69.516C145.185,72.474 146.587,76.444 146.305,80.478L143.244,124.324Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M130.922,123.228L111.617,119.617L113.656,111.247L129.178,115.048L130.922,123.228V123.228Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M142.163,40.926C140.456,47.792 133.961,52.349 126.937,51.609C126.175,51.527 125.422,51.386 124.682,51.186"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M142.576,37.094C142.433,30.281 137.491,24.525 130.789,23.366C124.086,22.206 117.504,25.968 115.088,32.338C112.673,38.708 115.103,45.9 120.884,49.491L121.506,49.888"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.058,32.465C115.276,32.465 116.263,31.476 116.263,30.256C116.263,29.035 115.276,28.046 114.058,28.046C112.84,28.046 111.853,29.035 111.853,30.256C111.853,31.476 112.84,32.465 114.058,32.465Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M114.152,29.085C114.986,29.085 115.662,28.407 115.662,27.571C115.662,26.735 114.986,26.058 114.152,26.058C113.317,26.058 112.641,26.735 112.641,27.571C112.641,28.407 113.317,29.085 114.152,29.085Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M133.698,31.398C133.215,32.141 132.335,32.522 131.464,32.366"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M135.076,33.911C135.96,33.952 136.734,34.52 137.04,35.352"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M144.231,25.128C143.976,26.891 143.033,28.479 141.608,29.545C140.184,30.61 138.395,31.065 136.636,30.809C132.972,30.274 120.951,25.73 121.485,22.06C122.019,18.389 134.903,16.984 138.564,17.519C140.323,17.774 141.908,18.72 142.971,20.147C144.034,21.574 144.487,23.366 144.231,25.128Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M118.818,22.169C120.031,22.168 121.195,22.649 122.054,23.507C122.912,24.366 123.396,25.531 123.397,26.746C123.397,29.277 121.627,38.169 119.103,38.169C116.579,38.169 114.252,29.282 114.25,26.756C114.248,25.541 114.729,24.375 115.586,23.515C116.442,22.654 117.605,22.17 118.818,22.169Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M114.328,39.222C116.468,39.222 118.202,37.485 118.202,35.341C118.202,33.198 116.468,31.46 114.328,31.46C112.189,31.46 110.454,33.198 110.454,35.341C110.454,37.485 112.189,39.222 114.328,39.222Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M113.587,34.357C114.013,34.366 114.418,34.544 114.713,34.853C115.008,35.161 115.169,35.574 115.16,36.001"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M136.089,36.497C136.216,36.014 136.104,35.566 135.838,35.496C135.572,35.426 135.254,35.761 135.127,36.243C135,36.725 135.113,37.174 135.379,37.244C135.644,37.314 135.963,36.979 136.089,36.497Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M141.606,29.542C140.184,30.609 138.395,31.064 136.636,30.806C134.201,30.451 128.07,28.322 124.39,25.855"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#F3D5CB"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M138.344,29.466C137.744,29.571 137.132,29.579 136.53,29.49"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M143.019,24.643C142.724,26.683 141.357,28.408 139.44,29.157"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M136.506,43.854C135.671,43.53 134.896,43.067 134.213,42.486C132.329,40.876 129.507,42.67 130.145,45.066C130.536,46.544 131.518,47.922 133.514,48.776C136.732,50.17 140.541,48.418 141.824,45.105C141.975,44.717 142.088,44.314 142.161,43.903C140.333,44.58 138.322,44.563 136.506,43.854V43.854Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M126.204,41.19C127.187,41.348 128.19,41.337 129.171,41.159C131.879,40.666 133.589,43.971 131.64,45.915C130.438,47.117 128.733,47.883 126.338,47.598C122.47,47.136 119.785,43.322 120.399,39.42C120.473,38.962 120.59,38.513 120.749,38.078C122.126,39.749 124.067,40.856 126.204,41.19V41.19Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M133.941,35.469L133.742,41.206L130.907,41.269"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M133.941,35.469L133.742,41.206L130.907,41.269"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M131.254,34.71L132.488,36.787L132.428,36.735C131.459,35.916 130.191,35.54 128.933,35.697V35.697"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M129.769,48.026C128.616,47.726 127.646,46.948 127.1,45.887"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M125.527,25.375C127.897,25.375 129.818,23.45 129.818,21.076C129.818,18.701 127.897,16.776 125.527,16.776C123.157,16.776 121.236,18.701 121.236,21.076C121.236,23.45 123.157,25.375 125.527,25.375Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M133.026,20.847C134.338,20.847 135.402,19.781 135.402,18.467C135.402,17.152 134.338,16.086 133.026,16.086C131.714,16.086 130.65,17.152 130.65,18.467C130.65,19.781 131.714,20.847 133.026,20.847Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M138.929,23.952C141.384,23.952 143.374,21.959 143.374,19.5C143.374,17.041 141.384,15.047 138.929,15.047C136.475,15.047 134.485,17.041 134.485,19.5C134.485,21.959 136.475,23.952 138.929,23.952Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M121.746,31.385C123.167,31.385 124.319,30.231 124.319,28.807C124.319,27.383 123.167,26.229 121.746,26.229C120.325,26.229 119.173,27.383 119.173,28.807C119.173,30.231 120.325,31.385 121.746,31.385Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M121.627,34.106C122.516,34.106 123.236,33.384 123.236,32.493C123.236,31.603 122.516,30.881 121.627,30.881C120.739,30.881 120.018,31.603 120.018,32.493C120.018,33.384 120.739,34.106 121.627,34.106Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M120.967,24.661C122.044,24.661 122.918,23.786 122.918,22.706C122.918,21.627 122.044,20.751 120.967,20.751C119.889,20.751 119.015,21.627 119.015,22.706C119.015,23.786 119.889,24.661 120.967,24.661Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M115.799,38.932C115.564,39.029 115.319,39.102 115.068,39.15C113.34,39.488 111.602,38.616 110.837,37.026C110.072,35.437 110.473,33.531 111.813,32.387C113.154,31.242 115.096,31.148 116.541,32.156C117.986,33.165 118.57,35.022 117.963,36.679"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M126.74,33.807L127.647,34.487"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M127.735,35.284L126.237,35.188"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M135.952,36.175C136.037,36.175 136.107,36.105 136.107,36.019C136.107,35.933 136.037,35.863 135.952,35.863C135.866,35.863 135.796,35.933 135.796,36.019C135.796,36.105 135.866,36.175 135.952,36.175Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M115.887,56.176h17.419v10.774h-17.419z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M93.6,117.127V117.127C90.083,115.99 88.197,112.065 89.392,108.36L100.664,73.364C102.117,68.857 106.764,66.323 111.042,67.707V67.707C115.574,69.174 117.826,74.405 115.919,79.042L101.926,113.028C100.537,116.416 96.914,118.199 93.6,117.127Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M134.602,86.119V95.476"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M127.927,95.263V90.468"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M134.602,84.686C136.495,84.686 138.03,83.148 138.03,81.252C138.03,79.355 136.495,77.817 134.602,77.817C132.708,77.817 131.173,79.355 131.173,81.252C131.173,83.148 132.708,84.686 134.602,84.686Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M134.836,86.895C135.851,86.187 135.268,83.592 133.535,81.099C131.801,78.606 129.574,77.159 128.559,77.867C127.544,78.575 128.127,81.17 129.86,83.663C131.594,86.156 133.821,87.603 134.836,86.895Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M139.442,83.647C141.175,81.154 141.758,78.559 140.743,77.851C139.729,77.143 137.501,78.59 135.768,81.083C134.034,83.576 133.452,86.171 134.466,86.879C135.481,87.588 137.708,86.141 139.442,83.647Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M134.932,87.443C135.828,86.545 134.716,83.974 132.447,81.701C130.178,79.428 127.612,78.313 126.715,79.212C125.819,80.11 126.931,82.681 129.2,84.954C131.469,87.227 134.035,88.341 134.932,87.443Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M140.107,84.955C142.376,82.682 143.489,80.112 142.592,79.213C141.695,78.315 139.129,79.429 136.86,81.702C134.592,83.976 133.479,86.546 134.376,87.445C135.272,88.343 137.839,87.229 140.107,84.955Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M141.816,93.586C143.114,93.586 144.166,92.532 144.166,91.231C144.166,89.931 143.114,88.877 141.816,88.877C140.518,88.877 139.466,89.931 139.466,91.231C139.466,92.532 140.518,93.586 141.816,93.586Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M141.977,95.104C142.672,94.619 142.272,92.84 141.084,91.131C139.895,89.421 138.368,88.429 137.673,88.914C136.978,89.399 137.378,91.178 138.566,92.887C139.755,94.597 141.282,95.589 141.977,95.104Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M145.134,92.867C146.322,91.158 146.722,89.379 146.027,88.893C145.332,88.408 143.805,89.401 142.616,91.11C141.427,92.82 141.027,94.599 141.723,95.084C142.418,95.569 143.945,94.576 145.134,92.867Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M142.042,95.475C142.656,94.86 141.893,93.098 140.338,91.54C138.783,89.981 137.024,89.217 136.41,89.832C135.795,90.448 136.558,92.21 138.114,93.768C139.669,95.326 141.428,96.091 142.042,95.475Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M145.592,93.767C147.147,92.209 147.91,90.447 147.296,89.831C146.681,89.216 144.922,89.98 143.367,91.538C141.812,93.097 141.049,94.859 141.663,95.474C142.277,96.09 144.036,95.325 145.592,93.767Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M128.082,89.497C129.38,89.497 130.432,88.443 130.432,87.142C130.432,85.842 129.38,84.788 128.082,84.788C126.784,84.788 125.732,85.842 125.732,87.142C125.732,88.443 126.784,89.497 128.082,89.497Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M128.243,91.012C128.938,90.527 128.538,88.748 127.349,87.039C126.161,85.329 124.634,84.337 123.939,84.822C123.243,85.307 123.643,87.086 124.832,88.796C126.02,90.505 127.547,91.498 128.243,91.012Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M131.479,88.679C130.663,89.864 129.437,90.703 128.038,91.034C127.998,90.848 127.969,90.66 127.95,90.47C127.838,89.295 128.141,88.117 128.808,87.142C129.624,85.957 130.85,85.118 132.249,84.788C132.536,86.136 132.258,87.543 131.479,88.679Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M128.307,91.386C128.921,90.771 128.158,89.008 126.603,87.45C125.048,85.892 123.289,85.128 122.674,85.743C122.06,86.358 122.823,88.12 124.379,89.679C125.934,91.237 127.693,92.002 128.307,91.386Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M131.856,89.678C133.412,88.12 134.175,86.358 133.56,85.742C132.946,85.127 131.187,85.891 129.632,87.45C128.077,89.008 127.314,90.77 127.928,91.385C128.542,92.001 130.301,91.237 131.856,89.678Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M134.848,131.679L121.692,95.476H147.616L134.848,131.679V131.679Z"
      android:fillColor="#BDE3FF"/>
  <path
      android:pathData="M138.658,105.214L121.692,95.476H147.616L138.658,105.214V105.214Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M144.029,105.65L134.848,131.679"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M129.077,115.798L147.616,95.476L145.555,101.319"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M134.661,94.207C135.012,93.856 134.578,92.853 133.692,91.966C132.807,91.079 131.805,90.644 131.455,90.995C131.105,91.346 131.539,92.349 132.424,93.236C133.31,94.123 134.311,94.558 134.661,94.207Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M137.044,88.36C137.929,87.473 138.363,86.47 138.013,86.119C137.663,85.768 136.661,86.203 135.776,87.09C134.89,87.977 134.456,88.981 134.806,89.331C135.157,89.682 136.158,89.247 137.044,88.36Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M137.644,122.153L133.573,123.179C132.666,123.406 131.745,122.855 131.516,121.946V121.946C131.289,121.036 131.839,120.114 132.747,119.884L136.818,118.849C137.726,118.621 138.646,119.173 138.875,120.082V120.082C138.987,120.52 138.921,120.985 138.69,121.374C138.459,121.763 138.082,122.043 137.644,122.153V122.153Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M130.948,118.602C130.731,117.739 131.255,116.864 132.117,116.647L136.35,115.581C137.214,115.364 138.09,115.888 138.307,116.752L138.349,116.919C138.565,117.782 138.042,118.657 137.179,118.874L132.946,119.94C132.083,120.157 131.206,119.633 130.99,118.769L130.948,118.602Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M130.168,115.489C129.951,114.626 130.475,113.751 131.337,113.534L135.57,112.468C136.434,112.25 137.31,112.775 137.527,113.638L137.569,113.806C137.785,114.668 137.262,115.543 136.399,115.76L132.166,116.827C131.302,117.044 130.426,116.52 130.21,115.656L130.168,115.489Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M127.151,115.292C126.583,114.608 126.676,113.594 127.358,113.024L130.715,110.226C131.399,109.655 132.417,109.748 132.986,110.433L133.095,110.565C133.663,111.248 133.57,112.263 132.888,112.832L129.531,115.631C128.847,116.201 127.829,116.108 127.26,115.423L127.151,115.292Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M130.396,111.747C130.13,110.899 130.602,109.995 131.45,109.729L135.615,108.419C136.465,108.152 137.37,108.625 137.636,109.475L137.688,109.639C137.954,110.488 137.482,111.392 136.633,111.658L132.468,112.967C131.619,113.235 130.714,112.762 130.448,111.912L130.396,111.747Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M127.12,115.383C126.652,114.203 127.228,112.866 128.408,112.397L128.633,112.307C129.815,111.837 131.154,112.414 131.623,113.597L134.681,121.307C135.149,122.487 134.573,123.824 133.393,124.293L133.168,124.383C131.986,124.853 130.647,124.276 130.178,123.093L127.12,115.383Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M127.207,115.364L129.358,121.019"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M126.281,122.361L111.617,119.617L113.656,111.247L125.628,114.178L126.281,122.361V122.361Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M122.347,122.361L95.489,117.639C93.863,117.352 92.436,116.386 91.564,114.982C90.692,113.578 90.457,111.869 90.918,110.281V110.281C91.82,107.195 95.049,105.426 98.129,106.33L124.801,113.989L122.347,122.361Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M133.56,112.968L135.493,112.483C135.929,112.372 136.391,112.439 136.777,112.67C137.163,112.901 137.441,113.276 137.551,113.713V113.713C137.66,114.15 137.592,114.613 137.362,115C137.131,115.386 136.756,115.665 136.32,115.775L132.251,116.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M138.333,116.826V116.826C138.443,117.263 138.375,117.726 138.144,118.112C137.913,118.499 137.538,118.778 137.102,118.888L133.29,119.747"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M133.29,123.228L137.602,122.112"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M89.392,108.36L100.667,73.364"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M111.871,88.877L103.566,109.054"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M104.892,107.535L121.269,112.935"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M124.975,113.576L123.05,119.965"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M104.896,119.292L122.348,122.361L105.199,123.612L104.896,119.292V119.292Z"
      android:fillColor="#E7E7E7"/>
  <path
      android:pathData="M104.548,124.324H129.357"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M123.949,197.34V206.484"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M110.529,88.876L113.607,88.302"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M91.604,113.205C92.023,113.925 91.78,114.849 91.06,115.269C90.341,115.688 89.419,115.444 89,114.723"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M131.699,56.173H133.306V66.947"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M115.887,66.947V56.173H129.769"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M112.628,65.449L115.888,64.94"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M136.074,65.508C135.69,65.353 133.306,64.948 133.306,64.948"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M118.238,58.164V65.693"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#A1D9FF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M126.714,58.164V65.693"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#A1D9FF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M130.433,58.164V62.707"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#A1D9FF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M121.218,61.173V65.693"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#A1D9FF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M121.218,58.164V59.096"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#A1D9FF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M124.252,65.693V60.849"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#A1D9FF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M143.879,84.102V89.032"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M149.204,93.952L158.763,97.924"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M143.146,70.312L145.059,72.786"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M141.487,44.888C140.69,46.79 138.931,48.114 136.885,48.351"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M122.855,41.172C122.257,40.829 121.726,40.38 121.288,39.848"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M130.821,46.255L131.086,47.657"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#F3D5CB"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M198.93,197.039L206.349,207.566L204.131,212.473L193.151,200.362L198.93,197.039V197.039Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M198.938,206.746L202.008,201.409L200.65,199.482V199.482L196.072,203.584L198.938,206.746V206.746Z"
      android:fillColor="#B76452"/>
  <path
      android:pathData="M154.028,146.788L151.608,145.031C149.869,143.768 148.635,141.928 148.125,139.838C148.006,139.285 147.597,138.84 147.057,138.676C146.517,138.511 145.93,138.653 145.524,139.047C145.118,139.441 144.957,140.024 145.104,140.57C145.784,143.384 147.442,145.862 149.781,147.562L152.204,149.322C152.899,149.762 153.816,149.584 154.297,148.916C154.777,148.247 154.655,147.319 154.018,146.799L154.028,146.788Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M152.318,148.411C151.945,149.181 151.018,149.502 150.249,149.128L143.164,145.683C142.396,145.31 142.076,144.385 142.448,143.617L142.52,143.468C142.893,142.699 143.82,142.377 144.589,142.751L151.674,146.197C152.442,146.57 152.762,147.495 152.39,148.263L152.318,148.411Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M151.6,146.163L144.658,142.788C144.27,142.598 143.823,142.571 143.415,142.712C143.007,142.854 142.672,143.152 142.484,143.541V143.541"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M152.345,149.217C152.177,150.056 151.361,150.599 150.523,150.431L142.802,148.879C141.964,148.711 141.422,147.896 141.589,147.059L141.622,146.896C141.79,146.058 142.606,145.514 143.444,145.683L151.165,147.234C152.002,147.402 152.545,148.217 152.377,149.055L152.345,149.217Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M156.418,150.697C155.751,152.074 154.093,152.648 152.718,151.979L150.694,150.995C149.321,150.327 148.748,148.674 149.414,147.3L149.542,147.035C150.209,145.658 151.867,145.084 153.242,145.753L155.266,146.737C156.639,147.404 157.212,149.058 156.546,150.432L156.418,150.697Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M144.787,145.939L150.605,147.243"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M151.6,147.279L152.978,137.43L158.816,139.899L155.842,149.608L151.6,147.279V147.279Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M207.248,101.543L209.126,94.534L213.555,94.308L212.669,101.413L207.248,101.543V101.543Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M208.482,96.94L209.127,94.534L213.555,94.308L212.998,98.76L208.482,96.94V96.94Z"
      android:fillColor="#B76452"/>
  <path
      android:pathData="M177.137,92.095V92.095C174.776,92.095 172.861,90.177 172.861,87.812V77.463H181.413V87.812C181.413,90.177 179.498,92.095 177.137,92.095Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M172.862,81.356L181.413,84.258V77.477H172.862V81.356V81.356Z"
      android:fillColor="#B76452"/>
  <path
      android:pathData="M172.862,79.754V87.501"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M181.439,80.348V87.204"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M159.941,73.403C161.023,73.403 161.9,72.525 161.9,71.441C161.9,70.357 161.023,69.478 159.941,69.478C158.86,69.478 157.982,70.357 157.982,71.441C157.982,72.525 158.86,73.403 159.941,73.403Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M159.804,71.779C159.767,71.565 159.647,71.376 159.471,71.252C159.294,71.127 159.075,71.079 158.863,71.116"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M161.721,76.438C161.154,77.545 159.869,78.075 158.688,77.689C157.508,77.302 156.783,76.114 156.978,74.885C157.173,73.657 158.23,72.752 159.472,72.752"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M173.229,81.249C180.126,81.249 185.717,75.647 185.717,68.738C185.717,61.828 180.126,56.227 173.229,56.227C166.333,56.227 160.742,61.828 160.742,68.738C160.742,75.647 166.333,81.249 173.229,81.249Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M161.641,68.743C161.641,62.012 166.958,56.487 173.673,56.243C173.523,56.243 173.372,56.243 173.219,56.243C166.323,56.243 160.732,61.844 160.732,68.753C160.732,75.663 166.323,81.264 173.219,81.264C173.372,81.264 173.523,81.264 173.673,81.251C166.955,81.008 161.636,75.478 161.641,68.743Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M199.054,223.063H205.792L209.484,216.663C209.866,216.002 209.644,215.156 208.987,214.768L208.09,214.236C207.422,213.856 207.14,213.037 207.432,212.325C207.602,211.934 207.932,211.634 208.338,211.503C208.743,211.372 209.185,211.422 209.552,211.64L210.534,212.219C210.84,212.4 211.205,212.453 211.549,212.364C211.893,212.275 212.188,212.053 212.369,211.746L213.628,209.609L205.421,206.38L202.395,218.343L200.84,219.503C199.718,220.341 199.057,221.661 199.057,223.063H199.054Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M203.102,215.726C204.004,215.987 204.946,215.467 205.208,214.563L206.73,209.293C207.04,208.216 206.584,207.065 205.62,206.494L205.343,206.235L203.065,215.719L203.102,215.726Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M180.12,201.912L178.915,214.742L183.525,216.757L186.782,201.912H180.12V201.912Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M180.119,201.912L179.684,206.528L186.273,204.22L186.781,201.912H180.119V201.912Z"
      android:fillColor="#B76452"/>
  <path
      android:pathData="M198.069,223.473H185.914C185.026,223.472 184.307,222.75 184.307,221.861V221.051C184.32,220.28 183.76,219.62 182.998,219.509C182.576,219.458 182.152,219.59 181.833,219.873C181.514,220.156 181.332,220.562 181.332,220.989V221.767C181.332,222.708 180.571,223.471 179.632,223.471H177.513L178.915,214.748L195.113,219.514C196.867,220.029 198.071,221.642 198.069,223.473V223.473Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M180.488,132.554L180.091,150.283L178.915,203.244H187.129L201.208,150.574C202.157,147.026 201.9,143.263 200.477,139.878L197.679,133.59C196.385,130.684 194.504,128.079 192.154,125.939V125.939L180.488,132.554Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M169.873,128.582L167.341,133.429V133.429C165.229,138.435 163.962,143.759 163.592,149.182L162.343,167.435C162.111,170.815 163.389,174.124 165.831,176.467L196.079,205.505L201.78,200.313L182.156,165.426L186.258,132.554L169.873,128.582Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M157.915,147.196L151.017,141.711L159.931,91.345C160.358,88.921 163.017,87.368 165.867,87.874V87.874C168.718,88.381 170.679,90.754 170.249,93.178L168.176,102.288L157.915,147.196Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M207.367,123.576V123.576C205.779,124.537 203.874,124.355 203.022,123.158L183.463,95.728C182.188,93.939 183.121,91.213 185.499,89.777V89.777C187.777,88.399 190.506,88.739 191.589,90.556L208.951,119.391C209.736,120.695 209.028,122.572 207.367,123.576Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M186.798,133.953H174.953C171.642,133.951 168.757,131.691 167.957,128.472L160.157,97.14C159.556,94.728 160.097,92.174 161.624,90.216C163.152,88.257 165.494,87.113 167.975,87.113H184.611C189.804,87.113 194.015,91.331 194.015,96.535V126.725C194.015,128.642 193.254,130.481 191.901,131.837C190.547,133.192 188.712,133.953 186.798,133.953Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M183.911,88.557C183.909,88.06 183.806,87.569 183.608,87.114L177.01,86.95L170.174,87.114C169.978,87.57 169.875,88.061 169.874,88.557C169.874,91.299 173.014,93.521 176.891,93.521C180.768,93.521 183.911,91.299 183.911,88.557Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M176.891,93.521C180.778,93.521 183.911,91.299 183.911,88.557"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M169.874,88.557C169.905,89.709 170.418,90.795 171.289,91.548"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M173.281,87.23H167.171"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M205.017,62.772C205.017,62.772 205.634,65.08 207.305,66.3C207.86,66.685 208.491,66.944 209.155,67.061C209.702,67.158 210.155,67.54 210.344,68.062C210.534,68.585 210.432,69.169 210.075,69.595C209.115,70.745 207.822,71.569 206.375,71.952C202.566,72.92 198.272,70.719 198.199,67.977C198.127,65.236 198.8,61.516 198.8,61.516L205.017,62.772Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M169.7,53.12C171.312,53.12 172.806,53.966 173.636,55.35C174.365,53.364 176.319,52.104 178.425,52.26C180.531,52.417 182.278,53.953 182.706,56.025C185.141,55.061 187.92,55.797 189.562,57.841C191.204,59.884 191.328,62.761 189.868,64.938C188.408,67.116 185.704,68.09 183.194,67.34C180.685,66.59 178.954,64.292 178.923,61.669C178.643,61.72 178.359,61.746 178.075,61.747C176.397,61.75 174.844,60.86 173.994,59.41C173.152,61.577 170.831,62.776 168.581,62.206C166.331,61.636 164.857,59.477 165.143,57.169C165.43,54.862 167.387,53.13 169.708,53.13L169.7,53.12Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M178.067,61.747C180.682,61.747 182.802,59.623 182.802,57.004C182.802,54.384 180.682,52.261 178.067,52.261C175.453,52.261 173.333,54.384 173.333,57.004C173.333,59.623 175.453,61.747 178.067,61.747Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M184.896,67.593C188.201,67.593 190.879,64.91 190.879,61.599C190.879,58.288 188.201,55.604 184.896,55.604C181.592,55.604 178.913,58.288 178.913,61.599C178.913,64.91 181.592,67.593 184.896,67.593Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M186.259,73.645C187.341,73.645 188.218,72.766 188.218,71.682C188.218,70.598 187.341,69.719 186.259,69.719C185.177,69.719 184.3,70.598 184.3,71.682C184.3,72.766 185.177,73.645 186.259,73.645Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M164.924,67.471C164.924,68.171 165.49,68.738 166.189,68.738C166.887,68.738 167.453,68.171 167.453,67.471"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M166.188,71.988C166.188,72.767 167.158,73.393 168.352,73.393C169.547,73.393 170.516,72.765 170.516,71.988"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M169.508,67.471C169.508,68.171 170.074,68.738 170.772,68.738C171.471,68.738 172.037,68.171 172.037,67.471"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M187.129,71.407C186.712,71.24 186.239,71.443 186.072,71.861"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M169.895,64.244C170.18,64.972 170.874,65.457 171.654,65.475"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M166.028,65.501C166.808,65.459 167.487,64.954 167.752,64.218"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M173.058,76.994C175.091,76.994 176.738,75.343 176.738,73.307"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M185.722,70.301C186.305,65.681 184.278,61.119 180.462,58.461C176.645,55.803 171.671,55.488 167.551,57.644C163.431,59.801 160.848,64.071 160.846,68.728C160.846,69.073 160.859,69.416 160.887,69.753"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M171.229,73.289C171.707,73.289 172.094,72.901 172.094,72.422C172.094,71.943 171.707,71.555 171.229,71.555"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M163.4,64.758C165.146,64.758 166.562,63.34 166.562,61.591C166.562,59.841 165.146,58.423 163.4,58.423C161.654,58.423 160.239,59.841 160.239,61.591C160.239,63.34 161.654,64.758 163.4,64.758Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M160.846,69.183C162.729,69.183 164.256,67.653 164.256,65.766C164.256,63.879 162.729,62.35 160.846,62.35C158.963,62.35 157.436,63.879 157.436,65.766C157.436,67.653 158.963,69.183 160.846,69.183Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M185.228,70.34C187.356,70.34 189.081,68.612 189.081,66.48C189.081,64.348 187.356,62.619 185.228,62.619C183.099,62.619 181.374,64.348 181.374,66.48C181.374,68.612 183.099,70.34 185.228,70.34Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M185.228,70.34C187.356,70.34 189.081,68.612 189.081,66.48C189.081,64.348 187.356,62.619 185.228,62.619C183.099,62.619 181.374,64.348 181.374,66.48C181.374,68.612 183.099,70.34 185.228,70.34Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M184.1,76.472C184.667,77.579 185.952,78.109 187.133,77.723C188.313,77.337 189.038,76.148 188.843,74.919C188.648,73.691 187.591,72.786 186.349,72.786"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M166.352,61.173C167.599,62.54 169.556,62.999 171.279,62.329C173.002,61.659 174.138,59.998 174.139,58.146C174.139,58.037 174.139,57.925 174.126,57.816"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#CB765E"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M181.791,65.15C180.965,66.544 181.094,68.306 182.115,69.563"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#CB765E"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M173.646,56.175C176.902,56.175 179.542,53.531 179.542,50.269C179.542,47.007 176.902,44.363 173.646,44.363C170.391,44.363 167.751,47.007 167.751,50.269C167.751,53.531 170.391,56.175 173.646,56.175Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M165.746,56.228C167.825,56.228 169.511,54.539 169.511,52.455C169.511,50.372 167.825,48.683 165.746,48.683C163.666,48.683 161.981,50.372 161.981,52.455C161.981,54.539 163.666,56.228 165.746,56.228Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M162.281,61.822C164.35,61.822 166.028,60.142 166.028,58.069C166.028,55.995 164.35,54.314 162.281,54.314C160.212,54.314 158.534,55.995 158.534,58.069C158.534,60.142 160.212,61.822 162.281,61.822Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M159.887,64.719C161.208,64.719 162.278,63.646 162.278,62.323C162.278,61 161.208,59.927 159.887,59.927C158.566,59.927 157.495,61 157.495,62.323C157.495,63.646 158.566,64.719 159.887,64.719Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M162.852,56.043C163.758,56.043 164.492,55.307 164.492,54.399C164.492,53.492 163.758,52.756 162.852,52.756C161.946,52.756 161.211,53.492 161.211,54.399C161.211,55.307 161.946,56.043 162.852,56.043Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M180.817,56.342C183.069,56.342 184.895,54.512 184.895,52.255C184.895,49.999 183.069,48.169 180.817,48.169C178.564,48.169 176.738,49.999 176.738,52.255C176.738,54.512 178.564,56.342 180.817,56.342Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M185.178,57.817C186.491,57.817 187.554,56.751 187.554,55.436C187.554,54.121 186.491,53.055 185.178,53.055C183.866,53.055 182.802,54.121 182.802,55.436C182.802,56.751 183.866,57.817 185.178,57.817Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M188.596,48.66C191.079,48.66 193.092,46.643 193.092,44.156C193.092,41.668 191.079,39.651 188.596,39.651C186.113,39.651 184.1,41.668 184.1,44.156C184.1,46.643 186.113,48.66 188.596,48.66Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M195.787,46.744C198.721,46.744 201.099,44.361 201.099,41.422C201.099,38.482 198.721,36.1 195.787,36.1C192.853,36.1 190.475,38.482 190.475,41.422C190.475,44.361 192.853,46.744 195.787,46.744Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M203.431,51.991C206.222,51.991 208.484,49.724 208.484,46.928C208.484,44.132 206.222,41.866 203.431,41.866C200.641,41.866 198.378,44.132 198.378,46.928C198.378,49.724 200.641,51.991 203.431,51.991Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M202.679,43.506C204.122,43.506 205.291,42.335 205.291,40.889C205.291,39.444 204.122,38.272 202.679,38.272C201.237,38.272 200.067,39.444 200.067,40.889C200.067,42.335 201.237,43.506 202.679,43.506Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M187.129,57.121C190.438,57.121 193.12,54.433 193.12,51.118C193.12,47.804 190.438,45.116 187.129,45.116C183.82,45.116 181.138,47.804 181.138,51.118C181.138,54.433 183.82,57.121 187.129,57.121Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M202.328,58.912C205.012,58.912 207.189,56.731 207.189,54.041C207.189,51.351 205.012,49.171 202.328,49.171C199.643,49.171 197.466,51.351 197.466,54.041C197.466,56.731 199.643,58.912 202.328,58.912Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M195.481,52.694C198.013,52.694 200.065,50.638 200.065,48.101C200.065,45.565 198.013,43.509 195.481,43.509C192.95,43.509 190.897,45.565 190.897,48.101C190.897,50.638 192.95,52.694 195.481,52.694Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M195.732,65.641C198.696,65.641 201.099,63.234 201.099,60.264C201.099,57.295 198.696,54.888 195.732,54.888C192.768,54.888 190.365,57.295 190.365,60.264C190.365,63.234 192.768,65.641 195.732,65.641Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M198.067,56.832C199.742,56.832 201.099,55.472 201.099,53.795C201.099,52.117 199.742,50.757 198.067,50.757C196.393,50.757 195.035,52.117 195.035,53.795C195.035,55.472 196.393,56.832 198.067,56.832Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M203.551,63.11C205.56,63.11 207.189,61.478 207.189,59.465C207.189,57.452 205.56,55.82 203.551,55.82C201.541,55.82 199.912,57.452 199.912,59.465C199.912,61.478 201.541,63.11 203.551,63.11Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M198.681,70.501C200.695,70.501 202.327,68.865 202.327,66.848C202.327,64.831 200.695,63.195 198.681,63.195C196.668,63.195 195.035,64.831 195.035,66.848C195.035,68.865 196.668,70.501 198.681,70.501Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M203.032,65.716C204.755,65.716 206.152,64.317 206.152,62.591C206.152,60.864 204.755,59.465 203.032,59.465C201.309,59.465 199.912,60.864 199.912,62.591C199.912,64.317 201.309,65.716 203.032,65.716Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M193.097,60.225C195.842,60.225 198.067,57.996 198.067,55.246C198.067,52.496 195.842,50.267 193.097,50.267C190.352,50.267 188.127,52.496 188.127,55.246C188.127,57.996 190.352,60.225 193.097,60.225Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M163.25,109.575L166.37,122.112"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#262944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M179.195,190.609L181.043,148.035"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M178.176,144.977L181.04,148.035L184.408,145.784"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M193.46,179.559L189.109,196.148"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M193.581,129.192C192.544,132.05 189.833,133.952 186.797,133.953H174.953"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#818181"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M191.188,106.561L197.228,115.03"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M205.453,124.526V124.526C203.116,124.2 201.484,122.039 201.807,119.697L207.132,100.284L213.336,98.819L210.27,120.886C209.94,123.222 207.785,124.85 205.453,124.526V124.526Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M205.663,105.65L203.203,114.614"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M213.68,85.715C214.309,85.695 214.836,86.188 214.857,86.817L215.069,93.216C215.09,93.844 214.597,94.371 213.968,94.392L213.85,94.396C213.221,94.417 212.694,93.924 212.673,93.295L212.461,86.896C212.441,86.267 212.934,85.74 213.563,85.719L213.68,85.715Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M213.68,85.715C214.309,85.695 214.836,86.188 214.857,86.817L215.069,93.216C215.09,93.844 214.597,94.371 213.968,94.392L213.85,94.396C213.221,94.417 212.694,93.924 212.673,93.295L212.461,86.896C212.441,86.267 212.934,85.74 213.563,85.719L213.68,85.715Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M212.69,89.772L210.267,88.014C208.528,86.747 207.294,84.902 206.787,82.809C206.686,82.238 206.277,81.771 205.725,81.596C205.174,81.421 204.571,81.567 204.16,81.976C203.75,82.384 203.6,82.987 203.771,83.541C204.447,86.353 206.101,88.831 208.435,90.532L210.858,92.293C211.553,92.733 212.47,92.555 212.951,91.886C213.431,91.218 213.309,90.29 212.672,89.769L212.69,89.772Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M212.69,89.772L210.267,88.014C208.528,86.747 207.294,84.902 206.787,82.809C206.686,82.238 206.277,81.771 205.725,81.596C205.174,81.421 204.571,81.567 204.16,81.976C203.75,82.384 203.6,82.987 203.771,83.541C204.447,86.353 206.101,88.831 208.435,90.532L210.858,92.293C211.122,92.484 211.441,92.588 211.767,92.589"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M210.985,91.403C210.612,92.173 209.685,92.494 208.916,92.12L201.831,88.675C201.063,88.301 200.743,87.377 201.115,86.609L201.187,86.46C201.56,85.69 202.487,85.369 203.256,85.743L210.341,89.188C211.109,89.562 211.429,90.486 211.057,91.254L210.985,91.403Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M210.268,89.154L203.325,85.779C202.938,85.59 202.49,85.563 202.083,85.704C201.675,85.845 201.34,86.143 201.151,86.532V86.532"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M211.012,92.212C210.844,93.051 210.028,93.594 209.19,93.426L201.469,91.874C200.632,91.706 200.089,90.891 200.257,90.054L200.289,89.891C200.457,89.053 201.273,88.509 202.111,88.678L209.832,90.229C210.669,90.397 211.212,91.212 211.044,92.05L211.012,92.212Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M214.648,95.091C213.981,96.467 212.324,97.042 210.948,96.373L208.924,95.388C207.551,94.721 206.979,93.068 207.644,91.694L207.773,91.428C208.44,90.052 210.097,89.477 211.473,90.146L213.497,91.131C214.87,91.798 215.442,93.451 214.777,94.825L214.648,95.091Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M200.285,89.959V89.959C200.108,90.841 200.679,91.7 201.56,91.878L209.124,93.407"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M203.452,88.931L209.269,90.234"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M210.821,96.314C212.267,97.015 214.006,96.411 214.708,94.964V94.964C215.239,93.867 215.032,92.555 214.19,91.675"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M207.766,122.818C207.64,123.342 207.245,123.759 206.729,123.912C206.213,124.065 205.655,123.93 205.265,123.559C204.875,123.188 204.712,122.636 204.838,122.112"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M191.188,124.192L194.209,122.112"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M192.698,125.233L194.208,124.565"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M163.25,172.768H164.81"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M187.554,182.607H190.342"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M188.127,217.502C188.016,217.94 187.734,218.316 187.345,218.546C186.956,218.776 186.491,218.841 186.054,218.727L180.755,217.359C179.672,217.078 178.915,216.099 178.915,214.978V214.752L188.137,217.465L188.127,217.502Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M177.902,201.912L185.287,200.975"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M204.32,210.461L203.576,213.717"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M182.485,215.794L185.59,216.705"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M179.903,204.22L179.195,211.749"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M204.896,205.505L206.349,207.166"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M205.624,221.461L209.321,214.742"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M184.038,222.198H190.793"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M210.565,212.234C211.201,212.598 212.01,212.38 212.379,211.746L213.638,209.61L210.99,208.571L210.565,212.234Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M178.083,219.909L177.513,223.465H179.632C180.571,223.465 181.332,222.703 181.332,221.762V221.295L178.083,219.909Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M179.62,79.489C185.27,76.098 187.302,68.883 184.255,63.033C181.208,57.184 174.138,54.728 168.13,57.432C162.123,60.136 159.261,67.063 161.603,73.23C163.945,79.397 170.679,82.666 176.961,80.686"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M151.698,142.256L160.91,91.397C161.169,89.959 162.206,88.827 163.603,88.251C164.139,88.03 164.085,87.864 163.504,88.012C161.747,88.461 160.395,89.717 160.086,91.387L151.032,141.599L151.698,142.256Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M151.017,141.71L159.931,91.345C160.239,89.595 161.708,88.3 163.574,87.903"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M206.362,101.543L211.697,100.092"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#262944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M155.771,149.232C156.241,149.65 156.283,150.369 155.866,150.839L151.622,155.623C151.204,156.094 150.482,156.137 150.012,155.718L149.922,155.638C149.453,155.22 149.41,154.501 149.827,154.031L154.072,149.247C154.49,148.775 155.211,148.733 155.682,149.152L155.771,149.232Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M152.592,141.71L158.816,145.94"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#262944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M153.116,150.322L151.24,152.435"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M171.573,111.79C168.887,111.788 166.613,109.802 166.245,107.135C166.209,107.372 166.19,107.612 166.188,107.852C166.187,110.385 168.048,112.532 170.552,112.885C173.055,113.239 175.436,111.691 176.134,109.256C175.152,110.833 173.428,111.791 171.573,111.79V111.79Z"
      android:fillColor="#C7C7C7"/>
  <path
      android:pathData="M184.336,111.79C187.023,111.788 189.298,109.802 189.666,107.135C189.702,107.372 189.721,107.612 189.723,107.852C189.711,110.376 187.852,112.508 185.358,112.861C182.863,113.213 180.489,111.678 179.78,109.256C180.762,110.831 182.483,111.788 184.336,111.79Z"
      android:fillColor="#C7C7C7"/>
  <path
      android:pathData="M197.466,201.442L200.187,197.039"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M192.737,98.339L195.965,95.652C196.054,95.579 196.135,95.497 196.206,95.408C196.71,94.779 196.702,93.881 196.188,93.261V93.261C195.901,92.914 195.488,92.697 195.041,92.655C194.593,92.614 194.147,92.752 193.802,93.04L190.581,95.743C190.235,96.03 190.017,96.443 189.975,96.892C189.933,97.34 190.071,97.787 190.358,98.134V98.134C190.961,98.844 192.022,98.935 192.737,98.339Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M187.994,95.704C187.427,95.019 187.521,94.005 188.205,93.437L191.565,90.643C192.25,90.073 193.268,90.168 193.836,90.854L193.945,90.985C194.512,91.669 194.418,92.684 193.734,93.252L190.374,96.046C189.689,96.616 188.671,96.521 188.103,95.835L187.994,95.704Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M185.949,93.23C185.382,92.546 185.477,91.531 186.16,90.963L189.521,88.169C190.206,87.599 191.223,87.694 191.792,88.38L191.9,88.511C192.467,89.196 192.373,90.21 191.69,90.778L188.329,93.572C187.644,94.142 186.627,94.048 186.058,93.361L185.949,93.23Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M186.062,87.65C185.847,86.787 186.371,85.913 187.234,85.697L191.468,84.636C192.332,84.419 193.208,84.944 193.423,85.808L193.465,85.976C193.68,86.839 193.156,87.713 192.293,87.929L188.059,88.99C187.195,89.206 186.319,88.681 186.104,87.817L186.062,87.65Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M182.537,90.841C182.01,90.126 182.162,89.118 182.877,88.59L186.393,85.995C187.109,85.466 188.12,85.619 188.648,86.336L188.749,86.474C189.276,87.19 189.123,88.197 188.408,88.725L184.893,91.321C184.176,91.85 183.166,91.697 182.638,90.979L182.537,90.841Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M189.404,88.435C188.73,87.359 189.054,85.941 190.129,85.265L190.334,85.135C191.411,84.458 192.834,84.783 193.51,85.861L197.913,92.886C198.588,93.961 198.264,95.38 197.189,96.056L196.983,96.185C195.906,96.863 194.484,96.538 193.808,95.46L189.404,88.435Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M187.759,89.623L186.225,90.898C185.879,91.186 185.661,91.599 185.619,92.048C185.578,92.496 185.715,92.943 186.002,93.289V93.289C186.289,93.636 186.702,93.854 187.149,93.896C187.597,93.938 188.043,93.8 188.389,93.512L191.618,90.826"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M188.049,95.761V95.761C188.337,96.107 188.749,96.325 189.197,96.367C189.645,96.408 190.09,96.269 190.436,95.982L193.38,93.406"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M79.3,223.431H91.01"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M83.58,222.899C83.58,222.468 80.212,212.873 82.373,212.219C84.534,211.564 85.156,222.899 85.156,222.899C85.156,222.899 85.482,216.463 86.949,216.204C88.416,215.944 86.345,221.765 86.345,221.765"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M80.472,218.829L82.115,221.082"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M95.044,223.431H172.159"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M161.854,223.05C161.595,222.468 158.314,215.544 160.128,214.161C161.942,212.777 162.719,220.521 162.719,220.521C162.719,220.521 162.631,216.256 163.841,216.367C165.052,216.479 164.36,222.362 164.36,222.362C164.36,222.362 164.878,219.805 165.829,219.922C166.78,220.038 165.829,223.037 165.829,223.037"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M176.756,223.431H225.797"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M233.42,223.431H239.813"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M220.463,220.939L221.37,223.336L222.018,221.687"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M227.69,93.02C228.291,93.02 228.779,92.531 228.779,91.929C228.779,91.327 228.291,90.839 227.69,90.839C227.089,90.839 226.602,91.327 226.602,91.929C226.602,92.531 227.089,93.02 227.69,93.02Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M236.822,89.572C237.218,89.572 237.54,89.25 237.54,88.853C237.54,88.456 237.218,88.134 236.822,88.134C236.425,88.134 236.104,88.456 236.104,88.853C236.104,89.25 236.425,89.572 236.822,89.572Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M218.243,47.463C218.639,47.463 218.96,47.141 218.96,46.743C218.96,46.346 218.639,46.024 218.243,46.024C217.846,46.024 217.525,46.346 217.525,46.743C217.525,47.141 217.846,47.463 218.243,47.463Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M224.213,83.845H223.423V83.053C223.423,82.91 223.307,82.794 223.164,82.794C223.021,82.794 222.905,82.91 222.905,83.053V83.845H222.127C221.984,83.845 221.868,83.961 221.868,84.104C221.868,84.248 221.984,84.364 222.127,84.364H222.905V85.143C222.905,85.286 223.021,85.403 223.164,85.403C223.307,85.403 223.423,85.286 223.423,85.143V84.364H224.213C224.356,84.364 224.472,84.248 224.472,84.104C224.472,83.961 224.356,83.845 224.213,83.845Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M160.382,35.741H159.604V34.962C159.604,34.819 159.488,34.703 159.345,34.703C159.202,34.703 159.086,34.819 159.086,34.962V35.741H158.296C158.152,35.741 158.036,35.857 158.036,36.001C158.036,36.144 158.152,36.26 158.296,36.26H159.086V37.039C159.086,37.182 159.202,37.299 159.345,37.299C159.488,37.299 159.604,37.182 159.604,37.039V36.26H160.382C160.525,36.26 160.641,36.144 160.641,36.001C160.641,35.857 160.525,35.741 160.382,35.741Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M185.341,23.537C185.365,22.902 185.041,22.304 184.496,21.979C183.95,21.654 183.271,21.654 182.726,21.979C182.181,22.304 181.857,22.902 181.881,23.537V23.537C181.881,22.579 181.106,21.802 180.15,21.802C179.194,21.802 178.419,22.579 178.419,23.537C178.419,24.258 179.419,25.172 179.419,25.172L181.881,27.636L184.34,25.172C184.34,25.172 185.341,24.258 185.341,23.537Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M184.703,21.19V20.11"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M186.667,20.808L185.906,21.574"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M188.033,19.726L187.65,20.11"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M186.129,23.017H187.209"
      android:strokeLineJoin="round"
      android:strokeWidth="0.549957"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M93.946,45.23H92.909V44.191C92.909,44.002 92.756,43.849 92.567,43.849C92.378,43.849 92.225,44.002 92.225,44.191V45.23H91.189C91,45.23 90.847,45.383 90.847,45.572C90.847,45.762 91,45.915 91.189,45.915H92.225V46.954C92.225,47.143 92.378,47.296 92.567,47.296C92.756,47.296 92.909,47.143 92.909,46.954V45.915H93.946C94.135,45.915 94.288,45.762 94.288,45.572C94.288,45.383 94.135,45.23 93.946,45.23V45.23Z"
      android:fillColor="#529ED6"/>
  <path
      android:pathData="M87.414,38.451C87.814,38.451 88.139,38.126 88.139,37.724C88.139,37.323 87.814,36.998 87.414,36.998C87.013,36.998 86.688,37.323 86.688,37.724C86.688,38.126 87.013,38.451 87.414,38.451Z"
      android:fillColor="#529ED6"/>
  <path
      android:pathData="M94.506,38.353C95.034,38.353 95.462,37.924 95.462,37.395C95.462,36.866 95.034,36.437 94.506,36.437C93.977,36.437 93.549,36.866 93.549,37.395C93.549,37.924 93.977,38.353 94.506,38.353Z"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#E4F4FC"/>
  <path
      android:pathData="M75.887,94.928C76.284,94.928 76.605,94.606 76.605,94.209C76.605,93.812 76.284,93.49 75.887,93.49C75.491,93.49 75.169,93.812 75.169,94.209C75.169,94.606 75.491,94.928 75.887,94.928Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.739142"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M167.788,133.384C167.788,133.384 166.358,137.865 164.925,142.949C164.013,146.185 164.135,150.665 163.957,152.445C163.5,157.021 162.95,171.48 162.95,171.48"
      android:strokeLineJoin="round"
      android:strokeWidth="0.798275"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
</vector>
