<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="200dp"
    android:height="200dp"
    android:viewportWidth="200"
    android:viewportHeight="200">
  <path
      android:pathData="M0,0H200V200H0V0Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M100.082,197.558C153.456,197.558 196.725,154.421 196.725,101.208C196.725,47.995 153.456,4.858 100.082,4.858C46.707,4.858 3.439,47.995 3.439,101.208C3.439,154.421 46.707,197.558 100.082,197.558Z"
      android:fillColor="#E3F3FF"/>
  <path
      android:pathData="M41.787,196.86H160.942"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M165.093,196.86H175.516"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M70.034,196.514C69.804,195.995 66.882,189.851 68.496,188.62C70.111,187.39 70.802,194.269 70.802,194.269C70.802,194.269 70.726,190.48 71.801,190.582C72.876,190.683 72.262,195.903 72.262,195.903C72.262,195.903 72.724,193.631 73.57,193.734C74.417,193.838 73.57,196.5 73.57,196.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M75.877,195.468L75.107,196.86"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M168.903,196.39C168.903,196.005 165.905,187.487 167.829,186.906C169.752,186.325 170.306,196.39 170.306,196.39C170.306,196.39 170.596,190.667 171.902,190.455C173.207,190.243 171.364,195.392 171.364,195.392"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M166.135,192.773L167.595,194.776"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M67.852,124.974H71.584C71.824,124.972 72.056,125.066 72.226,125.235C72.397,125.404 72.493,125.635 72.493,125.875C72.493,126.372 72.089,126.776 71.591,126.776H67.852C67.357,126.772 66.957,126.37 66.957,125.875C66.957,125.38 67.357,124.977 67.852,124.974Z"
      android:fillColor="#B76452"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M73.888,131.565H67.391L67.04,126.239L74.493,129.654C74.805,129.797 75.006,130.109 75.005,130.452C75.005,131.067 74.506,131.565 73.891,131.565H73.888Z"
      android:fillColor="#CB765E"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M65.686,129.166C64.805,128.585 64.563,127.398 65.145,126.518C65.726,125.639 66.911,125.395 67.791,125.976L70.954,128.064C71.835,128.645 72.077,129.831 71.495,130.712C70.914,131.591 69.729,131.834 68.849,131.254L65.686,129.166Z"
      android:fillColor="#CB765E"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M65.232,130.761L62.312,128.583L74.177,112.051C75.069,110.99 76.647,110.842 77.722,111.717C78.789,112.609 78.938,114.193 78.056,115.268L65.232,130.761Z"
      android:fillColor="#CB765E"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M62.96,133.826L61.637,130.122L59.831,130.929C57.279,132.089 54.446,132.482 51.675,132.061L49.583,131.752L48.762,133.826C46.764,139.011 46.188,144.634 47.092,150.116L47.161,150.531L48.407,162.858C48.549,164.349 48.974,165.798 49.659,167.129C49.862,167.524 50.001,167.949 50.072,168.388L53.938,192.068L55.854,191.124L57.771,192.068L61.653,168.397C61.722,167.958 61.862,167.533 62.068,167.138C62.753,165.806 63.177,164.356 63.318,162.865L64.561,150.537L64.63,150.123C65.534,144.639 64.958,139.013 62.96,133.826Z"
      android:fillColor="#CB765E"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M63.743,158.625L63.821,157.871L61.503,152.34L53.74,155.278H48.356L47.669,155.525L48.162,160.395L63.743,158.625Z"
      android:fillColor="#B76452"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M68.069,196.514H59.305V194.975C59.311,194.477 58.943,194.054 58.449,193.991C57.928,193.94 57.465,194.32 57.414,194.841C57.414,194.873 57.414,194.903 57.414,194.935V196.514H55.873V190.983L66.194,194.011C67.303,194.342 68.064,195.358 68.069,196.514Z"
      android:fillColor="#E43C28"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M61.736,192.728C61.587,193.304 61.002,193.655 60.423,193.513L57.04,192.642C56.349,192.465 55.866,191.843 55.866,191.13V190.983L61.743,192.709L61.736,192.728Z"
      android:fillColor="#CB765E"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M66.194,195.917H59.3"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M43.663,196.514H52.427V194.975C52.421,194.477 52.789,194.054 53.283,193.991C53.804,193.94 54.267,194.32 54.319,194.841C54.319,194.873 54.319,194.903 54.319,194.935V196.514H55.859V190.983L45.538,194.007C44.428,194.338 43.666,195.357 43.663,196.514V196.514Z"
      android:fillColor="#E43C28"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M55.864,158.927V185.018"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M49.994,192.728C50.063,193.006 50.241,193.246 50.488,193.393C50.735,193.541 51.03,193.584 51.308,193.513L54.687,192.642C55.377,192.465 55.86,191.842 55.859,191.13V190.983L49.982,192.709L49.994,192.728Z"
      android:fillColor="#CB765E"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M55.864,187.742V190.971"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M45.531,195.917H52.427"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M56.602,196.521H55.863V191.826L56.602,196.521Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M61.136,166.571C61.136,167.034 60.953,167.477 60.626,167.804C60.299,168.131 59.855,168.314 59.392,168.314"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M52.249,168.314C51.786,168.314 51.342,168.131 51.015,167.804C50.688,167.477 50.505,167.034 50.505,166.571"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M63.883,124.247C61.5,123.238 59.026,123.858 57.125,125.63C56.371,126.345 55.189,126.345 54.436,125.63C52.535,123.846 50.053,123.227 47.678,124.247C43.6,125.99 41.342,131.805 42.629,137.283L47.505,158.025H59.787L61.171,154.107L62.554,158.025H64.054L68.929,137.283C69.753,133.784 69.123,130.138 67.497,127.511C66.572,126.022 65.343,124.87 63.883,124.247Z"
      android:fillColor="#0D9AFF"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M48.672,124.247C49.259,123.997 49.882,123.841 50.517,123.786C49.549,123.704 48.575,123.862 47.683,124.247C43.6,125.99 41.342,131.805 42.629,137.283L47.505,158.025H48.506L43.628,137.283C42.343,131.805 44.601,125.99 48.672,124.247Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M68.314,126.057L71.282,128.348"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M72.039,130.758L75.3,130.115"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M66.561,126.243C65.83,125.385 64.915,124.702 63.883,124.247V124.247C61.5,123.238 59.026,123.858 57.125,125.63C56.371,126.345 55.189,126.345 54.436,125.63C52.535,123.846 50.053,123.227 47.678,124.247V124.247C43.6,125.99 41.342,131.805 42.629,137.283L47.505,158.025H59.787L60.998,154.568"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M50.817,156.2V158.005"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M52.085,157.104V158.005"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M65.214,125.335L60.552,126.278C57.223,126.951 53.793,126.944 50.466,126.259L45.98,125.338L42.048,100.739C41.272,95.899 45.237,91.537 50.415,91.537H58.306C63.172,91.537 67.036,95.406 66.757,100.002L65.214,125.335Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M51.228,83.583H58.839V92.758C58.839,94.858 57.135,96.561 55.033,96.561C52.932,96.561 51.228,94.858 51.228,92.758V83.583V83.583Z"
      android:fillColor="#CB765E"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M58.839,87.031L51.228,89.605V83.588H58.839V87.031Z"
      android:fillColor="#B76452"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M58.839,85.609V92.484"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M38.766,118.516C37.459,118.039 36.78,116.6 37.243,115.289L43.201,98.734C43.427,98.105 43.894,97.59 44.5,97.305C45.106,97.02 45.8,96.987 46.43,97.213C47.06,97.439 47.575,97.906 47.86,98.511C48.146,99.117 48.179,99.81 47.952,100.44L41.992,117.002C41.519,118.309 40.075,118.986 38.766,118.516Z"
      android:fillColor="#CB765E"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M43.84,111.864L43.374,113.162L37.839,113.644L38.897,110.694L43.84,111.864Z"
      android:fillColor="#B76452"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M37.299,111.048L44.176,112.92L51.153,102.049C51.673,100.152 48.095,92.459 46.197,91.942C45.285,91.692 44.311,91.813 43.489,92.281C42.667,92.748 42.065,93.522 41.815,94.434L37.299,111.048Z"
      android:fillColor="#E4F4FC"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M61.687,99.2C62.16,98.726 62.802,98.46 63.471,98.46C64.141,98.46 64.783,98.726 65.255,99.2L77.71,111.645C78.185,112.118 78.451,112.759 78.451,113.428C78.451,114.097 78.185,114.739 77.71,115.211C77.238,115.685 76.596,115.951 75.926,115.951C75.257,115.951 74.615,115.685 74.142,115.211L61.687,102.765C61.213,102.293 60.946,101.652 60.946,100.983C60.946,100.314 61.213,99.672 61.687,99.2Z"
      android:fillColor="#CB765E"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M70.274,111.348L71.25,112.323L76.463,110.398L74.246,108.184L70.274,111.348Z"
      android:fillColor="#B76452"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M75.683,108.188L70.072,112.567L59.42,105.248C58.207,103.699 58.521,95.222 60.071,94.01C60.818,93.431 61.765,93.172 62.703,93.292C63.641,93.411 64.493,93.898 65.071,94.646L75.683,108.188Z"
      android:fillColor="#E4F4FC"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M68.757,74.867H68.688C70.642,74.379 72.104,72.754 72.383,70.761C72.661,68.768 71.701,66.805 69.956,65.8C70.622,64.272 70.473,62.511 69.558,61.118C68.643,59.724 67.087,58.885 65.42,58.886H65.212C65.214,57.122 64.275,55.492 62.748,54.608C61.22,53.725 59.337,53.723 57.808,54.604C56.887,52.327 54.316,51.202 52.017,52.068C50.94,50.674 49.109,50.093 47.424,50.612C45.74,51.131 44.554,52.64 44.449,54.398C41.674,52.837 38.187,53.403 36.049,55.762C33.912,58.121 33.693,61.644 35.523,64.249C33.284,64.701 31.694,66.697 31.756,68.979C31.817,71.261 33.512,73.169 35.772,73.5C34.706,74.459 34.162,75.868 34.309,77.294C34.455,78.719 35.274,79.989 36.513,80.711C36.459,80.972 36.432,81.238 36.432,81.504C36.429,83.04 37.351,84.426 38.77,85.018C40.188,85.609 41.823,85.288 42.913,84.205V84.436C42.913,86.134 44.291,87.51 45.99,87.51C47.689,87.51 49.067,86.134 49.067,84.436C49.067,82.738 47.689,81.361 45.99,81.361C45.542,81.361 45.121,80.566 44.702,79.774C44.414,79.228 44.126,78.684 43.831,78.402C43.977,78.088 43.81,77.862 43.626,77.612C43.469,77.4 43.301,77.171 43.301,76.858C43.301,75.905 42.998,74.978 42.433,74.21C44.045,74.742 45.815,74.474 47.197,73.489C48.579,72.503 49.408,70.919 49.429,69.222C49.678,69.268 49.93,69.292 50.183,69.294C51.764,69.293 53.211,68.407 53.931,67.001C54.102,66.95 54.268,66.886 54.429,66.809C54.654,68.607 56.137,69.985 57.947,70.078C59.758,70.171 61.374,68.953 61.782,67.187C62.118,67.551 62.507,67.862 62.936,68.109C62.666,68.73 62.527,69.399 62.527,70.075C62.527,70.47 62.516,70.865 62.505,71.25C62.439,73.526 62.383,75.491 64.718,75.491C64.829,75.491 65.489,76.13 65.092,76.913C64.845,76.794 64.71,76.651 64.59,76.523C64.413,76.334 64.266,76.178 63.835,76.178C61.323,76.186 59.268,78.179 59.186,80.687C59.103,83.196 61.023,85.32 63.529,85.492C66.035,85.664 68.227,83.823 68.489,81.327C68.534,81.327 68.579,81.33 68.623,81.334L68.623,81.334C68.668,81.337 68.712,81.341 68.757,81.341C70.546,81.341 71.996,79.891 71.996,78.104C71.996,76.316 70.546,74.867 68.757,74.867ZM55.35,60.536C55.385,60.522 55.42,60.506 55.456,60.489C55.495,60.471 55.536,60.452 55.58,60.435C55.647,60.624 55.724,60.809 55.811,60.99C55.67,60.826 55.516,60.674 55.35,60.536ZM39.987,72.514C39.904,72.42 39.827,72.322 39.756,72.219L39.754,72.212C39.721,72.247 39.687,72.278 39.654,72.31C39.617,72.345 39.58,72.379 39.546,72.417C39.696,72.442 39.841,72.474 39.987,72.514Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M54.498,86.605C60.637,86.605 65.613,81.632 65.613,75.498C65.613,69.364 60.637,64.392 54.498,64.392C48.36,64.392 43.383,69.364 43.383,75.498C43.383,81.632 48.36,86.605 54.498,86.605Z"
      android:fillColor="#CB765E"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M44.221,75.498C44.22,69.546 48.918,64.652 54.87,64.403C54.717,64.403 54.563,64.392 54.408,64.392C48.566,64.387 43.717,68.903 43.312,74.727C42.906,80.55 47.081,85.694 52.868,86.499C53.098,86.529 52.918,86.522 52.962,86.499C53.006,86.476 53.216,86.407 52.847,86.324C47.798,85.165 44.22,80.674 44.221,75.498Z"
      android:fillColor="#FFF4EE"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M64.956,79.008C63.613,83.023 60.107,85.928 55.91,86.503"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M52.865,86.499C48.64,85.907 45.127,82.952 43.821,78.893"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M57.639,61.635C56.206,61.634 54.877,62.385 54.138,63.613C53.487,61.853 51.748,60.737 49.876,60.876C48.004,61.015 46.449,62.376 46.065,64.212C43.899,63.355 41.427,64.007 39.965,65.819C38.503,67.632 38.391,70.184 39.688,72.118C40.986,74.051 43.392,74.916 45.625,74.252C47.858,73.589 49.399,71.55 49.428,69.223C49.677,69.268 49.929,69.292 50.182,69.294C51.674,69.294 53.054,68.505 53.811,67.22C54.56,69.143 56.626,70.207 58.629,69.702C60.631,69.196 61.943,67.279 61.688,65.23C61.433,63.182 59.691,61.645 57.625,61.645L57.639,61.635Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M52.849,67.162C54.943,67.162 56.641,65.465 56.641,63.373C56.641,61.28 54.943,59.584 52.849,59.584C50.755,59.584 49.057,61.28 49.057,63.373C49.057,65.465 50.755,67.162 52.849,67.162Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M41.11,67.162C44.876,67.162 47.93,64.111 47.93,60.347C47.93,56.583 44.876,53.532 41.11,53.532C37.343,53.532 34.289,56.583 34.289,60.347C34.289,64.111 37.343,67.162 41.11,67.162Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M48.675,58.897C51.013,58.897 52.909,57.003 52.909,54.666C52.909,52.329 51.013,50.434 48.675,50.434C46.336,50.434 44.44,52.329 44.44,54.666C44.44,57.003 46.336,58.897 48.675,58.897Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M36.455,73.562C39.052,73.562 41.158,71.458 41.158,68.863C41.158,66.268 39.052,64.164 36.455,64.164C33.857,64.164 31.752,66.268 31.752,68.863C31.752,71.458 33.857,73.562 36.455,73.562Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M38.789,81.341C41.275,81.341 43.291,79.326 43.291,76.842C43.291,74.357 41.275,72.343 38.789,72.343C36.302,72.343 34.286,74.357 34.286,76.842C34.286,79.326 36.302,81.341 38.789,81.341Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M40.239,85.293C42.34,85.293 44.043,83.592 44.043,81.493C44.043,79.394 42.34,77.692 40.239,77.692C38.139,77.692 36.436,79.394 36.436,81.493C36.436,83.592 38.139,85.293 40.239,85.293Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M68.759,81.338C70.548,81.338 71.997,79.89 71.997,78.103C71.997,76.315 70.548,74.867 68.759,74.867C66.971,74.867 65.521,76.315 65.521,78.103C65.521,79.89 66.971,81.338 68.759,81.338Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M53.6,60.873C56.111,60.873 58.146,58.839 58.146,56.33C58.146,53.821 56.111,51.787 53.6,51.787C51.09,51.787 49.054,53.821 49.054,56.33C49.054,58.839 51.09,60.873 53.6,60.873Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M60.269,63.839C63,63.839 65.214,61.626 65.214,58.897C65.214,56.168 63,53.956 60.269,53.956C57.538,53.956 55.324,56.168 55.324,58.897C55.324,61.626 57.538,63.839 60.269,63.839Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M63.653,59.582C64.352,59.042 65.184,58.702 66.061,58.596"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M67.488,75.021C70.219,75.021 72.433,72.809 72.433,70.08C72.433,67.351 70.219,65.138 67.488,65.138C64.757,65.138 62.543,67.351 62.543,70.08C62.543,72.809 64.757,75.021 67.488,75.021Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M50.192,69.294C52.519,69.294 54.406,67.409 54.406,65.083C54.406,62.758 52.519,60.873 50.192,60.873C47.865,60.873 45.978,62.758 45.978,65.083C45.978,67.409 47.865,69.294 50.192,69.294Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M41.845,59.582C41.845,58.329 42.861,57.314 44.114,57.314C45.368,57.314 46.384,58.329 46.384,59.582C46.383,59.838 46.34,60.091 46.257,60.333"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#FFF4EE"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M47.249,58.605C48.194,58.604 49.041,59.188 49.375,60.07"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#FFF4EE"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M44.114,74.482C47.056,74.482 49.44,72.099 49.44,69.16C49.44,66.221 47.056,63.839 44.114,63.839C41.173,63.839 38.789,66.221 38.789,69.16C38.789,72.099 41.173,74.482 44.114,74.482Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M42.901,79.854C43.864,79.854 44.645,79.074 44.645,78.112C44.645,77.149 43.864,76.369 42.901,76.369C41.938,76.369 41.157,77.149 41.157,78.112C41.157,79.074 41.938,79.854 42.901,79.854Z"
      android:fillColor="#CB765E"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M65.521,79.67C66.484,79.67 67.265,78.89 67.265,77.927C67.265,76.965 66.484,76.185 65.521,76.185C64.558,76.185 63.777,76.965 63.777,77.927C63.777,78.89 64.558,79.67 65.521,79.67Z"
      android:fillColor="#CB765E"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M61.883,74.364C61.883,74.985 61.379,75.489 60.758,75.489C60.136,75.489 59.632,74.985 59.632,74.364"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M60.764,78.386C60.764,79.077 59.902,79.631 58.839,79.631C57.775,79.631 56.913,79.073 56.913,78.386"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M57.81,74.364C57.81,74.985 57.306,75.489 56.684,75.489C56.063,75.489 55.559,74.985 55.559,74.364"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M42.126,77.87C42.306,77.799 42.506,77.803 42.683,77.88C42.86,77.958 42.998,78.102 43.068,78.282"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M65.643,78.227C65.712,77.835 66.086,77.573 66.478,77.642V77.642"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M57.469,71.509C57.213,72.154 56.595,72.585 55.9,72.601"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M60.908,72.627C60.211,72.586 59.607,72.132 59.374,71.474"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M54.657,82.827C52.848,82.827 51.382,81.362 51.382,79.554V79.554"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M43.378,76.9C42.86,72.798 44.664,68.749 48.06,66.389C51.457,64.029 55.884,63.749 59.551,65.663C63.218,67.576 65.518,71.367 65.52,75.501C65.52,75.807 65.509,76.111 65.483,76.423"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M56.272,79.538C55.847,79.562 55.484,79.237 55.46,78.813C55.437,78.388 55.762,78.025 56.187,78.001H56.272"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M63.246,71.965C64.801,71.965 66.06,70.706 66.06,69.153C66.06,67.6 64.801,66.342 63.246,66.342C61.692,66.342 60.433,67.6 60.433,69.153C60.433,70.706 61.692,71.965 63.246,71.965Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M65.521,75.892C67.197,75.892 68.556,74.534 68.556,72.859C68.556,71.184 67.197,69.826 65.521,69.826C63.844,69.826 62.485,71.184 62.485,72.859C62.485,74.534 63.844,75.892 65.521,75.892Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M43.819,76.92C45.713,76.92 47.249,75.386 47.249,73.493C47.249,71.6 45.713,70.066 43.819,70.066C41.925,70.066 40.389,71.6 40.389,73.493C40.389,75.386 41.925,76.92 43.819,76.92Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M43.819,76.92C45.713,76.92 47.249,75.386 47.249,73.493C47.249,71.6 45.713,70.066 43.819,70.066C41.925,70.066 40.389,71.6 40.389,73.493C40.389,75.386 41.925,76.92 43.819,76.92Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M44.816,82.364C44.312,83.348 43.168,83.82 42.117,83.477C41.065,83.134 40.419,82.079 40.593,80.988C40.766,79.897 41.708,79.093 42.814,79.093"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M63.936,82.364C64.44,83.348 65.584,83.82 66.636,83.477C67.687,83.134 68.333,82.079 68.159,80.988C67.986,79.897 67.044,79.093 65.938,79.093"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M60.622,68.782C59.512,69.997 57.77,70.406 56.235,69.812C54.701,69.217 53.689,67.742 53.688,66.097C53.687,66 53.69,65.902 53.7,65.805"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#CB765E"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M46.877,72.315C47.614,73.552 47.499,75.118 46.589,76.233"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#CB765E"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M37.686,73.371C36.454,73.918 35.047,73.911 33.821,73.353"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M71.467,71.788C71.236,72.704 70.462,73.382 69.522,73.491"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#FFF4EE"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M54.138,97.706L53.481,96.57C54.803,93.712 56.41,91.928 57.586,92.263C58.627,92.56 59.079,94.438 58.903,97.029L57.383,97.518L58.793,98.165C58.629,99.425 58.369,100.671 58.015,101.892C57.585,103.431 57,104.922 56.272,106.345C55.882,107.089 55.022,107.457 54.214,107.225L52.526,106.743C51.726,106.52 51.195,105.764 51.255,104.936V104.923L53.169,104.001L51.324,104.19C51.486,102.845 51.756,101.515 52.132,100.214C52.378,99.352 52.658,98.532 52.96,97.766L54.138,97.706Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M57.097,94.876V94.876C55.531,98.23 54.391,101.767 53.704,105.404L52.673,110.871"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M56.286,101.115L54.079,103.618"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M53.887,121.304H46.764L44.405,109.696H56.246L53.887,121.304Z"
      android:fillColor="#EDA257"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M50.102,92.779L51.294,91.223C49.885,86.738 47.849,83.788 46.043,84.058C44.444,84.288 43.416,86.994 43.176,90.864L45.335,91.88L43.125,92.572C43.127,94.468 43.273,96.362 43.561,98.236C43.901,100.595 44.476,102.913 45.277,105.158C45.708,106.336 46.91,107.048 48.151,106.861L50.736,106.471C51.962,106.291 52.894,105.276 52.967,104.04V104.017L50.31,102.29C50.31,102.29 51.67,102.606 53.001,102.922C53.022,100.901 52.879,98.881 52.572,96.884C52.372,95.561 52.111,94.291 51.816,93.102L50.102,92.779Z"
      android:fillColor="#529ED6"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M46.266,88.013V88.013C47.94,93.279 48.947,98.733 49.264,104.249L49.583,109.709"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M46.266,97.414L49.052,101.544"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M43.644,98.246L44.057,97.285C42.74,95.148 41.289,93.888 40.399,94.266C39.605,94.6 39.425,96.13 39.811,98.163L41.058,98.405L40.004,99.05C40.255,100.031 40.579,100.992 40.973,101.924C41.459,103.102 42.061,104.228 42.77,105.287C43.149,105.838 43.864,106.047 44.481,105.787L45.77,105.245C46.384,104.995 46.734,104.346 46.605,103.696V103.683L45.005,103.139L46.481,103.113C46.225,102.065 45.886,101.038 45.466,100.044C45.189,99.384 44.889,98.762 44.578,98.186L43.644,98.246Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M41.035,96.282V96.282C42.595,98.788 43.835,101.48 44.725,104.293L46.063,108.52"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M42.267,101.147L44.25,102.92"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M48.562,91.537C48.834,91.537 49.055,91.252 49.055,90.9C49.055,90.549 48.834,90.264 48.562,90.264C48.289,90.264 48.068,90.549 48.068,90.9C48.068,91.252 48.289,91.537 48.562,91.537Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M44.936,96.282C45.162,96.282 45.346,96.099 45.346,95.872C45.346,95.645 45.162,95.462 44.936,95.462C44.709,95.462 44.525,95.645 44.525,95.872C44.525,96.099 44.709,96.282 44.936,96.282Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M51.5,101.115C52.148,101.115 52.674,100.749 52.674,100.297C52.674,99.845 52.148,99.479 51.5,99.479C50.851,99.479 50.326,99.845 50.326,100.297C50.326,100.749 50.851,101.115 51.5,101.115Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M75.683,108.188L70.072,112.567L59.42,105.248C58.207,103.699 58.521,95.222 60.071,94.01C60.818,93.431 61.765,93.172 62.703,93.292C63.641,93.411 64.493,93.898 65.071,94.646L75.683,108.188Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M68.868,111.749L63.777,108.25"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M75.683,108.186L70.624,112.141"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M73.801,112.574L70.274,117.55"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M77.706,113.293H78.645"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#B76452"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M53.012,118.719L54.357,114.697L39.679,113.602C39.009,113.598 38.366,113.861 37.89,114.331C37.415,114.801 37.145,115.441 37.142,116.11C37.138,116.778 37.4,117.421 37.871,117.897C38.341,118.372 38.982,118.641 39.651,118.645L53.012,118.719Z"
      android:fillColor="#CB765E"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M38.538,117.863L37.839,117.237"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#B76452"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M56.288,111.841H44.25L43.84,109.696H56.89L56.288,111.841Z"
      android:fillColor="#EDA257"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M44.864,111.846L54.353,111.841L45.041,112.719L44.864,111.846Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M47.925,88.72L47.014,90.543"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M51.204,86.137V92.221"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M51.543,114.736L54.272,113.95C54.929,113.759 55.441,113.242 55.626,112.583L55.783,112.028L56.832,113.18C57.727,114.154 58.059,115.519 57.711,116.794C57.254,118.471 55.588,119.518 53.877,119.205L51.207,118.709L51.543,114.736Z"
      android:fillColor="#CB765E"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M52.524,115.295C52.201,115.108 52.089,114.694 52.275,114.371L53.886,111.584C54.072,111.262 54.486,111.152 54.809,111.338C55.132,111.525 55.244,111.939 55.058,112.261L53.447,115.049C53.261,115.371 52.847,115.481 52.524,115.295Z"
      android:fillColor="#CB765E"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M54.793,112.719L53.804,114.434"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M55.983,115.31H57.835"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M56.285,116.953H58.14"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M41.702,113.644L45.284,114.015"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M52.088,118.871L49.929,118.509"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M44.525,107.292L44.813,109.156"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M58.848,91.553C60.178,91.629 61.472,92.008 62.633,92.661"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M45.978,125.333L45.346,121.378"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M65.959,112.92L65.357,122.83L65.205,125.335L60.543,126.278C57.214,126.951 53.784,126.944 50.457,126.26L48.598,125.877"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M62.114,107.14C62.114,108.667 60.875,109.905 59.346,109.905"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M52.39,107.14C52.39,108.043 51.948,108.89 51.207,109.407"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M65.426,134.008H68.867"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#529ED6"/>
  <path
      android:pathData="M67.495,135.621H69.197"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#529ED6"/>
  <path
      android:pathData="M61.576,155.26L62.543,158.002H64.042L68.918,137.26C69.558,134.552 69.348,131.714 68.318,129.129"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M44.405,130.115H46.386"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M61.575,151.6H65.426"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M64.561,150.526H65.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M91.02,196.77H80.519L79.596,176.028C79.596,173.14 82.154,170.798 85.307,170.798C88.46,170.798 91.018,173.14 91.018,176.028L91.02,196.77Z"
      android:fillColor="#EDA257"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M97.912,196.77H82.872V178.669H84.12C91.731,178.671 97.901,184.836 97.903,192.442L97.912,196.77Z"
      android:fillColor="#EDA257"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M85.9,171.978L90.1,165.947V174.209L85.9,171.978Z"
      android:fillColor="#EDA257"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M80.66,172.995V166.601L84.193,171.455L80.66,172.995Z"
      android:fillColor="#EDA257"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M85.31,196.77L87.344,191.008L87.715,193.389C87.903,194.588 88.322,195.74 88.949,196.779H85.31V196.77Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M82.881,196.77L83.472,191.589"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M86.115,176.694H83.333C82.942,176.673 82.608,176.972 82.586,177.362C82.564,177.752 82.862,178.087 83.252,178.111H84.025V178.235C84.025,178.626 84.342,178.943 84.733,178.943C85.124,178.943 85.441,178.626 85.441,178.235V178.111H86.133C86.387,178.126 86.63,178.005 86.77,177.793C86.91,177.58 86.925,177.309 86.81,177.082C86.695,176.855 86.468,176.707 86.214,176.694H86.124H86.115Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M83.176,189.162L82.586,183.861C82.546,183.509 82.648,183.154 82.869,182.877C83.091,182.599 83.414,182.421 83.767,182.382C83.818,182.378 83.868,182.378 83.919,182.382C84.365,182.383 84.781,182.605 85.029,182.975C85.278,183.344 85.328,183.812 85.162,184.226L83.176,189.162Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M88.262,176.325L92.25,174.947"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M88.46,177.812L91.364,178.669"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M82.06,176.078L78.367,174.405"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M81.813,177.812L78.367,178.669"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M84.35,175.352C84.35,175.667 84.095,175.922 83.78,175.922C83.466,175.922 83.211,175.667 83.211,175.352"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M86.196,175.352C86.196,175.671 85.938,175.928 85.619,175.928C85.301,175.928 85.043,175.671 85.043,175.352"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M88.001,168.964L86.193,171.557"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M90.257,180.102L91.364,180.718"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M93.623,184.903H96.134"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M92.706,185.839H96.556"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M145.592,103.865C144.381,104.989 142.254,104.71 140.734,103.228L105.881,69.204C103.609,66.987 103.22,63.691 105.03,62.009C106.765,60.395 109.853,60.893 111.927,63.122L145.147,98.794C146.655,100.407 146.856,102.682 145.592,103.865Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M130.706,93.431L105.882,69.204"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M138.234,100.779L133.531,96.19"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M108.635,55.735C113.662,55.735 117.737,51.664 117.737,46.641C117.737,41.618 113.662,37.546 108.635,37.546C103.609,37.546 99.534,41.618 99.534,46.641C99.534,51.664 103.609,55.735 108.635,55.735Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M116.423,46.641C116.426,47.816 116.117,48.975 115.52,50.029"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#FFF4EE"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.625,51.414C113.17,53.237 110.962,54.297 108.628,54.292"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#FFF4EE"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.382,60.02L100.632,63.097C100.099,64.035 98.697,64.2 97.917,63.419L94.52,60.02L91.713,46.652H99.571L102.382,60.02Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M100.77,52.333L93.593,55.583L92.17,48.793L100.016,48.743L100.77,52.333Z"
      android:fillColor="#F0917A"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M102.382,60.02L100.632,63.097C100.099,64.035 98.697,64.2 97.917,63.419L94.52,60.02L91.713,46.652H99.571L102.382,60.02Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M80.828,54.292C85.456,54.292 89.208,50.544 89.208,45.919C89.208,41.295 85.456,37.546 80.828,37.546C76.201,37.546 72.449,41.295 72.449,45.919C72.449,50.544 76.201,54.292 80.828,54.292Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M84.205,44.536C85.234,44.536 86.069,43.703 86.069,42.674C86.069,41.646 85.234,40.812 84.205,40.812C83.176,40.812 82.342,41.646 82.342,42.674C82.342,43.703 83.176,44.536 84.205,44.536Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M96.498,51.748C103.057,51.748 108.375,46.435 108.375,39.881C108.375,33.327 103.057,28.014 96.498,28.014C89.94,28.014 84.623,33.327 84.623,39.881C84.623,46.435 89.94,51.748 96.498,51.748Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M85.614,39.881C85.622,33.526 90.64,28.305 96.994,28.039C96.831,28.039 96.665,28.039 96.498,28.039C89.94,28.039 84.623,33.352 84.623,39.906C84.623,45.859 88.79,50.808 94.51,51.66C95.087,51.746 95.306,51.64 94.568,51.462C89.351,50.185 85.614,45.477 85.614,39.881Z"
      android:fillColor="#FFF4EE"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M95.156,19.742C90.948,18.908 86.623,20.47 83.922,23.801C81.221,27.132 80.587,31.683 82.277,35.624C86.172,39.593 92.344,40.227 96.966,37.133C101.588,34.04 103.35,28.095 101.16,22.985C99.533,21.322 97.44,20.191 95.156,19.742Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M104.511,101.56H98.895C94.678,101.56 91.003,98.691 89.983,94.602L82.641,65.191C82.327,63.938 82.609,62.61 83.405,61.591C84.202,60.573 85.423,59.979 86.717,59.98H104.753C107.125,59.979 109.401,60.92 111.079,62.595C112.758,64.27 113.701,66.543 113.702,68.914V92.382C113.7,97.453 109.585,101.561 104.511,101.56Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M108.374,44.338C109.404,44.338 110.238,43.505 110.238,42.476C110.238,41.447 109.404,40.614 108.374,40.614C107.345,40.614 106.511,41.447 106.511,42.476C106.511,43.505 107.345,44.338 108.374,44.338Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M100.378,36.666L100.094,38.722C100.017,40.055 100.338,41.381 101.017,42.532L101.586,43.516L101.125,43.822C100.141,44.486 98.875,44.569 97.813,44.039L97.488,43.877"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M104.402,39.611C104.374,40.255 103.844,40.762 103.2,40.762C102.555,40.762 102.025,40.255 101.997,39.611"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.425,39.611C98.396,40.255 97.866,40.762 97.222,40.762C96.577,40.762 96.047,40.255 96.019,39.611"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M90.437,43.048C90.225,42.419 90.561,41.738 91.189,41.524V41.524"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M83.377,42.414C83.774,42.256 84.223,42.447 84.383,42.842"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M108.506,42.79C108.583,42.375 108.98,42.098 109.396,42.17V42.17"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.128,36.189C98.514,36.608 97.709,36.618 97.085,36.214"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M101.114,34.493C101.855,34.449 102.55,34.853 102.878,35.518"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M94.866,46.286C93.936,46.289 93.043,45.922 92.384,45.266C91.725,44.61 91.354,43.719 91.353,42.79V42.79"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M87.409,40.607C89.968,40.261 92.339,39.07 94.144,37.224"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M71.34,50.625C73.566,51.928 76.163,52.452 78.72,52.114"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M73.49,52.949C76.026,53.435 78.652,53.062 80.951,51.886"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M105.809,24.536C103.32,25.217 101.129,26.708 99.582,28.772"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.017,24.29C101.003,25.905 99.587,28.146 98.992,30.657"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M111.487,37.4C113.37,35.815 112.67,31.888 109.922,28.629C107.174,25.369 103.42,24.012 101.537,25.597C99.654,27.183 100.355,31.11 103.102,34.369C105.85,37.628 109.604,38.985 111.487,37.4Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M110.238,41.117C112.205,41.117 113.799,39.523 113.799,37.558C113.799,35.593 112.205,34 110.238,34C108.271,34 106.677,35.593 106.677,37.558C106.677,39.523 108.271,41.117 110.238,41.117Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M83.059,40.614C84.748,40.614 86.117,39.246 86.117,37.558C86.117,35.87 84.748,34.502 83.059,34.502C81.369,34.502 80,35.87 80,37.558C80,39.246 81.369,40.614 83.059,40.614Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M86.894,101.56C85.245,101.677 83.795,100.573 83.582,99.038L78.695,63.763C78.377,61.458 80.192,59.383 82.658,59.192C85.022,59.022 87.067,60.681 87.227,62.898L89.801,98.391C89.918,100.027 88.617,101.44 86.894,101.56Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M97.753,72.548H109.55L108.243,80.294C107.933,82.199 106.281,83.594 104.349,83.583H102.938C101.018,83.582 99.379,82.197 99.058,80.306L97.753,72.548Z"
      android:fillColor="#0D9AFF"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M83.23,96.512L78.695,63.79"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M87.919,72.481L88.859,85.457"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M88.608,72.486C87.836,72.486 87.21,71.86 87.21,71.089"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M87.178,65.669V63.479C87.178,62.887 87.049,62.302 86.8,61.764V61.764C86.484,61.083 85.986,60.501 85.361,60.084"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M104.707,61.262C104.706,60.821 104.614,60.385 104.438,59.98L98.556,59.845L92.474,59.99C92.297,60.394 92.205,60.83 92.204,61.271C92.204,63.705 95.002,65.678 98.452,65.678C101.903,65.678 104.707,63.696 104.707,61.262Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M107.782,43.631C106.35,47.922 102.603,51.027 98.118,51.64"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M84.717,41.375C84.657,40.879 84.626,40.38 84.625,39.881C84.625,33.326 89.943,28.012 96.503,28.012C103.063,28.012 108.382,33.326 108.382,39.881C108.388,40.206 108.375,40.53 108.342,40.854"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M94.866,51.635C90.343,51.01 86.578,47.852 85.178,43.509"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M84.011,45.156C84.354,45.156 84.632,44.879 84.632,44.536C84.632,44.194 84.354,43.917 84.011,43.917C83.668,43.917 83.391,44.194 83.391,44.536C83.391,44.879 83.668,45.156 84.011,45.156Z"
      android:fillColor="#FFF4EE"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M109.126,44.617C109.469,44.617 109.747,44.34 109.747,43.997C109.747,43.655 109.469,43.377 109.126,43.377C108.784,43.377 108.506,43.655 108.506,43.997C108.506,44.34 108.784,44.617 109.126,44.617Z"
      android:fillColor="#FFF4EE"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M95.566,60.882H98.106"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.907,60.882H101.66"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.649,61.476C99.649,61.775 99.406,62.018 99.107,62.018C98.808,62.018 98.565,61.775 98.565,61.476"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.646,73.567C106.902,73.567 109.542,73.11 109.542,72.546C109.542,71.982 106.902,71.525 103.646,71.525C100.39,71.525 97.751,71.982 97.751,72.546C97.751,73.11 100.39,73.567 103.646,73.567Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M103.646,74.134C101.483,74.134 99.591,73.931 98.572,73.631"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.754,67.759C102.486,68.078 102.51,68.549 102.809,68.84L102.821,68.851C103.134,69.155 103.143,69.654 102.842,69.969L102.821,69.99V69.99C102.517,70.284 102.498,70.765 102.777,71.082"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#FFF4EE"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M105.475,66.21C105.205,66.528 105.229,67 105.53,67.289V67.289C105.682,67.434 105.77,67.634 105.773,67.844C105.777,68.054 105.697,68.258 105.551,68.409L105.53,68.429V68.429C105.227,68.724 105.207,69.204 105.486,69.522"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#FFF4EE"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M116.113,99.218L113.615,93.611L110.203,94.835C105.27,96.603 99.991,97.189 94.789,96.547L90.838,96.058L89.288,99.218C85.541,106.865 84.432,115.536 86.135,123.879L86.265,124.508L88.617,143.162C88.906,145.472 89.717,147.685 90.991,149.634C91.369,150.214 91.634,150.861 91.772,151.54L99.077,187.376H106.322L113.624,151.54C113.763,150.861 114.029,150.214 114.408,149.634C115.68,147.685 116.49,145.471 116.777,143.162L119.129,124.508L119.259,123.879C120.964,115.537 119.857,106.866 116.113,99.218Z"
      android:fillColor="#0D9AFF"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M111.527,94.835L113.797,94.021L113.615,93.611L110.204,94.835C106.808,96.05 103.239,96.71 99.633,96.789C103.683,96.861 107.713,96.199 111.527,94.835Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M92.328,149.625C91.055,147.676 90.244,145.463 89.955,143.153L87.603,124.499L87.476,123.87C85.771,115.527 86.879,106.856 90.626,99.209L92.103,96.213L90.855,96.059L89.305,99.218C85.557,106.865 84.449,115.536 86.152,123.879L86.281,124.508L88.633,143.162C88.922,145.472 89.734,147.685 91.007,149.634C91.385,150.214 91.651,150.861 91.789,151.54L99.093,187.376H100.401L93.101,151.531C92.966,150.853 92.704,150.206 92.328,149.625Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M99.977,184.643h5.372v8.733h-5.372z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M105.348,188.339L99.977,189.591V184.643H105.348V188.339Z"
      android:fillColor="#F0917A"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M94.612,151.966C94.612,152.929 93.831,153.709 92.868,153.709C91.905,153.709 91.125,152.929 91.125,151.966V151.966"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.369,151.966C114.335,152.905 113.564,153.648 112.624,153.648C111.685,153.648 110.914,152.905 110.88,151.966"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.703,187.367H97.37L96.798,184.454H102.703V187.367Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M102.703,187.367H108.034L108.606,184.454H102.703V187.367Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M99.656,120.908L102.662,122.367L105.669,120.908"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M120.041,196.862H102.664V191.693H115.592C118.049,191.693 120.041,193.683 120.041,196.138V196.862Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M84.583,196.862H102.664V191.693H89.023C86.566,191.693 84.574,193.683 84.574,196.138L84.583,196.862Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M106.903,189.335C106.903,189.941 105.805,191.693 105.805,191.693C105.805,191.693 104.707,189.941 104.707,189.335C104.707,188.729 105.198,188.238 105.805,188.238C106.411,188.238 106.903,188.729 106.903,189.335Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M108.167,192.79C107.562,192.79 105.807,191.693 105.807,191.693C105.807,191.693 107.562,190.598 108.167,190.598C108.558,190.58 108.93,190.772 109.142,191.102C109.353,191.432 109.373,191.85 109.193,192.198C109.012,192.546 108.66,192.772 108.268,192.79H108.167Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.658,189.335C98.658,189.941 99.755,191.693 99.755,191.693C99.755,191.693 100.853,189.941 100.853,189.335C100.853,188.729 100.362,188.238 99.755,188.238C99.149,188.238 98.658,188.729 98.658,189.335H98.658Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M97.403,192.79C98.009,192.79 99.765,191.693 99.765,191.693C99.765,191.693 98.009,190.598 97.403,190.598C96.817,190.625 96.355,191.107 96.355,191.694C96.355,192.28 96.817,192.763 97.403,192.79V192.79Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.081,187.367H102.703H106.322"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M86.141,123.87L86.27,124.499V124.499L88.622,143.153C88.911,145.463 89.723,147.676 90.996,149.625C91.374,150.205 91.639,150.852 91.778,151.531L97.122,177.747"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M107.842,95.584C103.597,96.759 99.161,97.086 94.789,96.547L90.839,96.059L89.289,99.218C86.334,105.243 85.007,111.934 85.439,118.629"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M110.203,94.835C109.841,94.964 109.475,95.088 109.108,95.205"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M118.223,131.752L119.146,124.499V124.499L119.275,123.869C120.978,115.527 119.869,106.856 116.122,99.209L113.624,93.602L111.703,94.293"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M117.603,136.665L117.949,133.909"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M107.344,182.361L113.626,151.531C113.765,150.852 114.031,150.205 114.411,149.625C115.681,147.675 116.49,145.462 116.777,143.153"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M120.041,196.141V196.86H102.661V191.693"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M112.625,191.693H115.594C117.33,191.693 118.907,192.701 119.635,194.276"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M95.657,191.693H89.023C86.566,191.693 84.574,193.683 84.574,196.138V196.138V196.86H98.018"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M100.403,196.86H102.661V191.693H100.403"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.662,124.874V155.161"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.662,163.331V181.358"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M86.895,101.56C85.246,101.677 83.795,100.574 83.583,99.039"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M94.755,98.518C99.954,99.159 105.232,98.572 110.164,96.805L114.364,95.296L113.615,93.611L110.203,94.835C105.27,96.603 99.991,97.189 94.789,96.547L90.838,96.058L90.123,97.517L94.755,98.518Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M87.21,111.415V111.415C89.101,109.7 90.374,107.411 90.833,104.902L90.85,104.807"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M117.831,109.914V109.914C115.941,108.2 114.668,105.912 114.208,103.404L114.192,103.309"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M97.31,97.955L97.038,100.306"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.664,99.817V98.255"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M108.886,96.512L109.655,99.036"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M91.196,87.305L94.935,87.289"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M73.736,45.919C73.736,43.424 75.051,41.112 77.196,39.835"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#FFF4EE"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.662,184.002V186.807"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M104.345,191.693H102.661"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M106.269,196.86H102.661V191.693H103.263L106.269,196.86Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M102.703,190.19V191.693"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.461,65.669C101.921,65.669 104.707,63.696 104.707,61.262"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M92.213,61.262C92.242,62.286 92.702,63.251 93.481,63.917"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M95.246,60.084H89.807"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M109.027,72.638L107.182,80.474C106.935,81.924 105.9,83.115 104.498,83.562C106.358,83.494 107.914,82.127 108.22,80.292L109.551,72.548L109.027,72.638Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M104.888,72.91C105.521,72.91 106.034,72.755 106.034,72.564C106.034,72.373 105.521,72.218 104.888,72.218C104.254,72.218 103.741,72.373 103.741,72.564C103.741,72.755 104.254,72.91 104.888,72.91Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M107.526,72.76C107.847,72.76 108.107,72.666 108.107,72.55C108.107,72.435 107.847,72.341 107.526,72.341C107.205,72.341 106.945,72.435 106.945,72.55C106.945,72.666 107.205,72.76 107.526,72.76Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M85.834,101.07C84.477,100.408 83.91,98.77 84.572,97.413L90.85,84.558C91.512,83.202 93.15,82.642 94.506,83.303C95.863,83.964 96.43,85.603 95.768,86.959L89.49,99.815C88.828,101.17 87.189,101.731 85.834,101.07Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M95.29,87.667L95.767,86.976C96.196,86.098 96.123,85.058 95.577,84.249C95.03,83.439 94.093,82.982 93.118,83.05C92.143,83.118 91.278,83.701 90.85,84.579L88.677,89.029L95.29,87.667Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M98.591,84.406C97.347,84.392 96.113,84.186 94.933,83.795C90.989,82.518 88.576,79.824 89.321,77.526C89.93,75.643 92.393,74.761 95.592,75.265C96.017,75.333 96.373,75.621 96.526,76.022C96.68,76.424 96.608,76.876 96.337,77.209C96.066,77.543 95.638,77.707 95.214,77.639C93.138,77.307 91.754,77.801 91.609,78.266C91.408,78.882 92.801,80.571 95.675,81.509C97.024,81.944 98.411,82.104 99.481,81.94C100.332,81.811 100.791,81.509 100.865,81.265C101.019,80.785 100.124,79.421 97.79,78.4C97.396,78.228 97.124,77.859 97.076,77.433C97.028,77.006 97.211,76.586 97.557,76.331C97.902,76.076 98.358,76.025 98.752,76.197C102.046,77.63 103.812,79.967 103.153,82.007C102.754,83.233 101.577,84.053 99.836,84.311C99.424,84.374 99.007,84.406 98.591,84.406Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M94.937,87.297C94.434,86.91 94.341,86.188 94.728,85.686C95.115,85.184 95.836,85.089 96.338,85.475L103.097,90.673C103.6,91.059 103.692,91.781 103.305,92.284C102.919,92.785 102.198,92.881 101.696,92.495L94.937,87.297Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M97.267,86.172C96.717,85.855 96.53,85.152 96.848,84.603C97.164,84.054 97.867,83.865 98.415,84.181L105.802,88.443C106.351,88.76 106.538,89.463 106.221,90.012C105.904,90.561 105.202,90.75 104.653,90.434L97.267,86.172Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M97.905,84.574C97.28,84.474 96.854,83.883 96.955,83.257C97.056,82.632 97.645,82.205 98.271,82.306L106.692,83.662C107.317,83.763 107.743,84.354 107.642,84.98C107.541,85.605 106.952,86.032 106.326,85.931L97.905,84.574Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M94.66,89.232L89.492,99.817"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M86.295,93.894L89.062,88.229"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M97.262,89.077L94.443,87.501L93.251,87.478C91.93,87.455 90.765,86.609 90.336,85.36C90.001,84.384 90.165,83.307 90.774,82.474L91.71,81.191L101.324,82.431L97.262,89.077Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M97.038,87.9L97.58,87.118"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.438,85.789L98.891,85.127"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.53,83.816L99.982,83.154"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M90.17,77.005L89.492,76.604"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.703,80.969H103.296"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.051,74.763H97.912C97.045,74.763 96.374,75.399 96.482,76.118L97.004,79.557C97.096,80.161 97.71,80.61 98.434,80.61H100.154"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#2BA7FF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M93.064,87.289L95.558,88.229"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M126.406,84.042L130.304,82.638"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M113.698,73.493L113.795,87.9"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M123.743,34.995C124.278,34.995 124.711,34.562 124.711,34.027C124.711,33.493 124.278,33.059 123.743,33.059C123.208,33.059 122.774,33.493 122.774,34.027C122.774,34.562 123.208,34.995 123.743,34.995Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M131.871,31.934C132.224,31.934 132.51,31.649 132.51,31.296C132.51,30.943 132.224,30.657 131.871,30.657C131.518,30.657 131.232,30.943 131.232,31.296C131.232,31.649 131.518,31.934 131.871,31.934Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M120.648,26.85H119.956V26.147C119.956,26.02 119.853,25.917 119.725,25.917C119.598,25.917 119.495,26.02 119.495,26.147V26.85H118.803C118.675,26.85 118.572,26.953 118.572,27.081C118.572,27.208 118.675,27.311 118.803,27.311H119.495V28.003C119.495,28.13 119.598,28.233 119.725,28.233C119.853,28.233 119.956,28.13 119.956,28.003V27.311H120.648C120.775,27.311 120.878,27.208 120.878,27.081C120.878,26.953 120.775,26.85 120.648,26.85Z"
      android:fillColor="#F0917A"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M29.132,88.423H28.209V87.501C28.209,87.333 28.073,87.197 27.905,87.197C27.737,87.197 27.6,87.333 27.6,87.501V88.423H26.678C26.51,88.423 26.373,88.559 26.373,88.727C26.373,88.895 26.51,89.031 26.678,89.031H27.6V89.953C27.6,90.121 27.737,90.257 27.905,90.257C28.073,90.257 28.209,90.121 28.209,89.953V89.031H29.132C29.3,89.031 29.436,88.895 29.436,88.727C29.436,88.559 29.3,88.423 29.132,88.423Z"
      android:fillColor="#529ED6"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M74.428,25.829C74.428,24.979 73.738,24.29 72.887,24.29C72.037,24.29 71.347,24.979 71.347,25.829V25.829C71.347,24.979 70.657,24.29 69.806,24.29C68.955,24.29 68.265,24.979 68.265,25.829C68.265,26.47 69.156,27.281 69.156,27.281L71.34,29.468L73.529,27.281C73.529,27.281 74.428,26.47 74.428,25.829Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M73.858,23.746V22.787"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M75.607,23.407L74.926,24.087"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M76.82,22.446L76.481,22.787"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M75.127,25.368H76.086"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M23.319,82.398C23.676,82.398 23.965,82.109 23.965,81.753C23.965,81.397 23.676,81.108 23.319,81.108C22.963,81.108 22.674,81.397 22.674,81.753C22.674,82.109 22.963,82.398 23.319,82.398Z"
      android:fillColor="#529ED6"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M29.632,82.311C30.102,82.311 30.483,81.93 30.483,81.46C30.483,80.991 30.102,80.61 29.632,80.61C29.162,80.61 28.781,80.991 28.781,81.46C28.781,81.93 29.162,82.311 29.632,82.311Z"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#E4F4FC"/>
  <path
      android:pathData="M171.83,127.315C172.626,127.315 173.272,126.67 173.272,125.875C173.272,125.079 172.626,124.434 171.83,124.434C171.034,124.434 170.389,125.079 170.389,125.875C170.389,126.67 171.034,127.315 171.83,127.315Z"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#EDA257"/>
  <path
      android:pathData="M171.83,137.283C172.171,137.283 172.448,137.006 172.448,136.665C172.448,136.324 172.171,136.048 171.83,136.048C171.489,136.048 171.212,136.324 171.212,136.665C171.212,137.006 171.489,137.283 171.83,137.283Z"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#EDA257"/>
  <path
      android:pathData="M167.404,108.988L173.221,107.769C173.716,107.664 174.233,107.761 174.657,108.037C175.081,108.313 175.377,108.747 175.482,109.242C175.698,110.272 175.037,111.283 174.005,111.5L168.189,112.719C167.693,112.824 167.177,112.728 166.752,112.452C166.328,112.175 166.032,111.742 165.928,111.247C165.824,110.752 165.921,110.235 166.198,109.812C166.475,109.388 166.909,109.092 167.404,108.988Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M160.831,125.133C160.278,126.565 159.021,127.608 157.51,127.886C155.999,128.165 154.452,127.638 153.425,126.497L142.054,113.853L152.486,103.533L160.731,121.728C161.219,122.804 161.254,124.03 160.831,125.133Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M155.259,197.081H145.129C142.732,197.081 140.79,195.21 140.79,192.903V188.86L155.845,192.474C156.912,192.71 157.676,193.649 157.69,194.742C157.678,195.374 157.416,195.975 156.96,196.414C156.505,196.853 155.894,197.093 155.261,197.081H155.259Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M126.326,197.081H136.456C138.853,197.081 140.795,195.21 140.795,192.903V188.86L125.74,192.474C124.673,192.71 123.909,193.649 123.895,194.742C123.907,195.374 124.169,195.975 124.625,196.414C125.08,196.853 125.691,197.093 126.324,197.081H126.326Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M144.48,191.501L140.79,190.137L137.623,191.411C137.111,191.617 136.529,191.555 136.072,191.246C135.614,190.937 135.34,190.421 135.34,189.869V181.704H146.718V189.941C146.717,190.484 146.451,190.992 146.005,191.303C145.56,191.614 144.991,191.688 144.48,191.501Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M146.722,186.558L135.342,188.171V181.704H146.722V186.558Z"
      android:fillColor="#F0917A"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M126.345,152.114C126.345,152.114 129.415,175.306 130.54,177.235C130.881,177.824 131.115,178.469 131.232,179.139L132.438,185.8H149.423L150.627,179.139C150.745,178.469 150.979,177.825 151.319,177.235C152.447,175.295 155.515,152.114 155.515,152.114L155.628,151.485C157.144,143.169 156.171,134.591 152.832,126.824L152.371,122.929C152.371,122.929 149.109,130.203 137.609,126.635C133.227,125.271 131.486,120.456 131.486,120.456L129.027,126.824C125.687,134.59 124.714,143.168 126.229,151.485L126.345,152.114Z"
      android:fillColor="#0D9AFF"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M138.227,141.802L140.896,143.259L143.564,141.802"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M152.527,124.328L152.359,122.945C152.359,122.945 150.368,127.368 144.035,127.615C148.722,127.785 151.298,125.759 152.527,124.328Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M131.651,177.24C130.526,175.299 127.456,152.119 127.456,152.119L127.343,151.489C125.828,143.173 126.801,134.595 130.141,126.829L132.099,121.758C131.865,121.341 131.661,120.907 131.488,120.461L129.029,126.829C125.689,134.595 124.716,143.173 126.231,151.489L126.344,152.119C126.344,152.119 129.414,175.306 130.54,177.24C130.88,177.829 131.115,178.473 131.232,179.144L132.438,185.804H133.55L132.346,179.144C132.226,178.474 131.991,177.83 131.651,177.24Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M153.399,71.897C150.263,69.659 149.54,65.297 151.781,62.163C154.019,59.031 158.375,58.302 161.508,60.538L163.482,61.947C166.618,64.186 167.341,68.548 165.1,71.682C162.862,74.814 158.507,75.543 155.374,73.307L153.399,71.897Z"
      android:fillColor="#FFD578"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M150.606,67.538C150.943,69.295 151.944,70.856 153.402,71.896L155.365,73.297C156.476,74.094 157.797,74.545 159.163,74.595C157.867,70.649 154.555,67.786 150.606,67.538Z"
      android:fillColor="#529ED6"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M166.35,68.556C166.199,69.681 165.773,70.752 165.112,71.674V71.674C162.867,74.806 158.507,75.528 155.371,73.288L153.409,71.886"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M160.369,72.656C158.168,73.525 155.675,73.219 153.75,71.843L151.792,70.446"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M163.495,70.216V70.216C163.132,70.723 162.704,71.179 162.222,71.573C162.084,71.69 161.939,71.798 161.788,71.898"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M155.351,63.841C154.446,64.528 153.724,65.427 153.25,66.459V66.459C152.987,67.024 152.802,67.622 152.698,68.236C152.668,68.416 152.645,68.593 152.629,68.773"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M154.488,67.252V67.252C154.227,67.818 154.042,68.416 153.937,69.031C153.907,69.208 153.884,69.388 153.868,69.568"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M150.498,65.768C150.582,64.469 151.027,63.221 151.785,62.163V62.163C154.03,59.032 158.389,58.31 161.525,60.55L163.488,61.951"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M136.68,82.173C138.304,82.173 139.621,80.857 139.621,79.234C139.621,77.611 138.304,76.296 136.68,76.296C135.056,76.296 133.74,77.611 133.74,79.234C133.74,80.857 135.056,82.173 136.68,82.173Z"
      android:fillColor="#E4F4FC"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M149.508,89.882C154.865,89.882 159.207,85.06 159.207,79.112C159.207,73.164 154.865,68.342 149.508,68.342C144.152,68.342 139.81,73.164 139.81,79.112C139.81,85.06 144.152,89.882 149.508,89.882Z"
      android:fillColor="#FFD578"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M140.417,101.329L142.031,104.162C142.52,105.024 143.809,105.178 144.529,104.459L147.654,101.329L150.237,89.018H142.995L140.417,101.329Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M141.899,94.265L148.502,97.252L149.812,91.007L142.593,90.96L141.899,94.265Z"
      android:fillColor="#F0917A"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M140.417,101.329L142.031,104.162C142.52,105.024 143.809,105.178 144.529,104.459L147.654,101.329L150.237,89.018H142.995L140.417,101.329Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M145.829,93.724C151.865,93.724 156.757,88.835 156.757,82.804C156.757,76.773 151.865,71.884 145.829,71.884C139.794,71.884 134.901,76.773 134.901,82.804C134.901,88.835 139.794,93.724 145.829,93.724Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M135.963,81.165C136.933,75.392 142.289,71.412 148.101,72.143C147.952,72.113 147.804,72.083 147.652,72.057C144.793,71.576 141.861,72.25 139.5,73.93C137.139,75.611 135.543,78.159 135.063,81.016C134.154,86.418 137.18,91.544 142.241,93.189C142.75,93.355 142.965,93.291 142.324,93.019C137.78,91.087 135.109,86.245 135.963,81.165Z"
      android:fillColor="#FFF4EE"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M138.82,78.282C138.142,78.247 137.508,78.618 137.206,79.225"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M141.98,79.232C141.302,79.266 140.668,78.896 140.366,78.289"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M135.46,86.254C136.775,90.205 140.223,93.065 144.351,93.629"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M147.345,93.62C151.499,93.038 154.954,90.134 156.239,86.144"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M138.151,86.118C137.628,86.118 137.145,85.839 136.883,85.387C136.621,84.934 136.621,84.376 136.883,83.923C137.145,83.47 137.628,83.191 138.151,83.191L138.949,81.7"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M138.564,81.069C138.795,81.069 138.982,80.785 138.982,80.435C138.982,80.085 138.795,79.801 138.564,79.801C138.334,79.801 138.147,80.085 138.147,80.435C138.147,80.785 138.334,81.069 138.564,81.069Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M139.937,81.069C140.167,81.069 140.354,80.785 140.354,80.435C140.354,80.085 140.167,79.801 139.937,79.801C139.706,79.801 139.519,80.085 139.519,80.435C139.519,80.785 139.706,81.069 139.937,81.069Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M138.79,80.322C138.876,80.322 138.945,80.253 138.945,80.167C138.945,80.082 138.876,80.013 138.79,80.013C138.705,80.013 138.636,80.082 138.636,80.167C138.636,80.253 138.705,80.322 138.79,80.322Z"
      android:fillColor="#FFF4EE"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M140.089,80.322C140.174,80.322 140.244,80.253 140.244,80.167C140.244,80.082 140.174,80.013 140.089,80.013C140.004,80.013 139.935,80.082 139.935,80.167C139.935,80.253 140.004,80.322 140.089,80.322Z"
      android:fillColor="#FFF4EE"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M135.443,79.402C135.083,80.5 134.9,81.649 134.901,82.804C134.901,83.106 134.913,83.406 134.938,83.701"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M156.679,84.18C156.736,83.721 156.765,83.259 156.764,82.797C156.769,77.739 153.298,73.34 148.375,72.164C143.451,70.988 138.364,73.344 136.08,77.858"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M144.651,83.816C145.237,83.816 145.712,83.341 145.712,82.756C145.712,82.17 145.237,81.696 144.651,81.696C144.065,81.696 143.59,82.17 143.59,82.756C143.59,83.341 144.065,83.816 144.651,83.816Z"
      android:strokeAlpha="0.6"
      android:fillColor="#F0917A"
      android:fillType="evenOdd"
      android:fillAlpha="0.6"/>
  <path
      android:pathData="M142.314,89.575C143.352,89.393 144.052,88.414 143.887,87.374V87.358"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M151.792,84.542C155.077,84.542 157.74,81.881 157.74,78.598C157.74,75.315 155.077,72.654 151.792,72.654C148.507,72.654 145.844,75.315 145.844,78.598C145.844,81.881 148.507,84.542 151.792,84.542Z"
      android:fillColor="#FFD578"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M147.082,68.254C146.646,68.242 146.21,68.287 145.786,68.388C145.081,65.417 142.532,63.245 139.485,63.016C136.438,62.789 133.594,64.558 132.454,67.39C131.314,70.223 132.14,73.467 134.496,75.411C136.853,77.354 140.197,77.55 142.764,75.895C144.041,77.87 146.533,78.675 148.726,77.822C150.919,76.969 152.209,74.692 151.814,72.374C151.418,70.056 149.446,68.335 147.093,68.257L147.082,68.254Z"
      android:fillColor="#FFD578"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M156.238,86.927C157.684,86.927 158.856,85.756 158.856,84.311C158.856,82.867 157.684,81.696 156.238,81.696C154.793,81.696 153.621,82.867 153.621,84.311C153.621,85.756 154.793,86.927 156.238,86.927Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M157.403,83.945C156.847,83.724 156.216,83.994 155.992,84.549"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M154.387,86.162C155.135,86.91 156.261,87.134 157.24,86.729C158.218,86.324 158.856,85.37 158.856,84.313C158.856,83.254 158.218,82.3 157.24,81.896C156.261,81.491 155.135,81.715 154.387,82.463L154.283,82.574"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M149.095,83.164C150.244,82.284 151.888,82.5 152.77,83.645V83.645C153.471,84.563 153.299,85.874 152.384,86.579C152.031,86.85 151.584,86.97 151.143,86.911C150.701,86.853 150.3,86.621 150.03,86.268V86.268C149.58,85.68 149.691,84.839 150.279,84.387C150.749,84.027 151.423,84.116 151.785,84.586C152.073,84.963 152.002,85.501 151.626,85.791"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#DBCC3F"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M136.975,63.329C137.567,63.174 138.178,63.097 138.79,63.099"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M131.924,68.565C132.266,67.086 133.074,65.755 134.23,64.77"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M144.09,74.744C142.162,76.952 139.065,77.733 136.319,76.704C133.574,75.675 131.755,73.051 131.755,70.121"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M147.59,82.804C146.016,81.229 145.452,78.91 146.125,76.789"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M157.742,73.417C158.714,75.158 159.222,77.119 159.216,79.112"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M149.508,68.342C151.433,68.348 153.305,68.971 154.85,70.119"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M133.497,68.001C133.82,67.262 134.298,66.601 134.899,66.063"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M135.46,74.871C134.159,73.977 133.296,72.578 133.079,71.016C133.046,70.763 133.029,70.508 133.029,70.253"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M164.521,64.26C165.395,65.802 165.652,67.618 165.239,69.342"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M156.405,86.927C156.685,86.927 156.912,86.7 156.912,86.42C156.912,86.14 156.685,85.913 156.405,85.913C156.125,85.913 155.897,86.14 155.897,86.42C155.897,86.7 156.125,86.927 156.405,86.927Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M140.931,177.8V183.158"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M135.901,185.804L140.931,183.596V185.71L135.901,185.804Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M144.206,183.451L149.236,181.953V182.87L144.206,183.451Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M140.896,186.906V190.628"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M149.988,190.628L149.22,192.946"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M151.942,191.052L151.171,193.37"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M132.187,190.455L132.957,192.773"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M130.235,190.879L131.003,193.197"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M137.305,134.826C137.305,135.822 136.497,136.628 135.501,136.628V136.628"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.15179"
      android:fillColor="#00000000"
      android:strokeColor="#55A3DE"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M133.481,162.361C133.784,162.731 133.927,163.206 133.879,163.682C133.83,164.158 133.594,164.594 133.222,164.896V164.896"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.15179"
      android:fillColor="#00000000"
      android:strokeColor="#58AAE7"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M136.705,151.78C136.705,152.258 136.515,152.716 136.177,153.054C135.838,153.392 135.38,153.582 134.901,153.582V153.582"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.15179"
      android:fillColor="#00000000"
      android:strokeColor="#5299D1"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M148.74,132.116C148.581,132.568 148.248,132.938 147.816,133.144C147.383,133.35 146.886,133.376 146.434,133.215V133.215"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.15179"
      android:fillColor="#00000000"
      android:strokeColor="#5299D1"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M148.235,151.923C147.248,152.045 146.349,151.344 146.226,150.358"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.15179"
      android:fillColor="#00000000"
      android:strokeColor="#5299D1"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M147.728,180.224C146.741,180.346 145.841,179.647 145.717,178.662V178.662"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.15179"
      android:fillColor="#00000000"
      android:strokeColor="#5299D1"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M152.221,157.564C152.22,158.558 151.414,159.364 150.419,159.367"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.15179"
      android:fillColor="#00000000"
      android:strokeColor="#5299D1"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M131.038,127.292C131.45,127.535 131.749,127.931 131.869,128.394C131.988,128.857 131.918,129.349 131.674,129.76V129.76"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.15179"
      android:fillColor="#00000000"
      android:strokeColor="#5299D1"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M130.102,142.692C130.959,143.199 131.243,144.303 130.738,145.16"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.15179"
      android:fillColor="#00000000"
      android:strokeColor="#5299D1"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M153.911,140.567C154.328,140.809 154.63,141.206 154.751,141.672C154.872,142.137 154.801,142.632 154.555,143.045V143.045"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.15179"
      android:fillColor="#00000000"
      android:strokeColor="#5299D1"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M143.456,168.095C144.444,168.201 145.159,169.087 145.052,170.074V170.074"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.15179"
      android:fillColor="#00000000"
      android:strokeColor="#55A3DE"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M135.46,175.382C135.871,175.625 136.169,176.021 136.288,176.483C136.407,176.945 136.337,177.435 136.094,177.846V177.846"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.15179"
      android:fillColor="#00000000"
      android:strokeColor="#55A3DE"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M143.452,157.567L140.931,153.582"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M142.192,158.466L140.931,156.979"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M139.342,171.884L140.896,169.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M140.896,144.497V175.38"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M152.216,166.274L154.004,162.87"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M152.744,169.5L153.397,166.862"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M127.632,161.321L130.102,164.893"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M128.865,166.274L127.97,163.626"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M150.03,182.455L150.629,179.144C150.747,178.474 150.981,177.829 151.321,177.24C152.449,175.299 155.517,152.118 155.517,152.118V152.118L155.63,151.489"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M146.722,185.804H149.423"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M134.42,185.804H144.257"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M127.971,129.514C125.499,136.564 124.9,144.134 126.234,151.485L126.347,152.114V152.114C126.347,152.114 129.417,175.306 130.542,177.235C130.883,177.824 131.117,178.469 131.234,179.139"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M156.322,143.927C156.354,138.049 155.168,132.228 152.837,126.831L152.375,122.936C152.375,122.936 149.114,130.21 137.614,126.642C133.232,125.278 131.49,120.463 131.49,120.463L129.031,126.831"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M151.718,128.525L152.532,131.858"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M150.258,130.781V128.525"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M133.552,128.525L134.112,126.709"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M153.538,128.596L152.528,128.852C145.117,130.721 137.266,129.542 130.732,125.58L129.754,124.987L132.752,107.492C133.486,101.555 138.896,97.336 144.838,98.068H144.852C150.794,98.802 155.015,104.21 154.281,110.147L153.538,128.596Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M147.589,114.738L148.436,114.863"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#262944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M136.343,111.009V111.009C136.865,112.376 138.086,113.353 139.535,113.563L146.441,114.575"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M144.868,97.799C141.687,97.411 138.624,98.706 136.382,100.659C138.074,103.629 141.073,105.625 144.469,106.04C147.524,106.419 150.595,105.477 152.913,103.452C151.284,100.589 148.394,98.234 144.868,97.799Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M144.376,105.547L144.097,107.824"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#F3D5CB"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M134.113,128.783L134.901,127.292"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M155.27,127.423C154.422,126.936 153.802,126.131 153.547,125.187C153.292,124.242 153.423,123.235 153.912,122.387L163.089,108.935L166.682,111.355L160.471,125.695C160.103,126.581 159.398,127.286 158.51,127.652C157.622,128.019 156.625,128.018 155.738,127.649C155.579,127.584 155.425,127.507 155.277,127.419L155.27,127.423Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M161.362,111.472L155.314,119.97"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#262944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M152.922,125.951L154.347,127.617"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#262944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M164.581,108.285H166.327C167.758,108.285 168.917,109.444 168.917,110.873C168.917,112.303 167.758,113.461 166.327,113.461H164.581C163.893,113.463 163.232,113.191 162.745,112.706C162.258,112.22 161.984,111.561 161.984,110.873C161.984,110.185 162.258,109.526 162.745,109.041C163.232,108.555 163.893,108.283 164.581,108.285Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M166.097,108.626L171.913,107.405C172.945,107.189 173.957,107.849 174.174,108.88C174.39,109.911 173.729,110.922 172.698,111.138L166.881,112.358C166.385,112.462 165.868,112.365 165.444,112.088C165.019,111.812 164.722,111.378 164.618,110.882C164.514,110.387 164.612,109.871 164.889,109.448C165.167,109.025 165.601,108.729 166.097,108.626Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M167.693,106.453L164.143,110.751C163.963,110.969 163.703,111.107 163.421,111.134C163.139,111.161 162.858,111.074 162.639,110.894C162.186,110.519 162.122,109.848 162.496,109.394L166.046,105.095C166.422,104.641 167.094,104.576 167.55,104.95C167.768,105.13 167.906,105.39 167.933,105.672C167.96,105.954 167.873,106.235 167.693,106.453Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M166.682,109.313C166.897,109.16 167.141,109.053 167.399,108.999L173.209,107.778C174.241,107.562 175.253,108.222 175.47,109.253V109.253"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M166.867,110.29C167.08,110.136 167.324,110.029 167.582,109.977L173.403,108.758C174.14,108.603 174.898,108.894 175.341,109.502"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M119.008,124.254C119.559,125.688 120.816,126.733 122.328,127.012C123.839,127.291 125.387,126.764 126.414,125.621L137.783,112.977L134.508,103.173L119.112,120.864C118.625,121.935 118.588,123.156 119.008,124.254Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M124.564,126.545C126.327,125.526 126.931,123.272 125.913,121.509L116.743,108.066L113.149,110.488L119.36,124.837C119.549,125.289 119.827,125.698 120.177,126.04C121.366,127.161 123.151,127.366 124.564,126.545Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M118.468,110.597L124.518,119.094"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#262944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M110.721,109.162C110.364,107.779 111.196,106.367 112.579,106.01L114.276,105.572C115.659,105.215 117.07,106.047 117.427,107.43L117.864,109.122C118.222,110.505 117.39,111.917 116.006,112.274L114.309,112.712C112.926,113.069 111.516,112.237 111.158,110.854L110.721,109.162Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M117.149,102.88L117.841,109.829C117.87,110.111 117.785,110.392 117.606,110.611C117.427,110.831 117.168,110.97 116.886,110.998C116.604,111.025 116.322,110.94 116.103,110.76C115.884,110.58 115.746,110.321 115.719,110.039L115.027,103.09C114.969,102.504 115.397,101.982 115.984,101.924C116.569,101.869 117.089,102.296 117.149,102.88Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M154.348,106.045L154.198,108.944"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#262944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M157.295,114.57L155.268,115.337C152.375,116.427 150.424,118.917 150.279,121.737L149.571,134.448"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#262944"/>
  <path
      android:pathData="M157.754,141.252V149.122"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#E4F4FC"/>
  <path
      android:pathData="M161.115,126.315L161.362,133.547"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#262944"/>
  <path
      android:pathData="M114.607,107.725C115.181,107.725 115.647,108.19 115.647,108.764V108.764C115.647,109.338 115.181,109.804 114.607,109.804H112.42"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.494,105.604C115.068,105.604 115.534,106.07 115.534,106.644V106.644C115.534,107.218 115.068,107.683 114.494,107.683H112.319"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M115.338,109.914C115.614,109.913 115.878,110.022 116.074,110.216C116.269,110.41 116.378,110.674 116.378,110.949V110.949V110.949C116.379,111.225 116.27,111.489 116.075,111.685C115.881,111.88 115.616,111.99 115.341,111.991H113.154"
      android:strokeLineJoin="round"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M156.077,134.9L155.422,135.361C155.311,135.439 155.236,135.559 155.214,135.693C155.192,135.828 155.225,135.965 155.305,136.075C155.316,136.092 155.328,136.107 155.342,136.121L156.896,137.841C158,140.256 157.678,143.084 156.057,145.189C154.437,147.294 151.785,148.331 149.165,147.885C148.876,147.838 148.59,147.981 148.455,148.24C148.32,148.499 148.366,148.816 148.57,149.025C148.69,149.152 148.814,149.274 148.946,149.394C151.777,151.982 156.146,151.887 158.861,149.177C161.577,146.468 161.68,142.103 159.094,139.27C159.06,139.23 159.023,139.196 158.986,139.157L156.822,135.082C156.69,134.832 156.381,134.735 156.13,134.865L156.077,134.9Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M156.347,135.306L155.782,135.879C155.686,135.975 155.632,136.106 155.632,136.241C155.632,136.377 155.686,136.507 155.782,136.603C155.795,136.617 155.809,136.629 155.824,136.64L157.669,138.064C159.166,140.252 159.334,143.087 158.104,145.435C156.875,147.783 154.449,149.263 151.797,149.281C151.505,149.286 151.249,149.477 151.161,149.755C151.073,150.033 151.173,150.336 151.409,150.507C151.549,150.612 151.695,150.711 151.845,150.805C155.081,152.788 159.303,151.911 161.478,148.802C163.653,145.693 163.029,141.43 160.054,139.074L159.929,138.982L157.099,135.347C157.013,135.242 156.888,135.176 156.753,135.163C156.617,135.15 156.482,135.191 156.377,135.278L156.347,135.306Z"
      android:fillColor="#F1D839"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M160.464,140.908C160.003,139.657 159.199,138.561 158.144,137.744L158.02,137.652H158.031"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M159.14,148.134C160.27,146.844 160.899,145.192 160.912,143.478"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M154.066,150.572C155.217,150.517 156.339,150.189 157.339,149.615"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M158.058,147.496C158.295,147.369 158.427,147.153 158.352,147.013C158.277,146.872 158.024,146.861 157.787,146.987C157.55,147.114 157.418,147.33 157.493,147.47C157.568,147.611 157.821,147.622 158.058,147.496Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M161.983,142.445C162.082,142.392 162.047,142.132 161.904,141.865C161.761,141.598 161.566,141.424 161.467,141.476C161.368,141.529 161.403,141.789 161.546,142.056C161.688,142.323 161.884,142.497 161.983,142.445Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M159.484,149.15C159.009,149.403 158.829,149.993 159.08,150.468C159.163,150.619 159.284,150.745 159.431,150.835C159.955,150.514 160.435,150.127 160.859,149.682C160.842,149.637 160.824,149.592 160.803,149.548C160.681,149.321 160.474,149.15 160.226,149.076C159.979,149.001 159.712,149.027 159.484,149.15Z"
      android:fillColor="#161E24"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M150.241,134.448L160.976,134.448A2.006,2.006 0,0 1,162.982 136.454L162.982,136.459A2.006,2.006 0,0 1,160.976 138.465L150.241,138.465A2.006,2.006 0,0 1,148.235 136.459L148.235,136.454A2.006,2.006 0,0 1,150.241 134.448z"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#262944"/>
  <path
      android:pathData="M162.982,151.148V151.148C162.982,152.257 162.083,153.156 160.973,153.156H150.246C149.137,153.154 148.237,152.257 148.235,151.148V151.148"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#262944"/>
  <path
      android:pathData="M165.093,138.989V138.989C165.093,140.097 164.194,140.996 163.084,140.996H148.235C147.126,140.996 146.226,140.097 146.226,138.989V138.989"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#262944"/>
  <path
      android:pathData="M165.093,142.031V142.031C165.094,142.563 164.883,143.074 164.506,143.451C164.13,143.828 163.619,144.04 163.087,144.04H148.235C147.126,144.04 146.226,143.141 146.226,142.033V142.033"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#262944"/>
  <path
      android:pathData="M165.093,145.075V145.075C165.093,146.184 164.194,147.083 163.084,147.083H148.235C147.126,147.083 146.226,146.184 146.226,145.075V145.075"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#262944"/>
  <path
      android:pathData="M165.093,148.115V148.115C165.093,149.224 164.194,150.123 163.084,150.123H148.235C147.703,150.123 147.191,149.912 146.814,149.536C146.437,149.16 146.225,148.65 146.224,148.118V148.118"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#262944"/>
  <path
      android:pathData="M161.361,137.58V148.12"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#262944"/>
  <path
      android:pathData="M150.029,137.58V149.122"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#262944"/>
  <path
      android:pathData="M153.111,137.58V148.986"
      android:strokeWidth="0.691075"
      android:fillColor="#00000000"
      android:strokeColor="#262944"/>
  <path
      android:pathData="M149.259,101.993L151.796,99.742C152.037,99.529 152.353,99.42 152.674,99.44C152.995,99.46 153.295,99.606 153.508,99.848C153.951,100.349 153.905,101.115 153.404,101.56L150.853,103.809C150.612,104.023 150.297,104.132 149.976,104.112C149.654,104.093 149.354,103.947 149.141,103.706C148.701,103.2 148.754,102.434 149.259,101.993Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M150.797,103.736L153.334,101.484C153.836,101.041 154.602,101.089 155.046,101.59C155.259,101.831 155.368,102.147 155.348,102.468C155.328,102.789 155.181,103.089 154.94,103.302L152.402,105.552C152.162,105.765 151.846,105.874 151.525,105.855C151.204,105.835 150.904,105.689 150.691,105.448C150.478,105.207 150.369,104.891 150.389,104.57C150.409,104.249 150.556,103.948 150.797,103.736Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M147.683,100.276L150.22,98.027C150.723,97.583 151.489,97.63 151.934,98.131C152.376,98.633 152.329,99.398 151.828,99.843L149.279,102.095C148.777,102.537 148.01,102.49 147.566,101.989C147.126,101.483 147.178,100.717 147.683,100.276Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M152.366,105.582L154.914,103.33C155.416,102.888 156.182,102.935 156.626,103.436C156.839,103.677 156.948,103.992 156.928,104.313C156.908,104.634 156.761,104.934 156.52,105.146L153.983,107.398C153.742,107.611 153.426,107.72 153.105,107.7C152.784,107.68 152.484,107.533 152.271,107.292C152.056,107.053 151.946,106.738 151.964,106.417C151.981,106.096 152.126,105.796 152.366,105.582Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M150.862,103.798C150.622,104.011 150.307,104.121 149.986,104.102C149.666,104.083 149.366,103.937 149.153,103.696V103.696C148.71,103.194 148.757,102.429 149.257,101.984"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M152.451,105.63C152.211,105.844 151.896,105.953 151.576,105.934C151.255,105.915 150.955,105.769 150.742,105.529V105.529C150.3,105.027 150.346,104.262 150.846,103.816"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M151.759,99.753L149.222,102.016C148.721,102.459 147.956,102.413 147.513,101.912V101.912C147.069,101.413 147.115,100.648 147.615,100.205V100.205"
      android:strokeLineJoin="round"
      android:strokeWidth="0.528175"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M123.004,113.503L130.159,105.219"
      android:strokeLineJoin="round"
      android:strokeWidth="0.692121"
      android:fillColor="#00000000"
      android:strokeColor="#262944"
      android:strokeLineCap="round"/>
</vector>
