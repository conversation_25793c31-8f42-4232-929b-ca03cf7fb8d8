<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="309dp"
    android:height="319dp"
    android:viewportWidth="309"
    android:viewportHeight="319">
  <path
      android:pathData="M22.512,284V272.46H17.852V269.9H30.272V272.46H25.612V284H22.512ZM31.529,284V269.9H34.549V275.52C34.896,275.013 35.342,274.633 35.889,274.38C36.436,274.113 37.042,273.98 37.709,273.98C40.016,273.98 41.169,275.353 41.169,278.1V284H38.149V278.24C38.149,277.56 38.022,277.073 37.769,276.78C37.516,276.473 37.143,276.32 36.649,276.32C36.009,276.32 35.496,276.52 35.109,276.92C34.736,277.32 34.549,277.853 34.549,278.52V284H31.529ZM48.524,284.22C47.364,284.22 46.364,284.013 45.524,283.6C44.697,283.173 44.057,282.58 43.604,281.82C43.164,281.047 42.944,280.14 42.944,279.1C42.944,278.087 43.157,277.2 43.584,276.44C44.024,275.667 44.617,275.067 45.364,274.64C46.124,274.2 46.997,273.98 47.984,273.98C49.41,273.98 50.544,274.433 51.384,275.34C52.224,276.233 52.644,277.447 52.644,278.98V279.74H45.824C45.93,280.513 46.21,281.08 46.664,281.44C47.13,281.787 47.77,281.96 48.584,281.96C49.117,281.96 49.657,281.88 50.204,281.72C50.75,281.56 51.244,281.313 51.684,280.98L52.484,283C51.99,283.373 51.384,283.673 50.664,283.9C49.957,284.113 49.244,284.22 48.524,284.22ZM48.104,275.98C47.464,275.98 46.944,276.173 46.544,276.56C46.157,276.947 45.917,277.487 45.824,278.18H50.144C50.064,276.713 49.384,275.98 48.104,275.98ZM63.787,284.22C62.933,284.22 62.18,284.013 61.527,283.6C60.887,283.173 60.387,282.58 60.027,281.82C59.667,281.047 59.487,280.133 59.487,279.08C59.487,278.027 59.667,277.12 60.027,276.36C60.387,275.6 60.887,275.013 61.527,274.6C62.18,274.187 62.933,273.98 63.787,273.98C64.44,273.98 65.04,274.12 65.587,274.4C66.133,274.68 66.547,275.053 66.827,275.52V269.9H69.847V284H66.887V282.52C66.62,283.04 66.207,283.453 65.647,283.76C65.1,284.067 64.48,284.22 63.787,284.22ZM64.687,281.96C65.327,281.96 65.847,281.727 66.247,281.26C66.66,280.793 66.867,280.067 66.867,279.08C66.867,278.107 66.66,277.393 66.247,276.94C65.847,276.473 65.327,276.24 64.687,276.24C64.047,276.24 63.527,276.473 63.127,276.94C62.727,277.393 62.527,278.107 62.527,279.08C62.527,280.067 62.727,280.793 63.127,281.26C63.527,281.727 64.047,281.96 64.687,281.96ZM75.494,284.22C74.76,284.22 74.114,284.08 73.554,283.8C72.994,283.52 72.547,283.14 72.214,282.66C71.894,282.18 71.734,281.64 71.734,281.04C71.734,280.333 71.92,279.767 72.294,279.34C72.667,278.913 73.274,278.613 74.114,278.44C74.954,278.253 76.067,278.16 77.454,278.16H78.174V277.82C78.174,277.233 78.04,276.82 77.774,276.58C77.507,276.327 77.054,276.2 76.414,276.2C75.88,276.2 75.314,276.287 74.714,276.46C74.127,276.62 73.54,276.867 72.954,277.2L72.134,275.18C72.48,274.953 72.9,274.747 73.394,274.56C73.9,274.373 74.427,274.233 74.974,274.14C75.52,274.033 76.04,273.98 76.534,273.98C78.054,273.98 79.187,274.327 79.934,275.02C80.68,275.7 81.054,276.76 81.054,278.2V284H78.234V282.52C78.034,283.04 77.694,283.453 77.214,283.76C76.747,284.067 76.174,284.22 75.494,284.22ZM76.174,282.2C76.734,282.2 77.207,282.007 77.594,281.62C77.98,281.233 78.174,280.733 78.174,280.12V279.72H77.474C76.447,279.72 75.72,279.813 75.294,280C74.867,280.173 74.654,280.48 74.654,280.92C74.654,281.293 74.78,281.6 75.034,281.84C75.3,282.08 75.68,282.2 76.174,282.2ZM88.127,284.22C85.367,284.22 83.987,282.88 83.987,280.2V276.44H82.127V274.18H83.987V271.3H87.007V274.18H89.907V276.44H87.007V280.08C87.007,280.64 87.133,281.06 87.386,281.34C87.653,281.62 88.073,281.76 88.646,281.76C88.82,281.76 89,281.74 89.187,281.7C89.386,281.66 89.6,281.607 89.826,281.54L90.267,283.74C89.987,283.887 89.653,284 89.267,284.08C88.88,284.173 88.5,284.22 88.127,284.22ZM91.272,272.28V269.46H94.532V272.28H91.272ZM91.392,284V274.18H94.412V284H91.392ZM96.744,284V274.18H99.704V275.62C100.037,275.087 100.484,274.68 101.044,274.4C101.604,274.12 102.231,273.98 102.924,273.98C104.084,273.98 104.951,274.32 105.524,275C106.097,275.667 106.384,276.7 106.384,278.1V284H103.364V278.24C103.364,277.56 103.237,277.073 102.984,276.78C102.731,276.473 102.357,276.32 101.864,276.32C101.224,276.32 100.711,276.52 100.324,276.92C99.951,277.32 99.764,277.853 99.764,278.52V284H96.744ZM113.359,287.82C112.412,287.82 111.525,287.72 110.699,287.52C109.885,287.32 109.172,287.007 108.559,286.58L109.379,284.52C109.912,284.853 110.512,285.107 111.179,285.28C111.859,285.467 112.499,285.56 113.099,285.56C114.805,285.56 115.659,284.773 115.659,283.2V282.08C115.392,282.587 114.972,282.993 114.399,283.3C113.825,283.607 113.199,283.76 112.519,283.76C111.639,283.76 110.872,283.56 110.219,283.16C109.565,282.747 109.059,282.173 108.699,281.44C108.339,280.707 108.159,279.847 108.159,278.86C108.159,277.887 108.339,277.033 108.699,276.3C109.059,275.567 109.565,275 110.219,274.6C110.872,274.187 111.639,273.98 112.519,273.98C113.225,273.98 113.859,274.133 114.419,274.44C114.979,274.747 115.392,275.153 115.659,275.66V274.18H118.599V282.86C118.599,284.513 118.152,285.753 117.259,286.58C116.365,287.407 115.065,287.82 113.359,287.82ZM113.419,281.5C114.085,281.5 114.619,281.267 115.019,280.8C115.432,280.333 115.639,279.687 115.639,278.86C115.639,278.033 115.432,277.393 115.019,276.94C114.619,276.473 114.085,276.24 113.419,276.24C112.739,276.24 112.199,276.473 111.799,276.94C111.399,277.393 111.199,278.033 111.199,278.86C111.199,279.687 111.399,280.333 111.799,280.8C112.199,281.267 112.739,281.5 113.419,281.5ZM129.83,284.22C129.096,284.22 128.45,284.08 127.89,283.8C127.33,283.52 126.883,283.14 126.55,282.66C126.23,282.18 126.07,281.64 126.07,281.04C126.07,280.333 126.256,279.767 126.63,279.34C127.003,278.913 127.61,278.613 128.45,278.44C129.29,278.253 130.403,278.16 131.79,278.16H132.51V277.82C132.51,277.233 132.376,276.82 132.11,276.58C131.843,276.327 131.39,276.2 130.75,276.2C130.216,276.2 129.65,276.287 129.05,276.46C128.463,276.62 127.876,276.867 127.29,277.2L126.47,275.18C126.816,274.953 127.236,274.747 127.73,274.56C128.236,274.373 128.763,274.233 129.31,274.14C129.856,274.033 130.376,273.98 130.87,273.98C132.39,273.98 133.523,274.327 134.27,275.02C135.016,275.7 135.39,276.76 135.39,278.2V284H132.57V282.52C132.37,283.04 132.03,283.453 131.55,283.76C131.083,284.067 130.51,284.22 129.83,284.22ZM130.51,282.2C131.07,282.2 131.543,282.007 131.93,281.62C132.316,281.233 132.51,280.733 132.51,280.12V279.72H131.81C130.783,279.72 130.056,279.813 129.63,280C129.203,280.173 128.99,280.48 128.99,280.92C128.99,281.293 129.116,281.6 129.37,281.84C129.636,282.08 130.016,282.2 130.51,282.2ZM137.642,287.6V274.18H140.602V275.64C140.869,275.133 141.276,274.733 141.822,274.44C142.382,274.133 143.009,273.98 143.702,273.98C144.556,273.98 145.302,274.187 145.942,274.6C146.596,275.013 147.102,275.6 147.462,276.36C147.822,277.12 148.002,278.027 148.002,279.08C148.002,280.133 147.822,281.047 147.462,281.82C147.102,282.58 146.596,283.173 145.942,283.6C145.302,284.013 144.556,284.22 143.702,284.22C143.049,284.22 142.449,284.08 141.902,283.8C141.369,283.52 140.956,283.147 140.662,282.68V287.6H137.642ZM142.802,281.96C143.442,281.96 143.962,281.727 144.362,281.26C144.762,280.793 144.962,280.067 144.962,279.08C144.962,278.107 144.762,277.393 144.362,276.94C143.962,276.473 143.442,276.24 142.802,276.24C142.149,276.24 141.622,276.473 141.222,276.94C140.822,277.393 140.622,278.107 140.622,279.08C140.622,280.067 140.822,280.793 141.222,281.26C141.622,281.727 142.149,281.96 142.802,281.96ZM149.849,287.6V274.18H152.809V275.64C153.076,275.133 153.483,274.733 154.029,274.44C154.589,274.133 155.216,273.98 155.909,273.98C156.763,273.98 157.509,274.187 158.149,274.6C158.803,275.013 159.309,275.6 159.669,276.36C160.029,277.12 160.209,278.027 160.209,279.08C160.209,280.133 160.029,281.047 159.669,281.82C159.309,282.58 158.803,283.173 158.149,283.6C157.509,284.013 156.763,284.22 155.909,284.22C155.256,284.22 154.656,284.08 154.109,283.8C153.576,283.52 153.163,283.147 152.869,282.68V287.6H149.849ZM155.009,281.96C155.649,281.96 156.169,281.727 156.569,281.26C156.969,280.793 157.169,280.067 157.169,279.08C157.169,278.107 156.969,277.393 156.569,276.94C156.169,276.473 155.649,276.24 155.009,276.24C154.356,276.24 153.829,276.473 153.429,276.94C153.029,277.393 152.829,278.107 152.829,279.08C152.829,280.067 153.029,280.793 153.429,281.26C153.829,281.727 154.356,281.96 155.009,281.96ZM172.443,284.22C169.683,284.22 168.303,282.88 168.303,280.2V276.44H166.443V274.18H168.303V271.3H171.323V274.18H174.223V276.44H171.323V280.08C171.323,280.64 171.45,281.06 171.703,281.34C171.97,281.62 172.39,281.76 172.963,281.76C173.136,281.76 173.316,281.74 173.503,281.7C173.703,281.66 173.916,281.607 174.143,281.54L174.583,283.74C174.303,283.887 173.97,284 173.583,284.08C173.196,284.173 172.816,284.22 172.443,284.22ZM175.709,284V269.9H178.729V275.52C179.075,275.013 179.522,274.633 180.069,274.38C180.615,274.113 181.222,273.98 181.889,273.98C184.195,273.98 185.349,275.353 185.349,278.1V284H182.329V278.24C182.329,277.56 182.202,277.073 181.949,276.78C181.695,276.473 181.322,276.32 180.829,276.32C180.189,276.32 179.675,276.52 179.289,276.92C178.915,277.32 178.729,277.853 178.729,278.52V284H175.709ZM190.923,284.22C190.19,284.22 189.543,284.08 188.983,283.8C188.423,283.52 187.977,283.14 187.643,282.66C187.323,282.18 187.163,281.64 187.163,281.04C187.163,280.333 187.35,279.767 187.723,279.34C188.097,278.913 188.703,278.613 189.543,278.44C190.383,278.253 191.497,278.16 192.883,278.16H193.603V277.82C193.603,277.233 193.47,276.82 193.203,276.58C192.937,276.327 192.483,276.2 191.843,276.2C191.31,276.2 190.743,276.287 190.143,276.46C189.557,276.62 188.97,276.867 188.383,277.2L187.563,275.18C187.91,274.953 188.33,274.747 188.823,274.56C189.33,274.373 189.857,274.233 190.403,274.14C190.95,274.033 191.47,273.98 191.963,273.98C193.483,273.98 194.617,274.327 195.363,275.02C196.11,275.7 196.483,276.76 196.483,278.2V284H193.663V282.52C193.463,283.04 193.123,283.453 192.643,283.76C192.177,284.067 191.603,284.22 190.923,284.22ZM191.603,282.2C192.163,282.2 192.637,282.007 193.023,281.62C193.41,281.233 193.603,280.733 193.603,280.12V279.72H192.903C191.877,279.72 191.15,279.813 190.723,280C190.297,280.173 190.083,280.48 190.083,280.92C190.083,281.293 190.21,281.6 190.463,281.84C190.73,282.08 191.11,282.2 191.603,282.2ZM203.556,284.22C200.796,284.22 199.416,282.88 199.416,280.2V276.44H197.556V274.18H199.416V271.3H202.436V274.18H205.336V276.44H202.436V280.08C202.436,280.64 202.563,281.06 202.816,281.34C203.083,281.62 203.503,281.76 204.076,281.76C204.25,281.76 204.43,281.74 204.616,281.7C204.816,281.66 205.03,281.607 205.256,281.54L205.696,283.74C205.416,283.887 205.083,284 204.696,284.08C204.31,284.173 203.93,284.22 203.556,284.22ZM212.389,287.6V274.18H215.349V275.64C215.615,275.133 216.022,274.733 216.569,274.44C217.129,274.133 217.755,273.98 218.449,273.98C219.302,273.98 220.049,274.187 220.689,274.6C221.342,275.013 221.849,275.6 222.209,276.36C222.569,277.12 222.749,278.027 222.749,279.08C222.749,280.133 222.569,281.047 222.209,281.82C221.849,282.58 221.342,283.173 220.689,283.6C220.049,284.013 219.302,284.22 218.449,284.22C217.795,284.22 217.195,284.08 216.649,283.8C216.115,283.52 215.702,283.147 215.409,282.68V287.6H212.389ZM217.549,281.96C218.189,281.96 218.709,281.727 219.109,281.26C219.509,280.793 219.709,280.067 219.709,279.08C219.709,278.107 219.509,277.393 219.109,276.94C218.709,276.473 218.189,276.24 217.549,276.24C216.895,276.24 216.369,276.473 215.969,276.94C215.569,277.393 215.369,278.107 215.369,279.08C215.369,280.067 215.569,280.793 215.969,281.26C216.369,281.727 216.895,281.96 217.549,281.96ZM228.216,284.22C226.962,284.22 226.036,283.873 225.436,283.18C224.849,282.487 224.556,281.447 224.556,280.06V274.18H227.576V280.14C227.576,281.3 228.076,281.88 229.076,281.88C229.649,281.88 230.116,281.68 230.476,281.28C230.849,280.867 231.036,280.327 231.036,279.66V274.18H234.056V284H231.116V282.66C230.462,283.7 229.496,284.22 228.216,284.22ZM241.212,284.22C238.452,284.22 237.072,282.88 237.072,280.2V276.44H235.212V274.18H237.072V271.3H240.092V274.18H242.992V276.44H240.092V280.08C240.092,280.64 240.219,281.06 240.472,281.34C240.739,281.62 241.159,281.76 241.732,281.76C241.906,281.76 242.086,281.74 242.272,281.7C242.472,281.66 242.686,281.607 242.912,281.54L243.352,283.74C243.072,283.887 242.739,284 242.352,284.08C241.966,284.173 241.586,284.22 241.212,284.22ZM248.238,284.22C247.372,284.22 246.565,284.12 245.818,283.92C245.072,283.72 244.452,283.447 243.958,283.1L244.678,281.14C245.172,281.447 245.732,281.693 246.358,281.88C246.998,282.053 247.632,282.14 248.258,282.14C248.818,282.14 249.232,282.053 249.498,281.88C249.765,281.693 249.898,281.453 249.898,281.16C249.898,280.693 249.558,280.4 248.878,280.28L246.778,279.9C245.938,279.753 245.298,279.453 244.858,279C244.418,278.547 244.198,277.953 244.198,277.22C244.198,276.553 244.385,275.98 244.758,275.5C245.132,275.02 245.645,274.647 246.298,274.38C246.952,274.113 247.705,273.98 248.558,273.98C249.265,273.98 249.952,274.073 250.618,274.26C251.285,274.433 251.858,274.713 252.338,275.1L251.578,277.04C251.178,276.747 250.698,276.507 250.138,276.32C249.592,276.133 249.078,276.04 248.598,276.04C247.998,276.04 247.565,276.14 247.298,276.34C247.032,276.527 246.898,276.767 246.898,277.06C246.898,277.527 247.212,277.82 247.838,277.94L249.938,278.32C250.805,278.467 251.465,278.76 251.918,279.2C252.372,279.627 252.598,280.213 252.598,280.96C252.598,281.987 252.198,282.787 251.398,283.36C250.598,283.933 249.545,284.22 248.238,284.22ZM264.709,284.22C261.949,284.22 260.569,282.88 260.569,280.2V276.44H258.709V274.18H260.569V271.3H263.589V274.18H266.489V276.44H263.589V280.08C263.589,280.64 263.715,281.06 263.969,281.34C264.235,281.62 264.655,281.76 265.229,281.76C265.402,281.76 265.582,281.74 265.769,281.7C265.969,281.66 266.182,281.607 266.409,281.54L266.849,283.74C266.569,283.887 266.235,284 265.849,284.08C265.462,284.173 265.082,284.22 264.709,284.22ZM271.294,284.22C270.561,284.22 269.914,284.08 269.354,283.8C268.794,283.52 268.348,283.14 268.014,282.66C267.694,282.18 267.534,281.64 267.534,281.04C267.534,280.333 267.721,279.767 268.094,279.34C268.468,278.913 269.074,278.613 269.914,278.44C270.754,278.253 271.868,278.16 273.254,278.16H273.974V277.82C273.974,277.233 273.841,276.82 273.574,276.58C273.308,276.327 272.854,276.2 272.214,276.2C271.681,276.2 271.114,276.287 270.514,276.46C269.928,276.62 269.341,276.867 268.754,277.2L267.934,275.18C268.281,274.953 268.701,274.747 269.194,274.56C269.701,274.373 270.228,274.233 270.774,274.14C271.321,274.033 271.841,273.98 272.334,273.98C273.854,273.98 274.988,274.327 275.734,275.02C276.481,275.7 276.854,276.76 276.854,278.2V284H274.034V282.52C273.834,283.04 273.494,283.453 273.014,283.76C272.548,284.067 271.974,284.22 271.294,284.22ZM271.974,282.2C272.534,282.2 273.008,282.007 273.394,281.62C273.781,281.233 273.974,280.733 273.974,280.12V279.72H273.274C272.248,279.72 271.521,279.813 271.094,280C270.668,280.173 270.454,280.48 270.454,280.92C270.454,281.293 270.581,281.6 270.834,281.84C271.101,282.08 271.481,282.2 271.974,282.2ZM282.767,284.22C281.501,284.22 280.574,283.893 279.987,283.24C279.401,282.573 279.107,281.587 279.107,280.28V269.9H282.127V280.16C282.127,281.227 282.601,281.76 283.547,281.76C283.694,281.76 283.834,281.753 283.967,281.74C284.114,281.727 284.254,281.7 284.387,281.66L284.347,284C283.841,284.147 283.314,284.22 282.767,284.22ZM289.427,284.22C288.161,284.22 287.234,283.893 286.647,283.24C286.061,282.573 285.767,281.587 285.767,280.28V269.9H288.787V280.16C288.787,281.227 289.261,281.76 290.207,281.76C290.354,281.76 290.494,281.753 290.627,281.74C290.774,281.727 290.914,281.7 291.047,281.66L291.007,284C290.501,284.147 289.974,284.22 289.427,284.22ZM98.753,312L94.753,302.18H97.813L100.153,308.48L102.493,302.18H104.493L106.833,308.56L109.173,302.18H112.073L108.113,312H105.553L103.393,306.42L101.293,312H98.753ZM117.685,312.22C116.631,312.22 115.711,312.013 114.925,311.6C114.151,311.173 113.551,310.58 113.125,309.82C112.698,309.047 112.485,308.133 112.485,307.08C112.485,306.04 112.698,305.14 113.125,304.38C113.551,303.607 114.151,303.013 114.925,302.6C115.711,302.187 116.631,301.98 117.685,301.98C118.738,301.98 119.651,302.187 120.425,302.6C121.211,303.013 121.818,303.607 122.245,304.38C122.685,305.14 122.905,306.04 122.905,307.08C122.905,308.133 122.685,309.047 122.245,309.82C121.818,310.58 121.211,311.173 120.425,311.6C119.651,312.013 118.738,312.22 117.685,312.22ZM117.685,309.96C118.325,309.96 118.845,309.727 119.245,309.26C119.658,308.793 119.865,308.067 119.865,307.08C119.865,306.107 119.658,305.393 119.245,304.94C118.845,304.473 118.325,304.24 117.685,304.24C117.045,304.24 116.525,304.473 116.125,304.94C115.725,305.393 115.525,306.107 115.525,307.08C115.525,308.067 115.725,308.793 116.125,309.26C116.525,309.727 117.045,309.96 117.685,309.96ZM124.782,312V302.18H127.742V303.6C128.035,303.093 128.435,302.7 128.942,302.42C129.462,302.127 130.062,301.98 130.742,301.98C131.435,301.98 132.022,302.133 132.502,302.44C132.995,302.747 133.369,303.213 133.622,303.84C133.929,303.253 134.369,302.8 134.942,302.48C135.529,302.147 136.175,301.98 136.882,301.98C138.002,301.98 138.835,302.32 139.382,303C139.929,303.667 140.202,304.7 140.202,306.1V312H137.182V306.2C137.182,305.547 137.075,305.073 136.862,304.78C136.662,304.473 136.315,304.32 135.822,304.32C135.249,304.32 134.802,304.52 134.482,304.92C134.162,305.32 134.002,305.887 134.002,306.62V312H130.982V306.2C130.982,305.547 130.875,305.073 130.662,304.78C130.449,304.473 130.102,304.32 129.622,304.32C129.049,304.32 128.602,304.52 128.282,304.92C127.962,305.32 127.802,305.887 127.802,306.62V312H124.782ZM147.615,312.22C146.455,312.22 145.455,312.013 144.615,311.6C143.789,311.173 143.149,310.58 142.695,309.82C142.255,309.047 142.035,308.14 142.035,307.1C142.035,306.087 142.249,305.2 142.675,304.44C143.115,303.667 143.709,303.067 144.455,302.64C145.215,302.2 146.089,301.98 147.075,301.98C148.502,301.98 149.635,302.433 150.475,303.34C151.315,304.233 151.735,305.447 151.735,306.98V307.74H144.915C145.022,308.513 145.302,309.08 145.755,309.44C146.222,309.787 146.862,309.96 147.675,309.96C148.209,309.96 148.749,309.88 149.295,309.72C149.842,309.56 150.335,309.313 150.775,308.98L151.575,311C151.082,311.373 150.475,311.673 149.755,311.9C149.049,312.113 148.335,312.22 147.615,312.22ZM147.195,303.98C146.555,303.98 146.035,304.173 145.635,304.56C145.249,304.947 145.009,305.487 144.915,306.18H149.235C149.155,304.713 148.475,303.98 147.195,303.98ZM153.492,312V302.18H156.452V303.62C156.785,303.087 157.232,302.68 157.792,302.4C158.352,302.12 158.979,301.98 159.672,301.98C160.832,301.98 161.699,302.32 162.272,303C162.845,303.667 163.132,304.7 163.132,306.1V312H160.112V306.24C160.112,305.56 159.985,305.073 159.732,304.78C159.479,304.473 159.105,304.32 158.612,304.32C157.972,304.32 157.459,304.52 157.072,304.92C156.699,305.32 156.512,305.853 156.512,306.52V312H153.492ZM178.333,300.28V297.46H181.573V300.28H178.333ZM171.713,312V304.44H169.853V302.18H171.713C171.74,300.793 172.14,299.753 172.913,299.06C173.7,298.353 174.893,297.953 176.493,297.86L177.453,297.8L177.573,300L176.833,300.04C176.06,300.08 175.513,300.247 175.193,300.54C174.886,300.82 174.733,301.253 174.733,301.84V302.18H181.453V312H178.433V304.44H174.733V312H171.713ZM183.785,312V302.18H186.745V303.88C187.212,302.733 188.212,302.1 189.745,301.98L190.625,301.92L190.805,304.46L189.105,304.64C187.612,304.787 186.865,305.547 186.865,306.92V312H183.785ZM195.69,312.22C194.823,312.22 194.016,312.12 193.27,311.92C192.523,311.72 191.903,311.447 191.41,311.1L192.13,309.14C192.623,309.447 193.183,309.693 193.81,309.88C194.45,310.053 195.083,310.14 195.71,310.14C196.27,310.14 196.683,310.053 196.95,309.88C197.216,309.693 197.35,309.453 197.35,309.16C197.35,308.693 197.01,308.4 196.33,308.28L194.23,307.9C193.39,307.753 192.75,307.453 192.31,307C191.87,306.547 191.65,305.953 191.65,305.22C191.65,304.553 191.836,303.98 192.21,303.5C192.583,303.02 193.096,302.647 193.75,302.38C194.403,302.113 195.156,301.98 196.01,301.98C196.716,301.98 197.403,302.073 198.07,302.26C198.736,302.433 199.31,302.713 199.79,303.1L199.03,305.04C198.63,304.747 198.15,304.507 197.59,304.32C197.043,304.133 196.53,304.04 196.05,304.04C195.45,304.04 195.016,304.14 194.75,304.34C194.483,304.527 194.35,304.767 194.35,305.06C194.35,305.527 194.663,305.82 195.29,305.94L197.39,306.32C198.256,306.467 198.916,306.76 199.37,307.2C199.823,307.627 200.05,308.213 200.05,308.96C200.05,309.987 199.65,310.787 198.85,311.36C198.05,311.933 196.996,312.22 195.69,312.22ZM206.496,312.22C203.736,312.22 202.356,310.88 202.356,308.2V304.44H200.496V302.18H202.356V299.3H205.376V302.18H208.276V304.44H205.376V308.08C205.376,308.64 205.502,309.06 205.756,309.34C206.022,309.62 206.442,309.76 207.016,309.76C207.189,309.76 207.369,309.74 207.556,309.7C207.756,309.66 207.969,309.607 208.196,309.54L208.636,311.74C208.356,311.887 208.022,312 207.636,312.08C207.249,312.173 206.869,312.22 206.496,312.22ZM209.972,312V308.8H213.212V312H209.972Z"
      android:fillColor="#161E24"/>
  <group>
    <clip-path
        android:pathData="M13.678,14.729h266v222h-266z"/>
    <path
        android:pathData="M227.747,75.76L43.053,75.76A12.978,12.978 0,0 0,30.075 88.739L30.075,88.739A12.978,12.978 0,0 0,43.053 101.717L227.747,101.717A12.978,12.978 0,0 0,240.725 88.739L240.725,88.739A12.978,12.978 0,0 0,227.747 75.76z"
        android:fillColor="#D6EEFF"/>
    <path
        android:pathData="M219.307,166.155L70.532,166.155A11.422,11.422 89.917,0 0,59.11 177.577L59.11,177.577A11.422,11.422 89.917,0 0,70.532 188.999L219.307,188.999A11.422,11.422 89.917,0 0,230.729 177.577L230.729,177.577A11.422,11.422 89.917,0 0,219.307 166.155z"
        android:fillColor="#D6EEFF"/>
    <path
        android:pathData="M201.889,121.603l-175.617,0l-0,24.558l175.617,0z"
        android:fillColor="#D6EEFF"/>
    <path
        android:pathData="M201.889,133.882m12.279,0a12.279,12.279 0,1 0,-24.558 0a12.279,12.279 0,1 0,24.558 0"
        android:fillColor="#D6EEFF"/>
    <path
        android:pathData="M26.271,133.882m12.279,0a12.279,12.279 0,1 0,-24.558 0a12.279,12.279 0,1 0,24.558 0"
        android:fillColor="#D6EEFF"/>
    <path
        android:pathData="M65.426,101.727H198.581V101.8C198.182,101.752 197.774,101.727 197.361,101.727C191.76,101.727 187.219,106.268 187.219,111.87C187.219,117.432 191.697,121.949 197.244,122.012H64.7V121.603C67.981,121.452 70.613,117.037 70.613,111.613C70.613,106.595 68.361,102.441 65.426,101.727ZM198.581,121.94C198.219,121.983 197.851,122.007 197.479,122.012H198.581V121.94Z"
        android:fillColor="#D6EEFF"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M193.892,166.158C188.372,166.158 183.897,161.683 183.897,156.163C183.897,150.649 188.363,146.178 193.874,146.169H107.672C113.184,146.178 117.65,150.649 117.65,156.163C117.65,161.682 113.176,166.156 107.658,166.158H193.896V166.158C193.894,166.158 193.893,166.158 193.892,166.158Z"
        android:fillColor="#D6EEFF"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M269.353,36.338L124.747,36.338A10.161,10.161 0,0 0,114.585 46.499L114.585,46.499A10.161,10.161 0,0 0,124.747 56.661L269.353,56.661A10.161,10.161 0,0 0,279.514 46.499L279.514,46.499A10.161,10.161 0,0 0,269.353 36.338z"
        android:fillColor="#D6EEFF"/>
    <path
        android:pathData="M262.744,107.114L146.26,107.114A8.943,8.943 0,0 0,137.317 116.057L137.317,116.057A8.943,8.943 0,0 0,146.26 125L262.744,125A8.943,8.943 135,0 0,271.687 116.057L271.687,116.057A8.943,8.943 135,0 0,262.744 107.114z"
        android:fillColor="#D6EEFF"/>
    <path
        android:pathData="M249.106,72.231l-137.5,0l-0,19.228l137.5,0z"
        android:fillColor="#D6EEFF"/>
    <path
        android:pathData="M249.106,81.845m9.614,0a9.614,9.614 0,1 0,-19.228 0a9.614,9.614 0,1 0,19.228 0"
        android:fillColor="#D6EEFF"/>
    <path
        android:pathData="M111.604,81.845m9.614,0a9.614,9.614 0,1 0,-19.228 0a9.614,9.614 0,1 0,19.228 0"
        android:fillColor="#D6EEFF"/>
    <path
        android:pathData="M141.694,72.231C144.264,72.113 146.324,68.656 146.324,64.409C146.324,60.482 144.562,57.231 142.266,56.669H245.442C241.111,56.733 237.62,60.264 237.62,64.61C237.62,68.995 241.176,72.551 245.561,72.551C245.885,72.551 246.204,72.532 246.518,72.494V72.552H141.694V72.231ZM246.518,56.669H245.681C245.964,56.674 246.243,56.693 246.518,56.725V56.669Z"
        android:fillColor="#D6EEFF"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M242.845,107.114C238.523,107.114 235.02,103.611 235.02,99.289C235.02,94.967 238.523,91.464 242.845,91.464H175.396C179.686,91.501 183.152,94.99 183.152,99.289C183.152,103.61 179.649,107.113 175.328,107.114V107.114H242.845ZM242.848,91.464V91.464H242.845C242.846,91.464 242.847,91.464 242.848,91.464Z"
        android:fillColor="#D6EEFF"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M258.577,103.749C259.429,103.749 260.119,103.058 260.119,102.205C260.119,101.352 259.429,100.661 258.577,100.661C257.726,100.661 257.036,101.352 257.036,102.205C257.036,103.058 257.726,103.749 258.577,103.749Z"
        android:strokeAlpha="0.6"
        android:strokeLineJoin="round"
        android:strokeWidth="1.2156"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#161E24"
        android:fillAlpha="0.6"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M271.511,98.866C272.073,98.866 272.528,98.41 272.528,97.848C272.528,97.285 272.073,96.829 271.511,96.829C270.95,96.829 270.495,97.285 270.495,97.848C270.495,98.41 270.95,98.866 271.511,98.866Z"
        android:strokeAlpha="0.6"
        android:strokeLineJoin="round"
        android:strokeWidth="1.2156"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#161E24"
        android:fillAlpha="0.6"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M245.196,39.222C245.757,39.222 246.212,38.766 246.212,38.204C246.212,37.641 245.757,37.185 245.196,37.185C244.634,37.185 244.179,37.641 244.179,38.204C244.179,38.766 244.634,39.222 245.196,39.222Z"
        android:strokeAlpha="0.6"
        android:strokeLineJoin="round"
        android:strokeWidth="1.2156"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#161E24"
        android:fillAlpha="0.6"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M253.652,90.753H252.533V89.632C252.533,89.429 252.369,89.264 252.166,89.264C251.963,89.264 251.799,89.429 251.799,89.632V90.753H250.698C250.495,90.753 250.331,90.918 250.331,91.121C250.331,91.324 250.495,91.489 250.698,91.489H251.799V92.592C251.799,92.795 251.963,92.96 252.166,92.96C252.369,92.96 252.533,92.795 252.533,92.592V91.489H253.652C253.855,91.489 254.02,91.324 254.02,91.121C254.02,90.918 253.855,90.753 253.652,90.753Z"
        android:strokeAlpha="0.6"
        android:fillColor="#F0917A"
        android:fillAlpha="0.6"/>
    <path
        android:pathData="M163.24,22.619H162.139V21.516C162.139,21.313 161.974,21.148 161.771,21.148C161.569,21.148 161.404,21.313 161.404,21.516V22.619H160.285C160.082,22.619 159.918,22.784 159.918,22.987C159.918,23.19 160.082,23.355 160.285,23.355H161.404V24.458C161.404,24.661 161.569,24.826 161.771,24.826C161.974,24.826 162.139,24.661 162.139,24.458V23.355H163.24C163.442,23.355 163.607,23.19 163.607,22.987C163.607,22.784 163.442,22.619 163.24,22.619Z"
        android:strokeAlpha="0.6"
        android:fillColor="#F0917A"
        android:fillAlpha="0.6"/>
    <path
        android:pathData="M126.593,29.332C126.627,28.432 126.168,27.586 125.396,27.125C124.623,26.665 123.662,26.665 122.889,27.125C122.117,27.586 121.658,28.432 121.693,29.332V29.332C121.693,27.975 120.595,26.875 119.241,26.875C117.887,26.875 116.789,27.975 116.789,29.332C116.789,30.354 118.206,31.648 118.206,31.648L121.693,35.138L125.176,31.648C125.176,31.648 126.593,30.354 126.593,29.332Z"
        android:strokeAlpha="0.6"
        android:strokeLineJoin="round"
        android:strokeWidth="0.904468"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#F0917A"
        android:fillAlpha="0.6"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M125.689,26.008V24.479"
        android:strokeAlpha="0.6"
        android:strokeLineJoin="round"
        android:strokeWidth="0.904468"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.6"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M128.472,25.467L127.393,26.552"
        android:strokeAlpha="0.6"
        android:strokeLineJoin="round"
        android:strokeWidth="0.904468"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.6"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M130.407,23.935L129.864,24.479"
        android:strokeAlpha="0.6"
        android:strokeLineJoin="round"
        android:strokeWidth="0.904468"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.6"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M127.708,28.597H129.239"
        android:strokeAlpha="0.6"
        android:strokeLineJoin="round"
        android:strokeWidth="0.904468"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.6"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M69.139,36.06H67.671V34.589C67.671,34.321 67.454,34.104 67.187,34.104C66.919,34.104 66.702,34.321 66.702,34.589V36.06H65.234C64.966,36.06 64.749,36.277 64.749,36.545C64.749,36.813 64.966,37.031 65.234,37.031H66.702V38.501C66.702,38.77 66.919,38.987 67.187,38.987C67.454,38.987 67.671,38.77 67.671,38.501V37.031H69.139C69.407,37.031 69.624,36.813 69.624,36.545C69.624,36.277 69.407,36.06 69.139,36.06Z"
        android:strokeAlpha="0.6"
        android:fillColor="#529ED6"
        android:fillAlpha="0.6"/>
    <path
        android:pathData="M59.887,26.459C60.455,26.459 60.915,25.998 60.915,25.429C60.915,24.86 60.455,24.399 59.887,24.399C59.319,24.399 58.859,24.86 58.859,25.429C58.859,25.998 59.319,26.459 59.887,26.459Z"
        android:strokeAlpha="0.6"
        android:fillColor="#529ED6"
        android:fillAlpha="0.6"/>
    <path
        android:pathData="M69.932,26.319C70.68,26.319 71.287,25.712 71.287,24.962C71.287,24.213 70.68,23.605 69.932,23.605C69.184,23.605 68.578,24.213 68.578,24.962C68.578,25.712 69.184,26.319 69.932,26.319Z"
        android:strokeAlpha="0.6"
        android:strokeWidth="1.2156"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#E4F4FC"
        android:fillAlpha="0.6"/>
    <path
        android:pathData="M43.561,106.452C44.122,106.452 44.578,105.996 44.578,105.434C44.578,104.871 44.122,104.415 43.561,104.415C42.999,104.415 42.544,104.871 42.544,105.434C42.544,105.996 42.999,106.452 43.561,106.452Z"
        android:strokeAlpha="0.6"
        android:strokeLineJoin="round"
        android:strokeWidth="1.2156"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#161E24"
        android:fillAlpha="0.6"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M30.154,215.724H111.554"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M132.305,235.854L184.449,235.854"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M36.52,214.934C36.52,214.284 31.525,200.066 34.731,199.091C37.937,198.115 38.866,214.934 38.866,214.934C38.866,214.934 39.354,205.386 41.538,205.015C43.722,204.666 40.632,213.262 40.632,213.262"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M31.897,208.894L34.336,212.239"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M180.713,215.794L264.907,215.794"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M256.75,204.829C257.958,208.081 258.353,215.213 258.353,215.213C258.353,215.213 258.724,208.012 260.374,207.733C262.023,207.477 259.7,213.959 259.7,213.959"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M254.032,213.215L255.867,215.747"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M259.212,201.228C259.119,200.462 258.422,199.927 257.656,200.02C256.889,200.113 256.355,200.81 256.448,201.577C256.471,201.67 256.471,201.67 256.448,201.577C256.355,200.81 255.658,200.276 254.891,200.369C254.125,200.462 253.59,201.159 253.683,201.925C253.753,202.506 254.636,203.133 254.636,203.133L256.843,204.876L258.585,202.669C258.562,202.622 259.282,201.786 259.212,201.228Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#EDA257"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M234.685,133.722C232.976,133.899 231.384,132.336 231.049,130.154L223.394,80.086C222.895,76.823 224.634,73.842 227.19,73.577C229.64,73.323 231.894,75.669 232.221,78.817L237.489,129.233C237.732,131.516 236.475,133.531 234.685,133.722Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M228.853,115.743L223.396,80.087"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M230.506,126.556L229.472,119.802"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M222.243,68.664C227.47,68.664 231.708,64.43 231.708,59.207C231.708,53.984 227.47,49.75 222.243,49.75C217.016,49.75 212.779,53.984 212.779,59.207C212.779,64.43 217.016,68.664 222.243,68.664Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M230.342,59.208C230.345,60.43 230.023,61.636 229.402,62.731"
        android:strokeAlpha="0.8"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#FFF4EE"
        android:fillAlpha="0.8"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M228.472,64.171C226.959,66.067 224.663,67.169 222.236,67.164"
        android:strokeAlpha="0.8"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#FFF4EE"
        android:fillAlpha="0.8"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M215.741,73.119L213.92,76.319C213.366,77.294 211.908,77.467 211.098,76.654L207.565,73.119L204.646,59.219H212.817L215.741,73.119Z"
        android:fillColor="#DFAD9C"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M214.065,65.128L206.601,68.507L205.121,61.446L213.281,61.394L214.065,65.128Z"
        android:fillColor="#F0917A"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M215.741,73.119L213.921,76.319C213.366,77.294 211.908,77.467 211.098,76.654L207.565,73.119L204.646,59.219H212.817L215.741,73.119Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.549244"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M193.327,67.164C198.14,67.164 202.041,63.266 202.041,58.457C202.041,53.648 198.14,49.75 193.327,49.75C188.515,49.75 184.614,53.648 184.614,58.457C184.614,63.266 188.515,67.164 193.327,67.164Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M196.839,57.02C197.909,57.02 198.777,56.153 198.777,55.083C198.777,54.014 197.909,53.146 196.839,53.146C195.768,53.146 194.901,54.014 194.901,55.083C194.901,56.153 195.768,57.02 196.839,57.02Z"
        android:fillColor="#DFAD9C"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M209.623,64.519C216.443,64.519 221.972,58.994 221.972,52.178C221.972,45.363 216.443,39.838 209.623,39.838C202.802,39.838 197.273,45.363 197.273,52.178C197.273,58.994 202.802,64.519 209.623,64.519Z"
        android:fillColor="#DFAD9C"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M198.304,52.178C198.313,45.569 203.53,40.14 210.138,39.864C209.968,39.864 209.795,39.864 209.623,39.864C202.802,39.864 197.273,45.389 197.273,52.205C197.273,58.395 201.607,63.541 207.555,64.427C208.155,64.516 208.383,64.406 207.615,64.221C202.19,62.894 198.304,57.997 198.304,52.178Z"
        android:fillColor="#D29784"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M208.226,31.236C203.85,30.368 199.353,31.993 196.544,35.457C193.735,38.92 193.077,43.653 194.833,47.751C198.884,51.879 205.302,52.538 210.109,49.321C214.915,46.104 216.748,39.922 214.47,34.608C212.778,32.878 210.601,31.703 208.226,31.236Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M217.955,116.317H212.114C207.729,116.317 203.907,113.333 202.847,109.082L195.212,78.498C194.886,77.194 195.179,75.813 196.007,74.754C196.835,73.695 198.106,73.077 199.45,73.079H218.206C220.673,73.078 223.04,74.056 224.785,75.798C226.53,77.54 227.511,79.904 227.512,82.368V106.774C227.51,112.046 223.231,116.318 217.955,116.317Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M221.972,56.813C223.042,56.813 223.91,55.946 223.91,54.877C223.91,53.807 223.042,52.94 221.972,52.94C220.902,52.94 220.034,53.807 220.034,54.877C220.034,55.946 220.902,56.813 221.972,56.813Z"
        android:fillColor="#DFAD9C"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M213.657,48.834L213.362,50.972C213.281,52.358 213.615,53.737 214.321,54.933L214.913,55.957L214.434,56.276C213.41,56.966 212.094,57.052 210.989,56.501L210.651,56.333"
        android:strokeLineJoin="round"
        android:strokeWidth="0.549244"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M217.842,51.898C217.812,52.567 217.261,53.095 216.591,53.095C215.921,53.095 215.369,52.567 215.34,51.898"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M211.626,51.898C211.596,52.567 211.045,53.095 210.375,53.095C209.705,53.095 209.153,52.567 209.124,51.898"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M203.32,55.471C203.099,54.818 203.449,54.109 204.101,53.887V53.887"
        android:strokeLineJoin="round"
        android:strokeWidth="0.549244"
        android:fillColor="#00000000"
        android:strokeColor="#F0917A"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M195.978,54.813C196.39,54.648 196.857,54.847 197.024,55.258"
        android:strokeLineJoin="round"
        android:strokeWidth="0.549244"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M222.109,55.203C222.189,54.771 222.601,54.484 223.035,54.559V54.559"
        android:strokeLineJoin="round"
        android:strokeWidth="0.549244"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M212.357,48.339C211.719,48.775 210.881,48.785 210.232,48.365"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M214.422,46.575C215.193,46.529 215.916,46.949 216.257,47.641"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M207.925,58.838C206.958,58.841 206.029,58.459 205.343,57.777C204.658,57.095 204.273,56.169 204.272,55.202V55.202"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M200.17,52.933C202.832,52.573 205.297,51.335 207.174,49.415"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M183.46,63.352C185.775,64.706 188.476,65.251 191.135,64.9"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M185.696,65.767C188.333,66.273 191.064,65.884 193.455,64.662"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M219.305,36.222C216.716,36.93 214.437,38.48 212.829,40.627"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M216.401,35.965C214.307,37.645 212.834,39.975 212.215,42.587"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M225.209,49.598C227.167,47.95 226.439,43.866 223.581,40.477C220.724,37.087 216.82,35.676 214.862,37.325C212.904,38.973 213.633,43.057 216.49,46.446C219.347,49.835 223.251,51.246 225.209,49.598Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M223.91,53.462C225.955,53.462 227.613,51.806 227.613,49.762C227.613,47.718 225.955,46.062 223.91,46.062C221.865,46.062 220.207,47.718 220.207,49.762C220.207,51.806 221.865,53.462 223.91,53.462Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M195.646,52.94C197.403,52.94 198.827,51.517 198.827,49.762C198.827,48.007 197.403,46.584 195.646,46.584C193.89,46.584 192.466,48.007 192.466,49.762C192.466,51.517 193.89,52.94 195.646,52.94Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M199.635,116.317C197.92,116.439 196.412,115.291 196.191,113.695L191.109,77.012C190.778,74.616 192.665,72.459 195.229,72.26C197.688,72.082 199.815,73.808 199.981,76.113L202.657,113.022C202.78,114.723 201.427,116.193 199.635,116.317Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M210.927,86.148H223.195L221.835,94.204C221.513,96.184 219.795,97.635 217.786,97.624H216.319C214.322,97.622 212.618,96.183 212.284,94.216L210.927,86.148Z"
        android:fillColor="#FF2F00"
        android:fillAlpha="0.9"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M195.824,111.069L191.109,77.041"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M200.7,86.078L201.679,99.571"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M201.417,86.083C200.614,86.083 199.963,85.433 199.963,84.631"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M199.93,78.995V76.718C199.93,76.102 199.796,75.493 199.537,74.935V74.935C199.208,74.226 198.691,73.621 198.04,73.188"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M218.159,74.411C218.157,73.953 218.062,73.499 217.878,73.079L211.762,72.938L205.437,73.089C205.254,73.509 205.158,73.962 205.157,74.421C205.157,76.952 208.066,79.003 211.654,79.003C215.242,79.003 218.159,76.942 218.159,74.411Z"
        android:fillColor="#DFAD9C"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M221.356,56.077C219.867,60.539 215.97,63.768 211.306,64.406"
        android:strokeLineJoin="round"
        android:strokeWidth="0.549244"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M197.371,53.732C197.308,53.216 197.276,52.698 197.275,52.179C197.275,45.362 202.805,39.836 209.627,39.836C216.449,39.836 221.979,45.362 221.979,52.179C221.986,52.516 221.973,52.854 221.939,53.19"
        android:strokeLineJoin="round"
        android:strokeWidth="0.549244"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M207.924,64.401C203.221,63.75 199.306,60.466 197.851,55.95"
        android:strokeLineJoin="round"
        android:strokeWidth="0.549244"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M196.637,57.663C196.993,57.663 197.282,57.375 197.282,57.019C197.282,56.663 196.993,56.374 196.637,56.374C196.281,56.374 195.992,56.663 195.992,57.019C195.992,57.375 196.281,57.663 196.637,57.663Z"
        android:fillColor="#FFF4EE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M222.754,57.103C223.11,57.103 223.399,56.814 223.399,56.458C223.399,56.102 223.11,55.813 222.754,55.813C222.398,55.813 222.109,56.102 222.109,56.458C222.109,56.814 222.398,57.103 222.754,57.103Z"
        android:fillColor="#FFF4EE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M208.653,74.016H211.294"
        android:strokeLineJoin="round"
        android:strokeWidth="0.549244"
        android:fillColor="#00000000"
        android:strokeColor="#F0917A"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M213.167,74.016H214.99"
        android:strokeLineJoin="round"
        android:strokeWidth="0.549244"
        android:fillColor="#00000000"
        android:strokeColor="#F0917A"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M212.899,74.635C212.899,74.946 212.646,75.198 212.335,75.198C212.024,75.198 211.771,74.946 211.771,74.635"
        android:strokeLineJoin="round"
        android:strokeWidth="0.549244"
        android:fillColor="#00000000"
        android:strokeColor="#F0917A"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M217.055,87.207C220.441,87.207 223.186,86.732 223.186,86.146C223.186,85.559 220.441,85.084 217.055,85.084C213.67,85.084 210.925,85.559 210.925,86.146C210.925,86.732 213.67,87.207 217.055,87.207Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M217.055,87.797C214.806,87.797 212.839,87.586 211.779,87.274"
        android:strokeLineJoin="round"
        android:strokeWidth="0.549244"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M216.127,81.168C215.849,81.5 215.874,81.99 216.185,82.292L216.197,82.304C216.523,82.62 216.533,83.139 216.219,83.466L216.197,83.488V83.488C215.881,83.794 215.861,84.294 216.151,84.624"
        android:strokeAlpha="0.8"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#FFF4EE"
        android:fillAlpha="0.8"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M218.957,79.558C218.676,79.888 218.702,80.379 219.014,80.679V80.679C219.172,80.831 219.263,81.039 219.267,81.257C219.271,81.476 219.188,81.687 219.036,81.844L219.014,81.866V81.866C218.699,82.172 218.679,82.671 218.969,83.002"
        android:strokeAlpha="0.8"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#FFF4EE"
        android:fillAlpha="0.8"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M230.019,113.882L227.421,108.051L223.874,109.323C218.744,111.162 213.254,111.772 207.845,111.104L203.736,110.596L202.125,113.882C198.228,121.834 197.075,130.851 198.846,139.526L198.98,140.18L201.427,159.579C201.727,161.98 202.571,164.282 203.895,166.309C204.288,166.912 204.564,167.585 204.708,168.291L212.304,205.556H219.837L227.431,168.291C227.575,167.584 227.852,166.912 228.246,166.309C229.569,164.281 230.411,161.98 230.71,159.579L233.156,140.18L233.29,139.526C235.063,130.851 233.913,121.834 230.019,113.882Z"
        android:fillColor="#0D9AFF"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M225.251,109.323L227.611,108.477L227.422,108.051L223.874,109.323C220.343,110.587 216.632,111.273 212.882,111.356C217.094,111.43 221.285,110.742 225.251,109.323Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M205.286,166.299C203.962,164.272 203.118,161.97 202.818,159.569L200.372,140.17L200.24,139.516C198.467,130.841 199.619,121.824 203.516,113.872L205.051,110.756L203.753,110.596L202.142,113.882C198.245,121.833 197.092,130.85 198.863,139.526L198.997,140.18L201.444,159.578C201.744,161.98 202.588,164.282 203.912,166.308C204.305,166.912 204.581,167.584 204.725,168.29L212.321,205.556H213.681L206.09,168.281C205.949,167.575 205.676,166.903 205.286,166.299Z"
        android:fillColor="#167DC6"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M213.239,202.714h5.586v9.081h-5.586z"
        android:fillColor="#DFAD9C"/>
    <path
        android:pathData="M218.825,206.558L213.239,207.859V202.714H218.825V206.558Z"
        android:fillColor="#F0917A"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M207.661,168.734C207.661,169.735 206.849,170.546 205.848,170.546C204.846,170.546 204.034,169.735 204.034,168.734V168.734"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M228.206,168.734C228.171,169.71 227.369,170.483 226.392,170.483C225.415,170.483 224.613,169.71 224.577,168.734"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M216.074,205.547H210.529L209.934,202.518H216.074V205.547Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M216.075,205.547H221.618L222.213,202.518H216.075V205.547Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M212.906,136.437L216.031,137.954L219.159,136.437"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M234.104,215.422H216.034V210.046H229.477C232.033,210.046 234.104,212.116 234.104,214.669V215.422Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M197.232,215.422H216.033V210.046H201.849C199.294,210.046 197.222,212.116 197.222,214.669L197.232,215.422Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M220.442,207.594C220.442,208.224 219.3,210.046 219.3,210.046C219.3,210.046 218.158,208.224 218.158,207.594C218.158,206.964 218.669,206.453 219.3,206.453C219.93,206.453 220.442,206.964 220.442,207.594Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M221.756,211.186C221.128,211.186 219.302,210.045 219.302,210.045C219.302,210.045 221.128,208.906 221.756,208.906C222.163,208.888 222.55,209.087 222.77,209.431C222.99,209.774 223.01,210.208 222.823,210.57C222.635,210.932 222.269,211.167 221.862,211.186H221.756Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M211.868,207.594C211.868,208.224 213.009,210.046 213.009,210.046C213.009,210.046 214.151,208.224 214.151,207.594C214.151,206.964 213.64,206.453 213.009,206.453C212.379,206.453 211.868,206.964 211.868,207.594V207.594Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M210.563,211.186C211.194,211.186 213.019,210.046 213.019,210.046C213.019,210.046 211.194,208.907 210.563,208.907C209.953,208.935 209.473,209.437 209.473,210.047C209.473,210.657 209.953,211.159 210.563,211.186V211.186Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M212.308,205.547H216.074H219.837"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M198.851,139.517L198.986,140.171V140.171L201.432,159.569C201.732,161.971 202.576,164.272 203.9,166.299C204.294,166.903 204.57,167.575 204.713,168.281L210.271,195.543"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M221.418,110.103C217.004,111.325 212.391,111.665 207.845,111.104L203.737,110.596L202.125,113.882C199.053,120.147 197.673,127.105 198.122,134.067"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M223.874,109.323C223.498,109.457 223.116,109.587 222.735,109.709"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M232.214,147.714L233.173,140.171V140.171L233.308,139.517C235.078,130.842 233.926,121.825 230.029,113.873L227.431,108.042L225.433,108.761"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M231.569,152.822L231.928,149.956"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M220.9,200.341L227.433,168.281C227.578,167.575 227.855,166.903 228.249,166.299C229.57,164.272 230.412,161.97 230.71,159.569"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M234.104,214.671V215.419H216.031V210.046"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M226.393,210.046H229.48C231.285,210.046 232.925,211.095 233.682,212.733"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M208.747,210.046H201.849C199.294,210.046 197.222,212.116 197.222,214.669V214.669V215.419H211.203"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M213.683,215.419H216.031V210.046H213.683"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M216.032,140.562V172.056"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M216.032,180.552V199.298"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M199.636,116.317C197.921,116.439 196.412,115.291 196.192,113.695"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M207.809,113.153C213.216,113.82 218.705,113.21 223.833,111.373L228.201,109.803L227.421,108.051L223.874,109.323C218.744,111.162 213.254,111.772 207.845,111.104L203.736,110.596L202.993,112.113L207.809,113.153Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M199.963,126.566V126.566C201.93,124.783 203.254,122.403 203.731,119.793L203.748,119.694"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M231.806,125.005V125.005C229.84,123.222 228.516,120.843 228.038,118.234L228.021,118.136"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M210.467,112.569L210.184,115.014"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M211.874,114.505V112.88"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M222.505,111.068L223.303,113.693"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M204.108,101.494L207.996,101.478"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M185.952,58.457C185.952,55.862 187.319,53.458 189.55,52.13"
        android:strokeAlpha="0.8"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#FFF4EE"
        android:fillAlpha="0.8"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M216.032,202.047V204.964"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M217.782,210.046H216.031"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M219.782,215.419H216.031V210.046H216.657L219.782,215.419Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M216.074,208.482V210.045"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M211.663,78.994C215.261,78.994 218.159,76.943 218.159,74.412"
        android:strokeLineJoin="round"
        android:strokeWidth="0.549244"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M205.166,74.412C205.196,75.477 205.675,76.48 206.485,77.173"
        android:strokeLineJoin="round"
        android:strokeWidth="0.549244"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M208.32,73.187H202.664"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M222.651,86.242L220.732,94.391C220.476,95.898 219.399,97.137 217.941,97.602C219.875,97.531 221.493,96.109 221.812,94.201L223.196,86.148L222.651,86.242Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M218.346,86.525C219.004,86.525 219.538,86.364 219.538,86.165C219.538,85.967 219.004,85.806 218.346,85.806C217.688,85.806 217.154,85.967 217.154,86.165C217.154,86.364 217.688,86.525 218.346,86.525Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M221.09,86.368C221.423,86.368 221.694,86.27 221.694,86.15C221.694,86.029 221.423,85.932 221.09,85.932C220.756,85.932 220.485,86.029 220.485,86.15C220.485,86.27 220.756,86.368 221.09,86.368Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M198.532,115.807C197.121,115.119 196.532,113.415 197.221,112.005L203.749,98.636C204.437,97.227 206.141,96.644 207.551,97.331C208.962,98.019 209.551,99.723 208.862,101.134L202.334,114.502C201.646,115.911 199.942,116.494 198.532,115.807Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M208.366,101.87L208.862,101.151C209.308,100.239 209.232,99.158 208.664,98.316C208.096,97.474 207.121,96.999 206.107,97.069C205.093,97.14 204.194,97.746 203.749,98.659L201.489,103.287L208.366,101.87Z"
        android:fillColor="#DFAD9C"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M211.798,98.479C210.505,98.465 209.222,98.251 207.994,97.844C203.893,96.516 201.384,93.714 202.159,91.325C202.792,89.367 205.353,88.449 208.68,88.974C209.121,89.044 209.492,89.344 209.651,89.761C209.811,90.178 209.736,90.649 209.455,90.995C209.173,91.342 208.728,91.513 208.287,91.442C206.128,91.097 204.689,91.61 204.538,92.094C204.329,92.734 205.778,94.491 208.766,95.466C210.169,95.919 211.611,96.085 212.724,95.914C213.609,95.78 214.086,95.466 214.163,95.212C214.324,94.714 213.393,93.295 210.966,92.233C210.556,92.055 210.273,91.672 210.223,91.228C210.173,90.784 210.363,90.347 210.723,90.082C211.082,89.817 211.556,89.764 211.966,89.942C215.391,91.433 217.228,93.863 216.542,95.984C216.127,97.259 214.904,98.112 213.093,98.381C212.665,98.446 212.232,98.479 211.798,98.479Z"
        android:fillColor="#DFAD9C"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M207.998,101.485C207.476,101.084 207.379,100.332 207.781,99.81C208.183,99.288 208.933,99.189 209.455,99.591L216.484,104.996C217.007,105.398 217.103,106.149 216.701,106.671C216.299,107.193 215.549,107.292 215.027,106.891L207.998,101.485Z"
        android:fillColor="#DFAD9C"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M210.421,100.315C209.85,99.986 209.656,99.254 209.985,98.683C210.315,98.113 211.045,97.916 211.615,98.245L219.297,102.676C219.868,103.006 220.062,103.738 219.733,104.308C219.403,104.879 218.673,105.076 218.102,104.747L210.421,100.315Z"
        android:fillColor="#DFAD9C"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M211.085,98.655C210.435,98.55 209.993,97.936 210.098,97.285C210.203,96.635 210.815,96.191 211.465,96.296L220.222,97.706C220.873,97.811 221.315,98.425 221.21,99.076C221.105,99.726 220.493,100.17 219.842,100.066L211.085,98.655Z"
        android:fillColor="#DFAD9C"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M207.711,103.497L202.336,114.505"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M199.012,108.346L201.89,102.455"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M210.416,103.337L207.485,101.698L206.245,101.674C204.872,101.65 203.66,100.77 203.214,99.471C202.866,98.457 203.036,97.337 203.669,96.471L204.643,95.136L214.64,96.425L210.416,103.337Z"
        android:fillColor="#DFAD9C"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M210.184,102.112L210.747,101.3"
        android:strokeLineJoin="round"
        android:strokeWidth="0.549244"
        android:fillColor="#00000000"
        android:strokeColor="#F0917A"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M211.64,99.917L212.11,99.229"
        android:strokeLineJoin="round"
        android:strokeWidth="0.549244"
        android:fillColor="#00000000"
        android:strokeColor="#F0917A"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M212.774,97.866L213.245,97.178"
        android:strokeLineJoin="round"
        android:strokeWidth="0.549244"
        android:fillColor="#00000000"
        android:strokeColor="#F0917A"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M203.041,90.783L202.336,90.366"
        android:strokeLineJoin="round"
        android:strokeWidth="0.549244"
        android:fillColor="#00000000"
        android:strokeColor="#F0917A"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M216.075,94.905H216.691"
        android:strokeLineJoin="round"
        android:strokeWidth="0.549244"
        android:fillColor="#00000000"
        android:strokeColor="#F0917A"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M211.237,88.451H211.093C210.191,88.451 209.493,89.113 209.606,89.86L210.148,93.436C210.244,94.064 210.882,94.532 211.635,94.532H213.424"
        android:strokeAlpha="0.9"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#FF2F00"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M206.051,101.478L208.644,102.455"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M240.723,98.1L244.777,96.641"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M227.508,87.131L227.609,102.112"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M76.179,208.986C77.526,208.382 78.432,207.058 78.548,205.525L77.735,188.009H69.604L71.718,205.897C71.718,208.313 74.065,209.939 76.179,208.986Z"
        android:fillColor="#F9D45C"/>
    <path
        android:pathData="M83.426,208.986C82.079,208.382 81.173,207.058 81.057,205.525L81.87,188.009H90.001L87.886,205.897C87.886,208.313 85.54,209.939 83.426,208.986Z"
        android:fillColor="#F9D45C"/>
    <path
        android:pathData="M70.719,197.487C72.786,198.718 75.295,198.811 77.409,197.72C77.665,197.58 77.92,197.441 78.129,197.255L77.711,188.032H69.58L70.719,197.487Z"
        android:fillColor="#DCB73E"/>
    <path
        android:pathData="M81.452,197.301C81.661,197.464 81.87,197.58 82.102,197.696C84.263,198.788 86.795,198.695 88.886,197.417L90.001,188.009H81.87L81.452,197.301Z"
        android:fillColor="#DCB73E"/>
    <path
        android:pathData="M101.733,215.422H85.959V212.657C85.959,211.774 85.331,210.985 84.449,210.892C83.403,210.775 82.544,211.565 82.544,212.587V215.422H79.779V207.523C79.779,206.478 80.778,205.734 81.777,206.013L98.341,210.868C100.339,211.496 101.733,213.331 101.733,215.422Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M57.896,215.422H73.67V212.657C73.67,211.774 74.297,210.985 75.18,210.892C76.225,210.775 77.085,211.565 77.085,212.587V215.422H79.849V207.523C79.849,206.478 78.85,205.734 77.851,206.013L61.288,210.868C59.267,211.496 57.896,213.331 57.896,215.422Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M69.929,211.728C70.045,212.425 70.673,212.89 71.37,212.797C72.066,212.704 72.531,212.053 72.438,211.356C72.322,210.659 70.742,208.592 70.742,208.592C70.742,208.592 69.813,211.031 69.929,211.728Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M67.537,209.358C66.979,209.776 66.863,210.566 67.281,211.124C67.699,211.681 68.489,211.797 69.047,211.379C69.604,210.961 70.673,208.568 70.673,208.568C70.673,208.568 68.094,208.94 67.537,209.358Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M76.922,200.508C76.271,200.508 75.737,201.042 75.737,201.693"
        android:strokeAlpha="0.7"
        android:strokeLineJoin="round"
        android:strokeWidth="1.16155"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.7"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M74.901,204.713C74.482,204.225 73.716,204.155 73.228,204.597"
        android:strokeAlpha="0.7"
        android:strokeLineJoin="round"
        android:strokeWidth="1.16155"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.7"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M73.368,199.555C73.275,198.905 72.647,198.463 71.997,198.556"
        android:strokeAlpha="0.7"
        android:strokeLineJoin="round"
        android:strokeWidth="1.16155"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.7"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M89.699,211.728C89.583,212.425 88.956,212.89 88.259,212.797C87.562,212.704 87.097,212.053 87.19,211.356C87.306,210.659 88.886,208.592 88.886,208.592C88.886,208.592 89.792,211.031 89.699,211.728Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M92.068,209.358C92.626,209.776 92.742,210.566 92.324,211.124C91.906,211.681 91.116,211.797 90.558,211.379C90.001,210.961 88.932,208.568 88.932,208.568C88.932,208.568 91.511,208.94 92.068,209.358Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M82.753,201.089C83.217,200.624 83.961,200.624 84.425,201.089"
        android:strokeAlpha="0.7"
        android:strokeLineJoin="round"
        android:strokeWidth="1.16155"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.7"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M85.029,203.992C85.679,203.852 86.307,204.247 86.446,204.898"
        android:strokeAlpha="0.7"
        android:strokeLineJoin="round"
        android:strokeWidth="1.16155"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.7"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M85.657,198.208C86.26,197.929 86.957,198.185 87.236,198.765"
        android:strokeAlpha="0.7"
        android:strokeLineJoin="round"
        android:strokeWidth="1.16155"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.7"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M79.849,209.126V214.632"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M95.088,112.974L92.208,108.142L89.861,109.373C83.543,112.672 76.016,112.672 69.697,109.373L67.351,108.142L64.47,112.974C58.685,122.661 56.618,134.369 58.709,145.683L68.535,192.423C68.721,193.306 69.186,194.096 69.883,194.654C72.09,196.442 74.993,196.721 77.433,195.467C78.083,195.118 78.664,194.63 79.152,194.073L79.779,193.329L80.406,194.073C80.894,194.63 81.452,195.118 82.125,195.467C84.541,196.698 87.468,196.419 89.675,194.654C90.349,194.096 90.814,193.329 91.023,192.423L100.873,145.683C102.94,134.393 100.873,122.661 95.088,112.974Z"
        android:fillColor="#0D9AFF"/>
    <path
        android:pathData="M58.108,141.512L55.994,139.654C54.484,138.306 53.485,136.471 53.207,134.473C53.09,133.683 52.347,133.126 51.557,133.242C50.767,133.358 50.21,134.101 50.326,134.891C50.698,137.586 52.045,140.049 54.066,141.837L56.18,143.696C56.412,143.905 56.714,144.021 57.016,144.044C57.458,144.091 57.922,143.928 58.248,143.556C58.782,142.952 58.712,142.046 58.108,141.512Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M48.049,139.491L54.368,143.348C55.065,143.766 55.948,143.557 56.366,142.86L56.436,142.744C56.854,142.047 56.645,141.164 55.948,140.746L49.629,136.889C48.932,136.471 48.049,136.68 47.631,137.377L47.561,137.493C47.143,138.167 47.352,139.073 48.049,139.491Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M55.901,140.699L49.699,136.912C48.978,136.471 48.026,136.703 47.608,137.423"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M47.445,142.441L54.531,144.578C55.297,144.811 56.11,144.369 56.343,143.603L56.389,143.463C56.621,142.697 56.18,141.884 55.414,141.651L48.328,139.514C47.561,139.282 46.748,139.723 46.516,140.49L46.47,140.629C46.237,141.396 46.655,142.209 47.445,142.441Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M54.624,145.136L56.412,146.228C57.644,146.972 59.246,146.6 59.99,145.369L60.129,145.16C60.873,143.928 60.501,142.325 59.27,141.582L57.481,140.49C56.25,139.747 54.647,140.118 53.903,141.35L53.764,141.559C52.997,142.79 53.392,144.393 54.624,145.136Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M49.536,139.886L54.856,141.628"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M55.785,141.744L57.969,132.661L63.219,135.495L59.549,144.3L55.785,141.744Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M55.065,149.527L59.455,145.462C59.897,145.067 59.92,144.37 59.502,143.952L59.432,143.882C59.037,143.441 58.34,143.418 57.922,143.836L53.532,147.924C53.09,148.319 53.067,149.016 53.485,149.434L53.555,149.504C53.95,149.922 54.623,149.945 55.065,149.527Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M56.947,144.718L54.995,146.53"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M56.529,138.632L56.947,136.959L60.873,141.141L56.529,138.632Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M69.673,109.373L67.327,108.142L64.446,112.974C64.075,113.624 63.703,114.251 63.354,114.925L60.985,128.306L65.19,136.507L72.623,110.627C71.625,110.279 70.626,109.861 69.673,109.373Z"
        android:strokeAlpha="0.5"
        android:fillColor="#161E24"
        android:fillAlpha="0.5"/>
    <path
        android:pathData="M63.866,136.506L55.898,136.808L61.334,85.514C62.078,80.543 66.05,76.663 71.068,76.083L75.226,75.595L63.866,136.506Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M63.866,136.506L55.898,136.808L61.334,85.514C62.078,80.543 66.05,76.663 71.068,76.083L75.226,75.595L63.866,136.506Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="75.5947"
            android:startX="65.5623"
            android:endY="136.808"
            android:endX="65.5623"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M46.466,214.19H55.084"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M108.028,134.671L110.142,132.813C111.652,131.465 112.651,129.63 112.929,127.632C113.046,126.842 113.789,126.285 114.579,126.401C115.369,126.517 115.926,127.261 115.81,128.05C115.438,130.745 114.091,133.208 112.07,134.997L109.956,136.855C109.724,137.064 109.422,137.18 109.12,137.203C108.678,137.25 108.214,137.087 107.888,136.716C107.354,136.112 107.424,135.206 108.028,134.671Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M118.087,132.65L111.768,136.507C111.071,136.925 110.188,136.716 109.77,136.019L109.7,135.903C109.282,135.206 109.491,134.323 110.188,133.905L116.507,130.049C117.204,129.63 118.087,129.839 118.505,130.536L118.575,130.653C118.993,131.326 118.784,132.232 118.087,132.65Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M110.235,133.858L116.438,130.072C117.158,129.63 118.11,129.862 118.528,130.583"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M118.691,135.6L111.605,137.738C110.839,137.97 110.026,137.529 109.793,136.762L109.747,136.623C109.515,135.856 109.956,135.043 110.723,134.811L117.808,132.673C118.575,132.441 119.388,132.882 119.62,133.649L119.666,133.788C119.899,134.555 119.481,135.368 118.691,135.6Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M111.513,138.296L109.724,139.387C108.492,140.131 106.89,139.759 106.146,138.528L106.007,138.319C105.263,137.088 105.635,135.485 106.866,134.741L108.655,133.649C109.886,132.906 111.489,133.278 112.233,134.509L112.372,134.718C113.139,135.949 112.744,137.552 111.513,138.296Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M116.6,133.045L111.28,134.787"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M110.351,134.904L108.167,125.82L102.917,128.654L106.588,137.459L110.351,134.904Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M111.071,142.687L106.681,138.621C106.239,138.226 106.216,137.529 106.634,137.111L106.704,137.041C107.099,136.6 107.796,136.577 108.214,136.995L112.604,141.084C113.046,141.479 113.069,142.175 112.651,142.594L112.581,142.663C112.186,143.081 111.513,143.105 111.071,142.687Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M109.189,137.877L111.141,139.689"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M109.607,131.791L109.189,130.118L105.263,134.3L109.607,131.791Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M67.304,108.211L67.351,108.142L69.697,109.373C76.016,112.672 83.543,112.672 89.861,109.373L92.208,108.142L92.277,108.258L95.599,91.044C97.574,81.728 90.698,72.715 81.173,72.413C80.987,72.413 80.801,72.413 80.616,72.413H77.503C69.349,72.413 62.983,78.685 63.819,85.91L67.304,108.211Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M67.304,108.211L67.351,108.142L69.697,109.373C76.016,112.672 83.543,112.672 89.861,109.373L92.208,108.142L92.277,108.258L95.599,91.044C97.574,81.728 90.698,72.715 81.173,72.413C80.987,72.413 80.801,72.413 80.616,72.413H77.503C69.349,72.413 62.983,78.685 63.819,85.91L67.304,108.211Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="72.4126"
            android:startX="79.8423"
            android:endY="111.847"
            android:endX="79.8423"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M72.856,57.336C72.554,53.201 69.627,50.088 66.305,50.343C63.007,50.622 60.567,54.176 60.869,58.312C60.916,59.032 61.055,59.729 61.264,60.379C62.263,63.585 59.359,66.698 56.339,65.583C56.246,65.56 56.153,65.513 56.061,65.467C55.991,65.443 55.944,65.42 55.875,65.374C56.618,66.21 57.547,66.907 58.616,67.348C61.752,68.696 65.214,67.743 67.374,65.257H67.397C70.742,65.025 73.158,61.471 72.856,57.336Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M77.967,59.38C77.665,55.245 74.738,52.132 71.416,52.387C68.117,52.666 65.678,56.22 65.98,60.355C66.026,61.076 66.166,61.772 66.375,62.423C67.374,65.629 64.47,68.742 61.45,67.627C61.357,67.604 61.264,67.557 61.171,67.511C61.102,67.487 61.055,67.464 60.985,67.418C61.729,68.254 62.658,68.951 63.727,69.392C66.863,70.74 70.324,69.787 72.485,67.301H72.508C75.83,67.069 78.269,63.492 77.967,59.38Z"
        android:fillColor="#F9D45C"/>
    <path
        android:pathData="M71.416,52.387C71.253,52.411 71.09,52.434 70.951,52.457C70.208,51.574 69.302,50.924 68.303,50.599C64.725,52.318 62.263,55.988 62.263,60.216C62.263,64.467 64.748,68.161 68.372,69.857C69.952,69.509 71.416,68.626 72.531,67.348H72.554C75.853,67.069 78.292,63.515 77.99,59.38C77.642,55.245 74.715,52.109 71.416,52.387Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M82.102,76.756H73.391L71.648,64.63H80.36L82.102,76.756Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M72.856,57.336C72.554,53.201 69.627,50.088 66.305,50.343C63.007,50.622 60.567,54.176 60.869,58.312C60.916,59.032 61.055,59.729 61.264,60.379C62.263,63.585 59.359,66.698 56.339,65.583C56.246,65.56 56.153,65.513 56.061,65.467C55.991,65.443 55.944,65.42 55.875,65.374C56.618,66.21 57.547,66.907 58.616,67.348"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M64.168,67.557C63.355,67.929 62.426,67.998 61.473,67.65C61.38,67.627 61.287,67.58 61.195,67.534C61.125,67.511 61.078,67.487 61.009,67.441"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M63.75,69.439C66.886,70.786 70.348,69.834 72.508,67.348H72.531C75.83,67.069 78.27,63.515 77.967,59.38C77.665,55.245 74.738,52.132 71.416,52.387C68.118,52.666 65.678,56.22 65.98,60.355C66.027,61.076 66.166,61.772 66.375,62.423"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M80.267,50.32C80.499,48.926 81.731,47.904 83.148,47.904"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M80.267,49.344C81.196,48.275 81.196,46.672 80.29,45.581"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M72.926,69.253C78.712,69.253 83.403,64.562 83.403,58.776C83.403,52.99 78.712,48.299 72.926,48.299C67.14,48.299 62.449,52.99 62.449,58.776C62.449,64.562 67.14,69.253 72.926,69.253Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M64.958,57.568C64.958,58.242 65.492,58.776 66.166,58.776C66.84,58.776 67.374,58.242 67.374,57.568"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M69.488,57.963C69.488,58.637 70.022,59.171 70.696,59.171C71.37,59.171 71.904,58.637 71.904,57.963"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M77.224,62.958C77.433,62.33 77.108,61.633 76.457,61.424"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#F0917A"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M70.069,55.523C70.324,55.779 70.649,55.942 71.021,56.011"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M67.839,55.523C67.583,55.779 67.258,55.942 66.886,56.011"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M72.879,65.792C74.157,65.792 75.202,64.746 75.202,63.469"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M68.303,57.777L68.28,57.847C67.583,59.728 67.281,61.726 67.42,63.724C67.444,63.979 67.56,64.212 67.746,64.351C68.094,64.63 68.559,64.7 68.977,64.537L70.046,64.119"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M73.507,69.23C73.321,69.23 73.112,69.253 72.926,69.253C67.142,69.253 62.449,64.56 62.449,58.776C62.449,56.639 63.076,54.664 64.168,53.015"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M76.875,48.624C71.369,48.624 71.764,54.176 74.506,54.385C78.292,54.664 79.663,57.475 79.663,61.192C79.663,64.909 76.643,67.929 72.926,67.929C72.833,67.929 72.763,67.929 72.67,67.929C73.948,68.51 75.365,68.835 76.875,68.835C82.451,68.835 86.98,64.305 86.98,58.729C86.98,53.154 82.451,48.624 76.875,48.624Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M78.13,48.717C82.59,49.762 85.935,53.828 85.935,58.66C85.935,64.119 81.684,68.579 76.364,68.858C76.527,68.858 76.713,68.881 76.875,68.881C82.451,68.881 86.981,64.351 86.981,58.776C86.981,53.595 83.124,49.321 78.13,48.717Z"
        android:fillColor="#F9D45C"/>
    <path
        android:pathData="M74.506,54.385C78.292,54.664 79.663,57.475 79.663,61.192C79.663,64.909 76.643,67.929 72.926,67.929C72.833,67.929 72.763,67.929 72.67,67.929C73.948,68.51 75.365,68.835 76.875,68.835C82.451,68.835 86.98,64.305 86.98,58.729C86.98,53.851 83.519,49.762 78.919,48.833"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M77.711,68.858C82.055,67.72 85.285,63.771 85.285,59.078C85.285,55.779 83.705,52.852 81.242,50.994"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M80.639,64.653C81.847,62.981 82.567,60.937 82.567,58.73C82.567,56.778 82.009,54.943 81.057,53.41"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M72.438,47.509C67.397,47.764 63.099,43.885 62.844,38.844C62.844,38.728 62.844,38.611 62.844,38.519C61.101,40.354 60.103,42.909 60.358,45.674C60.776,50.25 64.609,53.874 69.209,53.99C71.044,54.037 72.763,53.549 74.204,52.689C76.689,51.179 75.458,47.369 72.554,47.532C72.485,47.509 72.461,47.509 72.438,47.509Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M72.508,47.509C72.485,47.509 72.462,47.509 72.438,47.509C67.397,47.764 63.1,43.885 62.844,38.844C62.844,38.728 62.844,38.611 62.844,38.519C62.798,38.565 62.751,38.611 62.728,38.658C62.728,38.704 62.705,38.751 62.705,38.821C61.961,43.815 65.423,48.461 70.417,49.205C70.44,49.205 70.464,49.205 70.487,49.205C72.694,49.507 73.484,51.9 72.508,53.456C73.089,53.247 73.67,52.991 74.181,52.666C76.643,51.156 75.412,47.346 72.508,47.509Z"
        android:fillColor="#F9D45C"/>
    <path
        android:pathData="M62.821,38.519C61.078,40.354 60.079,42.909 60.335,45.674C60.753,50.25 64.586,53.874 69.186,53.99"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M62.031,45.279C62.333,48.694 64.54,51.551 67.536,52.852"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M75.691,44.814C74.483,44.698 73.344,45.023 72.415,45.627C71.346,46.347 69.86,46.022 69.163,44.954C68.652,44.164 68.35,43.258 68.303,42.259C68.303,42.189 68.303,42.119 68.303,42.05C67.211,43.188 66.631,44.791 66.816,46.487C67.049,48.531 68.442,50.227 70.278,50.947C70.789,53.201 72.81,54.873 75.203,54.873C78.107,54.873 80.43,52.434 80.244,49.484C80.081,47.044 78.13,45.047 75.691,44.814Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M70.696,52.132C71.532,53.781 73.228,54.896 75.203,54.896C76.295,54.896 77.293,54.548 78.106,53.967"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M68.303,42.073C67.211,43.211 66.631,44.814 66.816,46.51C66.933,47.462 67.304,48.345 67.839,49.089"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M68.466,44.977C68.164,46.51 68.512,48.182 69.604,49.53C70.208,50.273 70.998,50.831 71.857,51.156"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M71.486,47.509C71.184,49.042 71.532,50.715 72.624,52.062C73.228,52.805 74.018,53.363 74.878,53.688"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M80.244,49.507C80.081,47.044 78.13,45.046 75.69,44.814"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M79.779,149.005V190.681"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.054,129.073L79.779,132.348L76.48,129.073"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M79.779,143.755V132.348"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M92.115,165.94C91.255,167.404 89.374,167.892 87.91,167.032"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M68.048,165.94C68.907,167.404 70.789,167.892 72.252,167.032"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M81.546,114.228V115.622"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M81.545,118.619V123.102"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M81.545,124.612V126.006"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M90.605,114.763L93.648,123.916C94.322,125.937 96.227,127.307 98.341,127.307H99.131"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M84.564,85.283H73.623L71.416,70.043H82.357L84.564,85.283Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M84.564,85.283H73.623L71.416,70.043H82.357L84.564,85.283Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="70.043"
            android:startX="77.9901"
            android:endY="85.2825"
            android:endX="77.9901"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M104.752,111.719C104.59,111.162 104.613,110.558 104.799,110C104.845,109.861 104.868,109.698 104.868,109.536C104.868,109.001 104.59,108.514 104.171,108.235C103.753,107.933 103.451,107.491 103.312,106.98L95.437,81.217C94.74,78.917 92.045,77.686 89.443,78.476C86.841,79.266 85.308,81.798 86.005,84.098L89.094,92.716L104.334,135.229L110.095,129.166L104.752,111.719Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M104.752,111.719C104.59,111.162 104.613,110.558 104.799,110C104.845,109.861 104.868,109.698 104.868,109.536C104.868,109.001 104.59,108.514 104.171,108.235C103.753,107.933 103.451,107.491 103.312,106.98L95.437,81.217C94.74,78.917 92.045,77.686 89.443,78.476C86.841,79.266 85.308,81.798 86.005,84.098L89.094,92.716L104.334,135.229L110.095,129.166L104.752,111.719Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="78.2368"
            android:startX="97.966"
            android:endY="135.229"
            android:endX="97.966"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M99.386,108.815L96.947,111.324L100.176,111.789"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#D4C49C"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M61.775,111.091L64.214,113.6L60.985,114.065"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#D4C49C"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M73.739,93.413C72.252,93.413 71.021,92.321 70.835,90.881C70.812,91.02 70.812,91.136 70.812,91.276C70.812,92.809 72.043,94.04 73.576,94.04C74.831,94.04 75.899,93.204 76.225,92.042C75.714,92.855 74.784,93.413 73.739,93.413Z"
        android:fillColor="#C7C7C7"/>
    <path
        android:pathData="M86.214,93.553C87.701,93.553 88.932,92.461 89.118,91.021C89.141,91.16 89.141,91.276 89.141,91.415C89.141,92.949 87.91,94.18 86.377,94.18C85.122,94.18 84.054,93.344 83.728,92.182C84.263,92.995 85.169,93.553 86.214,93.553Z"
        android:fillColor="#C7C7C7"/>
    <path
        android:pathData="M93.09,103.937L95.274,92.6L94.601,108.142L93.09,103.937Z"
        android:fillColor="#E7E7E7"/>
    <path
        android:pathData="M95.042,93.134L93.764,100.406"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M64.865,92.6L67.443,101.985"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M61.107,88.495L60.004,98.165L57.195,123.154"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M72.926,71.251L73.321,73.969"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#ECE5D4"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M74.808,71.251L75.203,73.969"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#ECE5D4"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M76.69,71.251L77.085,73.969"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#ECE5D4"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M78.571,71.251L78.966,73.969"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#ECE5D4"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M80.453,71.251L80.848,73.969"
        android:strokeLineJoin="round"
        android:strokeWidth="0.464618"
        android:fillColor="#00000000"
        android:strokeColor="#ECE5D4"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M71.578,71.251L72.043,74.387"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M82.59,71.576L82.753,72.831"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M92.207,108.142L89.861,109.373C83.542,112.672 75.388,112.346 69.07,109.071"
        android:strokeLineJoin="round"
        android:strokeWidth="1.16155"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M108.075,129.073L102.615,134.3"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M85.819,114.066L85.029,112.161"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M87.236,112.742L86.447,111.789"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M74.273,113.81L74.645,112.416"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M168.282,210.116L175.506,220.366L173.346,225.144L162.655,213.351L168.282,210.116Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M168.289,219.568L171.279,214.37L169.957,212.495L165.499,216.489L168.289,219.568Z"
        android:fillColor="#B76452"/>
    <path
        android:pathData="M124.562,161.187L122.205,159.475C120.512,158.247 119.31,156.455 118.814,154.42C118.698,153.881 118.3,153.448 117.774,153.288C117.247,153.128 116.676,153.266 116.281,153.649C115.886,154.033 115.729,154.6 115.872,155.133C116.534,157.872 118.149,160.285 120.426,161.94L122.785,163.654C123.462,164.083 124.355,163.909 124.823,163.258C125.291,162.607 125.172,161.704 124.552,161.197L124.562,161.187Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M122.931,162.694C122.549,163.484 121.598,163.813 120.809,163.43L114.055,160.145C113.268,159.762 112.939,158.814 113.321,158.026C113.703,157.237 114.654,156.907 115.443,157.291L122.197,160.575C122.984,160.958 123.313,161.906 122.931,162.694Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M122.197,160.577L115.437,157.291C115.06,157.106 114.624,157.08 114.227,157.217C113.83,157.355 113.504,157.645 113.32,158.024V158.024"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M122.938,163.472C122.766,164.332 121.929,164.89 121.069,164.717L113.71,163.238C112.851,163.066 112.294,162.23 112.466,161.371C112.638,160.51 113.475,159.953 114.335,160.126L121.694,161.605C122.554,161.777 123.11,162.613 122.938,163.472Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M126.951,164.863C126.267,166.275 124.567,166.864 123.156,166.178L121.444,165.345C120.036,164.661 119.448,162.965 120.131,161.556C120.815,160.144 122.515,159.555 123.926,160.241L125.638,161.074C127.046,161.759 127.633,163.454 126.951,164.863Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M115.564,160.36L121.228,161.629"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M122.197,161.664L123.539,152.073L129.223,154.477L126.327,163.931L122.197,161.664Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M176.381,117.132L178.21,110.306L182.522,110.086L181.66,117.005L176.381,117.132Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M177.583,112.65L178.211,110.306L182.523,110.086L181.98,114.422L177.583,112.65Z"
        android:fillColor="#B76452"/>
    <path
        android:pathData="M147.062,107.933C144.763,107.933 142.899,106.065 142.899,103.762V93.686H151.226V103.762C151.226,106.065 149.362,107.933 147.062,107.933Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M142.9,97.474L151.226,100.3V93.698H142.9V97.474Z"
        android:fillColor="#B76452"/>
    <path
        android:pathData="M142.9,95.915V103.458"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M151.251,96.494V103.17"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M130.32,89.733C131.373,89.733 132.227,88.877 132.227,87.822C132.227,86.766 131.373,85.91 130.32,85.91C129.266,85.91 128.412,86.766 128.412,87.822C128.412,88.877 129.266,89.733 130.32,89.733Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M130.185,88.149C130.149,87.942 130.033,87.757 129.861,87.636C129.689,87.516 129.476,87.468 129.269,87.505"
        android:strokeLineJoin="round"
        android:strokeWidth="0.621765"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M132.052,92.687C131.5,93.765 130.248,94.281 129.099,93.905C127.95,93.529 127.244,92.372 127.433,91.175C127.623,89.979 128.653,89.098 129.862,89.098"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M143.257,97.371C149.973,97.371 155.416,91.917 155.416,85.189C155.416,78.461 149.973,73.007 143.257,73.007C136.542,73.007 131.098,78.461 131.098,85.189C131.098,91.917 136.542,97.371 143.257,97.371Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M131.974,85.195C131.974,78.64 137.151,73.261 143.689,73.023C143.543,73.023 143.397,73.023 143.248,73.023C136.532,73.023 131.089,78.477 131.089,85.205C131.089,91.932 136.532,97.386 143.248,97.386C143.397,97.386 143.543,97.386 143.689,97.374C137.148,97.137 131.969,91.752 131.974,85.195Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M168.403,235.455H174.964L178.559,229.224C178.931,228.58 178.714,227.756 178.075,227.378L177.202,226.86C176.551,226.49 176.276,225.693 176.561,225C176.726,224.619 177.047,224.327 177.442,224.199C177.837,224.072 178.268,224.12 178.625,224.332L179.581,224.896C179.879,225.073 180.234,225.124 180.569,225.038C180.905,224.951 181.192,224.735 181.367,224.436L182.594,222.356L174.603,219.211L171.656,230.859L170.142,231.989C169.05,232.805 168.406,234.09 168.406,235.455H168.403Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M172.344,228.311C173.223,228.565 174.14,228.058 174.395,227.178L175.876,222.047C176.179,220.998 175.735,219.877 174.797,219.322L174.527,219.069L172.309,228.303L172.344,228.311Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M149.967,214.86L148.793,227.352L153.282,229.314L156.454,214.86H149.967Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M149.966,214.86L149.542,219.354L155.958,217.107L156.453,214.86H149.966Z"
        android:fillColor="#B76452"/>
    <path
        android:pathData="M167.444,235.854H155.608C154.744,235.853 154.044,235.15 154.044,234.284V233.496C154.057,232.745 153.511,232.102 152.77,231.994C152.358,231.944 151.945,232.073 151.635,232.349C151.325,232.624 151.147,233.02 151.147,233.435V234.193C151.147,235.109 150.406,235.851 149.492,235.851H147.428L148.793,227.358L164.565,231.999C166.273,232.501 167.446,234.071 167.444,235.854Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M150.325,147.326L149.939,164.588L148.793,216.157H156.792L170.5,164.872C171.424,161.417 171.174,157.753 169.788,154.457L167.063,148.334C165.804,145.505 163.973,142.968 161.684,140.885L150.325,147.326Z"
        android:fillColor="#0D9AFF"/>
    <path
        android:pathData="M139.989,143.459L137.524,148.178C135.468,153.054 134.234,158.237 133.873,163.517L132.657,181.291C132.432,184.582 133.676,187.803 136.053,190.085L165.506,218.359L171.057,213.303L151.949,179.334L155.943,147.327L139.989,143.459Z"
        android:fillColor="#0D9AFF"/>
    <path
        android:pathData="M128.346,161.584L121.629,156.243L130.309,107.202C130.725,104.841 133.314,103.33 136.089,103.823C138.865,104.316 140.775,106.626 140.356,108.987L138.337,117.857L128.346,161.584Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M128.346,161.584L121.629,156.243L130.309,107.202C130.725,104.841 133.314,103.33 136.089,103.823C138.865,104.316 140.775,106.626 140.356,108.987L138.337,117.857L128.346,161.584Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="103.729"
            android:startX="131.021"
            android:endY="161.584"
            android:endX="131.021"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M176.497,138.585C174.951,139.52 173.096,139.343 172.266,138.178L153.221,111.469C151.98,109.727 152.888,107.073 155.204,105.675C157.422,104.333 160.079,104.664 161.134,106.433L178.039,134.51C178.803,135.779 178.115,137.607 176.497,138.585Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M176.497,138.585C174.951,139.52 173.096,139.343 172.266,138.178L153.221,111.469C151.98,109.727 152.888,107.073 155.204,105.675C157.422,104.333 160.079,104.664 161.134,106.433L178.039,134.51C178.803,135.779 178.115,137.607 176.497,138.585Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="104.852"
            android:startX="165.508"
            android:endY="139.184"
            android:endX="165.508"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M156.468,148.689H144.935C141.711,148.687 138.902,146.487 138.122,143.352L130.528,112.844C129.943,110.496 130.47,108.009 131.957,106.102C133.444,104.195 135.725,103.081 138.14,103.081H154.338C159.395,103.081 163.495,107.188 163.495,112.255V141.651C163.495,143.518 162.755,145.308 161.437,146.628C160.119,147.948 158.331,148.689 156.468,148.689Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M156.468,148.689H144.935C141.711,148.687 138.902,146.487 138.122,143.352L130.528,112.844C129.943,110.496 130.47,108.009 131.957,106.102C133.444,104.195 135.725,103.081 138.14,103.081H154.338C159.395,103.081 163.495,107.188 163.495,112.255V141.651C163.495,143.518 162.755,145.308 161.437,146.628C160.119,147.948 158.331,148.689 156.468,148.689Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="103.081"
            android:startX="146.894"
            android:endY="148.689"
            android:endX="146.894"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M153.658,104.487C153.656,104.003 153.556,103.524 153.363,103.081L146.939,102.922L140.283,103.081C140.091,103.525 139.992,104.003 139.99,104.487C139.99,107.156 143.048,109.32 146.823,109.32C150.597,109.32 153.658,107.156 153.658,104.487Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M146.823,109.32C150.608,109.32 153.658,107.156 153.658,104.487"
        android:strokeLineJoin="round"
        android:strokeWidth="0.621765"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M139.99,104.487C140.02,105.609 140.52,106.666 141.368,107.4"
        android:strokeLineJoin="round"
        android:strokeWidth="0.621765"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M143.308,103.195H137.358"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M174.209,79.38C174.209,79.38 174.809,81.628 176.437,82.816C176.977,83.19 177.592,83.443 178.238,83.556C178.77,83.651 179.212,84.022 179.396,84.531C179.581,85.04 179.481,85.608 179.134,86.023C178.199,87.144 176.94,87.946 175.531,88.319C171.822,89.262 167.641,87.118 167.571,84.449C167.5,81.779 168.156,78.157 168.156,78.157L174.209,79.38Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M139.821,69.982C141.39,69.982 142.845,70.806 143.653,72.154C144.363,70.22 146.266,68.993 148.316,69.145C150.367,69.298 152.068,70.794 152.484,72.811C154.856,71.872 157.561,72.589 159.16,74.579C160.759,76.569 160.88,79.369 159.458,81.49C158.037,83.611 155.403,84.558 152.96,83.828C150.516,83.098 148.831,80.861 148.801,78.306C148.528,78.356 148.252,78.382 147.976,78.382C146.342,78.385 144.829,77.519 144.002,76.107C143.182,78.217 140.922,79.384 138.731,78.829C136.54,78.275 135.105,76.172 135.384,73.925C135.663,71.679 137.569,69.992 139.828,69.992L139.821,69.982Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M147.968,78.382C150.514,78.382 152.578,76.314 152.578,73.764C152.578,71.213 150.514,69.146 147.968,69.146C145.422,69.146 143.358,71.213 143.358,73.764C143.358,76.314 145.422,78.382 147.968,78.382Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.621765"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M154.617,84.075C157.835,84.075 160.443,81.461 160.443,78.238C160.443,75.014 157.835,72.401 154.617,72.401C151.399,72.401 148.791,75.014 148.791,78.238C148.791,81.461 151.399,84.075 154.617,84.075Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.621765"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M155.944,89.968C156.998,89.968 157.852,89.112 157.852,88.057C157.852,87.001 156.998,86.145 155.944,86.145C154.891,86.145 154.036,87.001 154.036,88.057C154.036,89.112 154.891,89.968 155.944,89.968Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M135.171,83.956C135.171,84.637 135.722,85.19 136.402,85.19C137.082,85.19 137.634,84.637 137.634,83.956"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M136.402,88.354C136.402,89.113 137.346,89.722 138.509,89.722C139.673,89.722 140.616,89.11 140.616,88.354"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M139.634,83.956C139.634,84.637 140.185,85.19 140.866,85.19C141.546,85.19 142.097,84.637 142.097,83.956"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M156.792,87.788C156.386,87.625 155.925,87.823 155.762,88.23"
        android:strokeLineJoin="round"
        android:strokeWidth="0.621765"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M140.01,80.814C140.289,81.523 140.964,81.995 141.724,82.012"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M136.246,82.038C137.005,81.997 137.666,81.505 137.924,80.789"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M143.091,93.229C145.07,93.229 146.675,91.621 146.675,89.639"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M155.422,86.711C155.989,82.213 154.015,77.771 150.299,75.183C146.583,72.594 141.74,72.288 137.728,74.388C133.717,76.487 131.201,80.645 131.2,85.18C131.2,85.516 131.212,85.849 131.24,86.178"
        android:strokeLineJoin="round"
        android:strokeWidth="0.621765"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M141.309,89.621C141.775,89.621 142.152,89.243 142.152,88.777C142.152,88.31 141.775,87.932 141.309,87.932"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M133.687,81.313C135.387,81.313 136.765,79.933 136.765,78.229C136.765,76.526 135.387,75.146 133.687,75.146C131.987,75.146 130.609,76.526 130.609,78.229C130.609,79.933 131.987,81.313 133.687,81.313Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M131.2,85.622C133.034,85.622 134.52,84.132 134.52,82.295C134.52,80.458 133.034,78.968 131.2,78.968C129.366,78.968 127.879,80.458 127.879,82.295C127.879,84.132 129.366,85.622 131.2,85.622Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M154.94,86.749C157.012,86.749 158.692,85.066 158.692,82.99C158.692,80.914 157.012,79.231 154.94,79.231C152.868,79.231 151.188,80.914 151.188,82.99C151.188,85.066 152.868,86.749 154.94,86.749Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.621765"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M154.94,86.749C157.012,86.749 158.692,85.066 158.692,82.99C158.692,80.914 157.012,79.231 154.94,79.231C152.868,79.231 151.188,80.914 151.188,82.99C151.188,85.066 152.868,86.749 154.94,86.749Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M153.842,92.719C154.394,93.797 155.645,94.313 156.795,93.937C157.944,93.561 158.65,92.404 158.46,91.208C158.27,90.011 157.241,89.131 156.032,89.13"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M136.561,77.823C137.775,79.154 139.681,79.601 141.359,78.949C143.036,78.296 144.142,76.679 144.143,74.876C144.143,74.77 144.143,74.661 144.13,74.555"
        android:strokeLineJoin="round"
        android:strokeWidth="0.621765"
        android:fillColor="#00000000"
        android:strokeColor="#CB765E"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M151.594,81.696C150.789,83.053 150.915,84.769 151.909,85.994"
        android:strokeLineJoin="round"
        android:strokeWidth="0.621765"
        android:fillColor="#00000000"
        android:strokeColor="#CB765E"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M143.664,72.957C146.834,72.957 149.404,70.382 149.404,67.206C149.404,64.03 146.834,61.456 143.664,61.456C140.493,61.456 137.924,64.03 137.924,67.206C137.924,70.382 140.493,72.957 143.664,72.957Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M135.971,73.008C137.996,73.008 139.637,71.364 139.637,69.335C139.637,67.307 137.996,65.662 135.971,65.662C133.946,65.662 132.305,67.307 132.305,69.335C132.305,71.364 133.946,73.008 135.971,73.008Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M132.597,78.455C134.612,78.455 136.246,76.818 136.246,74.799C136.246,72.781 134.612,71.144 132.597,71.144C130.582,71.144 128.949,72.781 128.949,74.799C128.949,76.818 130.582,78.455 132.597,78.455Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M130.266,81.277C131.553,81.277 132.596,80.232 132.596,78.943C132.596,77.654 131.553,76.609 130.266,76.609C128.98,76.609 127.937,77.654 127.937,78.943C127.937,80.232 128.98,81.277 130.266,81.277Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M133.153,72.829C134.035,72.829 134.751,72.112 134.751,71.228C134.751,70.344 134.035,69.628 133.153,69.628C132.271,69.628 131.556,70.344 131.556,71.228C131.556,72.112 132.271,72.829 133.153,72.829Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M150.645,73.118C152.838,73.118 154.617,71.337 154.617,69.14C154.617,66.942 152.838,65.161 150.645,65.161C148.452,65.161 146.674,66.942 146.674,69.14C146.674,71.337 148.452,73.118 150.645,73.118Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M154.893,74.555C156.171,74.555 157.207,73.518 157.207,72.237C157.207,70.956 156.171,69.919 154.893,69.919C153.614,69.919 152.578,70.956 152.578,72.237C152.578,73.518 153.614,74.555 154.893,74.555Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M158.219,65.639C160.637,65.639 162.597,63.675 162.597,61.253C162.597,58.831 160.637,56.867 158.219,56.867C155.802,56.867 153.842,58.831 153.842,61.253C153.842,63.675 155.802,65.639 158.219,65.639Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M165.222,63.773C168.078,63.773 170.394,61.453 170.394,58.591C170.394,55.729 168.078,53.409 165.222,53.409C162.365,53.409 160.049,55.729 160.049,58.591C160.049,61.453 162.365,63.773 165.222,63.773Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M172.665,68.882C175.382,68.882 177.585,66.675 177.585,63.953C177.585,61.23 175.382,59.023 172.665,59.023C169.948,59.023 167.745,61.23 167.745,63.953C167.745,66.675 169.948,68.882 172.665,68.882Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M171.933,60.621C173.338,60.621 174.476,59.481 174.476,58.074C174.476,56.666 173.338,55.525 171.933,55.525C170.528,55.525 169.39,56.666 169.39,58.074C169.39,59.481 170.528,60.621 171.933,60.621Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M156.791,73.877C160.013,73.877 162.625,71.261 162.625,68.033C162.625,64.805 160.013,62.188 156.791,62.188C153.569,62.188 150.958,64.805 150.958,68.033C150.958,71.261 153.569,73.877 156.791,73.877Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M171.59,75.621C174.204,75.621 176.324,73.498 176.324,70.879C176.324,68.26 174.204,66.137 171.59,66.137C168.976,66.137 166.857,68.26 166.857,70.879C166.857,73.498 168.976,75.621 171.59,75.621Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M164.924,69.567C167.389,69.567 169.388,67.565 169.388,65.095C169.388,62.626 167.389,60.624 164.924,60.624C162.459,60.624 160.461,62.626 160.461,65.095C160.461,67.565 162.459,69.567 164.924,69.567Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M165.168,82.174C168.054,82.174 170.394,79.83 170.394,76.938C170.394,74.047 168.054,71.703 165.168,71.703C162.282,71.703 159.943,74.047 159.943,76.938C159.943,79.83 162.282,82.174 165.168,82.174Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M167.442,73.597C169.072,73.597 170.394,72.273 170.394,70.639C170.394,69.006 169.072,67.682 167.442,67.682C165.812,67.682 164.49,69.006 164.49,70.639C164.49,72.273 165.812,73.597 167.442,73.597Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M172.781,79.709C174.738,79.709 176.324,78.12 176.324,76.16C176.324,74.199 174.738,72.61 172.781,72.61C170.825,72.61 169.239,74.199 169.239,76.16C169.239,78.12 170.825,79.709 172.781,79.709Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M168.04,86.906C170.001,86.906 171.59,85.313 171.59,83.349C171.59,81.385 170.001,79.793 168.04,79.793C166.079,79.793 164.49,81.385 164.49,83.349C164.49,85.313 166.079,86.906 168.04,86.906Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M172.276,82.247C173.954,82.247 175.314,80.885 175.314,79.204C175.314,77.523 173.954,76.16 172.276,76.16C170.599,76.16 169.239,77.523 169.239,79.204C169.239,80.885 170.599,82.247 172.276,82.247Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M162.603,76.9C165.275,76.9 167.442,74.73 167.442,72.052C167.442,69.374 165.275,67.204 162.603,67.204C159.93,67.204 157.763,69.374 157.763,72.052C157.763,74.73 159.93,76.9 162.603,76.9Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M133.541,124.952L136.579,137.159"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#262944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M149.066,203.854L150.865,162.399"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M148.074,159.422L150.862,162.4L154.142,160.208"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M162.956,193.096L158.719,209.249"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M163.074,144.052C162.064,146.835 159.424,148.687 156.469,148.688H144.936"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M160.743,122.018L166.625,130.263"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M174.633,139.51C172.358,139.193 170.769,137.089 171.083,134.808L176.268,115.905L182.309,114.479L179.324,135.966C179.002,138.241 176.904,139.826 174.633,139.51Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M174.633,139.51C172.358,139.193 170.769,137.089 171.083,134.808L176.268,115.905L182.309,114.479L179.324,135.966C179.002,138.241 176.904,139.826 174.633,139.51Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="114.479"
            android:startX="176.676"
            android:endY="139.55"
            android:endX="176.676"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M174.837,121.13L172.443,129.859"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M182.587,101.722C183.231,101.7 183.771,102.205 183.792,102.849L183.995,108.965C184.016,109.609 183.511,110.149 182.867,110.17C182.223,110.192 181.683,109.687 181.662,109.043L181.46,102.927C181.438,102.283 181.943,101.743 182.587,101.722Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M182.587,101.722C183.231,101.7 183.771,102.205 183.792,102.849L183.995,108.965C184.016,109.609 183.511,110.149 182.867,110.17V110.17C182.223,110.192 181.683,109.687 181.662,109.043L181.46,102.927C181.438,102.283 181.943,101.743 182.587,101.722V101.722Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M181.68,105.67L179.321,103.959C177.627,102.725 176.427,100.929 175.932,98.89C175.834,98.335 175.436,97.879 174.899,97.709C174.362,97.539 173.775,97.681 173.375,98.079C172.975,98.477 172.829,99.064 172.996,99.603C173.654,102.341 175.264,104.754 177.537,106.411L179.896,108.124C180.573,108.553 181.466,108.38 181.934,107.729C182.402,107.078 182.283,106.175 181.662,105.667L181.68,105.67Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M181.68,105.67L179.321,103.959C177.627,102.725 176.427,100.929 175.932,98.89C175.834,98.335 175.436,97.879 174.899,97.709C174.362,97.539 173.775,97.681 173.375,98.079C172.975,98.477 172.829,99.064 172.996,99.603C173.654,102.341 175.264,104.754 177.537,106.411L179.896,108.124C180.154,108.311 180.464,108.412 180.782,108.413"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M180.055,107.185C179.672,107.975 178.722,108.304 177.933,107.92L171.179,104.636C170.391,104.253 170.063,103.305 170.444,102.517C170.827,101.727 171.777,101.398 172.566,101.782L179.321,105.066C180.108,105.449 180.437,106.397 180.055,107.185Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M179.321,105.069L172.562,101.782C172.184,101.598 171.749,101.572 171.352,101.709C170.954,101.847 170.628,102.137 170.445,102.516V102.516"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M180.062,107.967C179.89,108.827 179.053,109.384 178.193,109.212L170.833,107.733C169.974,107.56 169.417,106.724 169.589,105.865C169.762,105.005 170.599,104.448 171.459,104.62L178.818,106.099C179.677,106.272 180.234,107.108 180.062,107.967Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M183.65,110.719C182.966,112.131 181.266,112.72 179.855,112.034L178.142,111.201C176.734,110.517 176.147,108.821 176.83,107.412C177.514,106 179.213,105.411 180.624,106.097L182.337,106.93C183.745,107.614 184.332,109.31 183.65,110.719Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M169.601,105.852V105.852C169.43,106.711 169.985,107.547 170.843,107.72L178.208,109.209"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M172.685,104.851L178.35,106.12"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M179.861,112.04C181.268,112.722 182.961,112.134 183.645,110.725V110.725C184.162,109.657 183.961,108.379 183.141,107.522"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M176.886,137.847C176.764,138.358 176.379,138.764 175.876,138.913C175.374,139.061 174.83,138.93 174.45,138.569C174.071,138.207 173.912,137.67 174.035,137.16"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M160.743,139.185L163.686,137.16"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M162.214,140.198L163.685,139.548"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M133.541,186.483H135.06"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M157.205,196.062H159.921"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M157.763,230.039C157.654,230.466 157.38,230.832 157.002,231.056C156.623,231.28 156.17,231.344 155.745,231.232L150.585,229.9C149.53,229.627 148.793,228.674 148.793,227.582V227.362L157.773,230.004L157.763,230.039Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M147.807,214.86L154.998,213.947"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M173.53,223.185L172.806,226.355"
        android:strokeLineJoin="round"
        android:strokeWidth="0.621765"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M152.27,228.376L155.293,229.263"
        android:strokeLineJoin="round"
        android:strokeWidth="0.621765"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M149.755,217.108L149.066,224.439"
        android:strokeLineJoin="round"
        android:strokeWidth="0.621765"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M174.091,218.359L175.506,219.978"
        android:strokeLineJoin="round"
        android:strokeWidth="0.621765"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M174.8,233.895L178.4,227.353"
        android:strokeLineJoin="round"
        android:strokeWidth="0.621765"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M153.781,234.612H160.359"
        android:strokeLineJoin="round"
        android:strokeWidth="0.621765"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M179.611,224.912C180.23,225.266 181.019,225.054 181.378,224.437L182.604,222.356L180.025,221.345L179.611,224.912Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M147.983,232.383L147.428,235.847H149.493C150.407,235.847 151.148,235.105 151.148,234.189V233.733L147.983,232.383Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M149.48,95.658C154.981,92.356 156.96,85.33 153.993,79.635C151.026,73.939 144.142,71.548 138.292,74.181C132.443,76.814 129.656,83.559 131.937,89.564C134.217,95.568 140.774,98.751 146.891,96.823"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M122.293,156.773L131.262,107.252C131.515,105.852 132.524,104.75 133.884,104.189C134.406,103.974 134.353,103.812 133.788,103.956C132.077,104.393 130.76,105.617 130.46,107.242L121.644,156.134L122.293,156.773Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M121.629,156.242L130.309,107.202C130.609,105.498 132.039,104.237 133.856,103.85"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M175.518,117.131L180.713,115.718"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#262944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M126.215,163.527C126.696,163.956 126.74,164.693 126.312,165.175L122.257,169.745C121.829,170.229 121.089,170.272 120.607,169.843C120.126,169.414 120.082,168.677 120.51,168.195L124.565,163.624C124.994,163.141 125.733,163.098 126.215,163.527Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M123.163,156.242L129.223,160.36"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#262944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M123.673,164.626L121.846,166.685"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M141.645,127.109C139.029,127.107 136.815,125.173 136.457,122.577C136.422,122.808 136.404,123.041 136.402,123.274C136.401,125.741 138.213,127.831 140.65,128.175C143.088,128.519 145.406,127.012 146.086,124.642C145.13,126.177 143.451,127.11 141.645,127.109Z"
        android:fillColor="#C7C7C7"/>
    <path
        android:pathData="M154.072,127.109C156.688,127.107 158.903,125.173 159.262,122.577C159.297,122.808 159.315,123.041 159.317,123.274C159.305,125.732 157.496,127.808 155.067,128.151C152.638,128.495 150.326,127 149.636,124.642C150.592,126.175 152.268,127.107 154.072,127.109Z"
        android:fillColor="#C7C7C7"/>
    <path
        android:pathData="M166.857,214.404L169.507,210.116"
        android:strokeLineJoin="round"
        android:strokeWidth="0.835653"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M137.515,148.053C137.515,148.053 136.228,152.087 134.938,156.664C134.117,159.577 134.227,163.61 134.067,165.212C133.655,169.331 133.161,182.348 133.161,182.348"
        android:strokeLineJoin="round"
        android:strokeWidth="0.718641"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M95.574,81.598L100.176,98.165"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M72.043,74.387C69.362,75.103 64.724,78.071 62.146,81.96"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M82.752,73.429C83.485,73.195 88.662,74.697 89.866,76.058"
        android:strokeLineJoin="round"
        android:strokeWidth="0.696928"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
  </group>
</vector>
