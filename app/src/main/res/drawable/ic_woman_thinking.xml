<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="139dp"
    android:height="114dp"
    android:viewportWidth="139"
    android:viewportHeight="114">
  <path
      android:pathData="M71.846,108.751C63.194,108.75 56.182,101.787 56.182,93.199V91.761H87.51V93.199C87.51,97.324 85.86,101.279 82.923,104.196C79.985,107.112 76,108.751 71.846,108.751Z"
      android:fillColor="#BDE3FF"/>
  <path
      android:pathData="M83.905,83.528C89.861,83.528 94.688,78.736 94.688,72.824C94.688,66.912 89.861,62.119 83.905,62.119C77.95,62.119 73.122,66.912 73.122,72.824C73.122,78.736 77.95,83.528 83.905,83.528Z"
      android:fillColor="#B88352"/>
  <path
      android:pathData="M92.565,70.138C92.985,71.461 93.089,72.862 92.87,74.231"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#FFF4EE"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M92.36,76.09C91.362,78.644 89.245,80.607 86.609,81.42"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#FFF4EE"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M76.554,91.806L74.485,95.43C73.855,96.533 72.195,96.73 71.27,95.811L67.244,91.806L63.919,76.06H73.232L76.554,91.806Z"
      android:fillColor="#FDB988"/>
  <path
      android:pathData="M74.646,82.762L66.144,86.586L64.456,78.595L73.753,78.535L74.646,82.762Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M76.554,91.806L74.485,95.43C73.855,96.533 72.195,96.73 71.27,95.811L67.244,91.806L63.919,76.06H73.232L76.554,91.806Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M52.2,91.62C57.683,91.62 62.128,87.207 62.128,81.764C62.128,76.321 57.683,71.908 52.2,71.908C46.716,71.908 42.271,76.321 42.271,81.764C42.271,87.207 46.716,91.62 52.2,91.62Z"
      android:fillColor="#B88352"/>
  <path
      android:pathData="M54.875,79.127C56.095,79.127 57.084,78.145 57.084,76.934C57.084,75.723 56.095,74.741 54.875,74.741C53.655,74.741 52.666,75.723 52.666,76.934C52.666,78.145 53.655,79.127 54.875,79.127Z"
      android:fillColor="#FDB988"/>
  <path
      android:pathData="M67.789,83.453C75.561,83.453 81.861,77.199 81.861,69.484C81.861,61.769 75.561,55.515 67.789,55.515C60.017,55.515 53.717,61.769 53.717,69.484C53.717,77.199 60.017,83.453 67.789,83.453Z"
      android:fillColor="#FDB988"/>
  <path
      android:pathData="M55.478,73.299C53.23,66.152 57.06,58.518 64.159,55.99C63.971,56.04 63.782,56.091 63.594,56.149C56.175,58.449 52.04,66.283 54.356,73.648C56.461,80.338 62.922,84.436 69.695,83.388C70.377,83.282 70.586,83.085 69.695,83.145C63.348,83.569 57.456,79.587 55.478,73.299Z"
      android:fillColor="#FFF4EE"/>
  <path
      android:pathData="M82.139,70.431C83.359,70.431 84.348,69.449 84.348,68.238C84.348,67.027 83.359,66.045 82.139,66.045C80.919,66.045 79.93,67.027 79.93,68.238C79.93,69.449 80.919,70.431 82.139,70.431Z"
      android:fillColor="#FDB988"/>
  <path
      android:pathData="M70.844,67.887L71.18,68.989C71.696,70.42 72.801,71.732 74.05,72.611L75.153,73.386L74.817,73.934C74.087,75.129 72.765,75.839 71.359,75.794L70.928,75.779"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M53.842,76.931C54.234,76.614 54.811,76.672 55.131,77.061"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M82.399,68.549C82.367,68.309 82.433,68.067 82.581,67.876C82.73,67.684 82.949,67.56 83.19,67.529"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M69.742,65.56C69.194,66.243 68.287,66.536 67.439,66.302"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M71.101,64.153C71.923,63.844 72.853,64.054 73.459,64.686"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M67.617,79.197C66.737,79.189 65.947,78.661 65.61,77.854"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M57.772,73.486C60.541,72.198 62.798,70.029 64.185,67.324"
      android:strokeLineJoin="round"
      android:strokeWidth="0.816661"
      android:fillColor="#00000000"
      android:strokeColor="#B08155"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M43.136,90.373C46.117,91.056 49.243,90.733 52.018,89.453"
      android:strokeLineJoin="round"
      android:strokeWidth="0.816661"
      android:fillColor="#00000000"
      android:strokeColor="#B88352"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M46.383,92.234C49.424,91.891 52.263,90.551 54.449,88.425"
      android:strokeLineJoin="round"
      android:strokeWidth="0.816661"
      android:fillColor="#00000000"
      android:strokeColor="#B88352"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M72.895,48.979C70.316,50.616 68.362,53.059 67.342,55.924"
      android:strokeLineJoin="round"
      android:strokeWidth="0.816661"
      android:fillColor="#00000000"
      android:strokeColor="#C4905F"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M69.649,49.68C67.943,52.2 67.133,55.214 67.348,58.243"
      android:strokeLineJoin="round"
      android:strokeWidth="0.816661"
      android:fillColor="#00000000"
      android:strokeColor="#B88352"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M79.312,93.27C79.311,92.748 79.202,92.232 78.991,91.755L72.032,91.585L64.823,91.756C64.614,92.234 64.505,92.75 64.504,93.272C64.504,96.136 67.818,98.458 71.907,98.458C75.995,98.458 79.312,96.132 79.312,93.27Z"
      android:fillColor="#FDB988"/>
  <path
      android:pathData="M81.863,69.744C81.759,75.066 78.623,79.868 73.771,82.134"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M54.989,75.295C52.81,70.564 53.458,65.023 56.673,60.914C59.887,56.806 65.135,54.811 70.291,55.736C75.448,56.662 79.66,60.355 81.221,65.321C81.336,65.687 81.437,66.057 81.527,66.427"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M70.082,83.268C64.752,84.14 59.389,81.909 56.278,77.525"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M55.312,79.826C55.718,79.826 56.047,79.499 56.047,79.095C56.047,78.692 55.718,78.365 55.312,78.365C54.905,78.365 54.576,78.692 54.576,79.095C54.576,79.499 54.905,79.826 55.312,79.826Z"
      android:fillColor="#FFF4EE"/>
  <path
      android:pathData="M83.527,70.411C83.933,70.411 84.263,70.084 84.263,69.68C84.263,69.277 83.933,68.95 83.527,68.95C83.12,68.95 82.791,69.277 82.791,69.68C82.791,70.084 83.12,70.411 83.527,70.411Z"
      android:fillColor="#FFF4EE"/>
  <path
      android:pathData="M68.48,92.823H71.488"
      android:strokeLineJoin="round"
      android:strokeWidth="0.364581"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M73.624,92.823H75.701"
      android:strokeLineJoin="round"
      android:strokeWidth="0.364581"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M73.316,93.522C73.316,93.873 73.029,94.158 72.675,94.158C72.321,94.158 72.034,93.873 72.034,93.522"
      android:strokeLineJoin="round"
      android:strokeWidth="0.364581"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M44.178,84.25C43.295,81.447 43.962,78.389 45.936,76.201"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#FFF4EE"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M71.908,98.457C75.997,98.457 79.311,96.135 79.311,93.271"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M64.505,93.271C64.505,94.444 65.062,95.527 65.999,96.396"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M68.101,91.884H61.656"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M69.16,69.77C69.567,69.77 69.896,69.184 69.896,68.46C69.896,67.737 69.567,67.151 69.16,67.151C68.754,67.151 68.424,67.737 68.424,68.46C68.424,69.184 68.754,69.77 69.16,69.77Z"
      android:fillColor="#171A2D"/>
  <path
      android:pathData="M69.724,67.804C69.724,67.935 69.616,68.042 69.484,68.042C69.352,68.042 69.244,67.935 69.244,67.804C69.244,67.673 69.352,67.566 69.484,67.566C69.548,67.566 69.609,67.591 69.654,67.635C69.699,67.68 69.724,67.741 69.724,67.804Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M72.998,69.681C73.405,69.681 73.734,69.094 73.734,68.371C73.734,67.648 73.405,67.062 72.998,67.062C72.592,67.062 72.262,67.648 72.262,68.371C72.262,69.094 72.592,69.681 72.998,69.681Z"
      android:fillColor="#171A2D"/>
  <path
      android:pathData="M73.324,67.951C73.455,67.951 73.562,67.845 73.562,67.715C73.562,67.584 73.455,67.479 73.324,67.479C73.192,67.479 73.085,67.584 73.085,67.715C73.085,67.845 73.192,67.951 73.324,67.951Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M75.964,91.687H80.696"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M66.469,110.683L61.195,107.773L63.783,97.426L69.437,98.82L66.469,110.683Z"
      android:fillColor="#FDB988"/>
  <path
      android:pathData="M68.043,104.392L66.724,109.666"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M61.664,105.9L63.783,97.426L69.437,98.82L68.481,102.641"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M64.597,89.172L65.425,89.221L64.988,96.391L64.91,96.386C63.07,96.276 61.668,94.705 61.779,92.878L61.858,91.579C61.945,90.164 63.171,89.086 64.597,89.172Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M72.337,95.071L73.595,90.046C73.754,89.412 73.365,88.771 72.726,88.613C72.088,88.455 71.442,88.841 71.284,89.475L70.854,91.192L70.838,91.177L71.448,88.76C71.607,88.126 71.218,87.485 70.58,87.327C70.272,87.25 69.947,87.298 69.675,87.459C69.403,87.621 69.207,87.884 69.131,88.19C69.289,87.556 68.9,86.915 68.262,86.758C67.624,86.6 66.978,86.986 66.819,87.62L68.535,80.757C68.691,80.125 68.302,79.486 67.666,79.329C67.029,79.172 66.384,79.556 66.224,80.188L62.893,93.508L62.749,94.078L62.476,95.174C62.168,96.401 62.364,97.699 63.021,98.783C63.679,99.866 64.743,100.646 65.98,100.949C67.216,101.254 68.524,101.059 69.615,100.407C70.706,99.755 71.491,98.699 71.798,97.471L72.215,95.804C72.275,95.563 72.316,95.318 72.337,95.071Z"
      android:fillColor="#FDB988"/>
  <path
      android:pathData="M64.093,100.027C62.649,98.86 62.014,96.97 62.464,95.177L62.881,93.51"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M72.151,95.789L71.735,97.456C71.257,99.378 69.633,100.808 67.654,101.048"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M65.252,84.04L66.253,80.041C66.392,79.486 67.016,79.165 67.659,79.322V79.322C68.299,79.474 68.703,80.056 68.564,80.61L66.068,90.589"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M63.215,92.187L64.826,85.752"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M66.478,88.955L66.812,87.617C66.97,86.983 67.616,86.597 68.255,86.755V86.755C68.893,86.912 69.282,87.553 69.123,88.187"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M69.131,88.19C69.29,87.557 69.936,87.171 70.574,87.328V87.328C71.212,87.485 71.601,88.126 71.443,88.76L71.086,90.188"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M68.607,90.257L68.791,89.525"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M70.847,91.192L71.276,89.475C71.435,88.842 72.081,88.456 72.719,88.613V88.613C73.357,88.771 73.746,89.413 73.588,90.047L72.638,93.836"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M62.432,95.329C61.945,94.755 61.699,94.017 61.746,93.268L61.867,91.288C61.928,90.302 62.623,89.468 63.588,89.224"
      android:fillColor="#FDB988"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M62.432,95.329C61.945,94.755 61.699,94.017 61.746,93.268L61.867,91.288C61.928,90.302 62.623,89.468 63.588,89.224"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M66.994,80.247L66.959,80.365C66.904,80.555 66.927,80.759 67.024,80.931C67.121,81.104 67.283,81.231 67.475,81.284L67.54,81.302"
      android:strokeLineJoin="round"
      android:strokeWidth="0.364581"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M59.148,47.32C54.094,47.859 49.756,51.131 47.879,55.821C46.002,60.51 46.896,65.846 50.2,69.681C56.008,72.769 63.209,71.316 67.341,66.221C71.473,61.126 71.367,53.832 67.087,48.858C64.658,47.561 61.89,47.024 59.148,47.32Z"
      android:fillColor="#B88352"/>
  <path
      android:pathData="M83.857,61.426C85.427,58.984 83.246,54.816 78.986,52.118C74.727,49.42 70.001,49.212 68.431,51.655C66.861,54.097 69.042,58.264 73.302,60.962C77.562,63.66 82.288,63.868 83.857,61.426Z"
      android:fillColor="#B88352"/>
  <path
      android:pathData="M82.51,66.246C84.84,66.246 86.728,64.371 86.728,62.059C86.728,59.746 84.84,57.871 82.51,57.871C80.18,57.871 78.291,59.746 78.291,62.059C78.291,64.371 80.18,66.246 82.51,66.246Z"
      android:fillColor="#B88352"/>
  <path
      android:pathData="M51.767,75.185C53.768,75.185 55.39,73.575 55.39,71.589C55.39,69.602 53.768,67.992 51.767,67.992C49.767,67.992 48.145,69.602 48.145,71.589C48.145,73.575 49.767,75.185 51.767,75.185Z"
      android:fillColor="#B88352"/>
  <path
      android:pathData="M21.162,71.13C30.641,71.13 38.325,63.49 38.325,54.065C38.325,44.64 30.641,37 21.162,37C11.684,37 4,44.64 4,54.065C4,63.49 11.684,71.13 21.162,71.13Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M20.848,47.109m-4.109,0a4.109,4.109 0,1 1,8.217 0a4.109,4.109 0,1 1,-8.217 0"
      android:fillColor="#BDE3FF"/>
  <path
      android:pathData="M17.652,53.043C15.635,53.043 14,54.679 14,56.696V62.174C14,63.182 14.818,64 15.826,64H26.783C27.791,64 28.609,63.182 28.609,62.174V56.696C28.609,54.679 26.974,53.043 24.956,53.043H17.652ZM20.57,59.64C20.673,59.757 20.813,59.815 20.992,59.815C21.136,59.815 21.259,59.774 21.362,59.692C21.472,59.61 21.54,59.493 21.567,59.342C21.602,59.198 21.677,59.061 21.794,58.931C21.917,58.794 22.092,58.633 22.318,58.448C22.558,58.235 22.753,58.054 22.904,57.903C23.055,57.752 23.182,57.57 23.285,57.358C23.394,57.145 23.449,56.912 23.449,56.659C23.449,56.316 23.35,56.011 23.151,55.743C22.952,55.469 22.678,55.257 22.328,55.106C21.979,54.948 21.581,54.87 21.136,54.87C20.738,54.87 20.341,54.935 19.943,55.065C19.552,55.188 19.203,55.363 18.894,55.589C18.778,55.678 18.692,55.771 18.637,55.867C18.589,55.963 18.565,56.083 18.565,56.227C18.565,56.439 18.624,56.621 18.74,56.772C18.863,56.916 19.011,56.988 19.182,56.988C19.326,56.988 19.515,56.923 19.748,56.792L19.994,56.669C20.186,56.559 20.358,56.477 20.508,56.422C20.666,56.36 20.824,56.33 20.982,56.33C21.173,56.33 21.324,56.374 21.434,56.463C21.544,56.552 21.598,56.672 21.598,56.823C21.598,56.974 21.557,57.111 21.475,57.234C21.4,57.358 21.283,57.505 21.125,57.676C20.906,57.903 20.735,58.125 20.611,58.345C20.488,58.557 20.426,58.825 20.426,59.147C20.426,59.359 20.474,59.524 20.57,59.64ZM20.303,61.985C20.495,62.177 20.731,62.272 21.012,62.272C21.3,62.272 21.537,62.177 21.722,61.985C21.914,61.793 22.01,61.556 22.01,61.275C22.01,60.994 21.914,60.758 21.722,60.566C21.537,60.374 21.3,60.278 21.012,60.278C20.731,60.278 20.495,60.374 20.303,60.566C20.118,60.758 20.025,60.994 20.025,61.275C20.025,61.556 20.118,61.793 20.303,61.985Z"
      android:fillColor="#BDE3FF"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M21.001,71.163C12.34,71.159 5.012,64.808 3.839,56.29C2.666,47.772 8.007,39.701 16.349,37.39"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M21.001,36.762C29.211,36.764 36.293,42.484 37.964,50.463C39.636,58.442 35.438,66.491 27.909,69.741"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M70.162,37.13C79.641,37.13 87.325,29.49 87.325,20.065C87.325,10.64 79.641,3 70.162,3C60.684,3 53,10.64 53,20.065C53,29.49 60.684,37.13 70.162,37.13Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M69.848,14.109m-4.109,0a4.109,4.109 0,1 1,8.217 0a4.109,4.109 0,1 1,-8.217 0"
      android:fillColor="#BDE3FF"/>
  <path
      android:pathData="M66.652,20.044C64.635,20.044 63,21.679 63,23.696V29.174C63,30.182 63.818,31 64.826,31H75.783C76.791,31 77.609,30.182 77.609,29.174V23.696C77.609,21.679 75.974,20.044 73.956,20.044H66.652ZM69.57,26.64C69.673,26.757 69.813,26.815 69.992,26.815C70.136,26.815 70.259,26.774 70.362,26.692C70.472,26.61 70.54,26.493 70.567,26.342C70.602,26.198 70.677,26.061 70.794,25.931C70.917,25.794 71.092,25.633 71.318,25.448C71.558,25.235 71.753,25.053 71.904,24.903C72.055,24.752 72.182,24.57 72.285,24.358C72.394,24.145 72.449,23.912 72.449,23.659C72.449,23.316 72.35,23.011 72.151,22.743C71.952,22.469 71.678,22.257 71.328,22.106C70.979,21.948 70.581,21.87 70.136,21.87C69.738,21.87 69.341,21.935 68.943,22.065C68.552,22.188 68.203,22.363 67.894,22.589C67.778,22.678 67.692,22.771 67.637,22.867C67.589,22.963 67.565,23.083 67.565,23.227C67.565,23.439 67.623,23.621 67.74,23.772C67.863,23.916 68.011,23.988 68.182,23.988C68.326,23.988 68.515,23.923 68.748,23.792L68.994,23.669C69.186,23.559 69.358,23.477 69.508,23.422C69.666,23.36 69.824,23.33 69.981,23.33C70.173,23.33 70.324,23.374 70.434,23.463C70.544,23.552 70.598,23.672 70.598,23.823C70.598,23.974 70.557,24.111 70.475,24.234C70.4,24.358 70.283,24.505 70.125,24.677C69.906,24.903 69.735,25.125 69.611,25.345C69.488,25.557 69.426,25.825 69.426,26.147C69.426,26.359 69.474,26.524 69.57,26.64ZM69.303,28.985C69.495,29.177 69.731,29.272 70.012,29.272C70.3,29.272 70.537,29.177 70.722,28.985C70.914,28.793 71.01,28.556 71.01,28.275C71.01,27.994 70.914,27.758 70.722,27.566C70.537,27.374 70.3,27.278 70.012,27.278C69.731,27.278 69.495,27.374 69.303,27.566C69.118,27.758 69.025,27.994 69.025,28.275C69.025,28.556 69.118,28.793 69.303,28.985Z"
      android:fillColor="#BDE3FF"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M52.489,19.972C52.49,13.55 56.093,7.664 61.834,4.705C67.575,1.747 74.498,2.209 79.788,5.904"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M83.023,8.825C87.884,14.482 88.535,22.598 84.638,28.947C80.74,35.296 73.176,38.442 65.883,36.747C58.591,35.052 53.22,28.899 52.567,21.492"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M117.599,82.13C127.077,82.13 134.761,74.49 134.761,65.065C134.761,55.64 127.077,48 117.599,48C108.12,48 100.437,55.64 100.437,65.065C100.437,74.49 108.12,82.13 117.599,82.13Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M108.371,50.653C113.856,47.389 120.707,47.389 126.192,50.654C131.677,53.919 134.904,59.918 134.588,66.259C134.272,72.6 130.464,78.253 124.681,80.964"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M117.332,82.577C110.322,82.579 104.001,78.387 101.319,71.958C98.637,65.529 100.121,58.128 105.08,53.209"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M36.78,34.143C37.243,34.143 37.618,33.771 37.618,33.311C37.618,32.852 37.243,32.479 36.78,32.479C36.317,32.479 35.942,32.852 35.942,33.311C35.942,33.771 36.317,34.143 36.78,34.143Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M43.821,31.511C44.127,31.511 44.375,31.265 44.375,30.961C44.375,30.657 44.127,30.411 43.821,30.411C43.515,30.411 43.267,30.657 43.267,30.961C43.267,31.265 43.515,31.511 43.821,31.511Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M34.099,27.134H33.488V26.527C33.495,26.453 33.459,26.381 33.394,26.341C33.33,26.302 33.249,26.302 33.184,26.341C33.12,26.381 33.083,26.453 33.09,26.527V27.134H32.479C32.369,27.134 32.279,27.222 32.279,27.332C32.279,27.442 32.369,27.531 32.479,27.531H33.09V28.137C33.099,28.239 33.186,28.318 33.289,28.318C33.393,28.318 33.479,28.239 33.488,28.137V27.531H34.099C34.21,27.531 34.299,27.442 34.299,27.332C34.299,27.222 34.21,27.134 34.099,27.134Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M92.362,9.761C92.588,9.761 92.771,9.579 92.771,9.355C92.771,9.13 92.588,8.948 92.362,8.948C92.136,8.948 91.953,9.13 91.953,9.355C91.953,9.579 92.136,9.761 92.362,9.761Z"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#EDA257"/>
  <path
      android:pathData="M101.001,41.821C101.237,41.821 101.429,41.631 101.429,41.397C101.429,41.162 101.237,40.972 101.001,40.972C100.765,40.972 100.574,41.162 100.574,41.397C100.574,41.631 100.765,41.821 101.001,41.821Z"
      android:fillColor="#529ED6"/>
  <path
      android:pathData="M105.18,41.763C105.491,41.763 105.743,41.513 105.743,41.204C105.743,40.895 105.491,40.645 105.18,40.645C104.869,40.645 104.617,40.895 104.617,41.204C104.617,41.513 104.869,41.763 105.18,41.763Z"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#E4F4FC"/>
  <path
      android:pathData="M128.114,42.769C128.641,42.769 129.069,42.345 129.069,41.821C129.069,41.298 128.641,40.874 128.114,40.874C127.587,40.874 127.16,41.298 127.16,41.821C127.16,42.345 127.587,42.769 128.114,42.769Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M81.865,24.792C82.091,24.792 82.274,24.61 82.274,24.386C82.274,24.162 82.091,23.98 81.865,23.98C81.639,23.98 81.456,24.162 81.456,24.386C81.456,24.61 81.639,24.792 81.865,24.792Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M83.664,20.565C84.101,20.565 84.455,20.214 84.455,19.78C84.455,19.347 84.101,18.995 83.664,18.995C83.227,18.995 82.873,19.347 82.873,19.78C82.873,20.214 83.227,20.565 83.664,20.565Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.489997"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M116.848,59.109m-4.109,0a4.109,4.109 0,1 1,8.217 0a4.109,4.109 0,1 1,-8.217 0"
      android:fillColor="#BDE3FF"/>
  <path
      android:pathData="M113.652,65.044C111.635,65.044 110,66.679 110,68.696V74.174C110,75.182 110.818,76 111.826,76H122.783C123.791,76 124.609,75.182 124.609,74.174V68.696C124.609,66.679 122.974,65.044 120.957,65.044H113.652ZM116.57,71.64C116.673,71.757 116.814,71.815 116.992,71.815C117.136,71.815 117.259,71.774 117.362,71.692C117.472,71.609 117.54,71.493 117.568,71.342C117.602,71.198 117.677,71.061 117.794,70.931C117.917,70.794 118.092,70.633 118.318,70.448C118.558,70.235 118.753,70.053 118.904,69.903C119.055,69.752 119.182,69.57 119.285,69.358C119.394,69.145 119.449,68.912 119.449,68.659C119.449,68.316 119.35,68.011 119.151,67.743C118.952,67.469 118.678,67.257 118.328,67.106C117.979,66.948 117.581,66.87 117.136,66.87C116.738,66.87 116.341,66.935 115.943,67.065C115.552,67.188 115.203,67.363 114.894,67.589C114.778,67.678 114.692,67.771 114.637,67.867C114.589,67.963 114.565,68.083 114.565,68.227C114.565,68.439 114.623,68.621 114.74,68.772C114.863,68.916 115.011,68.988 115.182,68.988C115.326,68.988 115.515,68.923 115.748,68.792L115.994,68.669C116.186,68.559 116.358,68.477 116.508,68.422C116.666,68.36 116.824,68.33 116.981,68.33C117.173,68.33 117.324,68.374 117.434,68.463C117.544,68.552 117.598,68.672 117.598,68.823C117.598,68.974 117.557,69.111 117.475,69.234C117.4,69.358 117.283,69.505 117.125,69.676C116.906,69.903 116.735,70.126 116.611,70.345C116.488,70.557 116.426,70.825 116.426,71.147C116.426,71.359 116.474,71.524 116.57,71.64ZM116.303,73.985C116.495,74.177 116.731,74.272 117.012,74.272C117.3,74.272 117.537,74.177 117.722,73.985C117.914,73.793 118.01,73.556 118.01,73.275C118.01,72.994 117.914,72.758 117.722,72.566C117.537,72.374 117.3,72.278 117.012,72.278C116.731,72.278 116.495,72.374 116.303,72.566C116.118,72.758 116.025,72.994 116.025,73.275C116.025,73.556 116.118,73.793 116.303,73.985Z"
      android:fillColor="#BDE3FF"
      android:fillType="evenOdd"/>
</vector>
