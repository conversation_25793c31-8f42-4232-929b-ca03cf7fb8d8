<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="100dp"
    android:height="103dp"
    android:viewportWidth="100"
    android:viewportHeight="103">
  <path
      android:pathData="M52.133,54.513m-47.577,0a47.577,47.577 0,1 1,95.154 0a47.577,47.577 0,1 1,-95.154 0"
      android:fillColor="#E3F3FF"/>
  <path
      android:pathData="M11.385,55.76C11.106,55.73 10.822,55.714 10.534,55.714C6.71,55.714 3.61,58.472 3.61,61.874C3.61,62.072 3.62,62.268 3.641,62.462C3.734,63.326 2.968,64.073 1.992,64.073C1.428,64.073 0.97,64.479 0.97,64.981C0.97,65.483 1.428,65.89 1.992,65.89H42.307C42.871,65.89 43.328,65.483 43.328,64.981C43.328,64.479 42.871,64.073 42.307,64.073H40.765C39.286,64.073 38.079,63.028 38.027,61.714C38.027,61.695 38.026,61.677 38.025,61.658C37.906,59.425 35.912,57.594 33.407,57.421C32.677,57.371 31.979,57.461 31.339,57.662C30.402,53.359 26.144,50.109 21.033,50.109C16.708,50.109 12.994,52.437 11.385,55.76Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M11.385,55.76C11.106,55.73 10.822,55.714 10.534,55.714C6.71,55.714 3.61,58.472 3.61,61.874C3.61,62.072 3.62,62.268 3.641,62.462C3.734,63.326 2.968,64.073 1.992,64.073C1.428,64.073 0.97,64.479 0.97,64.981C0.97,65.483 1.428,65.89 1.992,65.89H42.307C42.871,65.89 43.328,65.483 43.328,64.981C43.328,64.479 42.871,64.073 42.307,64.073H40.765C39.286,64.073 38.079,63.028 38.027,61.714C38.027,61.695 38.026,61.677 38.025,61.658C37.906,59.425 35.912,57.594 33.407,57.421C32.677,57.371 31.979,57.461 31.339,57.662C30.402,53.359 26.144,50.109 21.033,50.109C16.708,50.109 12.994,52.437 11.385,55.76Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="65.8903"
          android:startX="22.1492"
          android:endY="50.1094"
          android:endX="22.1492"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M76.259,48.864C73.965,48.864 72.105,50.518 72.105,52.558C72.105,54.599 73.965,56.253 76.259,56.253H95.69C97.983,56.253 99.843,54.599 99.843,52.558C99.843,50.518 97.983,48.864 95.69,48.864H76.259Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M76.259,48.864C73.965,48.864 72.105,50.518 72.105,52.558C72.105,54.599 73.965,56.253 76.259,56.253H95.69C97.983,56.253 99.843,54.599 99.843,52.558C99.843,50.518 97.983,48.864 95.69,48.864H76.259Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="56.2534"
          android:startX="85.9742"
          android:endY="48.8636"
          android:endX="85.9742"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M31.791,27.64C31.791,25.381 33.849,23.549 36.389,23.549H54.885C54.765,23.327 54.698,23.078 54.698,22.816C54.698,21.879 55.551,21.12 56.603,21.12H65.519C66.571,21.12 67.424,21.879 67.424,22.816C67.424,23.752 66.571,24.511 65.519,24.511H57.887C58.888,25.261 59.524,26.385 59.524,27.64C59.524,29.9 57.465,31.731 54.925,31.731H36.389C33.849,31.731 31.791,29.9 31.791,27.64Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M31.791,27.64C31.791,25.381 33.849,23.549 36.389,23.549H54.885C54.765,23.327 54.698,23.078 54.698,22.816C54.698,21.879 55.551,21.12 56.603,21.12H65.519C66.571,21.12 67.424,21.879 67.424,22.816C67.424,23.752 66.571,24.511 65.519,24.511H57.887C58.888,25.261 59.524,26.385 59.524,27.64C59.524,29.9 57.465,31.731 54.925,31.731H36.389C33.849,31.731 31.791,29.9 31.791,27.64Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="31.7314"
          android:startX="49.6075"
          android:endY="21.1201"
          android:endX="49.6075"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M37.16,70.368C36.646,72.786 37.13,75.302 38.479,77.647C38.914,78.404 39.484,79.457 40.142,80.671C40.286,80.938 40.435,81.213 40.587,81.493C41.429,83.045 42.376,84.776 43.324,86.406C44.269,88.032 45.228,89.581 46.095,90.761C46.527,91.349 46.954,91.87 47.359,92.267C47.732,92.632 48.212,93.021 48.756,93.137C49.3,93.252 49.896,93.092 50.386,92.91C50.918,92.712 51.52,92.411 52.154,92.049C53.426,91.324 54.932,90.299 56.457,89.198C57.986,88.094 59.556,86.898 60.955,85.823C61.21,85.628 61.458,85.437 61.699,85.251C62.793,84.41 63.741,83.681 64.446,83.167C66.633,81.573 68.096,79.47 68.61,77.053C69.73,71.783 66.356,66.586 61.086,65.466C58.804,64.981 56.457,65.327 54.435,66.42C53.032,64.599 51.029,63.328 48.747,62.843C43.479,61.723 38.281,65.098 37.16,70.368Z"
      android:strokeWidth="1.8786"
      android:fillColor="#75C6FF"
      android:strokeColor="#ffffff"/>
  <path
      android:pathData="M61.448,70.292C61.448,70.292 62.417,70.944 62.974,71.88C63.531,72.816 63.739,73.573 63.739,73.573"
      android:strokeLineJoin="round"
      android:strokeWidth="1.26141"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M64.095,75.197L64.131,75.462L64.095,75.197Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="74.5815"
          android:startX="63.627"
          android:endY="75.8153"
          android:endX="63.3647"
          android:type="linear">
        <item android:offset="0" android:color="#7F007755"/>
        <item android:offset="1" android:color="#7F000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M64.095,75.197L64.131,75.462"
      android:strokeLineJoin="round"
      android:strokeWidth="1.26141"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M51.861,37.979C52.142,39.573 53.051,40.975 54.431,42.056L54.432,42.057C54.866,42.397 55.449,42.879 56.123,43.436C56.272,43.559 56.425,43.685 56.582,43.815C57.445,44.527 58.415,45.322 59.362,46.057C60.306,46.79 61.244,47.478 62.042,47.97C62.44,48.216 62.824,48.426 63.17,48.569C63.48,48.697 63.896,48.83 64.297,48.759C64.698,48.688 65.043,48.421 65.291,48.195C65.567,47.943 65.856,47.614 66.146,47.247C66.728,46.511 67.374,45.543 68.01,44.532C68.649,43.517 69.289,42.439 69.857,41.474C69.96,41.298 70.061,41.127 70.159,40.96C70.602,40.206 70.985,39.554 71.276,39.086C72.203,37.598 72.577,35.969 72.296,34.376C71.686,30.913 68.374,28.594 64.911,29.204C63.462,29.46 62.166,30.2 61.217,31.293C59.952,30.591 58.481,30.338 57.032,30.594C53.57,31.204 51.25,34.516 51.861,37.979Z"
      android:strokeWidth="1.55"
      android:fillColor="#75C6FF"
      android:strokeColor="#ffffff"/>
  <path
      android:pathData="M66.361,32.173C66.361,32.173 67.086,32.327 67.636,32.745C68.186,33.163 68.488,33.559 68.488,33.559"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800044"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M69.082,34.429L69.167,34.577L69.082,34.429Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="34.1786"
          android:startX="68.6611"
          android:endY="34.9665"
          android:endX="68.8001"
          android:type="linear">
        <item android:offset="0" android:color="#7F007755"/>
        <item android:offset="1" android:color="#7F000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M69.082,34.429L69.167,34.577"
      android:strokeLineJoin="round"
      android:strokeWidth="0.800044"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M42.148,8.692C41.577,7.775 41.341,6.763 41.516,5.767C41.896,3.612 43.958,2.17 46.112,2.549C46.969,2.701 47.74,3.124 48.321,3.75C49.081,3.361 49.951,3.226 50.807,3.377C52.962,3.757 54.405,5.818 54.025,7.973C53.85,8.969 53.282,9.839 52.432,10.505L42.148,8.692ZM42.148,8.692C42.32,8.969 42.547,9.355 42.811,9.805M42.148,8.692L42.811,9.805M42.811,9.805C42.87,9.904 42.93,10.007 42.992,10.112M42.811,9.805L42.992,10.112M42.992,10.112C43.33,10.687 43.712,11.331 44.094,11.938M42.992,10.112L44.094,11.938M44.094,11.938C44.474,12.542 44.863,13.125 45.215,13.571M44.094,11.938L45.215,13.571M45.215,13.571C45.391,13.793 45.569,13.997 45.743,14.155M45.215,13.571L45.743,14.155M45.743,14.155C45.895,14.294 46.131,14.482 46.42,14.533M45.743,14.155L46.42,14.533M46.42,14.533C46.709,14.584 46.995,14.488 47.184,14.41M46.42,14.533L47.184,14.41M47.184,14.41C47.402,14.32 47.64,14.189 47.881,14.041M47.184,14.41L47.881,14.041M47.881,14.041C48.364,13.742 48.929,13.328 49.493,12.89M47.881,14.041L49.493,12.89M49.493,12.89C50.059,12.45 50.639,11.976 51.154,11.551M49.493,12.89L51.154,11.551M51.154,11.551C51.248,11.473 51.34,11.397 51.429,11.324M51.154,11.551L51.429,11.324M51.429,11.324C51.83,10.992 52.175,10.707 52.432,10.506L51.429,11.324Z"
      android:strokeWidth="1.2586"
      android:fillColor="#75C6FF"
      android:strokeColor="#ffffff"/>
  <path
      android:pathData="M51.108,5.676C51.108,5.676 51.482,5.91 51.705,6.256C51.928,6.602 52.017,6.886 52.017,6.886"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476794"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M52.173,7.495L52.19,7.594L52.173,7.495Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="7.26819"
          android:startX="51.9878"
          android:endY="7.73774"
          android:endX="51.905"
          android:type="linear">
        <item android:offset="0" android:color="#7F007755"/>
        <item android:offset="1" android:color="#7F000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M52.173,7.495L52.19,7.594"
      android:strokeLineJoin="round"
      android:strokeWidth="0.476794"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
</vector>
