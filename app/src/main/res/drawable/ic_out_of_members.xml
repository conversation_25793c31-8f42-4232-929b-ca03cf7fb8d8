<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="200dp"
    android:height="200dp"
    android:viewportWidth="200"
    android:viewportHeight="200">
  <path
      android:pathData="M0,0h200v200h-200z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M0,0h200v200h-200z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="0"
          android:startX="100"
          android:endY="200"
          android:endX="100"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M147.965,89.239H143.113C138.465,89.239 134.668,85.554 134.502,80.888C134.502,80.813 134.502,80.758 134.502,80.684C134.131,72.776 127.854,66.313 119.965,65.702C117.668,65.517 115.465,65.851 113.465,66.554C110.52,51.332 97.113,39.832 81.039,39.832C67.428,39.832 55.743,48.073 50.687,59.813C49.817,59.702 48.909,59.647 48.002,59.647C35.965,59.647 26.205,69.406 26.205,81.443C26.205,82.147 26.243,82.832 26.298,83.517C26.594,86.573 24.187,89.221 21.113,89.221C19.335,89.221 17.891,90.665 17.891,92.443C17.891,94.221 19.335,95.665 21.113,95.665H147.983C149.761,95.665 151.205,94.221 151.205,92.443C151.187,90.684 149.742,89.239 147.965,89.239Z"
      android:fillColor="#BDE3FF"/>
  <path
      android:pathData="M58.539,157.851H60.761C62.891,157.851 64.65,156.147 64.724,154.018C64.724,153.981 64.724,153.962 64.724,153.925C64.891,150.296 67.78,147.314 71.391,147.036C72.447,146.962 73.447,147.11 74.372,147.425C75.724,140.425 81.891,135.147 89.261,135.147C95.521,135.147 100.891,138.925 103.206,144.333C103.613,144.277 104.021,144.259 104.428,144.259C109.947,144.259 114.428,148.74 114.428,154.259C114.428,154.573 114.409,154.907 114.391,155.222C114.261,156.629 115.372,157.833 116.78,157.833C117.595,157.833 118.261,158.499 118.261,159.314C118.261,160.129 117.595,160.796 116.78,160.796H58.539C57.724,160.796 57.058,160.129 57.058,159.314C57.076,158.518 57.724,157.851 58.539,157.851Z"
      android:fillColor="#BDE3FF"/>
  <path
      android:pathData="M149.428,25.425H148.02C146.668,25.425 145.557,24.351 145.52,22.999C145.52,22.981 145.52,22.962 145.52,22.944C145.409,20.647 143.594,18.758 141.298,18.592C140.631,18.536 139.983,18.629 139.409,18.833C138.557,14.407 134.65,11.073 129.983,11.073C126.02,11.073 122.631,13.462 121.168,16.888C120.909,16.851 120.65,16.833 120.391,16.833C116.891,16.833 114.057,19.666 114.057,23.166C114.057,23.369 114.076,23.573 114.094,23.777C114.187,24.666 113.483,25.425 112.594,25.425C112.076,25.425 111.668,25.851 111.668,26.351C111.668,26.869 112.094,27.277 112.594,27.277H149.428C149.946,27.277 150.354,26.851 150.354,26.351C150.354,25.851 149.928,25.425 149.428,25.425Z"
      android:fillColor="#E3F3FF"/>
  <path
      android:pathData="M175.464,115.555H144.242C140.52,115.555 137.483,112.536 137.483,108.795C137.483,105.073 140.501,102.036 144.242,102.036H175.464C179.187,102.036 182.224,105.055 182.224,108.795C182.224,112.536 179.187,115.555 175.464,115.555Z"
      android:fillColor="#E3F3FF"/>
  <path
      android:pathData="M44.743,55.666H20.28C18.131,55.666 16.409,53.925 16.409,51.795C16.409,49.647 18.15,47.925 20.28,47.925H44.743C46.891,47.925 48.613,49.666 48.613,51.795C48.613,53.925 46.872,55.666 44.743,55.666Z"
      android:fillColor="#E3F3FF"/>
  <path
      android:pathData="M143.872,50.925H138.687C136.65,50.925 134.983,49.258 134.983,47.221C134.983,45.184 136.65,43.518 138.687,43.518H143.872C145.909,43.518 147.576,45.184 147.576,47.221C147.576,49.258 145.909,50.925 143.872,50.925Z"
      android:fillColor="#E3F3FF"/>
  <path
      android:pathData="M155.372,43.518H153.002C151.983,43.518 151.168,42.703 151.168,41.685C151.168,40.666 151.983,39.852 153.002,39.852H155.372C156.391,39.852 157.205,40.666 157.205,41.685C157.205,42.703 156.391,43.518 155.372,43.518Z"
      android:fillColor="#E3F3FF"/>
  <path
      android:pathData="M119.687,41.684H115.817C115.039,41.684 114.409,41.054 114.409,40.277C114.409,39.499 115.039,38.869 115.817,38.869H119.687C120.465,38.869 121.094,39.499 121.094,40.277C121.094,41.054 120.465,41.684 119.687,41.684Z"
      android:fillColor="#E3F3FF"/>
  <path
      android:pathData="M69.428,174.073H46.65C44.909,174.073 43.502,172.665 43.502,170.925C43.502,169.184 44.909,167.776 46.65,167.776H69.428C71.169,167.776 72.576,169.184 72.576,170.925C72.576,172.665 71.169,174.073 69.428,174.073Z"
      android:fillColor="#E3F3FF"/>
  <path
      android:pathData="M129.52,82.258C129.52,82.202 129.52,82.147 129.52,82.091C129.28,76.943 126.15,72.499 121.706,70.369"
      android:strokeAlpha="0.6"
      android:strokeLineJoin="round"
      android:strokeWidth="2.59259"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.6"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M38.965,67.759C35.002,71.222 32.502,76.314 32.502,81.999"
      android:strokeAlpha="0.6"
      android:strokeLineJoin="round"
      android:strokeWidth="2.59259"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.6"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M85.076,140.685C81.946,142.666 79.65,145.944 79.02,149.888"
      android:strokeAlpha="0.6"
      android:strokeLineJoin="round"
      android:strokeWidth="2.59259"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.6"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.724,58.148C100.169,53.055 95.002,49.166 88.946,47.222"
      android:strokeAlpha="0.6"
      android:strokeLineJoin="round"
      android:strokeWidth="2.59259"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.6"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M63.613,63.073C65.607,63.073 67.224,61.456 67.224,59.462C67.224,57.467 65.607,55.851 63.613,55.851C61.618,55.851 60.001,57.467 60.001,59.462C60.001,61.456 61.618,63.073 63.613,63.073Z"
      android:strokeAlpha="0.6"
      android:strokeLineJoin="round"
      android:strokeWidth="2.59259"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.6"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M70.224,52.518C70.868,52.518 71.391,51.996 71.391,51.351C71.391,50.707 70.868,50.185 70.224,50.185C69.579,50.185 69.057,50.707 69.057,51.351C69.057,51.996 69.579,52.518 70.224,52.518Z"
      android:strokeAlpha="0.6"
      android:strokeLineJoin="round"
      android:strokeWidth="2.59259"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.6"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M121.687,23.888H134.983"
      android:strokeAlpha="0.6"
      android:strokeLineJoin="round"
      android:strokeWidth="2.59259"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.6"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M161.465,105.555H173.317"
      android:strokeAlpha="0.6"
      android:strokeLineJoin="round"
      android:strokeWidth="2.59259"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.6"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M162.907,59.814H162.352V59.258C162.352,59.147 162.278,59.073 162.167,59.073C162.056,59.073 161.981,59.147 161.981,59.258V59.814H161.426C161.315,59.814 161.241,59.888 161.241,59.999C161.241,60.11 161.315,60.184 161.426,60.184H161.981V60.74C161.981,60.851 162.056,60.925 162.167,60.925C162.278,60.925 162.352,60.851 162.352,60.74V60.184H162.907C163.019,60.184 163.093,60.11 163.093,59.999C163.093,59.888 163,59.814 162.907,59.814Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M162.056,79.999H160.926V78.869C160.926,78.666 160.76,78.499 160.556,78.499C160.352,78.499 160.186,78.666 160.186,78.869V79.999H159.056C158.852,79.999 158.686,80.166 158.686,80.369C158.686,80.573 158.852,80.74 159.056,80.74H160.186V81.869C160.186,82.073 160.352,82.24 160.556,82.24C160.76,82.24 160.926,82.073 160.926,81.869V80.74H162.056C162.26,80.74 162.426,80.573 162.426,80.369C162.426,80.166 162.26,79.999 162.056,79.999Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M62.574,122.147H61.019V120.592C61.019,120.314 60.797,120.073 60.5,120.073C60.222,120.073 59.982,120.295 59.982,120.592V122.147H58.426C58.148,122.147 57.908,122.37 57.908,122.666C57.908,122.962 58.13,123.184 58.426,123.184H59.982V124.74C59.982,125.018 60.204,125.258 60.5,125.258C60.778,125.258 61.019,125.036 61.019,124.74V123.184H62.574C62.852,123.184 63.093,122.962 63.093,122.666C63.093,122.37 62.852,122.147 62.574,122.147Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M76.185,126.462H75.111V125.369C75.111,125.166 74.944,125.018 74.759,125.018C74.555,125.018 74.407,125.184 74.407,125.369V126.444H73.333C73.13,126.444 72.981,126.61 72.981,126.795C72.981,126.999 73.148,127.147 73.333,127.147H74.407V128.221C74.407,128.425 74.574,128.573 74.759,128.573C74.963,128.573 75.111,128.406 75.111,128.221V127.147H76.185C76.389,127.147 76.537,126.981 76.537,126.795C76.537,126.61 76.389,126.462 76.185,126.462Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M67.722,28.758C67.722,27.555 66.741,26.573 65.537,26.573C64.333,26.573 63.352,27.555 63.352,28.758C63.352,28.925 63.352,28.925 63.352,28.758C63.352,27.555 62.37,26.573 61.167,26.573C59.963,26.573 58.981,27.555 58.981,28.758C58.981,29.666 60.241,30.814 60.241,30.814L63.352,33.925L66.463,30.814C66.463,30.814 67.722,29.666 67.722,28.758Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.11111"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M78.278,34.555H77.537V33.814C77.537,33.684 77.426,33.573 77.296,33.573C77.167,33.573 77.056,33.684 77.056,33.814V34.555H76.315C76.185,34.555 76.074,34.666 76.074,34.796C76.074,34.925 76.185,35.036 76.315,35.036H77.056V35.777C77.056,35.907 77.167,36.018 77.296,36.018C77.426,36.018 77.537,35.907 77.537,35.777V35.036H78.278C78.408,35.036 78.519,34.925 78.519,34.796C78.519,34.666 78.408,34.555 78.278,34.555Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M70.852,17.684C71.138,17.684 71.37,17.452 71.37,17.166C71.37,16.88 71.138,16.647 70.852,16.647C70.566,16.647 70.334,16.88 70.334,17.166C70.334,17.452 70.566,17.684 70.852,17.684Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M51.963,39.407C52.341,39.407 52.648,39.1 52.648,38.721C52.648,38.343 52.341,38.036 51.963,38.036C51.585,38.036 51.278,38.343 51.278,38.721C51.278,39.1 51.585,39.407 51.963,39.407Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.37037"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M124.078,92.297L117.638,98.737L131.343,112.442L137.783,106.002L124.078,92.297Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M118.81,97.552L117.612,98.75L131.317,112.455L132.515,111.257L118.81,97.552Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M131.264,99.473L124.062,92.271L117.602,98.731L124.804,105.933C125.969,104.98 127.098,103.956 128.193,102.862C129.287,101.803 130.311,100.638 131.264,99.473Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M63.94,38.608C46.605,55.942 46.605,84.08 63.94,101.414C81.274,118.748 109.411,118.748 126.746,101.414C144.08,84.08 144.08,55.942 126.746,38.608C109.411,21.274 81.309,21.274 63.94,38.608ZM119.402,94.071C106.128,107.345 84.592,107.345 71.318,94.071C58.044,80.796 58.044,59.261 71.318,45.987C84.592,32.712 106.128,32.712 119.402,45.987C132.677,59.261 132.677,80.796 119.402,94.071Z"
      android:fillColor="#C5C5C5"/>
  <path
      android:pathData="M96.384,103.991C105.422,104.274 114.565,100.955 121.45,94.071C134.724,80.797 134.724,59.261 121.45,45.987C114.565,39.103 105.422,35.784 96.384,36.066C104.751,36.313 113.012,39.632 119.402,45.987C132.676,59.261 132.676,80.797 119.402,94.071C113.012,100.426 104.751,103.744 96.384,103.991Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M66.023,101.414C48.688,84.08 48.688,55.943 66.023,38.608C74.425,30.206 85.369,25.864 96.384,25.617C84.663,25.334 72.872,29.676 63.94,38.608C46.605,55.943 46.605,84.08 63.94,101.414C72.872,110.346 84.663,114.688 96.384,114.406C85.369,114.159 74.425,109.852 66.023,101.414Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M185.386,149.57L174.9,160.055C173.912,161.044 172.288,161.044 171.299,160.055L125.828,114.584C124.839,113.595 124.839,111.971 125.828,110.983L136.313,100.497C137.302,99.509 138.926,99.509 139.914,100.497L185.386,145.969C186.374,146.957 186.374,148.581 185.386,149.57Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M172.888,158.465L127.452,112.994C126.463,112.005 126.463,110.381 127.452,109.393L125.863,110.981C124.874,111.97 124.874,113.594 125.863,114.582L171.299,160.054C172.288,161.042 173.912,161.042 174.9,160.054L176.489,158.465C175.5,159.454 173.876,159.454 172.888,158.465Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M177.301,157.653L178.113,156.841L177.301,157.653Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M136.313,100.497L125.828,110.983C124.839,111.971 124.839,113.595 125.828,114.584L129.676,118.432L143.762,104.345L139.914,100.497C138.926,99.509 137.302,99.509 136.313,100.497Z"
      android:fillColor="#C5C5C5"/>
  <path
      android:pathData="M185.386,149.569L174.9,160.055C173.912,161.043 172.288,161.043 171.299,160.055L167.451,156.206L181.537,142.12L185.386,145.968C186.374,146.957 186.374,148.581 185.386,149.569Z"
      android:fillColor="#C5C5C5"/>
  <path
      android:pathData="M185.386,149.57L174.9,160.055C173.912,161.044 172.288,161.044 171.299,160.055L125.828,114.584C124.839,113.595 124.839,111.971 125.828,110.983L136.313,100.497C137.302,99.509 138.926,99.509 139.914,100.497L185.386,145.969C186.374,146.957 186.374,148.581 185.386,149.57Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.05912"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M119.398,94.062C132.675,80.785 132.675,59.259 119.398,45.983C106.121,32.706 84.595,32.706 71.319,45.983C58.042,59.259 58.042,80.785 71.319,94.062C84.595,107.339 106.121,107.339 119.398,94.062Z"
      android:strokeAlpha="0.1"
      android:fillColor="#72BAE5"
      android:fillAlpha="0.1"/>
  <path
      android:pathData="M74.213,49.658C81.062,42.809 90.453,40.055 99.314,41.397"
      android:strokeLineJoin="round"
      android:strokeWidth="2.11824"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.48,42.315C107.964,43.657 112.201,46.128 115.731,49.659"
      android:strokeLineJoin="round"
      android:strokeWidth="2.11824"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M124.592,99.26C107.611,116.241 80.285,116.595 62.88,100.319C63.233,100.672 63.586,101.061 63.939,101.414C81.274,118.748 109.411,118.748 126.745,101.414C144.08,84.079 144.08,55.942 126.745,38.608C126.392,38.255 126.004,37.902 125.651,37.549C141.926,54.989 141.573,82.279 124.592,99.26Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M119.402,94.072C106.128,107.346 84.593,107.346 71.318,94.072C70.965,93.719 70.612,93.33 70.259,92.977C72.236,94.778 74.354,96.296 76.579,97.567C89.535,104.839 106.234,102.968 117.249,91.953C130.17,79.032 130.523,58.309 118.308,44.964C118.696,45.317 119.049,45.67 119.402,46.023C132.677,59.262 132.677,80.797 119.402,94.072Z"
      android:strokeAlpha="0.6"
      android:fillColor="#ffffff"
      android:fillAlpha="0.6"/>
  <path
      android:pathData="M83.249,88.464C85.772,85.941 85.772,81.85 83.249,79.327C80.726,76.804 76.635,76.804 74.112,79.327C71.589,81.85 71.589,85.941 74.112,88.464C76.635,90.987 80.726,90.987 83.249,88.464Z"
      android:strokeAlpha="0.6"
      android:fillColor="#ffffff"
      android:fillAlpha="0.6"/>
  <path
      android:pathData="M71.601,72.009C72.4,71.21 72.4,69.913 71.601,69.114C70.801,68.314 69.504,68.314 68.705,69.114C67.905,69.913 67.905,71.21 68.705,72.009C69.504,72.809 70.801,72.809 71.601,72.009Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M61.398,71.794C60.903,62.509 64.222,53.083 71.318,45.986C84.592,32.712 106.128,32.712 119.402,45.986C132.677,59.261 132.677,80.796 119.402,94.071C106.128,107.345 84.592,107.345 71.318,94.071C68,90.752 65.493,86.904 63.834,82.809M97.373,25.651C85.334,25.087 73.119,29.429 63.94,38.608C46.605,55.942 46.605,84.079 63.94,101.414C81.274,118.748 109.411,118.748 126.746,101.414C144.08,84.079 144.08,55.942 126.746,38.608C123.498,35.36 119.861,32.712 115.978,30.664L97.373,25.651ZM108.917,27.734C106.446,26.958 103.939,26.393 101.362,26.04L108.917,27.734Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.05912"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M139.244,114.018C140.232,114.618 140.585,115.889 139.985,116.877"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.7652"
      android:fillColor="#00000000"
      android:strokeColor="#454545"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M146.057,125.634C147.045,126.234 147.398,127.505 146.798,128.493"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.7652"
      android:fillColor="#00000000"
      android:strokeColor="#454545"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M167.98,146.18C168.969,146.78 169.322,148.051 168.722,149.039"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.7652"
      android:fillColor="#00000000"
      android:strokeColor="#454545"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M146.304,117.796C146.445,116.666 147.505,115.819 148.634,115.96"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.7652"
      android:fillColor="#00000000"
      android:strokeColor="#454545"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M159.296,138.343C158.307,137.708 158.025,136.437 158.66,135.448"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.7652"
      android:fillColor="#00000000"
      android:strokeColor="#454545"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M153.365,125.422C154.071,124.504 155.377,124.363 156.295,125.069"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.7652"
      android:fillColor="#00000000"
      android:strokeColor="#454545"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M168.475,139.12C169.181,138.202 170.487,138.061 171.405,138.767"
      android:strokeAlpha="0.7"
      android:strokeLineJoin="round"
      android:strokeWidth="1.7652"
      android:fillColor="#00000000"
      android:strokeColor="#454545"
      android:fillAlpha="0.7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M44.77,132.341H49.994"
      android:strokeLineJoin="round"
      android:strokeWidth="1.05912"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
</vector>
