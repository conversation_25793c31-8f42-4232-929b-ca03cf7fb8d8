<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="1194dp"
    android:height="1194dp"
    android:viewportWidth="1194"
    android:viewportHeight="1194">
  <path
      android:pathData="M0,0h1194v1194h-1194z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M597,597m-597,0a597,597 0,1 1,1194 0a597,597 0,1 1,-1194 0"
      android:strokeAlpha="0.9"
      android:fillColor="#F4F4F4"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M786.23,683.31L795.61,700.75C818.99,744.22 824.83,795 811.93,842.65L803.96,872.08C792.83,913.17 802.25,957.09 829.23,990.01L878.81,1050.49"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M493.83,454.58L375.44,537.56V397.78L493.83,285.78V454.58Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M630.32,1128.25L593.66,1125.78L596.19,996.28C596.42,984.22 598.39,972.24 602.03,960.7L607.15,944.45C598.23,920.93 593.66,896.06 593.66,871V644.45H704.99L696.46,699.69L630.32,1128.25Z"
      android:strokeAlpha="0.9"
      android:fillColor="#A2A2A2"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M850.24,693.92L810.49,689.9L707.82,445.89H773.67L850.24,693.92Z"
      android:strokeAlpha="0.9"
      android:fillColor="#AEAEAE"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M813.31,677.04L844.41,675.01L849.42,691.25L813.31,677.04Z"
      android:strokeAlpha="0.9"
      android:fillColor="#A2A2A2"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M732.35,1177.88H582.58L594.57,1125.36L636.8,1126.66L717.79,1157.71C726.61,1161.09 732.35,1169.05 732.35,1177.88Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M561,1131.26L596.52,1125.78L594,996.28C593.76,984.22 591.8,972.24 588.17,960.7L583.06,944.45C591.96,920.93 596.52,896.06 596.52,871V644.45H485.44L561,1131.26Z"
      android:strokeAlpha="0.9"
      android:fillColor="#A2A2A2"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M546.77,948.44C546.77,954.46 541.9,959.33 535.88,959.33C529.87,959.33 524.99,954.46 524.99,948.44C524.99,942.43 529.87,937.55 535.88,937.55C541.9,937.55 546.77,942.43 546.77,948.44Z"
      android:strokeAlpha="0.9"
      android:fillColor="#A2A2A2"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M642.88,948.44C642.88,954.46 647.75,959.33 653.76,959.33C659.78,959.33 664.65,954.46 664.65,948.44C664.65,942.43 659.78,937.55 653.76,937.55C647.75,937.55 642.88,942.43 642.88,948.44Z"
      android:strokeAlpha="0.9"
      android:fillColor="#A2A2A2"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M573.44,945.12L580.55,944.45"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M616.95,945.12L609.84,944.45"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M849.2,731.49L854.47,710.54C854.61,709.96 854.7,709.37 854.72,708.79C854.9,704.74 852.22,700.99 848.14,699.96C843.46,698.78 838.72,701.62 837.55,706.29L832.28,727.23C831.11,731.91 833.95,736.64 838.62,737.82C843.29,738.99 848.03,736.16 849.2,731.49Z"
      android:strokeAlpha="0.9"
      android:fillColor="#AEAEAE"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M832.43,728.7L837.7,707.75C838.87,703.08 836.04,698.34 831.37,697.17C826.7,695.99 821.96,698.83 820.78,703.5L815.52,724.44C814.34,729.11 817.18,733.85 821.85,735.03C826.52,736.2 831.26,733.37 832.43,728.7Z"
      android:strokeAlpha="0.9"
      android:fillColor="#AEAEAE"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M816.45,724.68L821.71,703.73C822.89,699.06 820.05,694.32 815.38,693.15C810.71,691.97 805.97,694.81 804.8,699.48L799.53,720.42C798.36,725.09 801.19,729.83 805.86,731.01C810.53,732.18 815.27,729.35 816.45,724.68Z"
      android:strokeAlpha="0.9"
      android:fillColor="#AEAEAE"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M801.66,706.66L815.47,690.06C818.55,686.36 818.05,680.86 814.34,677.78C810.64,674.7 805.14,675.2 802.06,678.9L788.25,695.5C785.17,699.2 785.67,704.7 789.37,707.78C793.08,710.87 798.57,710.36 801.66,706.66Z"
      android:strokeAlpha="0.9"
      android:fillColor="#AEAEAE"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M795.45,725.86L801.92,705.25C803.36,700.65 800.8,695.76 796.21,694.32C791.61,692.88 786.72,695.43 785.28,700.03L778.81,720.63C777.37,725.23 779.93,730.13 784.52,731.57C789.12,733.01 794.01,730.45 795.45,725.86Z"
      android:strokeAlpha="0.9"
      android:fillColor="#AEAEAE"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M860.41,709.03C862.96,702.63 859.83,695.39 853.44,692.85L815.02,677.58C808.63,675.04 801.39,678.16 798.84,684.56C796.3,690.95 799.43,698.2 805.82,700.74L844.24,716C850.63,718.54 857.87,715.42 860.41,709.03Z"
      android:strokeAlpha="0.9"
      android:fillColor="#AEAEAE"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M460.68,328.68C459.4,291.75 488.99,261.12 525.93,261.12H667.12C704.42,261.12 734.15,292.32 732.33,329.58L705,644.45L702.69,645.8C635.51,685.23 552.13,684.71 485.44,644.45L472.48,479.12"
      android:strokeAlpha="0.9"
      android:fillColor="#ffffff"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M498.78,267.01C507.04,263.23 516.23,261.12 525.93,261.12H644.33"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M482.21,603.18L460.68,328.68C460.16,313.71 464.71,299.77 472.77,288.49"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M667.12,261.12C704.42,261.12 734.15,292.32 732.33,329.58L705,644.45L702.69,645.8C635.51,685.23 552.13,684.71 485.45,644.45H485.44"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M630.33,264.18L595.15,324.45L559.97,264.18V177.34H630.33V264.18Z"
      android:strokeAlpha="0.9"
      android:fillColor="#AEAEAE"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M559.97,207.78L630.33,228.45V188.78L559.97,187.45V207.78Z"
      android:strokeAlpha="0.9"
      android:fillColor="#979797"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M588.11,255.15C581.65,255.15 576.41,249.91 576.41,243.45"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M614.73,290.9L595.15,324.45L559.97,264.17V177.34H630.33V264.17L625.34,272.73"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M807.08,526.67H769.66H732.41L630.33,285.78H700.85C715.29,285.78 728.13,294.97 732.79,308.64L807.08,526.67Z"
      android:strokeAlpha="0.9"
      android:fillColor="#ffffff"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M636.7,62.57C636.7,72.14 634.95,79.9 632.78,79.9C630.62,79.9 628.87,72.14 628.87,62.57C628.87,52.99 630.62,45.23 632.78,45.23C634.95,45.23 636.7,52.99 636.7,62.57Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M641.69,70.32C636.3,78.23 630.47,83.65 628.69,82.43C626.9,81.22 629.83,73.82 635.22,65.91C640.62,58 646.44,52.58 648.23,53.8C650.01,55.02 647.09,62.42 641.69,70.32Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M542,95.87C542,101.99 537.03,106.95 530.91,106.95C524.79,106.95 519.83,101.99 519.83,95.87C519.83,89.75 524.79,84.78 530.91,84.78C537.03,84.78 542,89.75 542,95.87Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M538.83,131.89C538.83,142.39 530.32,150.89 519.83,150.89C509.33,150.89 500.83,142.39 500.83,131.89C500.83,121.4 509.33,112.89 519.83,112.89C530.32,112.89 538.83,121.4 538.83,131.89Z"
      android:strokeAlpha="0.9"
      android:fillColor="#AEAEAE"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M538.83,131.89C538.83,142.39 530.32,150.89 519.83,150.89C509.33,150.89 500.83,142.39 500.83,131.89C500.83,121.4 509.33,112.89 519.83,112.89C530.32,112.89 538.83,121.4 538.83,131.89Z"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M665.14,140.86C660.93,180.88 625.07,209.9 585.06,205.68C545.04,201.47 516.02,165.61 520.23,125.6C524.45,85.58 560.3,56.56 600.32,60.77C640.34,64.99 669.36,100.84 665.14,140.86Z"
      android:strokeAlpha="0.9"
      android:fillColor="#AEAEAE"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M458.08,1177.88H607.86L595.86,1125.36L553.64,1126.66L472.64,1157.71C463.83,1161.09 458.08,1169.05 458.08,1177.88Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M578.16,1169.33H498.97"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M814.34,677.78L843.38,688.85"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M802.03,710.47L799.53,720.42C798.36,725.09 801.19,729.83 805.86,731.01C810.53,732.18 815.27,729.35 816.45,724.68L821.71,703.73"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M821.85,735.03C826.52,736.2 831.26,733.37 832.43,728.7L836.85,709.08"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M854.73,709.08L848.99,731.27"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M619.67,1169.33H691.47"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M633.89,80L575.12,90.55C558.83,93.48 542.03,86.92 532.73,73.22C528.62,67.16 526.39,60.4 527.44,54.06C531.33,30.6 556.66,17.45 578.09,27.78L616.44,46.26C627.11,51.4 633.89,62.2 633.89,74.04V80Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M668.67,119.5C649.46,119.5 633.89,103.93 633.89,84.73V66.67H638.17C655.01,66.67 668.67,80.33 668.67,97.17V119.5Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M549.44,47.78C549.44,56.5 542.38,63.56 533.67,63.56C524.95,63.56 517.89,56.5 517.89,47.78C517.89,39.07 524.95,32.01 533.67,32.01C542.38,32.01 549.44,39.07 549.44,47.78Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M538.33,32.3C538.33,26.33 543.18,21.48 549.15,21.48C555.12,21.48 559.97,26.33 559.97,32.3C559.97,38.27 555.12,43.12 549.15,43.12C543.18,43.12 538.33,38.27 538.33,32.3Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M563.92,71.9C563.92,82.58 555.27,91.24 544.59,91.24C533.91,91.24 525.26,82.58 525.26,71.9C525.26,61.23 533.91,52.57 544.59,52.57C555.27,52.57 563.92,61.23 563.92,71.9Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M571.71,118.2C571.71,122.39 568.32,125.78 564.13,125.78C559.94,125.78 556.55,122.39 556.55,118.2"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M567.72,162.58C563.53,162.58 560.13,159.19 560.13,155"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M600.27,162.58C596.08,162.58 592.69,159.19 592.69,155"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M592.81,118.2C592.81,122.39 589.41,125.78 585.23,125.78C581.04,125.78 577.64,122.39 577.64,118.2"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M578.36,101.97C583.26,99.29 589.41,101.08 592.09,105.99"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="5"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M557.85,104.37C559.42,99 565.04,95.93 570.41,97.49"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="5"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M592.69,151.47C592.69,158.18 587.25,163.62 580.53,163.62C573.82,163.62 568.38,158.18 568.38,151.47V139.56"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M608.6,183.12C619.8,183.12 628.87,174.04 628.87,162.85"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M741.81,526.67H798.6L806.37,551.81L745.47,535.37L741.81,526.67Z"
      android:strokeAlpha="0.9"
      android:fillColor="#A2A2A2"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M705,644.45L702.69,645.8C638.61,683.41 559.81,684.64 494.8,649.74L494.84,650.28H494.84C593.67,716.78 703.17,656.59 703.17,656.59L705,644.45Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M786.23,683.31L784.93,658.88C784.78,656.02 782.42,653.78 779.56,653.78H774.31C769.84,653.78 767.32,658.92 770.06,662.46L786.23,683.31Z"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M585.06,205.68C550.4,202.04 523.99,174.65 520.28,141.39"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M520.23,125.6C524.45,85.58 560.3,56.56 600.32,60.77C640.34,64.99 669.36,100.85 665.14,140.86C662.17,169.05 643.5,191.78 618.74,201.29"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M687.67,131.89C687.67,142.39 679.16,150.89 668.67,150.89C658.17,150.89 649.67,142.39 649.67,131.89C649.67,121.4 658.17,112.89 668.67,112.89C679.16,112.89 687.67,121.4 687.67,131.89Z"
      android:strokeAlpha="0.9"
      android:fillColor="#AEAEAE"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M663.72,137.4C663.72,131.94 668.15,127.51 673.61,127.51"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M649.67,131.89C649.67,121.4 658.17,112.89 668.67,112.89C679.16,112.89 687.67,121.4 687.67,131.89C687.67,142.39 679.16,150.89 668.67,150.89C664.81,150.89 661.22,149.74 658.22,147.77"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M658.33,112.78C658.33,117.43 654.56,121.2 649.92,121.2C645.27,121.2 641.5,117.43 641.5,112.78C641.5,108.13 645.27,104.37 649.92,104.37C654.56,104.37 658.33,108.13 658.33,112.78Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M677.53,102.93C677.53,108.37 673.12,112.78 667.68,112.78C662.24,112.78 657.83,108.37 657.83,102.93C657.83,97.5 662.24,93.09 667.68,93.09C673.12,93.09 677.53,97.5 677.53,102.93Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M670.67,90.2C670.67,93.19 668.24,95.62 665.25,95.62C662.26,95.62 659.83,93.19 659.83,90.2C659.83,87.21 662.26,84.78 665.25,84.78C668.24,84.78 670.67,87.21 670.67,90.2Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M596.52,1132.37V1069.4"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M599.22,1128.89L612.27,1178.73"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M573.44,726.57L585.77,734.97C590.53,738.2 596.78,738.16 601.49,734.88L613.39,726.57"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M593.42,738.08V885.12"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M714.52,529.2L698.23,446.02L718.71,494.33L714.52,529.2Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M693.98,449.23C697.49,447.25 699.66,443.53 699.66,439.5V431.56"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M734.11,508.67H799.64"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#A2A2A2"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M761.66,515.12H811.78"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M761.66,393.37L736.53,319.62C734.04,312.32 730.73,305.33 726.67,298.79L720.27,288.49"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M698.23,446.02L732.41,526.67H769.67H807.08L774.67,431.56"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M569,385.78H493.83V397.78L569,385.78Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M559.97,377.12H569V385.78H495.67V377.12H540.55"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M704.99,642.23L680.51,621.79L706.78,626.01L704.99,642.23Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M701.27,668.55L661.8,924.26"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M651.95,988.1L639.4,1069.4"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M538.36,988.1L561,1131.26"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M503.67,689.45L485.44,680.12"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M489.67,696.9H503.67"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M538.57,1151.62L527.67,1134.35"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M546.21,1143.98L535.31,1126.7"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M653.94,1151.62L664.84,1134.35"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M646.29,1143.98L657.19,1126.7"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M732.2,1052.12C714.1,1041.51 702.26,1021.56 703.01,999.17C700.57,1004.9 699.1,1011.16 698.82,1017.74C697.84,1040.91 711.83,1061.24 732.2,1069.4V1052.12Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C7C7C7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M703.01,999.17C700.57,1004.9 699.1,1011.15 698.82,1017.74C697.84,1040.91 711.83,1061.24 732.2,1069.4V1052.12"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M800.36,1159.7H798.63V1108.59L778.35,1086.37V1168.87C778.35,1174.11 782.6,1178.37 787.85,1178.37H788.49H798.63H811.69V1171.04C811.69,1164.78 806.61,1159.7 800.36,1159.7Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M915.79,1159.7H914.06V1086.37L893.78,1108.59V1168.87C893.78,1174.11 898.03,1178.37 903.28,1178.37H903.92H914.06H927.12V1171.04C927.12,1164.78 922.04,1159.7 915.79,1159.7Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M850.51,1131.26H795.06C755.29,1131.26 723.06,1099.02 723.06,1059.26C723.06,1052.88 728.23,1047.7 734.61,1047.7H922.51V1059.26C922.51,1099.02 890.27,1131.26 850.51,1131.26Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C7C7C7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M765.99,1047.7C764.1,1050.71 763,1054.26 763,1058.07C763,1068.91 771.79,1077.7 782.63,1077.7C793.46,1077.7 802.25,1068.91 802.25,1058.07C802.25,1057.95 802.23,1057.84 802.23,1057.72C802.72,1057.77 803.21,1057.8 803.72,1057.8C810.03,1057.8 815.34,1053.52 816.93,1047.7H765.99Z"
      android:strokeAlpha="0.9"
      android:fillColor="#ffffff"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M834.33,1054.12C834.33,1060.93 839.85,1066.45 846.67,1066.45C853.48,1066.45 859,1060.93 859,1054.12C859,1051.76 858.33,1049.57 857.19,1047.7H836.15C835,1049.57 834.33,1051.76 834.33,1054.12Z"
      android:strokeAlpha="0.9"
      android:fillColor="#ffffff"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M734.61,1047.7H922.51V1059.26C922.51,1068.85 920.63,1078 917.23,1086.37C909.2,1106.12 892.66,1121.49 872.16,1127.95"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M875.61,1159.7H873.89V1108.59H853.6V1118.23V1168.87C853.6,1174.11 857.86,1178.37 863.11,1178.37H863.74H873.89H886.94V1171.04C886.94,1164.78 881.87,1159.7 875.61,1159.7Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C7C7C7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M759.34,1159.7H757.61V1108.59L737.33,1086.37V1168.87C737.33,1174.11 741.58,1178.37 746.83,1178.37H747.47H757.61H770.67V1171.04C770.67,1164.78 765.59,1159.7 759.34,1159.7Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C7C7C7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M853.6,1122.38V1145.1"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M850.51,1131.26H795.06C787.39,1131.26 780,1130.06 773.07,1127.84C766.2,1125.64 759.79,1122.44 753.99,1118.41"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M737.33,1080.37V1135.56"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M917.19,1086.42C920.61,1078.03 922.51,1068.87 922.51,1059.26V1047.7H866.24C874.92,1068.38 894.15,1083.52 917.19,1086.42Z"
      android:strokeAlpha="0.9"
      android:fillColor="#969696"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M917.19,1086.42C920.61,1078.03 922.51,1068.87 922.51,1059.26V1047.7H866.24C874.92,1068.38 894.15,1083.52 917.19,1086.42Z"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M897.18,1023.45C887.26,1033.37 870.47,1030.31 864.69,1017.52L842.74,969.01L891.26,990.96C904.05,996.74 907.11,1013.52 897.18,1023.45Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C7C7C7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M874.11,992.01C878.37,992.01 882.3,990.65 885.53,988.36L854.44,974.3C855.48,984.25 863.89,992.01 874.11,992.01Z"
      android:strokeAlpha="0.9"
      android:fillColor="#ffffff"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M897.18,1023.45C887.26,1033.37 870.47,1030.31 864.69,1017.52L842.74,969.01L891.26,990.96C904.05,996.74 907.11,1013.52 897.18,1023.45Z"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M915.91,1013.79C906.12,1007.92 905.22,994.07 914.16,986.98L948.12,960.08L940.38,1002.7C938.34,1013.94 925.7,1019.67 915.91,1013.79Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M915.91,1013.79C906.12,1007.92 905.22,994.07 914.16,986.98L948.12,960.08L940.38,1002.7C938.34,1013.94 925.7,1019.67 915.91,1013.79Z"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M938.73,1004.98C938.73,991.39 927.71,980.37 914.12,980.37H913.73C889.73,980.37 870.27,999.83 870.27,1023.83C870.27,1055.06 895.59,1080.37 926.81,1080.37H928.73C971.04,1080.37 1005.5,1046.89 1007.12,1004.98H938.73Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C7C7C7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M918.87,1011.07C918.87,1014.46 916.12,1017.21 912.73,1017.21C909.34,1017.21 906.59,1014.46 906.59,1011.07"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M920.88,1056.59C915.57,1056.59 911.27,1052.29 911.27,1046.98"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M937.06,1011.07C937.06,1014.46 934.31,1017.21 930.92,1017.21C927.53,1017.21 924.79,1014.46 924.79,1011.07"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M987.46,1013.51C990.77,1019.49 995.59,1026.16 1001.2,1027.72C1003.68,1028.41 1006.27,1026.75 1006.94,1024.26C1008.44,1018.64 1009.49,1012.83 1010.01,1006.88C1010.25,1004.2 1008.11,1001.89 1005.42,1001.89L993.44,1002.59C988.03,1002.91 984.83,1008.76 987.46,1013.51Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M979.89,1026.34C979.89,1027.14 979.24,1027.78 978.44,1027.78C977.65,1027.78 977,1027.14 977,1026.34C977,1025.54 977.65,1024.89 978.44,1024.89C979.24,1024.89 979.89,1025.54 979.89,1026.34Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M989.33,1024.73C989.33,1025.25 988.91,1025.67 988.39,1025.67C987.87,1025.67 987.44,1025.25 987.44,1024.73C987.44,1024.21 987.87,1023.78 988.39,1023.78C988.91,1023.78 989.33,1024.21 989.33,1024.73Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M987.44,1033.89C987.44,1034.69 986.8,1035.34 986,1035.34C985.2,1035.34 984.55,1034.69 984.55,1033.89C984.55,1033.1 985.2,1032.45 986,1032.45C986.8,1032.45 987.44,1033.1 987.44,1033.89Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M900.32,1073.79C908.22,1077.99 917.24,1080.37 926.81,1080.37H928.73C942.97,1080.37 956.32,1076.58 967.83,1069.95"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M872.16,1011.12C870.93,1015.14 870.27,1019.41 870.27,1023.83C870.27,1036.58 874.49,1048.34 881.61,1057.8"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M929.13,985.47C924.97,982.27 919.77,980.37 914.12,980.37H913.73C900.29,980.37 888.27,986.47 880.3,996.06"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M1007.12,1004.98H938.73C938.73,1001.84 938.14,998.83 937.07,996.06"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M875.55,1047.7H866.24C874.92,1068.38 894.15,1083.52 917.19,1086.42C918.04,1084.34 918.79,1082.22 919.45,1080.05"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M394.13,1124.24H369.83V666.62H390.17C448.12,666.62 483.82,750.88 470.02,808.34L394.13,1124.24Z"
      android:strokeAlpha="0.9"
      android:fillColor="#A2A2A2"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M346.89,1124.24H371.22V666.62H350.86C292.86,666.62 257.13,750.88 270.94,808.34L346.89,1124.24Z"
      android:strokeAlpha="0.9"
      android:fillColor="#A2A2A2"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M430.59,327.35C449.09,327.92 463.42,343.61 462.47,362.1C461.85,374.02 463.98,386.03 468.8,392.63L475.98,402.46C487.91,418.77 480.99,441.96 462.08,449.07C445.7,455.22 438.48,474.37 446.72,489.8L459.46,513.68C467.46,528.68 461.68,547.32 446.59,555.16L425.84,565.94L322.77,518.61L390.13,326.1L430.59,327.35Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C7C7C7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M346.94,295.87L322.33,314.45C305.4,321.23 293.84,345.73 302.5,361.78L298.17,378.23C306.8,394.22 298.74,414.11 281.42,419.58L269.81,423.25C250.54,429.34 242.39,452.13 253.43,469.06C262.98,483.71 257.47,503.42 241.7,510.98L217.3,522.68C201.97,530.04 195.6,548.48 203.12,563.73L213.46,584.7L324.56,607.56L384.2,412.53L346.94,295.87Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C7C7C7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M393.46,462.67H348.8V435.78H393.46V462.67Z"
      android:strokeAlpha="0.9"
      android:fillColor="#E7E7E7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M393.46,455.78L348.79,456.23V435.78H393.46V455.78Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C3C3C3"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M400.3,481.03V460.89H340.75V481.03C340.75,485.64 337.78,489.79 333.37,491.14C315.32,496.68 302.56,514.05 303.84,533.94L312.18,663.82C313.37,682.42 328.81,696.9 347.46,696.9H393.59C412.24,696.9 427.68,682.42 428.87,663.82L437.21,533.94C438.49,514.05 425.73,496.68 407.68,491.14C403.27,489.79 400.3,485.64 400.3,481.03Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M460.64,365.1C460.64,409.72 422.89,445.89 376.32,445.89C329.74,445.89 291.98,409.72 291.98,365.1C291.98,320.48 329.74,284.3 376.32,284.3C422.89,284.3 460.64,320.48 460.64,365.1Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C7C7C7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M456.64,401.47C456.64,406.44 452.61,410.47 447.64,410.47C442.67,410.47 438.64,406.44 438.64,401.47C438.64,396.5 442.67,392.47 447.64,392.47C452.61,392.47 456.64,396.5 456.64,401.47Z"
      android:strokeAlpha="0.9"
      android:fillColor="#E7E7E7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M456.64,401.47C456.64,406.44 452.61,410.47 447.64,410.47C442.67,410.47 438.64,406.44 438.64,401.47C438.64,396.5 442.67,392.47 447.64,392.47C452.61,392.47 456.64,396.5 456.64,401.47Z"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M447.64,386.01C447.64,422.64 417.94,452.34 381.31,452.34C344.67,452.34 314.98,422.64 314.98,386.01C314.98,349.37 344.67,319.67 381.31,319.67C417.94,319.67 447.64,349.37 447.64,386.01Z"
      android:strokeAlpha="0.9"
      android:fillColor="#E7E7E7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M324.23,419.6C318.35,409.7 314.97,398.13 314.97,385.78C314.97,349.15 344.67,319.45 381.31,319.45C417.94,319.45 447.64,349.15 447.64,385.78C447.64,389.73 447.3,393.6 446.64,397.35"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M364.37,449.93C351.6,446.57 340.34,439.5 331.81,429.94"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M445.36,403.1C437.74,431.33 411.95,452.12 381.31,452.12"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M398.27,545.12H360.61L357.04,513.28H402.11L398.27,545.12Z"
      android:strokeAlpha="0.9"
      android:fillColor="#ffffff"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M468.37,1178.73H405.84C401.27,1178.73 397.56,1175.02 397.56,1170.45V1166.29C397.56,1162.36 394.74,1158.83 390.84,1158.37C386.19,1157.82 381.93,1173.84 381.93,1178.37H369.29L370.57,1133.57L453.16,1158.4C462.18,1161.05 468.37,1169.33 468.37,1178.73Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C3C3C3"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M391.77,1144.28L394,1124.24H370.57L370.6,1133.93L375.14,1139.03C379.45,1143.16 385.4,1145.11 391.32,1144.34L391.77,1144.28Z"
      android:strokeAlpha="0.9"
      android:fillColor="#E7E7E7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M341.04,593.23L350.7,557.23L373.49,556.08L368.93,592.57L341.04,593.23Z"
      android:strokeAlpha="0.9"
      android:fillColor="#E7E7E7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M272.04,1178.73H334.58C339.15,1178.73 342.85,1175.02 342.85,1170.45V1166.29C342.85,1162.36 345.67,1158.83 349.58,1158.37C354.22,1157.82 358.48,1173.84 358.48,1178.37H371.12L370.6,1133.93L287.26,1158.4C278.24,1161.05 272.04,1169.33 272.04,1178.73Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C3C3C3"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M349.21,1144.28L347.04,1124.24H371.22L370.6,1133.93L365.65,1138.8C361.31,1143.07 355.24,1145.09 349.21,1144.28Z"
      android:strokeAlpha="0.9"
      android:fillColor="#E7E7E7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M422.93,931.95C422.93,935.44 420.1,938.28 416.6,938.28C413.1,938.28 410.27,935.44 410.27,931.95"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M331.56,931.95C331.56,935.44 328.73,938.28 325.23,938.28C321.73,938.28 318.9,935.44 318.9,931.95"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M303.75,385.27C303.75,390.83 304.37,396.26 305.51,401.48C315.23,406.03 326.08,408.58 337.53,408.58C379.31,408.58 413.18,374.71 413.18,332.93C413.18,327.36 412.56,321.94 411.42,316.72C401.7,312.17 390.85,309.61 379.4,309.61C337.62,309.61 303.75,343.48 303.75,385.27Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C7C7C7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M356.82,372.88C378.77,364.41 395.77,346.01 402.32,323.19"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M322.56,386.29C345.83,382.84 366.45,368.61 377.84,347.77"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M333.76,340.88C346.41,341.22 358.66,335.61 366.65,325.64"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M415.02,322.23C411.96,322.23 408.98,322.58 406.11,323.22C403.6,328.68 402.2,334.78 402.2,341.21C402.2,364.69 420.83,383.73 443.81,383.73C446.88,383.73 449.86,383.38 452.73,382.74C455.23,377.27 456.64,371.18 456.64,364.74C456.64,341.26 438.01,322.23 415.02,322.23Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C7C7C7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M406.88,401.45C406.88,405.81 403.35,409.34 398.99,409.34C394.63,409.34 391.1,405.81 391.1,401.45"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M427.97,401.67C427.97,406.03 424.44,409.56 420.08,409.56C415.72,409.56 412.19,406.03 412.19,401.67"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M414.22,388.3L420.08,390.26"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M401.18,390.65L406.71,387.9"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M387.42,434.9C378.29,434.9 370.9,427.5 370.9,418.38"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M406.78,415.7C408.93,415.7 410.67,417.44 410.67,419.59C410.67,421.74 408.93,423.48 406.78,423.48"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M365.07,413.02C365.07,409.2 368.17,406.1 372,406.1"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#C3C3C3"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M322.56,449.78L313.14,452.76C297.5,457.7 290.88,476.2 299.85,489.94C307.6,501.83 303.12,517.83 290.32,523.97L270.51,533.47C258.07,539.44 252.9,554.41 259,566.79"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M269.12,468.02C265.19,456.02 271.67,443.1 283.63,439.08L285.2,438.55C292.67,436.04 298.34,429.87 300.2,422.22"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M423.68,472.13C419.74,460.14 426.22,447.21 438.18,443.19L439.76,442.66C447.23,440.15 452.89,433.99 454.76,426.33"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M217.7,566.46C213.76,554.47 220.24,541.55 232.2,537.53L233.77,537C241.25,534.49 246.91,528.32 248.77,520.66"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M404.11,543.56H368.33C365.02,543.56 362.33,540.87 362.33,537.56C362.33,534.25 365.02,531.56 368.33,531.56H404.11C407.42,531.56 410.11,534.25 410.11,537.56C410.11,540.87 407.42,543.56 404.11,543.56Z"
      android:strokeAlpha="0.9"
      android:fillColor="#E7E7E7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M404.11,555.56H368.33C365.02,555.56 362.33,552.87 362.33,549.56C362.33,546.25 365.02,543.56 368.33,543.56H404.11C407.42,543.56 410.11,546.25 410.11,549.56C410.11,552.87 407.42,555.56 404.11,555.56Z"
      android:strokeAlpha="0.9"
      android:fillColor="#E7E7E7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M404.11,567.4H368.33C365.02,567.4 362.33,564.72 362.33,561.4C362.33,558.09 365.02,555.4 368.33,555.4H404.11C407.42,555.4 410.11,558.09 410.11,561.4C410.11,564.72 407.42,567.4 404.11,567.4Z"
      android:strokeAlpha="0.9"
      android:fillColor="#E7E7E7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M713.27,592.19L677.92,586.69C674.64,586.17 672.4,583.11 672.91,579.83C673.42,576.56 676.49,574.32 679.76,574.83L715.11,580.34C718.39,580.85 720.63,583.91 720.12,587.19C719.61,590.46 716.54,592.7 713.27,592.19Z"
      android:strokeAlpha="0.9"
      android:fillColor="#E7E7E7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M711.42,604.05L676.07,598.54C672.79,598.03 670.55,594.96 671.06,591.69C671.57,588.42 674.64,586.17 677.91,586.68L713.27,592.19C716.54,592.7 718.78,595.77 718.27,599.04V599.05C717.76,602.32 714.69,604.56 711.42,604.05Z"
      android:strokeAlpha="0.9"
      android:fillColor="#E7E7E7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M709.6,615.75L676.96,610.67C673.68,610.16 671.44,607.09 671.95,603.81C672.46,600.54 675.53,598.3 678.8,598.81L711.44,603.89C714.72,604.4 716.96,607.47 716.45,610.75C715.94,614.02 712.87,616.26 709.6,615.75Z"
      android:strokeAlpha="0.9"
      android:fillColor="#E7E7E7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M707.99,626.07L680.51,621.79C677.24,621.28 675,618.21 675.51,614.94C676.02,611.66 679.09,609.42 682.36,609.93L709.84,614.21C713.11,614.72 715.35,617.79 714.84,621.07C714.33,624.34 711.26,626.58 707.99,626.07Z"
      android:strokeAlpha="0.9"
      android:fillColor="#E7E7E7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M375.45,567.4L350.2,574.66V543.96C350.2,537.11 355.75,531.56 362.6,531.56H375.45V567.4Z"
      android:strokeAlpha="0.9"
      android:fillColor="#E7E7E7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M407.84,513.28H351.3L352.6,503.28H406.2L407.84,513.28Z"
      android:strokeAlpha="0.9"
      android:fillColor="#ffffff"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M399.55,503.28H359.59V501.31C359.59,499.36 361.17,497.78 363.12,497.78H396.03C397.98,497.78 399.55,499.36 399.55,501.31V503.28Z"
      android:strokeAlpha="0.9"
      android:fillColor="#AEAEAE"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M481.58,595.11L419.67,585.79V503.28L477.38,541.59L481.58,595.11Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M707.84,626.05C701.95,625.13 697.92,619.61 698.83,613.72L702.64,589.32C703.56,583.43 709.07,579.4 714.97,580.31C720.86,581.23 724.89,586.75 723.97,592.64L720.17,617.04C719.25,622.93 713.73,626.97 707.84,626.05Z"
      android:strokeAlpha="0.9"
      android:fillColor="#E7E7E7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M345.17,577.85L370.65,579.07L370.52,580.87L342.91,586.27L345.17,577.85Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C3C3C3"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M331.8,711.27C319.78,709.6 311.39,698.5 313.06,686.47L340.46,586.77L372.37,579.24L356.6,692.53C354.93,704.56 343.82,712.95 331.8,711.27Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M331.41,619.7L321.51,655.74"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M371.13,586.33L358.35,682.09"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M420.29,579.05L366.24,571.57C363.83,571.24 362.15,569.01 362.48,566.6C362.82,564.19 365.04,562.5 367.45,562.84L421.5,570.32C423.91,570.65 425.59,572.88 425.26,575.29C424.92,577.7 422.7,579.38 420.29,579.05Z"
      android:strokeAlpha="0.9"
      android:fillColor="#E7E7E7"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M386.22,555.4H404.11C407.42,555.4 410.11,558.09 410.11,561.4C410.11,564.72 407.42,567.4 404.11,567.4L400.14,567.36"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M385,543.56H399.55"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M435.09,566.98L432.44,608.23"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M349.44,462.45V445.12"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M393,463.01V451.09"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M305.51,401.48C315.23,406.03 326.08,408.59 337.53,408.59C379.31,408.59 413.18,374.71 413.18,332.93"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M407.44,361.88C414.55,374.91 428.17,383.73 443.81,383.73C446.88,383.73 449.86,383.38 452.73,382.74"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M445.36,330.96C450.88,341.19 453.99,352.79 453.99,365.1"
      android:strokeAlpha="0.300003"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.300003"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M428.08,309.61C431.48,312.52 434.6,315.71 437.42,319.14"
      android:strokeAlpha="0.300003"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.300003"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M299.1,365.1C299.1,338 314.3,314.3 336.98,301.42"
      android:strokeAlpha="0.300003"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.300003"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M418.36,355.97C425.32,366.54 436.93,373.38 449.68,374.23"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M357.04,513.28H402.11L401.67,516.9L358.57,526.92L357.04,513.28Z"
      android:strokeAlpha="0.9"
      android:fillColor="#F3D5CB"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M408.5,503.28H349.44"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M411.22,531.56H355.67"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M373.49,490.49C373.49,491.5 374.3,492.32 375.31,492.32C376.32,492.32 377.13,491.5 377.13,490.49C377.13,489.49 376.32,488.67 375.31,488.67C374.3,488.67 373.49,489.49 373.49,490.49Z"
      android:strokeAlpha="0.9"
      android:fillAlpha="0.9">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="488.672"
          android:startX="375.311"
          android:endY="492.316"
          android:endX="375.311"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M382.94,469.71C380.43,470.23 378.44,472.31 377.99,474.83C377.59,477.11 378.39,479.22 379.87,480.63C380.65,481.38 380.98,482.44 380.74,483.5C380.64,483.97 380.64,484.46 380.74,484.98C381.04,486.43 382.29,487.58 383.76,487.76C385.92,488.02 387.75,486.34 387.75,484.24C387.75,484.02 387.73,483.8 387.69,483.6C387.49,482.55 387.84,481.51 388.63,480.8C389.95,479.62 390.78,477.91 390.78,476.01C390.78,472.45 387.89,469.56 384.33,469.56C383.88,469.56 383.41,469.61 382.94,469.71Z"
      android:strokeAlpha="0.9"
      android:fillAlpha="0.9">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="469.56"
          android:startX="384.333"
          android:endY="487.783"
          android:endX="384.333"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M630.58,127.51C633.91,127.51 636.6,130.21 636.6,133.54"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#A2A2A2"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M524.17,133.54C524.17,130.21 526.87,127.51 530.19,127.51"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#A2A2A2"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M707.99,626.07L680.51,621.79C677.24,621.28 675,618.21 675.51,614.94C676.02,611.66 679.08,609.42 682.36,609.93"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M687.67,598.89L706.78,601.56"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M691.71,588.83L677.91,586.68C674.64,586.17 672.4,583.11 672.91,579.83C673.42,576.56 676.49,574.32 679.76,574.83L715.11,580.34"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M704.99,590.9L709.22,591.56"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M370.6,1177.01V1136.45"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M307.75,744.12C307.75,764.37 291.33,780.78 271.08,780.78"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M433.49,744.12C433.49,764.37 449.91,780.78 470.16,780.78"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M359.33,776.78L365.11,780.51C368.85,782.92 373.64,783 377.46,780.71L383.44,777.12"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M419.67,696.9L435.09,682.09"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M429.33,696.9L443.33,691.12"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M371.22,1124.24H342.67L339,1114.45H371.22V1124.24Z"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M370.6,1124.24H399.16L402.82,1114.45H370.6V1124.24Z"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M370.6,1055.39V1114.45H380.33L370.6,1055.39Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M217.3,522.68C201.97,530.03 195.6,548.48 203.11,563.73L213.46,584.71"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M281.42,419.58L269.8,423.25C250.54,429.34 242.39,452.13 253.43,469.06C262.98,483.71 257.47,503.42 241.7,510.98"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M475.98,402.46L468.8,392.63C465,387.43 462.97,376.1 463.74,364.89C464.19,358.3 461,347.77 458.44,341.01"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M446.71,489.8C438.48,474.37 445.7,455.22 462.07,449.07H462.08C473.56,444.75 480.63,434.5 481.73,423.46"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M446.59,555.16C461.67,547.32 467.46,528.68 459.45,513.68"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M275.58,597.48L279.38,568.89L287.09,599.85L275.58,597.48Z"
      android:strokeAlpha="0.9"
      android:fillColor="#161E24"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M371.13,953.78V782.37"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M385.58,895.43L370.87,873.06"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M379.12,900.88L371.22,884.78"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M370.6,1007.78V1034.29"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M334.69,1135.54C333.38,1138.63 331.22,1150.65 334.3,1151.96C337.39,1153.27 344.55,1143.37 345.86,1140.29C347.17,1137.21 345.73,1133.64 342.65,1132.33C339.56,1131.02 336,1132.46 334.69,1135.54Z"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M316.55,1151.02C319.64,1152.33 331.66,1154.48 332.97,1151.4C334.28,1148.31 324.38,1141.16 321.3,1139.84C318.21,1138.53 314.65,1139.97 313.34,1143.06C312.03,1146.14 313.47,1149.7 316.55,1151.02Z"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M405.65,1135.54C406.96,1138.63 409.12,1150.65 406.04,1151.96C402.95,1153.27 395.79,1143.37 394.48,1140.29C393.17,1137.21 394.61,1133.64 397.7,1132.33C400.78,1131.02 404.34,1132.46 405.65,1135.54Z"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M423.79,1151.02C420.7,1152.33 408.68,1154.48 407.37,1151.4C406.06,1148.31 415.96,1141.16 419.04,1139.84C422.13,1138.53 425.69,1139.97 427,1143.06C428.31,1146.14 426.87,1149.7 423.79,1151.02Z"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M329.31,614.24L307.85,616.68C307.25,616.75 306.66,616.75 306.08,616.7C302.04,616.34 298.68,613.18 298.2,609C297.66,604.21 301.1,599.89 305.88,599.35L327.34,596.91C332.13,596.37 336.45,599.81 336.99,604.6C337.53,609.38 334.09,613.7 329.31,614.24Z"
      android:strokeAlpha="0.9"
      android:fillColor="#AEAEAE"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M328.77,597.26L307.32,599.69C302.53,600.23 298.21,596.79 297.67,592.01C297.12,587.22 300.56,582.9 305.35,582.36L326.81,579.92C331.59,579.38 335.91,582.82 336.46,587.61V587.61C337,592.39 333.56,596.71 328.77,597.26Z"
      android:strokeAlpha="0.9"
      android:fillColor="#AEAEAE"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M326.92,580.88L305.46,583.31C300.67,583.85 296.35,580.41 295.81,575.63C295.27,570.84 298.71,566.52 303.49,565.98L324.95,563.54C329.73,563 334.05,566.44 334.6,571.23C335.14,576.01 331.7,580.33 326.92,580.88Z"
      android:strokeAlpha="0.9"
      android:fillColor="#AEAEAE"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M311.03,563.82L292.74,575.3C288.66,577.87 283.27,576.64 280.71,572.56C278.15,568.48 279.38,563.09 283.46,560.53L301.75,549.05C305.83,546.49 311.21,547.72 313.77,551.8C316.33,555.88 315.1,561.26 311.03,563.82Z"
      android:strokeAlpha="0.9"
      android:fillColor="#AEAEAE"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M330.88,560.23L309.59,563.9C304.85,564.71 300.33,561.53 299.52,556.78C298.7,552.03 301.89,547.52 306.63,546.71L327.91,543.04C332.66,542.22 337.17,545.4 337.99,550.15C338.81,554.9 335.62,559.41 330.88,560.23Z"
      android:strokeAlpha="0.9"
      android:fillColor="#AEAEAE"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M305.56,622.37C298.88,624.04 292.12,619.98 290.45,613.31L280.43,573.2C278.76,566.53 282.82,559.76 289.49,558.1C296.17,556.43 302.93,560.49 304.6,567.16L314.62,607.27C316.29,613.94 312.23,620.71 305.56,622.37Z"
      android:strokeAlpha="0.9"
      android:fillColor="#AEAEAE"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M314.76,564.7L324.95,563.54C329.74,563 334.06,566.44 334.6,571.23C335.14,576.01 331.7,580.33 326.92,580.88L305.46,583.31"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M336.45,587.61C337,592.39 333.56,596.71 328.77,597.26L308.75,599.02"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M740.27,1178.37H91.67"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M450.17,1178.37H846.95"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M867.51,1178.37H975.67"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M1001.73,1178.37H1034.62"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M964,1165.57L968.67,1177.88L972,1169.41"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.73,367.07C114.73,370.15 117.23,372.66 120.32,372.66C123.41,372.66 125.92,370.15 125.92,367.07C125.92,363.98 123.41,361.47 120.32,361.47C117.23,361.47 114.73,363.98 114.73,367.07Z"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M69.63,351.26C69.63,353.3 71.28,354.96 73.33,354.96C75.37,354.96 77.03,353.3 77.03,351.26C77.03,349.22 75.37,347.56 73.33,347.56C71.28,347.56 69.63,349.22 69.63,351.26Z"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M165.23,135C165.23,137.05 166.88,138.7 168.93,138.7C170.97,138.7 172.63,137.05 172.63,135C172.63,132.96 170.97,131.3 168.93,131.3C166.88,131.3 165.23,132.96 165.23,135Z"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M138.22,325.54H142.28V321.47C142.28,320.73 142.88,320.14 143.61,320.14C144.35,320.14 144.95,320.73 144.95,321.47V325.54H149.01C149.74,325.54 150.34,326.13 150.34,326.87C150.34,327.6 149.74,328.2 149.01,328.2H144.95V332.26C144.95,333 144.35,333.6 143.61,333.6C142.88,333.6 142.28,333 142.28,332.26V328.2H138.22C137.48,328.2 136.88,327.6 136.88,326.87C136.88,326.13 137.48,325.54 138.22,325.54Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C3C3C3"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M357.43,149.56H361.49V145.5C361.49,144.76 362.09,144.16 362.82,144.16C363.56,144.16 364.16,144.76 364.16,145.5V149.56H368.22C368.96,149.56 369.55,150.16 369.55,150.89C369.55,151.63 368.96,152.23 368.22,152.23H364.16V156.29C364.16,157.03 363.56,157.62 362.82,157.62C362.09,157.62 361.49,157.03 361.49,156.29V152.23H357.43C356.69,152.23 356.1,151.63 356.1,150.89C356.1,150.16 356.69,149.56 357.43,149.56Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C3C3C3"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M771.45,164.83H776.82V159.46C776.82,158.49 777.6,157.7 778.58,157.7C779.55,157.7 780.34,158.49 780.34,159.46V164.83H785.71C786.68,164.83 787.47,165.62 787.47,166.59C787.47,167.56 786.68,168.35 785.71,168.35H780.34V173.72C780.34,174.69 779.55,175.48 778.58,175.48C777.6,175.48 776.82,174.69 776.82,173.72V168.35H771.45C770.48,168.35 769.69,167.56 769.69,166.59C769.69,165.62 770.48,164.83 771.45,164.83Z"
      android:strokeAlpha="0.9"
      android:fillColor="#C3C3C3"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M801.34,126.29C801.34,128.35 803.01,130.02 805.07,130.02C807.13,130.02 808.8,128.35 808.8,126.29C808.8,124.23 807.13,122.56 805.07,122.56C803.01,122.56 801.34,124.23 801.34,126.29Z"
      android:strokeAlpha="0.9"
      android:fillColor="#2D353A"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M763.66,124.59C763.66,127.3 765.86,129.51 768.58,129.51C771.29,129.51 773.49,127.3 773.49,124.59C773.49,121.87 771.29,119.67 768.58,119.67C765.86,119.67 763.66,121.87 763.66,124.59Z"
      android:strokeAlpha="0.9"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#E4F4FC"
      android:fillAlpha="0.9"/>
  <path
      android:pathData="M964.3,465.38C964.3,467.42 965.96,469.08 968,469.08C970.04,469.08 971.7,467.42 971.7,465.38C971.7,463.33 970.04,461.68 968,461.68C965.96,461.68 964.3,463.33 964.3,465.38Z"
      android:strokeAlpha="0.9"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:fillAlpha="0.9"
      android:strokeLineCap="round"/>
</vector>
