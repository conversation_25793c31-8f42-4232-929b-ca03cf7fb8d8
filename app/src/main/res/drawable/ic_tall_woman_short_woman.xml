<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="1194dp"
    android:height="1194dp"
    android:viewportWidth="1194"
    android:viewportHeight="1194">
  <group>
    <clip-path android:pathData="M0,0h1194v1194h-1194z M 0,0"/>
    <path
        android:pathData="M0,0h1194v1194h-1194z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M597,597m-597,0a597,597 0,1 1,1194 0a597,597 0,1 1,-1194 0"
        android:fillColor="#F3F3F3"/>
    <path
        android:pathData="M674.5,676.93L618.06,703.28L581.3,560.99L522.09,331.87L550.79,329.79C570.21,328.41 587.82,341.44 592.82,360.72L674.5,676.93Z"
        android:fillColor="#A8A8A8"/>
    <path
        android:pathData="M581.58,565.15L581.86,566.4L609.18,447.82L563,494.55L581.58,565.15Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M562.3,553.77L551.9,556.27C506,567.09 457.73,560.43 416.4,537.55L384.37,377.22C378.68,348.65 388.53,319.25 410.3,299.83L462.45,253.51L521.39,278.89L607.1,423.68C612.51,438.25 609.88,454.61 600.3,466.82C590.18,479.44 582.41,493.86 577.28,509.12L562.3,553.77Z"
        android:fillColor="#969083"/>
    <path
        android:pathData="M562.3,553.77L551.9,556.27C506,567.09 457.73,560.43 416.4,537.55L384.37,377.22C378.68,348.65 388.53,319.25 410.3,299.83L462.45,253.51L521.39,278.89L568.82,358.92L607.1,423.55C612.51,438.11 609.88,454.47 600.3,466.68C590.18,479.3 582.41,493.72 577.28,508.98L562.3,553.77Z"
        android:fillColor="#A8A8A8"/>
    <path
        android:pathData="M547.47,365.85L466.33,309.96V199.15H521.53V278.89L547.47,365.85Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M499.76,1164.56C484.08,1163.45 468.97,1158.87 455.52,1150.97L435.4,1139.18H435.13L447.61,1092.44H393.66L366.75,1193.68H393.66H420.57H537.34C531.93,1177.32 516.95,1165.67 499.76,1164.56Z"
        android:fillColor="#838383"/>
    <path
        android:pathData="M596.01,1164.56C580.33,1163.45 565.22,1158.87 551.77,1150.97L531.66,1139.18H531.38L543.86,1092.44H489.91L463,1193.68H489.91H516.82H633.73C628.18,1177.32 613.34,1165.67 596.01,1164.56Z"
        android:fillColor="#838383"/>
    <path
        android:pathData="M379.24,746.97L413.91,889.68L382.29,1099.65H454.27L512.52,900.77C515.84,889.4 516.54,877.2 514.32,865.41L493.65,753.21L379.24,746.97Z"
        android:fillColor="#BDBDBD"/>
    <path
        android:pathData="M475.9,746.97L510.57,889.68L479.09,1099.51H551.07L609.32,900.64C612.65,889.26 613.34,877.06 611.12,865.27L590.32,753.21L475.9,746.97Z"
        android:fillColor="#BDBDBD"/>
    <path
        android:pathData="M590.32,753.21L475.9,746.83L477.29,752.24L379.24,746.83L397.68,822.41C464.81,840.86 536.23,837.11 601.14,811.6L590.32,753.21Z"
        android:fillColor="#949494"/>
    <path
        android:pathData="M445.53,1099.51H391.72L383.12,1131.83L445.53,1099.51Z"
        android:fillColor="#707070"/>
    <path
        android:pathData="M386.58,1070.67L382.29,1099.51H454.27L465.78,1058.05L386.58,1070.67Z"
        android:fillColor="#949494"/>
    <path
        android:pathData="M525,889.54L523.75,897.72"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M521.25,914.5L502.25,1040.29"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M501.01,1048.62L499.76,1056.94"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M610.01,899.39C600.86,908.54 585.88,908.54 576.73,899.39"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M393.52,746.97L395.46,755.01"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M399.21,770.27L428.33,889.54L405.45,1040.85"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M404.34,1048.62L403.09,1056.94"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M513.35,899.39C504.19,908.54 489.22,908.54 480.06,899.39"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M541.92,1099.51H487.97L479.37,1131.83L541.92,1099.51Z"
        android:fillColor="#707070"/>
    <path
        android:pathData="M515.57,1148.06C519.31,1145.7 535.95,1139.73 538.31,1143.48C540.67,1147.22 527.77,1159.43 524.03,1161.79C520.28,1164.14 515.29,1162.89 512.93,1159.15C510.57,1155.41 511.82,1150.41 515.57,1148.06Z"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M454.27,1099.51H369.11L374.1,1072.75H461.76L454.27,1099.51Z"
        android:fillColor="#EBEBEB"/>
    <path
        android:pathData="M419.18,1148.06C422.92,1145.7 439.57,1139.73 441.92,1143.48C444.28,1147.22 431.38,1159.43 427.64,1161.79C423.89,1164.14 418.9,1162.89 416.54,1159.15C414.32,1155.41 415.43,1150.41 419.18,1148.06Z"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M443.73,1168.86C442.06,1164.7 439.15,1147.22 443.31,1145.7C447.47,1144.03 457.18,1158.87 458.7,1163.03C460.37,1167.19 458.29,1171.91 454.13,1173.44C449.97,1174.96 445.39,1173.02 443.73,1168.86Z"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M539.98,1168.86C538.31,1164.7 535.4,1147.22 539.56,1145.7C543.72,1144.03 553.43,1158.87 554.95,1163.03C556.62,1167.19 554.54,1171.91 550.38,1173.44C546.35,1174.96 541.64,1173.02 539.98,1168.86Z"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M473.4,1034.47L512.65,900.77C515.98,889.4 516.68,877.2 514.46,865.41L507.52,827.68"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M483.67,1070.67L479.37,1099.51H551.35L562.86,1058.05L483.67,1070.67Z"
        android:fillColor="#949494"/>
    <path
        android:pathData="M550.93,1099.51H465.78L470.77,1072.75H558.42L550.93,1099.51Z"
        android:fillColor="#EBEBEB"/>
    <path
        android:pathData="M537.34,1193.68C531.93,1177.18 516.96,1165.67 499.76,1164.56C484.08,1163.45 468.97,1158.87 455.52,1150.97L435.41,1139.18"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M465.78,1099.51H541.92"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M376.32,1099.51H452.32"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M553.98,535.47C577.84,588.86 593.65,645.45 600.86,703.42L611.4,789.68C535.96,822.41 450.94,825.05 373.55,797.17L357.88,791.49L356.08,737.12C353.58,660.29 378.13,584.98 425.28,524.23L553.98,535.47Z"
        android:fillColor="#A8A8A8"/>
    <path
        android:pathData="M559.11,605.23C567.99,638.93 573.95,673.46 576.73,708.41L579.78,746.28"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M494.48,800.78C453.71,802.17 412.66,795.92 373.55,781.92"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M568.82,209.27C578.01,209.27 585.47,201.82 585.47,192.63C585.47,183.44 578.01,175.99 568.82,175.99C559.63,175.99 552.18,183.44 552.18,192.63C552.18,201.82 559.63,209.27 568.82,209.27Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M558.14,180.15C570.63,180.15 580.75,170.03 580.75,157.54C580.75,145.06 570.63,134.93 558.14,134.93C545.66,134.93 535.54,145.06 535.54,157.54C535.54,170.03 545.66,180.15 558.14,180.15Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M547.74,158.51C560.23,158.51 570.35,148.39 570.35,135.91C570.35,123.42 560.23,113.3 547.74,113.3C535.26,113.3 525.14,123.42 525.14,135.91C525.14,148.39 535.26,158.51 547.74,158.51Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M582.69,211.21C581.58,209.83 580.89,208.16 580.89,206.5C580.89,197.35 573.4,189.86 564.25,189.86C556.06,189.86 549.41,195.68 547.88,203.45C535.95,208.44 527.91,220.92 529.44,234.93C530.96,248.94 542.2,260.31 556.34,261.83C574.79,263.92 590.32,249.49 590.32,231.46C590.46,223.7 587.55,216.62 582.69,211.21Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M507.94,246.3C542.48,246.3 570.49,218.3 570.49,183.75C570.49,149.21 542.48,121.21 507.94,121.21C473.39,121.21 445.39,149.21 445.39,183.75C445.39,218.3 473.39,246.3 507.94,246.3Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M475.9,181.4C496.58,181.4 513.35,164.63 513.35,143.95C513.35,123.27 496.58,106.5 475.9,106.5C455.22,106.5 438.45,123.27 438.45,143.95C438.45,164.63 455.22,181.4 475.9,181.4Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M528.88,189.16C530.27,185.42 533.87,182.51 538.17,182.37"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M546.77,182.51C550.79,181.67 554.96,183.2 557.45,186.8"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M519.59,232.43C510.99,233.54 503.09,227.44 501.98,218.84"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M292.56,286.38C295.77,286.38 298.38,283.78 298.38,280.56C298.38,277.34 295.77,274.73 292.56,274.73C289.34,274.73 286.73,277.34 286.73,280.56C286.73,283.78 289.34,286.38 292.56,286.38Z"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M341.51,267.94C343.66,267.94 345.4,266.2 345.4,264.05C345.4,261.91 343.66,260.17 341.51,260.17C339.37,260.17 337.63,261.91 337.63,264.05C337.63,266.2 339.37,267.94 341.51,267.94Z"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M273.97,237.29H269.81V233.13C269.81,232.29 269.26,231.74 268.42,231.74C267.59,231.74 267.04,232.29 267.04,233.13V237.29H262.88C262.04,237.29 261.49,237.84 261.49,238.67C261.49,239.51 262.04,240.06 262.88,240.06H267.04V244.22C267.04,245.05 267.59,245.61 268.42,245.61C269.26,245.61 269.81,245.05 269.81,244.22V240.06H273.97C274.8,240.06 275.36,239.51 275.36,238.67C275.36,237.84 274.8,237.29 273.97,237.29Z"
        android:fillColor="#BDBDBD"/>
    <path
        android:pathData="M890.86,154.35C895.61,154.35 899.46,150.5 899.46,145.75C899.46,141 895.61,137.15 890.86,137.15C886.11,137.15 882.26,141 882.26,145.75C882.26,150.5 886.11,154.35 890.86,154.35Z"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#C1C1C1"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M928.3,228.83C930.37,228.83 932.05,227.15 932.05,225.08C932.05,223.01 930.37,221.34 928.3,221.34C926.23,221.34 924.56,223.01 924.56,225.08C924.56,227.15 926.23,228.83 928.3,228.83Z"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#C1C1C1"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M549.82,197.9C549.82,199.48 548.78,200.54 547.74,200.54C546.71,200.54 545.66,199.48 545.66,197.9C545.66,196.32 546.71,195.26 547.74,195.26C548.78,195.26 549.82,196.32 549.82,197.9Z"
        android:strokeWidth="1.38689"
        android:fillColor="#161E24"
        android:strokeColor="#161E24"/>
    <path
        android:pathData="M541.22,197.9C541.22,199.48 540.18,200.54 539.14,200.54C538.11,200.54 537.06,199.48 537.06,197.9C537.06,196.32 538.11,195.26 539.14,195.26C540.18,195.26 541.22,196.32 541.22,197.9Z"
        android:strokeWidth="1.38689"
        android:fillColor="#161E24"
        android:strokeColor="#161E24"/>
    <path
        android:pathData="M569.38,126.47C566.05,128 562.3,128.69 558.28,128.28C548.57,127.17 540.81,119.26 539.98,109.42C539.7,106.78 539.98,104.29 540.67,101.93C541.36,99.57 539.7,97.21 537.2,97.21C511.13,97.21 490.05,114.55 490.05,135.91C490.05,157.26 511.13,174.6 537.2,174.6C563.28,174.6 584.36,157.26 584.36,135.91V135.77C584.22,128.14 576.17,123.15 569.38,126.47Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M435.68,212.32C457.89,212.32 475.9,194.32 475.9,172.1C475.9,149.89 457.89,131.88 435.68,131.88C413.47,131.88 395.46,149.89 395.46,172.1C395.46,194.32 413.47,212.32 435.68,212.32Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M479.23,209.27C490.03,209.27 498.79,200.52 498.79,189.72C498.79,178.92 490.03,170.16 479.23,170.16C468.43,170.16 459.67,178.92 459.67,189.72C459.67,200.52 468.43,209.27 479.23,209.27Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M489.63,177.51C499.67,177.51 507.8,169.38 507.8,159.34C507.8,149.31 499.67,141.18 489.63,141.18C479.6,141.18 471.46,149.31 471.46,159.34C471.46,169.38 479.6,177.51 489.63,177.51Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M521.53,164.89C542.21,164.89 558.98,148.13 558.98,127.45C558.98,106.76 542.21,90 521.53,90C500.85,90 484.08,106.76 484.08,127.45C484.08,148.13 500.85,164.89 521.53,164.89Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M435.82,251.16C450.76,251.16 462.86,239.05 462.86,224.11C462.86,209.18 450.76,197.07 435.82,197.07C420.89,197.07 408.78,209.18 408.78,224.11C408.78,239.05 420.89,251.16 435.82,251.16Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M454.68,239.51C468.16,239.51 479.09,228.58 479.09,215.1C479.09,201.62 468.16,190.69 454.68,190.69C441.2,190.69 430.27,201.62 430.27,215.1C430.27,228.58 441.2,239.51 454.68,239.51Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M498.92,122.18C498.92,114.69 505.03,108.58 512.52,108.58C520.01,108.58 526.11,114.69 526.11,122.18C526.11,123.7 525.83,125.23 525.28,126.75"
        android:strokeLineJoin="round"
        android:strokeWidth="1.38689"
        android:fillColor="#00000000"
        android:strokeColor="#FFF4EE"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M472.71,180.01C470.49,183.62 465.78,184.86 462.17,182.64C458.57,180.43 457.32,175.71 459.54,172.1"
        android:strokeLineJoin="round"
        android:strokeWidth="1.38689"
        android:fillColor="#00000000"
        android:strokeColor="#FFF4EE"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M466.33,167.25C463.14,172.66 456.07,174.32 450.66,171.13C445.25,167.94 443.59,160.87 446.78,155.46C447.47,154.35 448.3,153.38 449.27,152.55"
        android:strokeLineJoin="round"
        android:strokeWidth="1.38689"
        android:fillColor="#00000000"
        android:strokeColor="#FFF4EE"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M531.38,116.21C537.2,116.21 542.2,119.96 544.14,125.09"
        android:strokeLineJoin="round"
        android:strokeWidth="1.38689"
        android:fillColor="#00000000"
        android:strokeColor="#FFF4EE"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M427.22,201.51C422.09,198.73 419.46,193.18 420.15,187.64"
        android:strokeLineJoin="round"
        android:strokeWidth="1.38689"
        android:fillColor="#00000000"
        android:strokeColor="#FFF4EE"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M524.86,423.55L533.6,439.49C539.01,453.92 536.37,470.14 526.8,482.21"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M551.9,556.27C506,567.09 457.73,560.43 416.4,537.55L416.27,536.72C413.08,541.29 409.89,546.01 406.83,550.72C453.3,576.24 507.38,583.59 558.98,571.39L567.85,569.31C565.91,564.18 563.83,558.91 561.75,553.78L551.9,556.27Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M473.27,578.74L457.18,575.69L453.99,548.5L470.08,551.56L473.27,578.74Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M457.04,575.69L473.27,578.74L486.03,554.61L469.94,551.55L457.04,575.69Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M466.33,246.3L547.47,365.85L559.95,423.55L516.54,371.26L435.82,346.71L460.51,300.39L423.62,278.89L466.33,246.3Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M463.56,656.4H451.49L457.46,573.33H469.52L463.56,656.4Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M475.49,617.99H487.55L481.45,573.33H469.52L475.49,617.99Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M466.33,246.3L547.47,365.85L559.95,423.55L518.9,365.85L445.25,340.33L469.52,299L423.62,278.89L466.33,246.3Z"
        android:fillColor="#A8A8A8"/>
    <path
        android:pathData="M345.26,1193.68H649.26"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M326.26,568.61C307.95,559.88 300.18,537.83 309.06,519.52L400.18,329.52L466.47,361.27L439.84,416.61L375.35,551.28C366.48,569.72 344.56,577.49 326.26,568.61Z"
        android:fillColor="#A8A8A8"/>
    <path
        android:pathData="M425.42,683.45C422.64,701.06 410.02,715.48 392.97,720.62L386.73,722.56L309.2,552.25C303.37,537.83 310.31,521.32 324.73,515.36L368.56,497.61L425.42,683.45Z"
        android:fillColor="#A8A8A8"/>
    <path
        android:pathData="M431.8,672.35C427.78,697.46 409.75,718.12 385.48,725.61L376.46,728.38"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M387.56,724.36L361.48,667.08C360.1,674.85 358.85,682.76 358.02,690.8L387.56,724.36Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M376.46,524.37L418.9,662.09"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M567.85,452.95L547.6,557.24"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M546.77,808.41V772.35H582.83C582.83,792.32 566.74,808.41 546.77,808.41Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M536.92,542.96C540.29,542.96 543.03,540.22 543.03,536.85C543.03,533.48 540.29,530.75 536.92,530.75C533.55,530.75 530.82,533.48 530.82,536.85C530.82,540.22 533.55,542.96 536.92,542.96Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M563,542.96C566.37,542.96 569.1,540.22 569.1,536.85C569.1,533.48 566.37,530.75 563,530.75C559.63,530.75 556.9,533.48 556.9,536.85C556.9,540.22 559.63,542.96 563,542.96Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M566.47,594.69C569.84,594.69 572.57,591.96 572.57,588.59C572.57,585.22 569.84,582.48 566.47,582.48C563.1,582.48 560.36,585.22 560.36,588.59C560.36,591.96 563.1,594.69 566.47,594.69Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M538.45,599.27C541.82,599.27 544.55,596.53 544.55,593.16C544.55,589.79 541.82,587.06 538.45,587.06C535.08,587.06 532.35,589.79 532.35,593.16C532.35,596.53 535.08,599.27 538.45,599.27Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M569.1,351.71L597.67,407.46"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M466.33,246.3L547.47,365.85L563,431.45"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M550.79,379.44L572.43,334.92L547.47,365.85L550.79,379.44Z"
        android:fillColor="#A8A8A8"/>
    <path
        android:pathData="M521.53,278.89L575.2,331.6L547.47,365.85L521.53,278.89Z"
        android:fillColor="#A8A8A8"/>
    <path
        android:pathData="M547.47,365.85L521.53,278.89"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M568.96,170.16C569.93,174.46 570.49,179.04 570.49,183.61C570.49,218.15 542.47,246.16 507.94,246.16"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M475.9,237.43C457.6,226.47 445.39,206.5 445.39,183.62"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M410.44,507.04L438.32,420.08L406.28,486.65L410.44,507.04Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M316.27,552.39C316.13,551.83 315.86,551.42 315.72,550.86C311.28,541.29 311.14,529.92 315.99,519.66L391.99,351.71L310.86,519.66C306.01,529.92 306.15,541.29 310.58,550.86C310.72,551.42 310.86,551.83 311.14,552.39L388.67,722.7L393.24,721.31L316.27,552.39Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M389.5,351.71L308.92,519.66C304.07,529.92 304.2,541.29 308.64,550.86C308.78,551.42 308.92,551.83 309.2,552.39L386.72,722.7"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M443.73,416.47L432.77,425.63"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M584.22,788.71V806.19"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M553.43,209.55C553.43,213.57 548.44,216.76 542.33,216.76C536.23,216.76 531.24,213.57 531.24,209.55"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M527.49,216.21C525,216.21 523.06,214.26 523.06,211.77C523.06,209.27 525,207.33 527.49,207.33"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M738.3,1155.27C746.35,1151.66 751.76,1143.76 752.45,1134.6L747.59,1030.03H699.05L711.67,1136.82C711.67,1151.25 725.68,1160.95 738.3,1155.27Z"
        android:fillColor="#969696"/>
    <path
        android:pathData="M781.57,1155.27C773.53,1151.66 768.12,1143.76 767.43,1134.6L772.28,1030.03H820.82L808.2,1136.82C808.2,1151.25 794.19,1160.95 781.57,1155.27Z"
        android:fillColor="#969696"/>
    <path
        android:pathData="M705.71,1086.62C718.05,1093.97 733.03,1094.52 745.65,1088C747.18,1087.17 748.7,1086.34 749.95,1085.23L747.46,1030.17H698.91L705.71,1086.62Z"
        android:fillColor="#767676"/>
    <path
        android:pathData="M769.78,1085.51C771.03,1086.48 772.28,1087.17 773.67,1087.87C786.57,1094.38 801.68,1093.83 814.17,1086.2L820.82,1030.03H772.28L769.78,1085.51Z"
        android:fillColor="#767676"/>
    <path
        android:pathData="M890.86,1193.68H796.69V1177.18C796.69,1171.91 792.95,1167.19 787.68,1166.64C781.43,1165.95 776.3,1170.66 776.3,1176.76V1193.68H759.8V1146.53C759.8,1140.29 765.76,1135.85 771.73,1137.52L870.61,1166.5C882.54,1170.25 890.86,1181.2 890.86,1193.68Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M629.16,1193.68H723.32V1177.18C723.32,1171.91 727.07,1167.19 732.34,1166.64C738.58,1165.95 743.71,1170.66 743.71,1176.76V1193.68H760.22V1146.53C760.22,1140.29 754.25,1135.85 748.29,1137.52L649.4,1166.5C637.34,1170.25 629.16,1181.2 629.16,1193.68Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M700.99,1171.63C701.69,1175.79 705.43,1178.57 709.59,1178.01C713.75,1177.46 716.53,1173.57 715.97,1169.41C715.28,1165.25 705.85,1152.91 705.85,1152.91C705.85,1152.91 700.3,1167.47 700.99,1171.63Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M686.71,1157.49C683.38,1159.98 682.69,1164.7 685.18,1168.03C687.68,1171.36 692.4,1172.05 695.72,1169.55C699.05,1167.06 705.43,1152.77 705.43,1152.77C705.43,1152.77 690.04,1154.99 686.71,1157.49Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M742.74,1104.65C738.86,1104.65 735.67,1107.84 735.67,1111.72"
        android:strokeAlpha="0.7"
        android:strokeLineJoin="round"
        android:strokeWidth="6.93443"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.7"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M730.67,1129.75C728.18,1126.84 723.6,1126.42 720.69,1129.06"
        android:strokeAlpha="0.7"
        android:strokeLineJoin="round"
        android:strokeWidth="6.93443"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.7"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M721.52,1098.96C720.97,1095.08 717.22,1092.44 713.34,1093"
        android:strokeAlpha="0.7"
        android:strokeLineJoin="round"
        android:strokeWidth="6.93443"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.7"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M819.02,1171.63C818.33,1175.79 814.58,1178.57 810.42,1178.01C806.26,1177.46 803.49,1173.57 804.04,1169.41C804.73,1165.25 814.16,1152.91 814.16,1152.91C814.16,1152.91 819.57,1167.47 819.02,1171.63Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M833.17,1157.49C836.49,1159.98 837.19,1164.7 834.69,1168.03C832.2,1171.36 827.48,1172.05 824.15,1169.55C820.82,1167.06 814.44,1152.77 814.44,1152.77C814.44,1152.77 829.84,1154.99 833.17,1157.49Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M777.55,1108.11C780.33,1105.34 784.76,1105.34 787.54,1108.11"
        android:strokeAlpha="0.7"
        android:strokeLineJoin="round"
        android:strokeWidth="6.93443"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.7"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M791.14,1125.45C795.03,1124.62 798.77,1126.98 799.6,1130.86"
        android:strokeAlpha="0.7"
        android:strokeLineJoin="round"
        android:strokeWidth="6.93443"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.7"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M794.89,1090.92C798.49,1089.25 802.65,1090.78 804.32,1094.24"
        android:strokeAlpha="0.7"
        android:strokeLineJoin="round"
        android:strokeWidth="6.93443"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.7"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M760.22,1156.1V1188.97"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M851.19,582.07L834,553.22L819.99,560.57C782.27,580.27 737.33,580.27 699.61,560.57L685.6,553.22L668.4,582.07C633.87,639.9 621.53,709.8 634.01,777.34L692.67,1056.38C693.78,1061.65 696.56,1066.37 700.72,1069.7C713.89,1080.38 731.23,1082.04 745.79,1074.55C749.67,1072.47 753.14,1069.56 756.05,1066.23L759.8,1061.79L763.54,1066.23C766.46,1069.56 769.78,1072.47 773.81,1074.55C788.23,1081.9 805.7,1080.24 818.88,1069.7C822.9,1066.37 825.68,1061.79 826.92,1056.38L885.73,777.34C898.07,709.94 885.73,639.9 851.19,582.07Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M699.47,560.57L685.46,553.22L668.26,582.07C666.04,585.95 663.83,589.7 661.74,593.72L647.6,673.6L672.7,722.56L717.08,568.06C711.12,565.98 705.16,563.48 699.47,560.57Z"
        android:strokeAlpha="0.5"
        android:fillColor="#161E24"
        android:fillAlpha="0.5"/>
    <path
        android:pathData="M664.8,722.56L617.23,724.36L649.68,418.14C654.12,388.46 677.84,365.3 707.79,361.83L732.62,358.92L664.8,722.56Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M664.8,722.56L617.23,724.36L649.68,418.14C654.12,388.46 677.84,365.3 707.79,361.83L732.62,358.92L664.8,722.56Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="358.918"
            android:startX="674.922"
            android:endY="724.362"
            android:endX="674.922"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M376.33,1186.33H521.12"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M560.92,1186.33H612.37"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M685.32,553.64L685.6,553.22L699.61,560.57C737.33,580.27 782.27,580.27 819.99,560.57L834,553.22L834.41,553.91L854.25,451.15C866.03,395.53 824.98,341.72 768.12,339.92C767.01,339.92 765.9,339.92 764.79,339.92H746.21C697.53,339.92 659.53,377.36 664.52,420.5L685.32,553.64Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M685.32,553.64L685.6,553.22L699.61,560.57C737.33,580.27 782.27,580.27 819.99,560.57L834,553.22L834.41,553.91L854.25,451.15C866.03,395.53 824.98,341.72 768.12,339.92C767.01,339.92 765.9,339.92 764.79,339.92H746.21C697.53,339.92 659.53,377.36 664.52,420.5L685.32,553.64Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="339.917"
            android:startX="760.176"
            android:endY="575.341"
            android:endX="760.176"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M684.49,630.19L672.7,463.63L699.47,560.57L684.49,630.19Z"
        android:fillColor="#E7E7E7"/>
    <path
        android:pathData="M415.57,890.09L406.28,903.69"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M413.63,902.3L403.37,924.49"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M718.47,249.91C716.67,225.22 699.19,206.64 679.36,208.16C659.67,209.83 645.1,231.05 646.91,255.73C647.18,260.03 648.02,264.19 649.27,268.08C655.23,287.22 637.89,305.8 619.86,299.14C619.31,299.01 618.75,298.73 618.2,298.45C617.78,298.31 617.51,298.17 617.09,297.89C621.53,302.89 627.07,307.05 633.45,309.68C652.18,317.73 672.84,312.04 685.74,297.2H685.88C705.85,295.82 720.27,274.6 718.47,249.91Z"
        android:fillColor="#969696"/>
    <path
        android:pathData="M748.98,262.11C747.18,237.43 729.7,218.84 709.87,220.37C690.18,222.03 675.61,243.25 677.42,267.94C677.7,272.24 678.53,276.4 679.78,280.28C685.74,299.42 668.4,318.01 650.37,311.35C649.82,311.21 649.26,310.93 648.71,310.65C648.29,310.52 648.02,310.38 647.6,310.1C652.04,315.09 657.59,319.25 663.97,321.89C682.69,329.93 703.35,324.25 716.25,309.41H716.39C736.22,308.02 750.78,286.66 748.98,262.11Z"
        android:fillColor="#969696"/>
    <path
        android:pathData="M709.87,220.37C708.9,220.51 707.93,220.65 707.1,220.78C702.66,215.51 697.25,211.63 691.29,209.69C669.93,219.95 655.23,241.87 655.23,267.11C655.23,292.49 670.07,314.54 691.7,324.66C701.13,322.58 709.87,317.31 716.53,309.68H716.67C736.36,308.02 750.92,286.8 749.12,262.11C747.04,237.43 729.57,218.7 709.87,220.37Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M773.67,365.85H721.66L711.26,293.46H763.27L773.67,365.85Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M718.47,249.91C716.67,225.22 699.19,206.64 679.36,208.16C659.67,209.83 645.1,231.05 646.91,255.73C647.18,260.03 648.02,264.19 649.27,268.08C655.23,287.22 637.89,305.8 619.86,299.14C619.31,299.01 618.75,298.73 618.2,298.45C617.78,298.31 617.51,298.17 617.09,297.89C621.53,302.89 627.07,307.05 633.45,309.68"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M666.6,310.93C661.75,313.15 656.2,313.57 650.51,311.49C649.96,311.35 649.4,311.07 648.85,310.79C648.43,310.65 648.16,310.52 647.74,310.24"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M664.1,322.17C682.83,330.21 703.49,324.52 716.39,309.68H716.53C736.22,308.02 750.78,286.8 748.98,262.11C747.18,237.43 729.71,218.84 709.87,220.37C690.18,222.03 675.62,243.25 677.42,267.94C677.7,272.24 678.53,276.4 679.78,280.28"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M762.71,208.02C764.1,199.7 771.45,193.6 779.91,193.6"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M762.71,202.2C768.26,195.82 768.26,186.25 762.85,179.73"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M718.89,321.06C753.43,321.06 781.44,293.05 781.44,258.51C781.44,223.96 753.43,195.96 718.89,195.96C684.34,195.96 656.34,223.96 656.34,258.51C656.34,293.05 684.34,321.06 718.89,321.06Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M671.32,251.29C671.32,255.32 674.51,258.51 678.53,258.51C682.55,258.51 685.74,255.32 685.74,251.29"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M698.36,253.65C698.36,257.68 701.55,260.87 705.57,260.87C709.59,260.87 712.78,257.68 712.78,253.65"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M744.54,283.47C745.79,279.73 743.85,275.57 739.97,274.32"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#BDBDBD"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M701.83,239.09C703.35,240.62 705.3,241.59 707.52,242"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M688.51,239.09C686.99,240.62 685.05,241.59 682.83,242"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M718.61,300.39C726.24,300.39 732.48,294.15 732.48,286.52"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M691.29,252.54L691.15,252.96C686.99,264.19 685.19,276.12 686.02,288.05C686.16,289.57 686.85,290.96 687.96,291.79C690.04,293.46 692.81,293.87 695.31,292.9L701.69,290.41"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M722.35,320.92C721.24,320.92 720,321.06 718.89,321.06C684.35,321.06 656.34,293.04 656.34,258.51C656.34,245.75 660.08,233.96 666.6,224.11"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M742.46,197.9C709.59,197.9 711.95,231.05 728.32,232.29C750.92,233.96 759.11,250.74 759.11,272.93C759.11,295.12 741.08,313.15 718.89,313.15C718.33,313.15 717.92,313.15 717.36,313.15C724.99,316.62 733.45,318.56 742.46,318.56C775.75,318.56 802.79,291.52 802.79,258.23C802.79,224.95 775.75,197.9 742.46,197.9Z"
        android:fillColor="#969696"/>
    <path
        android:pathData="M749.95,198.46C776.58,204.7 796.55,228.97 796.55,257.81C796.55,290.41 771.17,317.03 739.41,318.7C740.38,318.7 741.49,318.84 742.46,318.84C775.75,318.84 802.79,291.79 802.79,258.51C802.79,227.58 779.77,202.06 749.95,198.46Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M728.32,232.29C750.92,233.96 759.11,250.74 759.11,272.93C759.11,295.12 741.08,313.15 718.89,313.15C718.33,313.15 717.92,313.15 717.36,313.15C724.99,316.62 733.45,318.56 742.46,318.56C775.75,318.56 802.79,291.52 802.79,258.23C802.79,229.1 782.13,204.7 754.67,199.15"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M747.46,318.7C773.39,311.9 792.67,288.33 792.67,260.31C792.67,240.62 783.24,223.14 768.54,212.05"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M764.93,293.6C772.14,283.61 776.44,271.41 776.44,258.23C776.44,246.58 773.11,235.62 767.43,226.47"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M715.97,191.24C685.88,192.77 660.22,169.61 658.7,139.51C658.7,138.82 658.7,138.13 658.7,137.57C648.29,148.53 642.33,163.78 643.86,180.29C646.35,207.61 669.24,229.24 696.7,229.94C707.65,230.21 717.92,227.3 726.51,222.17C741.35,213.16 734,190.41 716.67,191.38C716.25,191.24 716.11,191.24 715.97,191.24Z"
        android:fillColor="#969696"/>
    <path
        android:pathData="M716.39,191.24C716.25,191.24 716.11,191.24 715.97,191.24C685.88,192.77 660.22,169.61 658.7,139.51C658.7,138.82 658.7,138.13 658.7,137.57C658.42,137.85 658.14,138.13 658,138.4C658,138.68 657.86,138.96 657.86,139.37C653.43,169.19 674.09,196.93 703.91,201.37C704.05,201.37 704.19,201.37 704.33,201.37C717.5,203.17 722.22,217.46 716.39,226.75C719.86,225.5 723.33,223.97 726.38,222.03C741.08,213.02 733.73,190.27 716.39,191.24Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M658.56,137.57C648.16,148.53 642.19,163.78 643.72,180.29C646.21,207.61 669.1,229.24 696.56,229.94"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M653.84,177.93C655.64,198.32 668.82,215.38 686.71,223.14"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M735.39,175.16C728.18,174.46 721.38,176.4 715.84,180.01C709.46,184.31 700.58,182.37 696.42,175.99C693.37,171.27 691.57,165.86 691.29,159.9C691.29,159.48 691.29,159.07 691.29,158.65C684.77,165.45 681.3,175.02 682.41,185.14C683.8,197.35 692.12,207.47 703.08,211.77C706.13,225.22 718.19,235.21 732.48,235.21C749.82,235.21 763.68,220.65 762.57,203.03C761.6,188.47 749.95,176.54 735.39,175.16Z"
        android:fillColor="#969696"/>
    <path
        android:pathData="M705.57,218.84C710.57,228.69 720.69,235.35 732.48,235.35C739,235.35 744.96,233.27 749.81,229.8"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M691.29,158.79C684.77,165.59 681.3,175.15 682.41,185.28C683.1,190.97 685.33,196.24 688.51,200.67"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M692.26,176.13C690.46,185.28 692.54,195.26 699.05,203.31C702.66,207.75 707.38,211.08 712.51,213.02"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M710.29,191.24C708.49,200.4 710.57,210.38 717.08,218.43C720.69,222.87 725.41,226.19 730.54,228.13"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M762.57,203.17C761.6,188.47 749.95,176.54 735.39,175.16"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M759.8,797.17V1045.98"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M779.35,678.18L759.8,697.73L740.1,678.18"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M759.8,765.83V697.73"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M833.44,898.28C828.31,907.02 817.08,909.93 808.34,904.8"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M689.76,898.28C694.89,907.02 706.13,909.93 714.86,904.8"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M770.34,589.56V597.88"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M770.34,615.77V642.54"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M770.34,651.55V659.87"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M824.43,592.75L842.6,647.39C846.62,659.46 857.99,667.64 870.61,667.64H875.33"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M788.37,416.75H723.05L709.87,325.77H775.19L788.37,416.75Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M788.37,416.75H723.05L709.87,325.77H775.19L788.37,416.75Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="325.772"
            android:startX="749.12"
            android:endY="416.751"
            android:endX="749.12"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M928.45,711.6L941.07,700.51C950.08,692.46 956.04,681.51 957.71,669.58C958.4,664.86 962.84,661.54 967.55,662.23C972.27,662.92 975.6,667.36 974.91,672.08C972.69,688.16 964.64,702.87 952.58,713.54L939.96,724.64C938.57,725.89 936.77,726.58 934.96,726.72C932.33,727 929.55,726.03 927.61,723.81C924.42,720.2 924.84,714.79 928.45,711.6Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M988.5,699.54L950.77,722.56C946.61,725.06 941.34,723.81 938.85,719.65L938.43,718.95C935.93,714.79 937.18,709.52 941.34,707.03L979.07,684C983.23,681.51 988.5,682.76 990.99,686.92L991.41,687.61C993.91,691.63 992.66,697.04 988.5,699.54Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M941.62,706.75L978.65,684.14C982.95,681.51 988.64,682.9 991.13,687.19"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M992.1,717.15L949.8,729.91C945.23,731.3 940.37,728.66 938.98,724.09L938.71,723.25C937.32,718.68 939.96,713.82 944.53,712.43L986.83,699.68C991.41,698.29 996.26,700.92 997.65,705.5L997.93,706.33C999.32,710.91 996.82,715.76 992.1,717.15Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M949.25,733.24L938.57,739.76C931.22,744.2 921.65,741.98 917.21,734.63L916.38,733.38C911.94,726.03 914.16,716.46 921.51,712.02L932.19,705.5C939.54,701.06 949.11,703.28 953.55,710.63L954.38,711.88C958.96,719.23 956.6,728.8 949.25,733.24Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M979.62,701.9L947.86,712.3"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M942.31,712.99L929.28,658.76L897.93,675.68L919.84,728.24L942.31,712.99Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M946.61,759.45L920.4,735.18C917.77,732.82 917.63,728.66 920.12,726.17L920.54,725.75C922.9,723.11 927.06,722.98 929.55,725.47L955.77,749.88C958.4,752.24 958.54,756.4 956.04,758.9L955.63,759.31C953.27,761.81 949.25,761.95 946.61,759.45Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M935.38,730.74L947.03,741.56"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M908.89,574.58C907.92,571.25 908.06,567.65 909.17,564.32C909.44,563.48 909.58,562.51 909.58,561.54C909.58,558.35 907.92,555.44 905.42,553.78C902.93,551.97 901.12,549.34 900.29,546.29L853.28,392.48C849.11,378.75 833.03,371.4 817.49,376.12C801.96,380.83 792.81,395.95 796.97,409.68L815.41,461.13L906.39,714.93L940.79,678.73L908.89,574.58Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M908.89,574.58C907.92,571.25 908.06,567.65 909.17,564.32C909.44,563.48 909.58,562.51 909.58,561.54C909.58,558.35 907.92,555.44 905.42,553.78C902.93,551.97 901.12,549.34 900.29,546.29L853.28,392.48C849.11,378.75 833.03,371.4 817.49,376.12C801.96,380.83 792.81,395.95 796.97,409.68L815.41,461.13L906.39,714.93L940.79,678.73L908.89,574.58Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="374.688"
            android:startX="868.376"
            android:endY="714.932"
            android:endX="868.376"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M876.85,557.24L862.29,572.22L881.57,574.99"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#ECE5D4"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M723.74,465.29C714.87,465.29 707.52,458.77 706.41,450.17C706.27,451.01 706.27,451.7 706.27,452.53C706.27,461.69 713.62,469.04 722.77,469.04C730.26,469.04 736.64,464.04 738.58,457.11C735.53,461.96 729.98,465.29 723.74,465.29Z"
        android:fillColor="#E7E7E7"/>
    <path
        android:pathData="M798.22,466.12C807.09,466.12 814.44,459.61 815.55,451.01C815.69,451.84 815.69,452.53 815.69,453.36C815.69,462.52 808.34,469.87 799.19,469.87C791.7,469.87 785.32,464.88 783.38,457.94C786.57,462.8 791.98,466.12 798.22,466.12Z"
        android:fillColor="#E7E7E7"/>
    <path
        android:pathData="M839.27,528.12L852.3,460.44L848.28,553.22L839.27,528.12Z"
        android:fillColor="#E7E7E7"/>
    <path
        android:pathData="M850.92,463.63L843.29,507.04"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M670.76,460.44L686.16,516.47"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M718.89,332.98L721.24,349.21"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#EBEBEB"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M730.12,332.98L732.48,349.21"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#EBEBEB"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M741.35,332.98L743.71,349.21"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#EBEBEB"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M752.59,332.98L754.95,349.21"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#EBEBEB"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M763.82,332.98L766.18,349.21"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#EBEBEB"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M710.84,332.98L713.62,351.71"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M776.58,334.92L777.55,342.41"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M799.6,400.25C788.37,400.25 779.22,409.4 779.22,420.63H781.3C781.3,410.51 789.62,402.33 799.74,402.33C809.87,402.33 818.05,410.51 818.19,420.63H820.27C819.99,409.26 810.84,400.25 799.6,400.25Z"
        android:strokeAlpha="0.8"
        android:fillColor="#E4E4E4"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M815.97,420.49H818.05C818.05,410.37 809.73,402.19 799.6,402.19C789.48,402.19 781.3,410.37 781.16,420.49H783.24C783.24,411.48 790.59,404.27 799.6,404.27C808.62,404.27 815.97,411.48 815.97,420.49Z"
        android:strokeAlpha="0.8"
        android:fillColor="#C4C4C4"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M799.6,404.27C790.59,404.27 783.24,411.62 783.24,420.49H785.32C785.32,412.59 791.84,406.21 799.6,406.21C807.51,406.21 813.89,412.59 813.89,420.49H815.97C815.97,411.48 808.62,404.27 799.6,404.27Z"
        android:strokeAlpha="0.8"
        android:fillColor="#FCFCFC"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M799.6,406.35C791.7,406.35 785.32,412.73 785.32,420.63H787.4C787.4,413.84 792.95,408.43 799.74,408.43C806.54,408.43 811.95,413.84 812.09,420.63H814.17C813.89,412.59 807.51,406.35 799.6,406.35Z"
        android:strokeAlpha="0.8"
        android:fillColor="#8C8C8C"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M799.6,408.29C792.81,408.29 787.4,413.7 787.26,420.49H789.34C789.34,414.81 794.05,410.37 799.6,410.37C805.29,410.37 809.87,414.95 809.87,420.49H811.95C811.81,413.84 806.4,408.29 799.6,408.29Z"
        android:strokeAlpha="0.8"
        android:fillColor="#D6D6D6"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M799.6,410.37C793.92,410.37 789.34,414.95 789.34,420.49H791.42C791.42,415.92 795.16,412.31 799.74,412.31C804.32,412.31 807.92,415.92 808.06,420.49H810.14C809.87,414.81 805.29,410.37 799.6,410.37Z"
        android:strokeAlpha="0.8"
        android:fillColor="#777777"
        android:fillAlpha="0.8"/>
    <path
        android:pathData="M834,553.22L819.99,560.57C782.27,580.27 733.59,578.32 695.86,558.77"
        android:strokeLineJoin="round"
        android:strokeWidth="6.93443"
        android:fillColor="#00000000"
        android:strokeColor="#BDBDBD"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M937.88,694.41L935.38,684.42L911.94,709.38L937.88,694.41Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M928.72,678.18L896.13,709.38"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M795.86,588.59L791.14,577.21"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M804.32,580.68L799.6,574.99"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M726.93,587.06L729.15,578.74"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M586.72,582.07L618.06,703.28L674.51,676.93L630.54,507.04L586.72,582.07Z"
        android:fillColor="#A8A8A8"/>
    <path
        android:pathData="M618.75,739.76L616.26,737.82C608.77,731.71 607.52,720.76 613.62,713.27L622.78,701.76C629.85,692.88 642.89,691.49 651.76,698.57L618.75,739.76Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M683.66,763.89L654.67,775.4L626.1,704.11V703.97C626.66,699.4 629.57,695.38 633.87,693.43C646.07,688.03 661.19,690.94 674.09,696.49C673.53,704.95 674.64,713.41 677.7,721.31L689.07,751.54C690.87,756.54 688.51,761.95 683.66,763.89Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M667.99,783.86L653.7,757.37C651.34,752.93 653.01,747.24 657.45,744.89C661.89,742.53 667.57,744.19 669.93,748.63L684.08,775.12C686.43,779.56 684.77,785.25 680.33,787.6C675.89,789.96 670.34,788.3 667.99,783.86Z"
        android:fillColor="#A6A6A6"/>
    <path
        android:pathData="M612.37,681.09L615.56,693.43L671.87,675.82L612.37,681.09Z"
        android:fillColor="#969696"/>
    <path
        android:pathData="M623.61,479.99L659.25,617.99"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M675.2,712.71C674.09,707.44 673.81,702.03 674.09,696.62C672.01,695.79 669.93,694.96 667.71,694.27L627.91,697.18C625.96,698.43 624.16,699.95 622.64,701.76L613.9,712.71H675.2Z"
        android:fillColor="#9D9D9D"/>
    <path
        android:pathData="M685.05,696.21L615.15,710.63L610.16,694.13L678.25,673.6L685.05,696.21Z"
        android:fillColor="#A8A8A8"/>
    <path
        android:pathData="M610.29,694.13L678.39,673.6"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M686.57,773.18L648.85,796.2C644.69,798.7 639.42,797.45 636.92,793.29L636.51,792.6C634.01,788.44 635.26,783.17 639.42,780.67L677.14,757.65C681.3,755.15 686.57,756.4 689.07,760.56L689.48,761.25C691.98,765.41 690.59,770.68 686.57,773.18Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M678.8,757.78L641.08,780.81C636.92,783.3 631.65,782.06 629.15,777.9L628.74,777.2C626.24,773.04 627.49,767.77 631.65,765.28L669.37,742.25C673.53,739.76 678.8,741 681.3,745.16L681.72,745.86C684.21,750.02 682.96,755.29 678.8,757.78Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M669.51,742.53L631.79,765.55C627.63,768.05 622.36,766.8 619.86,762.64L619.45,761.95C616.95,757.79 618.2,752.52 622.36,750.02L660.08,727C664.24,724.5 669.51,725.75 672.01,729.91L672.43,730.6C674.92,734.63 673.53,740.03 669.51,742.53Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M660.36,727.69L622.64,750.71C618.47,753.21 613.2,751.96 610.71,747.8L610.29,747.11C607.79,742.95 609.04,737.68 613.2,735.18L650.93,712.16C655.09,709.66 660.36,710.91 662.85,715.07L663.27,715.76C665.77,719.79 664.52,725.19 660.36,727.69Z"
        android:fillColor="#D0D0D0"/>
    <path
        android:pathData="M689.48,761.25L689.07,760.56C687.27,757.51 683.94,756.12 680.61,756.4C683.24,753.63 683.94,749.33 681.72,745.86L681.3,745.16C679.08,741.56 674.64,740.17 670.9,741.56C673.95,738.78 674.64,734.21 672.42,730.6L672.01,729.91C670.07,726.58 666.18,725.19 662.58,726.03C664.94,723.25 665.35,719.09 663.41,715.9L662.99,715.21C660.5,711.05 655.09,709.8 651.07,712.3L613.34,735.32C609.18,737.81 607.93,743.22 610.43,747.24L610.85,747.94C612.79,751.27 616.67,752.65 620.28,751.82C617.92,754.6 617.5,758.76 619.45,761.95L619.86,762.64C622.08,766.25 626.52,767.63 630.26,766.25C627.21,769.02 626.52,773.6 628.74,777.2L629.15,777.9C630.96,780.95 634.29,782.33 637.61,782.06C634.98,784.83 634.29,789.13 636.51,792.6L636.92,793.29C639.42,797.45 644.83,798.7 648.85,796.2L686.57,773.18C690.59,770.68 691.98,765.41 689.48,761.25Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M644.27,737.54L662.44,725.89"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M662.02,746.97L670.76,741.56"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M660.78,768.88L680.47,756.4"
        android:strokeLineJoin="round"
        android:strokeWidth="2.77377"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M159,1193.68H262.88"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M197,1188.97C197,1185.09 167.18,1100.21 186.32,1094.38C205.46,1088.56 211.01,1188.97 211.01,1188.97C211.01,1188.97 213.92,1131.97 226.96,1129.75C239.99,1127.67 221.55,1178.98 221.55,1178.98"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M169.4,1152.91L183.96,1172.88"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M732.61,1194.1H1035.1"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M986.42,1128.64C993.63,1148.06 995.99,1190.63 995.99,1190.63C995.99,1190.63 998.21,1147.64 1008.05,1145.98C1017.9,1144.45 1004.03,1183.14 1004.03,1183.14"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M970.19,1178.71L981.15,1193.82"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M1001.12,1107.14C1000.56,1102.57 996.4,1099.38 991.82,1099.93C987.25,1100.49 984.06,1104.65 984.61,1109.22C984.75,1109.78 984.75,1109.78 984.61,1109.22C984.06,1104.65 979.9,1101.46 975.32,1102.01C970.74,1102.57 967.55,1106.73 968.11,1111.3C968.53,1114.77 973.79,1118.52 973.79,1118.52L986.97,1128.92L997.37,1115.74C997.23,1115.46 1001.53,1110.47 1001.12,1107.14Z"
        android:strokeLineJoin="round"
        android:strokeWidth="4.16066"
        android:fillColor="#00000000"
        android:strokeColor="#C1C1C1"
        android:strokeLineCap="round"/>
  </group>
</vector>
