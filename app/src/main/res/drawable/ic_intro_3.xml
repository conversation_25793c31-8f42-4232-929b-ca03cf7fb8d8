<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="286dp"
    android:height="282dp"
    android:viewportWidth="286"
    android:viewportHeight="282">
  <path
      android:pathData="M83.198,170.96C107.007,170.96 128.48,160.959 143.646,144.928C158.626,160.15 179.491,169.592 202.568,169.592C248.162,169.592 285.123,132.733 285.123,87.264C285.123,41.796 248.162,4.936 202.568,4.936C179.243,4.936 158.178,14.582 143.165,30.093C128.028,14.357 106.756,4.565 83.198,4.565C37.249,4.565 0,41.814 0,87.762C0,133.711 37.249,170.96 83.198,170.96Z"
      android:fillColor="#E3F3FF"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M17.617,250.992C15.857,250.992 14.449,250.517 13.393,249.568L13.905,248.56C14.459,249.029 15.025,249.365 15.601,249.568C16.187,249.771 16.865,249.872 17.633,249.872C18.539,249.872 19.238,249.696 19.729,249.344C20.219,248.992 20.465,248.496 20.465,247.856C20.465,247.483 20.347,247.179 20.113,246.944C19.878,246.709 19.569,246.523 19.185,246.384C18.801,246.245 18.283,246.101 17.633,245.952C16.758,245.749 16.038,245.536 15.473,245.312C14.918,245.088 14.465,244.768 14.113,244.352C13.771,243.936 13.601,243.392 13.601,242.72C13.601,242.08 13.766,241.515 14.097,241.024C14.438,240.523 14.918,240.139 15.537,239.872C16.166,239.595 16.891,239.456 17.713,239.456C18.491,239.456 19.217,239.579 19.889,239.824C20.571,240.069 21.147,240.421 21.617,240.88L21.121,241.888C20.587,241.429 20.043,241.099 19.489,240.896C18.945,240.683 18.353,240.576 17.713,240.576C16.838,240.576 16.15,240.763 15.649,241.136C15.158,241.499 14.913,242.011 14.913,242.672C14.913,243.237 15.131,243.664 15.569,243.952C16.006,244.24 16.667,244.485 17.553,244.688C18.523,244.923 19.286,245.141 19.841,245.344C20.395,245.536 20.854,245.829 21.217,246.224C21.59,246.619 21.777,247.147 21.777,247.808C21.777,248.448 21.606,249.008 21.265,249.488C20.934,249.968 20.454,250.341 19.825,250.608C19.195,250.864 18.459,250.992 17.617,250.992ZM27.636,242.88C29.502,242.88 30.436,243.909 30.436,245.968V250.864H29.14V246.048C29.14,245.323 28.996,244.795 28.708,244.464C28.43,244.133 27.982,243.968 27.364,243.968C26.649,243.968 26.078,244.187 25.652,244.624C25.225,245.061 25.012,245.653 25.012,246.4V250.864H23.716V239.584H25.012V244.32C25.268,243.851 25.62,243.493 26.068,243.248C26.526,243.003 27.049,242.88 27.636,242.88ZM36.041,250.976C35.305,250.976 34.655,250.811 34.089,250.48C33.535,250.149 33.103,249.68 32.793,249.072C32.495,248.453 32.345,247.739 32.345,246.928C32.345,246.117 32.495,245.408 32.793,244.8C33.103,244.181 33.535,243.707 34.089,243.376C34.655,243.045 35.305,242.88 36.041,242.88C36.777,242.88 37.428,243.045 37.993,243.376C38.559,243.707 38.991,244.181 39.289,244.8C39.599,245.408 39.753,246.117 39.753,246.928C39.753,247.739 39.599,248.453 39.289,249.072C38.991,249.68 38.559,250.149 37.993,250.48C37.428,250.811 36.777,250.976 36.041,250.976ZM36.041,249.904C36.809,249.904 37.396,249.653 37.801,249.152C38.217,248.64 38.425,247.899 38.425,246.928C38.425,245.979 38.217,245.248 37.801,244.736C37.385,244.213 36.799,243.952 36.041,243.952C35.284,243.952 34.697,244.213 34.281,244.736C33.865,245.248 33.657,245.979 33.657,246.928C33.657,247.888 33.86,248.624 34.265,249.136C34.681,249.648 35.273,249.904 36.041,249.904ZM46.1,244L45.252,244.08C44.452,244.155 43.871,244.405 43.508,244.832C43.156,245.259 42.98,245.787 42.98,246.416V250.864H41.684V243.072H42.948V244.432C43.386,243.525 44.244,243.019 45.524,242.912L45.988,242.88L46.1,244ZM51.374,249.936C51.609,249.936 51.843,249.92 52.078,249.888L52.014,250.912C51.769,250.955 51.502,250.976 51.214,250.976C50.275,250.976 49.566,250.731 49.086,250.24C48.617,249.749 48.382,249.067 48.382,248.192V244.096H46.862V243.072H48.382V240.704H49.678V243.072H51.886V244.096H49.678V248.096C49.678,249.323 50.243,249.936 51.374,249.936ZM59.916,247.168H54.22C54.262,248.075 54.507,248.763 54.956,249.232C55.403,249.691 56.038,249.92 56.86,249.92C57.766,249.92 58.598,249.611 59.355,248.992L59.787,249.936C59.446,250.256 59.009,250.512 58.475,250.704C57.942,250.885 57.398,250.976 56.843,250.976C55.627,250.976 54.667,250.619 53.964,249.904C53.27,249.189 52.924,248.203 52.924,246.944C52.924,246.144 53.078,245.44 53.388,244.832C53.697,244.213 54.129,243.733 54.683,243.392C55.249,243.051 55.889,242.88 56.604,242.88C57.638,242.88 58.449,243.221 59.035,243.904C59.622,244.576 59.916,245.504 59.916,246.688V247.168ZM56.619,243.888C55.937,243.888 55.388,244.101 54.972,244.528C54.566,244.955 54.326,245.557 54.251,246.336H58.779C58.758,245.547 58.561,244.944 58.188,244.528C57.814,244.101 57.292,243.888 56.619,243.888ZM66.241,244L65.393,244.08C64.593,244.155 64.012,244.405 63.649,244.832C63.297,245.259 63.121,245.787 63.121,246.416V250.864H61.825V243.072H63.089V244.432C63.526,243.525 64.385,243.019 65.665,242.912L66.129,242.88L66.241,244ZM78.151,243.072V250.864H76.887V249.536C76.641,250.005 76.295,250.363 75.847,250.608C75.409,250.853 74.913,250.976 74.359,250.976C73.431,250.976 72.732,250.72 72.263,250.208C71.793,249.685 71.559,248.917 71.559,247.904V243.072H72.855V247.872C72.855,248.565 72.993,249.077 73.271,249.408C73.548,249.728 73.985,249.888 74.583,249.888C75.265,249.888 75.815,249.669 76.231,249.232C76.647,248.784 76.855,248.197 76.855,247.472V243.072H78.151ZM83.199,250.976C81.887,250.976 80.847,250.64 80.079,249.968L80.511,249.008C80.927,249.339 81.349,249.579 81.775,249.728C82.213,249.867 82.703,249.936 83.247,249.936C83.823,249.936 84.261,249.84 84.559,249.648C84.869,249.445 85.023,249.157 85.023,248.784C85.023,248.475 84.922,248.229 84.719,248.048C84.517,247.867 84.181,247.723 83.711,247.616L82.399,247.312C81.738,247.163 81.231,246.907 80.879,246.544C80.527,246.181 80.351,245.733 80.351,245.2C80.351,244.507 80.623,243.947 81.167,243.52C81.711,243.093 82.437,242.88 83.343,242.88C83.887,242.88 84.405,242.971 84.895,243.152C85.386,243.323 85.797,243.573 86.127,243.904L85.695,244.848C84.981,244.24 84.197,243.936 83.343,243.936C82.799,243.936 82.373,244.043 82.063,244.256C81.765,244.459 81.615,244.747 81.615,245.12C81.615,245.429 81.706,245.68 81.887,245.872C82.079,246.053 82.383,246.192 82.799,246.288L84.127,246.608C84.853,246.779 85.391,247.04 85.743,247.392C86.095,247.733 86.271,248.181 86.271,248.736C86.271,249.419 85.994,249.963 85.439,250.368C84.895,250.773 84.149,250.976 83.199,250.976ZM94.775,247.168H89.079C89.121,248.075 89.367,248.763 89.815,249.232C90.263,249.691 90.897,249.92 91.719,249.92C92.626,249.92 93.457,249.611 94.215,248.992L94.647,249.936C94.305,250.256 93.868,250.512 93.335,250.704C92.801,250.885 92.257,250.976 91.703,250.976C90.487,250.976 89.527,250.619 88.823,249.904C88.13,249.189 87.783,248.203 87.783,246.944C87.783,246.144 87.938,245.44 88.247,244.832C88.556,244.213 88.988,243.733 89.543,243.392C90.108,243.051 90.748,242.88 91.463,242.88C92.497,242.88 93.308,243.221 93.895,243.904C94.481,244.576 94.775,245.504 94.775,246.688V247.168ZM91.479,243.888C90.796,243.888 90.247,244.101 89.831,244.528C89.425,244.955 89.186,245.557 89.111,246.336H93.639C93.618,245.547 93.42,244.944 93.047,244.528C92.674,244.101 92.151,243.888 91.479,243.888ZM101.1,244L100.252,244.08C99.452,244.155 98.871,244.405 98.508,244.832C98.156,245.259 97.98,245.787 97.98,246.416V250.864H96.684V243.072H97.948V244.432C98.386,243.525 99.244,243.019 100.524,242.912L100.988,242.88L101.1,244ZM104.887,250.976C103.575,250.976 102.535,250.64 101.767,249.968L102.199,249.008C102.615,249.339 103.036,249.579 103.463,249.728C103.9,249.867 104.391,249.936 104.935,249.936C105.511,249.936 105.948,249.84 106.247,249.648C106.556,249.445 106.711,249.157 106.711,248.784C106.711,248.475 106.61,248.229 106.407,248.048C106.204,247.867 105.868,247.723 105.399,247.616L104.087,247.312C103.426,247.163 102.919,246.907 102.567,246.544C102.215,246.181 102.039,245.733 102.039,245.2C102.039,244.507 102.311,243.947 102.855,243.52C103.399,243.093 104.124,242.88 105.031,242.88C105.575,242.88 106.092,242.971 106.583,243.152C107.074,243.323 107.484,243.573 107.815,243.904L107.383,244.848C106.668,244.24 105.884,243.936 105.031,243.936C104.487,243.936 104.06,244.043 103.751,244.256C103.452,244.459 103.303,244.747 103.303,245.12C103.303,245.429 103.394,245.68 103.575,245.872C103.767,246.053 104.071,246.192 104.487,246.288L105.815,246.608C106.54,246.779 107.079,247.04 107.431,247.392C107.783,247.733 107.959,248.181 107.959,248.736C107.959,249.419 107.682,249.963 107.127,250.368C106.583,250.773 105.836,250.976 104.887,250.976ZM114.059,239.584H115.355V250.864H114.059V239.584ZM121.01,250.976C120.274,250.976 119.623,250.811 119.058,250.48C118.503,250.149 118.071,249.68 117.762,249.072C117.463,248.453 117.314,247.739 117.314,246.928C117.314,246.117 117.463,245.408 117.762,244.8C118.071,244.181 118.503,243.707 119.058,243.376C119.623,243.045 120.274,242.88 121.01,242.88C121.746,242.88 122.397,243.045 122.962,243.376C123.527,243.707 123.959,244.181 124.258,244.8C124.567,245.408 124.722,246.117 124.722,246.928C124.722,247.739 124.567,248.453 124.258,249.072C123.959,249.68 123.527,250.149 122.962,250.48C122.397,250.811 121.746,250.976 121.01,250.976ZM121.01,249.904C121.778,249.904 122.365,249.653 122.77,249.152C123.186,248.64 123.394,247.899 123.394,246.928C123.394,245.979 123.186,245.248 122.77,244.736C122.354,244.213 121.767,243.952 121.01,243.952C120.253,243.952 119.666,244.213 119.25,244.736C118.834,245.248 118.626,245.979 118.626,246.928C118.626,247.888 118.829,248.624 119.234,249.136C119.65,249.648 120.242,249.904 121.01,249.904ZM129.885,250.976C129.149,250.976 128.498,250.811 127.933,250.48C127.378,250.149 126.946,249.68 126.637,249.072C126.338,248.453 126.189,247.739 126.189,246.928C126.189,246.117 126.338,245.408 126.637,244.8C126.946,244.181 127.378,243.707 127.933,243.376C128.498,243.045 129.149,242.88 129.885,242.88C130.621,242.88 131.272,243.045 131.837,243.376C132.402,243.707 132.834,244.181 133.133,244.8C133.442,245.408 133.597,246.117 133.597,246.928C133.597,247.739 133.442,248.453 133.133,249.072C132.834,249.68 132.402,250.149 131.837,250.48C131.272,250.811 130.621,250.976 129.885,250.976ZM129.885,249.904C130.653,249.904 131.24,249.653 131.645,249.152C132.061,248.64 132.269,247.899 132.269,246.928C132.269,245.979 132.061,245.248 131.645,244.736C131.229,244.213 130.642,243.952 129.885,243.952C129.128,243.952 128.541,244.213 128.125,244.736C127.709,245.248 127.501,245.979 127.501,246.928C127.501,247.888 127.704,248.624 128.109,249.136C128.525,249.648 129.117,249.904 129.885,249.904ZM140.792,250.864L136.824,247.136V250.864H135.528V239.584H136.824V246.608L140.456,243.088H142.104L138.232,246.816L142.472,250.864H140.792ZM143.528,243.072H144.824V250.864H143.528V243.072ZM144.968,239.696V241.152H143.4V239.696H144.968ZM151.167,242.88C153.034,242.88 153.967,243.909 153.967,245.968V250.864H152.671V246.048C152.671,245.323 152.527,244.795 152.239,244.464C151.962,244.133 151.514,243.968 150.895,243.968C150.18,243.968 149.61,244.187 149.183,244.624C148.756,245.061 148.543,245.653 148.543,246.4V250.864H147.247V243.072H148.527V244.352C148.783,243.872 149.14,243.509 149.599,243.264C150.058,243.008 150.58,242.88 151.167,242.88ZM163.317,243.072V250.336C163.317,251.499 163.018,252.373 162.421,252.96C161.823,253.557 160.938,253.856 159.765,253.856C158.399,253.856 157.253,253.504 156.325,252.8L156.757,251.84C157.279,252.181 157.77,252.427 158.229,252.576C158.687,252.725 159.199,252.8 159.765,252.8C160.511,252.8 161.077,252.597 161.461,252.192C161.845,251.797 162.037,251.216 162.037,250.448V249.072C161.813,249.573 161.466,249.957 160.997,250.224C160.538,250.491 159.994,250.624 159.365,250.624C158.682,250.624 158.074,250.464 157.541,250.144C157.018,249.824 156.607,249.371 156.309,248.784C156.021,248.197 155.877,247.52 155.877,246.752C155.877,245.984 156.021,245.307 156.309,244.72C156.607,244.133 157.018,243.68 157.541,243.36C158.074,243.04 158.682,242.88 159.365,242.88C159.983,242.88 160.522,243.013 160.981,243.28C161.439,243.547 161.786,243.925 162.021,244.416V243.072H163.317ZM159.621,249.552C160.367,249.552 160.954,249.307 161.381,248.816C161.807,248.315 162.021,247.627 162.021,246.752C162.021,245.888 161.807,245.211 161.381,244.72C160.965,244.219 160.378,243.968 159.621,243.968C158.863,243.968 158.266,244.219 157.829,244.72C157.402,245.211 157.189,245.888 157.189,246.752C157.189,247.627 157.402,248.315 157.829,248.816C158.266,249.307 158.863,249.552 159.621,249.552ZM173.265,249.936C173.499,249.936 173.734,249.92 173.969,249.888L173.905,250.912C173.659,250.955 173.393,250.976 173.105,250.976C172.166,250.976 171.457,250.731 170.977,250.24C170.507,249.749 170.273,249.067 170.273,248.192V244.096H168.753V243.072H170.273V240.704H171.569V243.072H173.777V244.096H171.569V248.096C171.569,249.323 172.134,249.936 173.265,249.936ZM178.51,250.976C177.774,250.976 177.123,250.811 176.558,250.48C176.003,250.149 175.571,249.68 175.262,249.072C174.963,248.453 174.814,247.739 174.814,246.928C174.814,246.117 174.963,245.408 175.262,244.8C175.571,244.181 176.003,243.707 176.558,243.376C177.123,243.045 177.774,242.88 178.51,242.88C179.246,242.88 179.897,243.045 180.462,243.376C181.027,243.707 181.459,244.181 181.758,244.8C182.067,245.408 182.222,246.117 182.222,246.928C182.222,247.739 182.067,248.453 181.758,249.072C181.459,249.68 181.027,250.149 180.462,250.48C179.897,250.811 179.246,250.976 178.51,250.976ZM178.51,249.904C179.278,249.904 179.865,249.653 180.27,249.152C180.686,248.64 180.894,247.899 180.894,246.928C180.894,245.979 180.686,245.248 180.27,244.736C179.854,244.213 179.267,243.952 178.51,243.952C177.753,243.952 177.166,244.213 176.75,244.736C176.334,245.248 176.126,245.979 176.126,246.928C176.126,247.888 176.329,248.624 176.734,249.136C177.15,249.648 177.742,249.904 178.51,249.904ZM195.19,239.584V250.864H193.894V249.44C193.659,249.931 193.313,250.309 192.854,250.576C192.406,250.843 191.878,250.976 191.27,250.976C190.587,250.976 189.985,250.805 189.462,250.464C188.939,250.123 188.534,249.648 188.246,249.04C187.958,248.421 187.814,247.707 187.814,246.896C187.814,246.096 187.958,245.392 188.246,244.784C188.534,244.176 188.939,243.707 189.462,243.376C189.985,243.045 190.587,242.88 191.27,242.88C191.878,242.88 192.406,243.013 192.854,243.28C193.313,243.547 193.659,243.925 193.894,244.416V239.584H195.19ZM191.526,249.904C192.283,249.904 192.865,249.648 193.27,249.136C193.686,248.613 193.894,247.877 193.894,246.928C193.894,245.979 193.686,245.248 193.27,244.736C192.865,244.224 192.283,243.968 191.526,243.968C190.769,243.968 190.177,244.224 189.75,244.736C189.334,245.248 189.126,245.968 189.126,246.896C189.126,247.845 189.334,248.587 189.75,249.12C190.177,249.643 190.769,249.904 191.526,249.904ZM200.759,242.88C201.751,242.88 202.487,243.136 202.967,243.648C203.457,244.149 203.703,244.917 203.703,245.952V250.864H202.439V249.536C202.236,249.995 201.927,250.352 201.511,250.608C201.095,250.853 200.615,250.976 200.071,250.976C199.569,250.976 199.105,250.875 198.679,250.672C198.263,250.469 197.932,250.187 197.687,249.824C197.441,249.461 197.319,249.061 197.319,248.624C197.319,248.037 197.468,247.584 197.767,247.264C198.065,246.933 198.556,246.699 199.239,246.56C199.921,246.411 200.871,246.336 202.087,246.336H202.439V245.808C202.439,245.157 202.305,244.688 202.039,244.4C201.772,244.101 201.34,243.952 200.743,243.952C199.847,243.952 198.956,244.229 198.071,244.784L197.639,243.84C198.044,243.552 198.529,243.323 199.095,243.152C199.671,242.971 200.225,242.88 200.759,242.88ZM200.247,249.952C200.887,249.952 201.409,249.739 201.815,249.312C202.231,248.875 202.439,248.315 202.439,247.632V247.168H202.151C201.212,247.168 200.497,247.211 200.007,247.296C199.516,247.371 199.164,247.504 198.951,247.696C198.748,247.877 198.647,248.149 198.647,248.512C198.647,248.928 198.796,249.275 199.095,249.552C199.404,249.819 199.788,249.952 200.247,249.952ZM209.499,249.936C209.734,249.936 209.968,249.92 210.203,249.888L210.139,250.912C209.894,250.955 209.627,250.976 209.339,250.976C208.4,250.976 207.691,250.731 207.211,250.24C206.742,249.749 206.507,249.067 206.507,248.192V244.096H204.987V243.072H206.507V240.704H207.803V243.072H210.011V244.096H207.803V248.096C207.803,249.323 208.368,249.936 209.499,249.936ZM218.041,247.168H212.345C212.387,248.075 212.633,248.763 213.081,249.232C213.529,249.691 214.163,249.92 214.985,249.92C215.891,249.92 216.723,249.611 217.481,248.992L217.913,249.936C217.571,250.256 217.134,250.512 216.601,250.704C216.067,250.885 215.523,250.976 214.969,250.976C213.753,250.976 212.793,250.619 212.089,249.904C211.395,249.189 211.049,248.203 211.049,246.944C211.049,246.144 211.203,245.44 211.513,244.832C211.822,244.213 212.254,243.733 212.809,243.392C213.374,243.051 214.014,242.88 214.729,242.88C215.763,242.88 216.574,243.221 217.161,243.904C217.747,244.576 218.041,245.504 218.041,246.688V247.168ZM214.745,243.888C214.062,243.888 213.513,244.101 213.097,244.528C212.691,244.955 212.451,245.557 212.377,246.336H216.905C216.883,245.547 216.686,244.944 216.313,244.528C215.939,244.101 215.417,243.888 214.745,243.888ZM220.074,246.064H224.554V247.136H220.074V246.064ZM233.479,243.072V250.864H232.215V249.536C231.969,250.005 231.623,250.363 231.175,250.608C230.737,250.853 230.241,250.976 229.687,250.976C228.759,250.976 228.06,250.72 227.591,250.208C227.121,249.685 226.887,248.917 226.887,247.904V243.072H228.183V247.872C228.183,248.565 228.321,249.077 228.599,249.408C228.876,249.728 229.313,249.888 229.911,249.888C230.593,249.888 231.143,249.669 231.559,249.232C231.975,248.784 232.183,248.197 232.183,247.472V243.072H233.479ZM239.808,242.88C240.49,242.88 241.093,243.051 241.616,243.392C242.138,243.733 242.544,244.213 242.832,244.832C243.12,245.44 243.264,246.149 243.264,246.96C243.264,247.771 243.12,248.48 242.832,249.088C242.544,249.685 242.138,250.149 241.616,250.48C241.104,250.811 240.501,250.976 239.808,250.976C239.2,250.976 238.666,250.843 238.208,250.576C237.76,250.309 237.418,249.931 237.184,249.44V253.744H235.888V243.072H237.184V244.416C237.418,243.925 237.76,243.547 238.208,243.28C238.666,243.013 239.2,242.88 239.808,242.88ZM239.552,249.904C240.309,249.904 240.896,249.648 241.312,249.136C241.728,248.624 241.936,247.899 241.936,246.96C241.936,246.021 241.728,245.291 241.312,244.768C240.896,244.235 240.309,243.968 239.552,243.968C238.794,243.968 238.208,244.224 237.792,244.736C237.386,245.248 237.184,245.979 237.184,246.928C237.184,247.877 237.386,248.613 237.792,249.136C238.208,249.648 238.794,249.904 239.552,249.904ZM252.621,250.976C251.853,250.976 251.186,250.811 250.621,250.48C250.056,250.149 249.618,249.685 249.309,249.088C249.01,248.48 248.861,247.771 248.861,246.96C248.861,246.149 249.016,245.44 249.325,244.832C249.645,244.213 250.088,243.733 250.653,243.392C251.229,243.051 251.901,242.88 252.669,242.88C253.192,242.88 253.704,242.976 254.205,243.168C254.706,243.349 255.112,243.595 255.421,243.904L254.989,244.864C254.253,244.267 253.506,243.968 252.749,243.968C251.949,243.968 251.325,244.229 250.877,244.752C250.429,245.275 250.205,246.011 250.205,246.96C250.205,247.899 250.429,248.624 250.877,249.136C251.325,249.648 251.949,249.904 252.749,249.904C253.506,249.904 254.253,249.605 254.989,249.008L255.421,249.968C255.09,250.277 254.674,250.523 254.173,250.704C253.672,250.885 253.154,250.976 252.621,250.976ZM260.149,242.88C261.141,242.88 261.877,243.136 262.357,243.648C262.848,244.149 263.093,244.917 263.093,245.952V250.864H261.829V249.536C261.627,249.995 261.317,250.352 260.901,250.608C260.485,250.853 260.005,250.976 259.461,250.976C258.96,250.976 258.496,250.875 258.069,250.672C257.653,250.469 257.323,250.187 257.077,249.824C256.832,249.461 256.709,249.061 256.709,248.624C256.709,248.037 256.859,247.584 257.157,247.264C257.456,246.933 257.947,246.699 258.629,246.56C259.312,246.411 260.261,246.336 261.477,246.336H261.829V245.808C261.829,245.157 261.696,244.688 261.429,244.4C261.163,244.101 260.731,243.952 260.133,243.952C259.237,243.952 258.347,244.229 257.461,244.784L257.029,243.84C257.435,243.552 257.92,243.323 258.485,243.152C259.061,242.971 259.616,242.88 260.149,242.88ZM259.637,249.952C260.277,249.952 260.8,249.739 261.205,249.312C261.621,248.875 261.829,248.315 261.829,247.632V247.168H261.541C260.603,247.168 259.888,247.211 259.397,247.296C258.907,247.371 258.555,247.504 258.341,247.696C258.139,247.877 258.037,248.149 258.037,248.512C258.037,248.928 258.187,249.275 258.485,249.552C258.795,249.819 259.179,249.952 259.637,249.952ZM269.386,242.88C271.252,242.88 272.186,243.909 272.186,245.968V250.864H270.89V246.048C270.89,245.323 270.746,244.795 270.458,244.464C270.18,244.133 269.732,243.968 269.114,243.968C268.399,243.968 267.828,244.187 267.402,244.624C266.975,245.061 266.762,245.653 266.762,246.4V250.864H265.466V243.072H266.746V244.352C267.002,243.872 267.359,243.509 267.818,243.264C268.276,243.008 268.799,242.88 269.386,242.88ZM88.098,274.784C88.098,275.744 87.863,276.464 87.394,276.944C86.924,277.424 86.199,277.696 85.218,277.76L84.498,277.808L84.402,276.8L85.122,276.752C86.242,276.677 86.802,276.08 86.802,274.96V267.072H88.098V274.784ZM88.242,263.696V265.152H86.674V263.696H88.242ZM93.752,274.976C93.016,274.976 92.366,274.811 91.8,274.48C91.246,274.149 90.814,273.68 90.504,273.072C90.206,272.453 90.056,271.739 90.056,270.928C90.056,270.117 90.206,269.408 90.504,268.8C90.814,268.181 91.246,267.707 91.8,267.376C92.366,267.045 93.016,266.88 93.752,266.88C94.488,266.88 95.139,267.045 95.704,267.376C96.27,267.707 96.702,268.181 97,268.8C97.31,269.408 97.464,270.117 97.464,270.928C97.464,271.739 97.31,272.453 97,273.072C96.702,273.68 96.27,274.149 95.704,274.48C95.139,274.811 94.488,274.976 93.752,274.976ZM93.752,273.904C94.52,273.904 95.107,273.653 95.512,273.152C95.928,272.64 96.136,271.899 96.136,270.928C96.136,269.979 95.928,269.248 95.512,268.736C95.096,268.213 94.51,267.952 93.752,267.952C92.995,267.952 92.408,268.213 91.992,268.736C91.576,269.248 91.368,269.979 91.368,270.928C91.368,271.888 91.571,272.624 91.976,273.136C92.392,273.648 92.984,273.904 93.752,273.904ZM99.395,267.072H100.691V274.864H99.395V267.072ZM100.835,263.696V265.152H99.267V263.696H100.835ZM107.034,266.88C108.901,266.88 109.834,267.909 109.834,269.968V274.864H108.538V270.048C108.538,269.323 108.394,268.795 108.106,268.464C107.829,268.133 107.381,267.968 106.762,267.968C106.047,267.968 105.477,268.187 105.05,268.624C104.623,269.061 104.41,269.653 104.41,270.4V274.864H103.114V267.072H104.394V268.352C104.65,267.872 105.007,267.509 105.466,267.264C105.925,267.008 106.447,266.88 107.034,266.88ZM119.501,266.88C120.493,266.88 121.229,267.136 121.709,267.648C122.199,268.149 122.445,268.917 122.445,269.952V274.864H121.181V273.536C120.978,273.995 120.669,274.352 120.253,274.608C119.837,274.853 119.357,274.976 118.813,274.976C118.311,274.976 117.847,274.875 117.421,274.672C117.005,274.469 116.674,274.187 116.429,273.824C116.183,273.461 116.061,273.061 116.061,272.624C116.061,272.037 116.21,271.584 116.509,271.264C116.807,270.933 117.298,270.699 117.981,270.56C118.663,270.411 119.613,270.336 120.829,270.336H121.181V269.808C121.181,269.157 121.047,268.688 120.781,268.4C120.514,268.101 120.082,267.952 119.485,267.952C118.589,267.952 117.698,268.229 116.813,268.784L116.381,267.84C116.786,267.552 117.271,267.323 117.837,267.152C118.413,266.971 118.967,266.88 119.501,266.88ZM118.989,273.952C119.629,273.952 120.151,273.739 120.557,273.312C120.973,272.875 121.181,272.315 121.181,271.632V271.168H120.893C119.954,271.168 119.239,271.211 118.749,271.296C118.258,271.371 117.906,271.504 117.693,271.696C117.49,271.877 117.389,272.149 117.389,272.512C117.389,272.928 117.538,273.275 117.837,273.552C118.146,273.819 118.53,273.952 118.989,273.952ZM127.457,274.976C126.145,274.976 125.105,274.64 124.337,273.968L124.769,273.008C125.185,273.339 125.607,273.579 126.033,273.728C126.471,273.867 126.961,273.936 127.505,273.936C128.081,273.936 128.519,273.84 128.817,273.648C129.127,273.445 129.281,273.157 129.281,272.784C129.281,272.475 129.18,272.229 128.977,272.048C128.775,271.867 128.439,271.723 127.969,271.616L126.657,271.312C125.996,271.163 125.489,270.907 125.137,270.544C124.785,270.181 124.609,269.733 124.609,269.2C124.609,268.507 124.881,267.947 125.425,267.52C125.969,267.093 126.695,266.88 127.601,266.88C128.145,266.88 128.663,266.971 129.153,267.152C129.644,267.323 130.055,267.573 130.385,267.904L129.953,268.848C129.239,268.24 128.455,267.936 127.601,267.936C127.057,267.936 126.631,268.043 126.321,268.256C126.023,268.459 125.873,268.747 125.873,269.12C125.873,269.429 125.964,269.68 126.145,269.872C126.337,270.053 126.641,270.192 127.057,270.288L128.385,270.608C129.111,270.779 129.649,271.04 130.001,271.392C130.353,271.733 130.529,272.181 130.529,272.736C130.529,273.419 130.252,273.963 129.697,274.368C129.153,274.773 128.407,274.976 127.457,274.976ZM138.07,265.328V266.96H136.438V265.808C136.438,265.36 136.512,264.949 136.662,264.576C136.811,264.203 137.062,263.84 137.414,263.488L138.022,263.968C137.606,264.427 137.371,264.88 137.318,265.328H138.07ZM140.726,265.328V266.96H139.094V265.808C139.094,265.36 139.168,264.949 139.318,264.576C139.467,264.203 139.718,263.84 140.07,263.488L140.678,263.968C140.262,264.427 140.027,264.88 139.974,265.328H140.726ZM151.953,268.992V274.208C151.505,274.443 150.918,274.635 150.193,274.784C149.478,274.923 148.753,274.992 148.017,274.992C146.897,274.992 145.931,274.763 145.121,274.304C144.31,273.845 143.686,273.184 143.249,272.32C142.822,271.456 142.609,270.421 142.609,269.216C142.609,268.021 142.822,266.992 143.249,266.128C143.675,265.264 144.289,264.603 145.089,264.144C145.889,263.685 146.833,263.456 147.921,263.456C148.71,263.456 149.435,263.579 150.097,263.824C150.758,264.059 151.323,264.411 151.793,264.88L151.297,265.888C150.753,265.419 150.214,265.083 149.681,264.88C149.158,264.677 148.571,264.576 147.921,264.576C146.651,264.576 145.675,264.976 144.993,265.776C144.31,266.576 143.969,267.723 143.969,269.216C143.969,270.741 144.315,271.904 145.009,272.704C145.702,273.493 146.715,273.888 148.049,273.888C149.019,273.888 149.921,273.744 150.753,273.456V270.016H147.953V268.992H151.953ZM160.971,267.072V274.864H159.707V273.536C159.462,274.005 159.115,274.363 158.667,274.608C158.23,274.853 157.734,274.976 157.179,274.976C156.251,274.976 155.552,274.72 155.083,274.208C154.614,273.685 154.379,272.917 154.379,271.904V267.072H155.675V271.872C155.675,272.565 155.814,273.077 156.091,273.408C156.368,273.728 156.806,273.888 157.403,273.888C158.086,273.888 158.635,273.669 159.051,273.232C159.467,272.784 159.675,272.197 159.675,271.472V267.072H160.971ZM169.908,271.168H164.212C164.254,272.075 164.5,272.763 164.948,273.232C165.396,273.691 166.03,273.92 166.852,273.92C167.758,273.92 168.59,273.611 169.348,272.992L169.78,273.936C169.438,274.256 169.001,274.512 168.468,274.704C167.934,274.885 167.39,274.976 166.836,274.976C165.62,274.976 164.66,274.619 163.956,273.904C163.262,273.189 162.916,272.203 162.916,270.944C162.916,270.144 163.07,269.44 163.38,268.832C163.689,268.213 164.121,267.733 164.676,267.392C165.241,267.051 165.881,266.88 166.596,266.88C167.63,266.88 168.441,267.221 169.028,267.904C169.614,268.576 169.908,269.504 169.908,270.688V271.168ZM166.612,267.888C165.929,267.888 165.38,268.101 164.964,268.528C164.558,268.955 164.318,269.557 164.244,270.336H168.772C168.75,269.547 168.553,268.944 168.18,268.528C167.806,268.101 167.284,267.888 166.612,267.888ZM174.457,274.976C173.145,274.976 172.105,274.64 171.337,273.968L171.769,273.008C172.185,273.339 172.607,273.579 173.033,273.728C173.471,273.867 173.961,273.936 174.505,273.936C175.081,273.936 175.519,273.84 175.817,273.648C176.127,273.445 176.281,273.157 176.281,272.784C176.281,272.475 176.18,272.229 175.977,272.048C175.775,271.867 175.439,271.723 174.969,271.616L173.657,271.312C172.996,271.163 172.489,270.907 172.137,270.544C171.785,270.181 171.609,269.733 171.609,269.2C171.609,268.507 171.881,267.947 172.425,267.52C172.969,267.093 173.695,266.88 174.601,266.88C175.145,266.88 175.663,266.971 176.153,267.152C176.644,267.323 177.055,267.573 177.385,267.904L176.953,268.848C176.239,268.24 175.455,267.936 174.601,267.936C174.057,267.936 173.631,268.043 173.321,268.256C173.023,268.459 172.873,268.747 172.873,269.12C172.873,269.429 172.964,269.68 173.145,269.872C173.337,270.053 173.641,270.192 174.057,270.288L175.385,270.608C176.111,270.779 176.649,271.04 177.001,271.392C177.353,271.733 177.529,272.181 177.529,272.736C177.529,273.419 177.252,273.963 176.697,274.368C176.153,274.773 175.407,274.976 174.457,274.976ZM182.851,273.936C183.085,273.936 183.32,273.92 183.555,273.888L183.491,274.912C183.245,274.955 182.979,274.976 182.691,274.976C181.752,274.976 181.043,274.731 180.563,274.24C180.093,273.749 179.859,273.067 179.859,272.192V268.096H178.339V267.072H179.859V264.704H181.155V267.072H183.363V268.096H181.155V272.096C181.155,273.323 181.72,273.936 182.851,273.936ZM187.504,274.976C186.192,274.976 185.152,274.64 184.384,273.968L184.816,273.008C185.232,273.339 185.653,273.579 186.08,273.728C186.517,273.867 187.008,273.936 187.552,273.936C188.128,273.936 188.565,273.84 188.864,273.648C189.173,273.445 189.328,273.157 189.328,272.784C189.328,272.475 189.227,272.229 189.024,272.048C188.821,271.867 188.485,271.723 188.016,271.616L186.704,271.312C186.043,271.163 185.536,270.907 185.184,270.544C184.832,270.181 184.656,269.733 184.656,269.2C184.656,268.507 184.928,267.947 185.472,267.52C186.016,267.093 186.741,266.88 187.648,266.88C188.192,266.88 188.709,266.971 189.2,267.152C189.691,267.323 190.101,267.573 190.432,267.904L190,268.848C189.285,268.24 188.501,267.936 187.648,267.936C187.104,267.936 186.677,268.043 186.368,268.256C186.069,268.459 185.92,268.747 185.92,269.12C185.92,269.429 186.011,269.68 186.192,269.872C186.384,270.053 186.688,270.192 187.104,270.288L188.432,270.608C189.157,270.779 189.696,271.04 190.048,271.392C190.4,271.733 190.576,272.181 190.576,272.736C190.576,273.419 190.299,273.963 189.744,274.368C189.2,274.773 188.453,274.976 187.504,274.976ZM193.913,273.232V274.864H192.281V273.232H193.913ZM196.726,263.584V264.736C196.726,265.184 196.651,265.595 196.502,265.968C196.363,266.341 196.118,266.704 195.766,267.056L195.158,266.576C195.574,266.117 195.809,265.664 195.862,265.216H195.094V263.584H196.726ZM199.382,263.584V264.736C199.382,265.184 199.307,265.595 199.158,265.968C199.019,266.341 198.774,266.704 198.422,267.056L197.814,266.576C198.23,266.117 198.465,265.664 198.518,265.216H197.75V263.584H199.382Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M54.264,214.056H48.36L47.154,216.864H44.328L50.16,204.174H52.41L58.242,216.864H55.47L54.264,214.056ZM53.346,211.932L51.312,207.18L49.278,211.932H53.346ZM64.632,207.81C65.688,207.81 66.474,208.122 66.99,208.746C67.506,209.358 67.764,210.288 67.764,211.536V216.864H65.046V211.662C65.046,211.062 64.932,210.624 64.704,210.348C64.488,210.072 64.146,209.934 63.678,209.934C63.114,209.934 62.658,210.114 62.31,210.474C61.974,210.834 61.806,211.314 61.806,211.914V216.864H59.088V208.026H61.734V209.286C62.046,208.806 62.448,208.44 62.94,208.188C63.444,207.936 64.008,207.81 64.632,207.81ZM78.739,204.174V216.864H76.039V215.55C75.775,216.006 75.397,216.366 74.905,216.63C74.425,216.882 73.879,217.008 73.267,217.008C72.511,217.008 71.833,216.816 71.233,216.432C70.645,216.036 70.183,215.49 69.847,214.794C69.523,214.086 69.361,213.276 69.361,212.364C69.361,211.452 69.523,210.654 69.847,209.97C70.183,209.274 70.645,208.74 71.233,208.368C71.821,207.996 72.499,207.81 73.267,207.81C73.867,207.81 74.407,207.936 74.887,208.188C75.379,208.44 75.757,208.794 76.021,209.25V204.174H78.739ZM74.059,214.938C74.695,214.938 75.181,214.722 75.517,214.29C75.865,213.846 76.039,213.216 76.039,212.4C76.039,211.584 75.865,210.96 75.517,210.528C75.181,210.096 74.695,209.88 74.059,209.88C73.423,209.88 72.931,210.096 72.583,210.528C72.235,210.948 72.061,211.56 72.061,212.364C72.061,213.18 72.235,213.816 72.583,214.272C72.931,214.716 73.423,214.938 74.059,214.938ZM89.009,206.46H85.031V204.174H95.795V206.46H91.817V216.864H89.009V206.46ZM102.601,207.81C103.657,207.81 104.443,208.122 104.959,208.746C105.475,209.358 105.733,210.288 105.733,211.536V216.864H103.015V211.662C103.015,211.062 102.901,210.624 102.673,210.348C102.457,210.072 102.115,209.934 101.647,209.934C101.083,209.934 100.627,210.114 100.279,210.474C99.943,210.834 99.775,211.314 99.775,211.914V216.864H97.057V204.174H99.775V209.196C100.087,208.74 100.483,208.398 100.963,208.17C101.455,207.93 102.001,207.81 102.601,207.81ZM112.01,217.008C111.074,217.008 110.252,216.822 109.544,216.45C108.836,216.066 108.29,215.532 107.906,214.848C107.522,214.152 107.33,213.336 107.33,212.4C107.33,211.464 107.522,210.654 107.906,209.97C108.29,209.274 108.836,208.74 109.544,208.368C110.252,207.996 111.074,207.81 112.01,207.81C112.946,207.81 113.768,207.996 114.476,208.368C115.184,208.74 115.73,209.274 116.114,209.97C116.498,210.654 116.69,211.464 116.69,212.4C116.69,213.336 116.498,214.152 116.114,214.848C115.73,215.532 115.184,216.066 114.476,216.45C113.768,216.822 112.946,217.008 112.01,217.008ZM112.01,214.938C113.33,214.938 113.99,214.092 113.99,212.4C113.99,211.548 113.816,210.912 113.468,210.492C113.132,210.072 112.646,209.862 112.01,209.862C110.69,209.862 110.03,210.708 110.03,212.4C110.03,214.092 110.69,214.938 112.01,214.938ZM121.746,217.008C120.93,217.008 120.18,216.918 119.496,216.738C118.824,216.546 118.242,216.276 117.75,215.928L118.47,214.056C118.95,214.38 119.478,214.632 120.054,214.812C120.63,214.992 121.212,215.082 121.8,215.082C122.22,215.082 122.544,215.016 122.772,214.884C123.012,214.74 123.132,214.548 123.132,214.308C123.132,214.092 123.048,213.924 122.88,213.804C122.724,213.684 122.424,213.576 121.98,213.48L120.54,213.156C119.688,212.964 119.058,212.67 118.65,212.274C118.254,211.866 118.056,211.314 118.056,210.618C118.056,210.066 118.212,209.58 118.524,209.16C118.848,208.74 119.298,208.41 119.874,208.17C120.462,207.93 121.128,207.81 121.872,207.81C122.508,207.81 123.126,207.906 123.726,208.098C124.326,208.29 124.866,208.56 125.346,208.908L124.626,210.708C123.69,210.06 122.766,209.736 121.854,209.736C121.434,209.736 121.104,209.808 120.864,209.952C120.624,210.096 120.504,210.3 120.504,210.564C120.504,210.756 120.576,210.906 120.72,211.014C120.864,211.122 121.116,211.224 121.476,211.32L122.97,211.662C123.87,211.866 124.524,212.178 124.932,212.598C125.352,213.006 125.562,213.564 125.562,214.272C125.562,215.124 125.22,215.796 124.536,216.288C123.864,216.768 122.934,217.008 121.746,217.008ZM135.071,212.958H129.311C129.395,213.666 129.623,214.182 129.995,214.506C130.379,214.818 130.919,214.974 131.615,214.974C132.071,214.974 132.521,214.902 132.965,214.758C133.421,214.602 133.835,214.386 134.207,214.11L134.927,215.928C134.495,216.264 133.967,216.528 133.343,216.72C132.731,216.912 132.113,217.008 131.489,217.008C130.001,217.008 128.825,216.6 127.961,215.784C127.109,214.956 126.683,213.834 126.683,212.418C126.683,211.518 126.869,210.72 127.241,210.024C127.613,209.328 128.129,208.788 128.789,208.404C129.449,208.008 130.199,207.81 131.039,207.81C132.275,207.81 133.253,208.212 133.973,209.016C134.705,209.82 135.071,210.912 135.071,212.292V212.958ZM131.111,209.682C130.595,209.682 130.181,209.85 129.869,210.186C129.569,210.51 129.383,210.984 129.311,211.608H132.785C132.749,210.972 132.587,210.492 132.299,210.168C132.023,209.844 131.627,209.682 131.111,209.682ZM146.402,217.026C145.442,217.026 144.518,216.9 143.63,216.648C142.742,216.384 142.004,216.024 141.416,215.568L142.316,213.57C143.54,214.422 144.914,214.848 146.438,214.848C147.23,214.848 147.836,214.722 148.256,214.47C148.688,214.206 148.904,213.846 148.904,213.39C148.904,212.982 148.706,212.664 148.31,212.436C147.926,212.208 147.236,211.986 146.24,211.77C145.124,211.542 144.23,211.266 143.558,210.942C142.898,210.618 142.412,210.222 142.1,209.754C141.8,209.274 141.65,208.686 141.65,207.99C141.65,207.222 141.86,206.538 142.28,205.938C142.712,205.338 143.312,204.87 144.08,204.534C144.848,204.186 145.736,204.012 146.744,204.012C147.644,204.012 148.508,204.144 149.336,204.408C150.176,204.672 150.842,205.026 151.334,205.47L150.434,207.468C149.27,206.616 148.046,206.19 146.762,206.19C146.03,206.19 145.454,206.334 145.034,206.622C144.614,206.898 144.404,207.282 144.404,207.774C144.404,208.062 144.482,208.302 144.638,208.494C144.806,208.674 145.076,208.836 145.448,208.98C145.832,209.124 146.372,209.274 147.068,209.43C148.7,209.79 149.87,210.252 150.578,210.816C151.298,211.38 151.658,212.16 151.658,213.156C151.658,214.356 151.196,215.304 150.272,216C149.348,216.684 148.058,217.026 146.402,217.026ZM161.421,212.958H155.661C155.745,213.666 155.973,214.182 156.345,214.506C156.729,214.818 157.269,214.974 157.965,214.974C158.421,214.974 158.871,214.902 159.315,214.758C159.771,214.602 160.185,214.386 160.557,214.11L161.277,215.928C160.845,216.264 160.317,216.528 159.693,216.72C159.081,216.912 158.463,217.008 157.839,217.008C156.351,217.008 155.175,216.6 154.311,215.784C153.459,214.956 153.033,213.834 153.033,212.418C153.033,211.518 153.219,210.72 153.591,210.024C153.963,209.328 154.479,208.788 155.139,208.404C155.799,208.008 156.549,207.81 157.389,207.81C158.625,207.81 159.603,208.212 160.323,209.016C161.055,209.82 161.421,210.912 161.421,212.292V212.958ZM157.461,209.682C156.945,209.682 156.531,209.85 156.219,210.186C155.919,210.51 155.733,210.984 155.661,211.608H159.135C159.099,210.972 158.937,210.492 158.649,210.168C158.373,209.844 157.977,209.682 157.461,209.682ZM171.019,212.958H165.259C165.343,213.666 165.571,214.182 165.943,214.506C166.327,214.818 166.867,214.974 167.563,214.974C168.019,214.974 168.469,214.902 168.913,214.758C169.369,214.602 169.783,214.386 170.155,214.11L170.875,215.928C170.443,216.264 169.915,216.528 169.291,216.72C168.679,216.912 168.061,217.008 167.437,217.008C165.949,217.008 164.773,216.6 163.909,215.784C163.057,214.956 162.631,213.834 162.631,212.418C162.631,211.518 162.817,210.72 163.189,210.024C163.561,209.328 164.077,208.788 164.737,208.404C165.397,208.008 166.147,207.81 166.987,207.81C168.223,207.81 169.201,208.212 169.921,209.016C170.653,209.82 171.019,210.912 171.019,212.292V212.958ZM167.059,209.682C166.543,209.682 166.129,209.85 165.817,210.186C165.517,210.51 165.331,210.984 165.259,211.608H168.733C168.697,210.972 168.535,210.492 168.247,210.168C167.971,209.844 167.575,209.682 167.059,209.682ZM178.762,216.864L175.378,212.922V216.864H172.66V204.174H175.378V211.806L178.636,208.044H181.876L178.168,212.256L182.092,216.864H178.762ZM182.697,208.026H185.415V216.864H182.697V208.026ZM185.523,203.778V206.316H182.607V203.778H185.523ZM193.04,207.81C194.096,207.81 194.882,208.122 195.398,208.746C195.914,209.358 196.172,210.288 196.172,211.536V216.864H193.454V211.662C193.454,211.062 193.34,210.624 193.112,210.348C192.896,210.072 192.554,209.934 192.086,209.934C191.522,209.934 191.066,210.114 190.718,210.474C190.382,210.834 190.214,211.314 190.214,211.914V216.864H187.496V208.026H190.142V209.286C190.454,208.806 190.856,208.44 191.348,208.188C191.852,207.936 192.416,207.81 193.04,207.81ZM207.219,208.026V215.91C207.219,217.338 206.811,218.418 205.995,219.15C205.179,219.882 203.973,220.248 202.377,220.248C201.585,220.248 200.823,220.152 200.091,219.96C199.371,219.768 198.735,219.498 198.183,219.15L198.903,217.278C200.043,217.902 201.177,218.214 202.305,218.214C203.793,218.214 204.537,217.512 204.537,216.108V215.136C204.285,215.592 203.907,215.952 203.403,216.216C202.899,216.48 202.335,216.612 201.711,216.612C200.943,216.612 200.259,216.432 199.659,216.072C199.071,215.712 198.609,215.202 198.273,214.542C197.937,213.87 197.769,213.096 197.769,212.22C197.769,211.344 197.937,210.576 198.273,209.916C198.609,209.244 199.071,208.728 199.659,208.368C200.259,207.996 200.943,207.81 201.711,207.81C202.323,207.81 202.875,207.942 203.367,208.206C203.871,208.458 204.255,208.812 204.519,209.268V208.026H207.219ZM202.503,214.542C203.139,214.542 203.631,214.338 203.979,213.93C204.339,213.522 204.519,212.952 204.519,212.22C204.519,211.488 204.339,210.918 203.979,210.51C203.631,210.09 203.139,209.88 202.503,209.88C201.867,209.88 201.369,210.09 201.009,210.51C200.649,210.918 200.469,211.488 200.469,212.22C200.469,212.94 200.649,213.51 201.009,213.93C201.369,214.338 201.867,214.542 202.503,214.542ZM217.488,206.46H213.51V204.174H224.274V206.46H220.296V216.864H217.488V206.46ZM228.018,207.81C229.338,207.81 230.31,208.122 230.934,208.746C231.57,209.37 231.888,210.336 231.888,211.644V216.864H229.314V215.55C229.134,216.006 228.834,216.366 228.414,216.63C227.994,216.882 227.502,217.008 226.938,217.008C226.338,217.008 225.792,216.888 225.3,216.648C224.82,216.408 224.436,216.072 224.148,215.64C223.872,215.208 223.734,214.728 223.734,214.2C223.734,213.552 223.896,213.042 224.22,212.67C224.556,212.298 225.09,212.028 225.822,211.86C226.554,211.692 227.556,211.608 228.828,211.608H229.296V211.284C229.296,210.756 229.182,210.384 228.954,210.168C228.726,209.952 228.33,209.844 227.766,209.844C227.334,209.844 226.854,209.922 226.326,210.078C225.798,210.234 225.294,210.45 224.814,210.726L224.094,208.908C224.598,208.596 225.216,208.338 225.948,208.134C226.692,207.918 227.382,207.81 228.018,207.81ZM227.532,215.136C228.06,215.136 228.486,214.962 228.81,214.614C229.134,214.254 229.296,213.792 229.296,213.228V212.922H228.99C228.018,212.922 227.334,213 226.938,213.156C226.554,213.312 226.362,213.594 226.362,214.002C226.362,214.326 226.47,214.596 226.686,214.812C226.914,215.028 227.196,215.136 227.532,215.136ZM233.92,204.174H236.638V216.864H233.92V204.174ZM238.719,204.174H241.437V216.864H238.719V204.174Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M109.568,99.791L110.875,102.221C114.134,108.279 114.948,115.356 113.15,121.996L112.039,126.098C110.489,131.823 111.801,137.945 115.561,142.532L122.47,150.961"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M68.82,67.915L52.321,79.479V60L68.82,44.391V67.915Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M87.84,161.798L82.733,161.453L83.085,143.406C83.117,141.725 83.391,140.056 83.898,138.448L84.613,136.183C83.369,132.905 82.733,129.44 82.733,125.946V94.375H98.247L97.059,102.074L87.84,161.798Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M118.489,101.269L112.949,100.708L98.641,66.704H107.817L118.489,101.269Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M113.343,98.917L117.676,98.634L118.374,100.897L113.343,98.917Z"
      android:fillColor="#B76452"/>
  <path
      android:pathData="M102.06,168.714H81.187L82.859,161.395L88.743,161.575L100.031,165.903C101.259,166.374 102.06,167.483 102.06,168.714Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M78.18,162.216L83.13,161.453L82.779,143.406C82.746,141.725 82.473,140.056 81.967,138.448L81.255,136.183C82.495,132.905 83.13,129.44 83.13,125.946V94.375H67.651L78.18,162.216Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M76.198,136.739C76.198,137.577 75.518,138.257 74.68,138.257C73.842,138.257 73.163,137.577 73.163,136.739C73.163,135.901 73.842,135.222 74.68,135.222C75.518,135.222 76.198,135.901 76.198,136.739Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M89.59,136.739C89.59,137.577 90.27,138.257 91.108,138.257C91.946,138.257 92.625,137.577 92.625,136.739C92.625,135.901 91.946,135.222 91.108,135.222C90.27,135.222 89.59,135.901 89.59,136.739Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M79.915,136.276L80.906,136.183"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M85.978,136.276L84.987,136.183"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M118.344,106.504L119.078,103.586C119.098,103.504 119.11,103.423 119.114,103.342C119.139,102.777 118.765,102.254 118.196,102.111C117.544,101.947 116.884,102.342 116.72,102.993L115.987,105.912C115.823,106.563 116.218,107.223 116.869,107.387C117.52,107.55 118.18,107.155 118.344,106.504Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M116.007,106.116L116.741,103.197C116.905,102.546 116.51,101.886 115.859,101.722C115.208,101.558 114.547,101.953 114.384,102.604L113.65,105.523C113.486,106.174 113.881,106.834 114.532,106.998C115.183,107.162 115.843,106.766 116.007,106.116Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M113.779,105.555L114.513,102.637C114.677,101.986 114.282,101.325 113.631,101.162C112.98,100.998 112.319,101.393 112.156,102.044L111.422,104.963C111.258,105.614 111.653,106.274 112.304,106.438C112.955,106.601 113.616,106.206 113.779,105.555Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M111.718,103.045L113.643,100.731C114.073,100.215 114.002,99.449 113.486,99.02C112.971,98.59 112.204,98.661 111.775,99.176L109.85,101.49C109.42,102.006 109.491,102.772 110.006,103.201C110.522,103.631 111.289,103.561 111.718,103.045Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M110.854,105.72L111.755,102.848C111.956,102.208 111.599,101.526 110.959,101.325C110.319,101.124 109.636,101.48 109.436,102.12L108.535,104.992C108.334,105.633 108.69,106.315 109.33,106.515C109.971,106.716 110.653,106.36 110.854,105.72Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M119.907,103.374C120.261,102.483 119.826,101.474 118.934,101.12L113.581,98.992C112.69,98.638 111.681,99.073 111.326,99.965C110.972,100.856 111.408,101.865 112.299,102.219L117.652,104.347C118.543,104.701 119.553,104.265 119.907,103.374Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M64.2,50.369C64.021,45.223 68.145,40.954 73.294,40.954H92.969C98.168,40.954 102.31,45.303 102.057,50.495L98.248,94.375L97.927,94.563C88.564,100.058 76.945,99.985 67.651,94.375L65.844,71.334"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M69.51,41.775C70.661,41.248 71.942,40.954 73.294,40.954H89.794"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M67.2,88.624L64.2,50.369C64.128,48.283 64.762,46.341 65.886,44.769"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M92.969,40.954C98.168,40.954 102.31,45.303 102.057,50.495L98.248,94.375L97.927,94.564C88.564,100.058 76.945,99.986 67.651,94.375H67.651"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M87.842,41.38L82.939,49.78L78.036,41.38V29.279H87.842V41.38Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M78.036,33.521L87.842,36.402V30.874L78.036,30.688V33.521Z"
      android:fillColor="#B76452"/>
  <path
      android:pathData="M81.959,40.122C81.058,40.122 80.328,39.392 80.328,38.492"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M85.668,45.105L82.939,49.78L78.036,41.38V29.279H87.842V41.38L87.147,42.572"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M112.474,77.962H107.26H102.068L87.842,44.391H97.67C99.682,44.391 101.472,45.672 102.12,47.577L112.474,77.962Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M88.73,13.284C88.73,14.618 88.486,15.7 88.184,15.7C87.883,15.7 87.638,14.618 87.638,13.284C87.638,11.95 87.883,10.869 88.184,10.869C88.486,10.869 88.73,11.95 88.73,13.284Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M89.426,14.365C88.674,15.467 87.862,16.223 87.613,16.053C87.364,15.883 87.772,14.852 88.524,13.75C89.276,12.648 90.087,11.892 90.336,12.062C90.585,12.232 90.178,13.263 89.426,14.365Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M75.532,17.925C75.532,18.778 74.841,19.47 73.988,19.47C73.134,19.47 72.443,18.778 72.443,17.925C72.443,17.072 73.134,16.38 73.988,16.38C74.841,16.38 75.532,17.072 75.532,17.925Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M75.091,22.946C75.091,24.408 73.905,25.593 72.443,25.593C70.98,25.593 69.795,24.408 69.795,22.946C69.795,21.483 70.98,20.298 72.443,20.298C73.905,20.298 75.091,21.483 75.091,22.946Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M75.091,22.946C75.091,24.408 73.905,25.593 72.443,25.593C70.98,25.593 69.795,24.408 69.795,22.946C69.795,21.483 70.98,20.298 72.443,20.298C73.905,20.298 75.091,21.483 75.091,22.946Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M92.694,24.195C92.106,29.772 87.11,33.816 81.533,33.229C75.956,32.642 71.912,27.645 72.499,22.068C73.087,16.492 78.084,12.447 83.66,13.035C89.237,13.622 93.281,18.618 92.694,24.195Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M63.838,168.714H84.711L83.039,161.395L77.154,161.575L65.867,165.903C64.638,166.374 63.838,167.483 63.838,168.714Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M80.573,167.522H69.536"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M113.486,99.02L117.532,100.562"
      android:strokeLineJoin="round"
      android:strokeWidth="0.278719"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M111.771,103.576L111.422,104.963C111.258,105.614 111.653,106.274 112.304,106.438C112.955,106.601 113.616,106.206 113.779,105.555L114.513,102.637"
      android:strokeLineJoin="round"
      android:strokeWidth="0.278719"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.532,106.998C115.183,107.162 115.844,106.767 116.007,106.116L116.622,103.382"
      android:strokeLineJoin="round"
      android:strokeWidth="0.278719"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M119.115,103.382L118.315,106.475"
      android:strokeLineJoin="round"
      android:strokeWidth="0.278719"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M86.356,167.522H96.362"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M88.338,15.714L80.148,17.184C77.878,17.592 75.538,16.677 74.242,14.769C73.668,13.924 73.357,12.982 73.504,12.099C74.046,8.829 77.576,6.997 80.562,8.436L85.907,11.012C87.393,11.729 88.338,13.233 88.338,14.883V15.714Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M93.185,21.219C90.508,21.219 88.338,19.049 88.338,16.372V13.856H88.934C91.282,13.856 93.185,15.759 93.185,18.107V21.219Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M76.57,11.224C76.57,12.438 75.586,13.423 74.371,13.423C73.157,13.423 72.172,12.438 72.172,11.224C72.172,10.01 73.157,9.025 74.371,9.025C75.586,9.025 76.57,10.01 76.57,11.224Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M75.022,9.066C75.022,8.234 75.697,7.559 76.529,7.559C77.362,7.559 78.036,8.234 78.036,9.066C78.036,9.899 77.362,10.573 76.529,10.573C75.697,10.573 75.022,9.899 75.022,9.066Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M78.588,14.585C78.588,16.073 77.382,17.28 75.894,17.28C74.406,17.28 73.199,16.073 73.199,14.585C73.199,13.097 74.406,11.891 75.894,11.891C77.382,11.891 78.588,13.097 78.588,14.585Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M79.674,21.037C79.674,21.621 79.201,22.094 78.617,22.094C78.033,22.094 77.56,21.621 77.56,21.037"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M79.116,27.222C78.533,27.222 78.06,26.749 78.06,26.166"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M83.653,27.222C83.07,27.222 82.597,26.749 82.597,26.166"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M82.613,21.037C82.613,21.621 82.14,22.094 81.557,22.094C80.973,22.094 80.5,21.621 80.5,21.037"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M80.599,18.775C81.283,18.401 82.139,18.652 82.513,19.335"
      android:strokeLineJoin="round"
      android:strokeWidth="0.696797"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M77.742,19.109C77.961,18.362 78.744,17.933 79.491,18.152"
      android:strokeLineJoin="round"
      android:strokeWidth="0.696797"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M82.597,25.674C82.597,26.609 81.838,27.367 80.903,27.367C79.968,27.367 79.209,26.609 79.209,25.674V24.014"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M84.815,30.084C86.374,30.084 87.639,28.82 87.639,27.26"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.378,77.962H111.293L112.375,81.465L103.887,79.173L103.378,77.962Z"
      android:fillColor="#B76452"/>
  <path
      android:pathData="M98.248,94.375L97.926,94.564C88.996,99.804 78.014,99.976 68.954,95.113L68.96,95.188H68.96C82.733,104.455 97.993,96.067 97.993,96.067L98.248,94.375Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M109.568,99.791L109.388,96.386C109.367,95.988 109.038,95.676 108.639,95.676H107.907C107.284,95.676 106.933,96.392 107.315,96.885L109.568,99.791Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M81.533,33.229C76.704,32.72 73.024,28.905 72.505,24.269"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M72.499,22.068C73.087,16.492 78.083,12.447 83.66,13.034C89.237,13.622 93.281,18.619 92.694,24.195C92.28,28.124 89.678,31.292 86.226,32.616"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M95.832,22.946C95.832,24.408 94.647,25.593 93.185,25.593C91.722,25.593 90.537,24.408 90.537,22.946C90.537,21.483 91.722,20.298 93.185,20.298C94.647,20.298 95.832,21.483 95.832,22.946Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M92.496,23.713C92.496,22.952 93.113,22.335 93.874,22.335"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M90.537,22.946C90.537,21.483 91.722,20.298 93.185,20.298C94.647,20.298 95.832,21.483 95.832,22.946C95.832,24.408 94.647,25.593 93.185,25.593C92.647,25.593 92.147,25.433 91.729,25.158"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M91.745,20.282C91.745,20.93 91.219,21.455 90.572,21.455C89.924,21.455 89.399,20.93 89.399,20.282C89.399,19.635 89.924,19.109 90.572,19.109C91.219,19.109 91.745,19.635 91.745,20.282Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M94.42,18.91C94.42,19.668 93.805,20.282 93.047,20.282C92.289,20.282 91.675,19.668 91.675,18.91C91.675,18.152 92.289,17.538 93.047,17.538C93.805,17.538 94.42,18.152 94.42,18.91Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M93.464,17.135C93.464,17.552 93.126,17.89 92.709,17.89C92.292,17.89 91.954,17.552 91.954,17.135C91.954,16.718 92.292,16.38 92.709,16.38C93.126,16.38 93.464,16.718 93.464,17.135Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M83.13,162.371V153.596"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M83.507,161.887L85.325,168.832"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M79.915,105.819L81.633,106.989C82.295,107.44 83.167,107.435 83.824,106.977L85.482,105.819"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M82.698,107.424V127.914"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.574,78.314L97.305,66.722L100.158,73.454L99.574,78.314Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M96.713,67.169C97.202,66.893 97.505,66.375 97.505,65.813V64.707"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.305,75.453H111.437"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#0D9AFF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M106.144,76.351H113.129"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M106.144,59.385L102.642,49.107C102.295,48.09 101.834,47.116 101.268,46.204L100.377,44.769"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M97.305,66.722L102.068,77.962H107.26H112.474L107.958,64.707"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M79.295,58.327H68.82V60L79.295,58.327Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M78.036,57.12H79.295V58.327H69.076V57.12H75.331"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.247,94.065L94.835,91.217L98.496,91.804L98.247,94.065Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M97.728,97.733L92.228,133.37"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M90.855,142.266L89.106,153.596"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M75.026,142.266L78.18,162.216"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M70.19,100.646L67.651,99.345"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M68.239,101.685H70.19"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M75.054,165.054L73.535,162.647"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M76.119,163.989L74.6,161.582"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M91.132,165.054L92.651,162.647"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M90.067,163.989L91.586,161.582"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.039,151.188C99.516,149.708 97.866,146.929 97.97,143.809C97.631,144.607 97.426,145.479 97.387,146.397C97.251,149.626 99.2,152.458 102.039,153.596V151.188Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M97.97,143.808C97.631,144.607 97.425,145.479 97.387,146.397C97.251,149.626 99.2,152.458 102.038,153.596V151.188"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M111.537,166.18H111.297V159.057L108.47,155.961V167.457C108.47,168.189 109.063,168.782 109.795,168.782H109.884H111.297H113.116V167.76C113.116,166.887 112.409,166.18 111.537,166.18Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M127.623,166.18H127.383V155.961L124.556,159.057V167.457C124.556,168.189 125.149,168.782 125.881,168.782H125.97H127.383H129.203V167.76C129.203,166.887 128.495,166.18 127.623,166.18Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M118.526,162.216H110.798C105.257,162.216 100.764,157.724 100.764,152.182C100.764,151.293 101.485,150.572 102.375,150.572H128.56V152.182C128.56,157.724 124.067,162.216 118.526,162.216Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M106.747,150.572C106.485,150.992 106.331,151.486 106.331,152.017C106.331,153.528 107.555,154.752 109.066,154.752C110.576,154.752 111.801,153.528 111.801,152.017C111.801,152.001 111.799,151.984 111.798,151.967C111.866,151.975 111.935,151.979 112.005,151.979C112.886,151.979 113.625,151.382 113.847,150.572H106.747Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M116.272,151.466C116.272,152.415 117.041,153.185 117.991,153.185C118.94,153.185 119.71,152.415 119.71,151.466C119.71,151.138 119.616,150.833 119.457,150.572H116.525C116.365,150.833 116.272,151.138 116.272,151.466Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M102.375,150.572H128.56V152.182C128.56,153.519 128.299,154.795 127.824,155.961C126.705,158.712 124.4,160.855 121.543,161.755"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M122.024,166.18H121.784V159.057H118.957V160.4V167.457C118.957,168.189 119.55,168.782 120.282,168.782H120.371H121.784H123.604V167.76C123.604,166.887 122.896,166.18 122.024,166.18Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M105.82,166.18H105.58V159.057L102.753,155.961V167.457C102.753,168.189 103.346,168.782 104.078,168.782H104.167H105.58H107.4V167.76C107.4,166.887 106.692,166.18 105.82,166.18Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M118.957,160.98V164.145"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M118.526,162.216H110.798C109.729,162.216 108.7,162.049 107.734,161.74C106.777,161.433 105.883,160.987 105.076,160.426"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.753,155.124V162.816"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M127.819,155.967C128.295,154.799 128.56,153.522 128.56,152.182V150.572H120.719C121.928,153.454 124.608,155.563 127.819,155.967Z"
      android:fillColor="#E43C28"/>
  <path
      android:pathData="M127.819,155.967C128.295,154.799 128.56,153.522 128.56,152.182V150.572H120.719C121.928,153.454 124.608,155.563 127.819,155.967Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M125.031,147.192C123.648,148.575 121.309,148.149 120.502,146.366L117.444,139.605L124.205,142.664C125.987,143.47 126.414,145.809 125.031,147.192Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M121.815,142.81C122.408,142.81 122.956,142.621 123.406,142.302L119.074,140.343C119.219,141.729 120.391,142.81 121.815,142.81Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M125.031,147.192C123.648,148.575 121.309,148.149 120.502,146.366L117.444,139.605L124.205,142.664C125.987,143.47 126.414,145.809 125.031,147.192Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M127.64,145.846C126.276,145.028 126.15,143.098 127.397,142.11L132.129,138.36L131.051,144.301C130.766,145.866 129.005,146.665 127.64,145.846Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M127.64,145.846C126.276,145.028 126.15,143.098 127.397,142.11L132.129,138.36L131.051,144.301C130.766,145.866 129.005,146.665 127.64,145.846Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M130.821,144.619C130.821,142.724 129.285,141.188 127.391,141.188H127.337C123.992,141.188 121.281,143.9 121.281,147.245C121.281,151.597 124.808,155.124 129.16,155.124H129.428C135.323,155.124 140.126,150.459 140.351,144.619H130.821Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M128.053,145.467C128.053,145.939 127.67,146.322 127.198,146.322C126.725,146.322 126.342,145.939 126.342,145.467"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M128.333,151.811C127.593,151.811 126.994,151.211 126.994,150.472"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M130.588,145.467C130.588,145.939 130.205,146.322 129.733,146.322C129.261,146.322 128.878,145.939 128.878,145.467"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M137.611,145.807C138.073,146.641 138.745,147.569 139.527,147.787C139.873,147.884 140.234,147.652 140.326,147.305C140.536,146.522 140.681,145.712 140.754,144.882C140.787,144.509 140.49,144.188 140.114,144.188L138.446,144.285C137.691,144.329 137.245,145.146 137.611,145.807Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M136.557,147.595C136.557,147.706 136.466,147.796 136.355,147.796C136.244,147.796 136.154,147.706 136.154,147.595C136.154,147.484 136.244,147.393 136.355,147.393C136.466,147.393 136.557,147.484 136.557,147.595Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M137.873,147.37C137.873,147.443 137.814,147.502 137.741,147.502C137.668,147.502 137.609,147.443 137.609,147.37C137.609,147.298 137.668,147.239 137.741,147.239C137.814,147.239 137.873,147.298 137.873,147.37Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M137.609,148.648C137.609,148.759 137.519,148.849 137.408,148.849C137.297,148.849 137.207,148.759 137.207,148.648C137.207,148.537 137.297,148.446 137.408,148.446C137.519,148.446 137.609,148.537 137.609,148.648Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M125.468,154.208C126.569,154.793 127.826,155.124 129.16,155.124H129.428C131.412,155.124 133.272,154.596 134.876,153.672"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M121.544,145.473C121.373,146.034 121.281,146.629 121.281,147.245C121.281,149.022 121.869,150.661 122.861,151.979"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M129.482,141.9C128.903,141.454 128.178,141.188 127.391,141.188H127.337C125.464,141.188 123.789,142.039 122.678,143.375"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M140.351,144.619H130.821C130.821,144.18 130.739,143.761 130.589,143.376"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M122.017,150.572H120.719C121.928,153.454 124.608,155.563 127.819,155.967C127.937,155.678 128.042,155.382 128.133,155.08"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M54.926,161.238H51.539V97.465H54.373C62.45,97.465 67.425,109.207 65.501,117.214L54.926,161.238Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M48.343,161.238H51.733V97.465H48.896C40.812,97.465 35.833,109.207 37.758,117.214L48.343,161.238Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M60.006,50.184C62.585,50.263 64.583,52.45 64.449,55.027C64.363,56.688 64.659,58.362 65.331,59.281L66.333,60.651C67.995,62.924 67.031,66.156 64.394,67.147C62.113,68.004 61.107,70.673 62.254,72.824L64.029,76.151C65.145,78.241 64.339,80.839 62.237,81.931L59.345,83.434L44.98,76.838L54.368,50.011L60.006,50.184Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M48.349,45.797L44.92,48.386C42.56,49.331 40.949,52.745 42.155,54.983L41.553,57.275C42.755,59.503 41.632,62.275 39.218,63.038L37.6,63.549C34.915,64.398 33.779,67.574 35.317,69.933C36.649,71.974 35.88,74.721 33.683,75.775L30.282,77.406C28.147,78.43 27.258,81.001 28.306,83.126L29.747,86.049L45.23,89.234L53.542,62.055L48.349,45.797Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M54.833,69.043H48.608V65.295H54.833V69.043Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M54.832,68.082L48.608,68.144V65.295H54.832V68.082Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M55.786,71.6V68.795H47.486V71.6C47.486,72.244 47.073,72.821 46.458,73.01C43.942,73.783 42.164,76.203 42.342,78.975L43.505,97.074C43.671,99.667 45.823,101.685 48.421,101.685H54.851C57.449,101.685 59.6,99.667 59.767,97.074L60.93,78.975C61.107,76.203 59.329,73.783 56.814,73.01C56.199,72.821 55.786,72.244 55.786,71.6Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M64.195,55.445C64.195,61.663 58.933,66.704 52.443,66.704C45.952,66.704 40.691,61.663 40.691,55.445C40.691,49.226 45.952,44.185 52.443,44.185C58.933,44.185 64.195,49.226 64.195,55.445Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M63.637,60.513C63.637,61.206 63.076,61.768 62.383,61.768C61.69,61.768 61.129,61.206 61.129,60.513C61.129,59.82 61.69,59.259 62.383,59.259C63.076,59.259 63.637,59.82 63.637,60.513Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M63.637,60.513C63.637,61.206 63.076,61.768 62.383,61.768C61.69,61.768 61.129,61.206 61.129,60.513C61.129,59.82 61.69,59.259 62.383,59.259C63.076,59.259 63.637,59.82 63.637,60.513Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M62.383,58.358C62.383,63.464 58.244,67.603 53.139,67.603C48.033,67.603 43.894,63.464 43.894,58.358C43.894,53.253 48.033,49.114 53.139,49.114C58.244,49.114 62.383,53.253 62.383,58.358Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M45.184,63.04C44.365,61.66 43.894,60.049 43.894,58.327C43.894,53.222 48.033,49.083 53.139,49.083C58.244,49.083 62.383,53.222 62.383,58.327C62.383,58.877 62.335,59.416 62.243,59.94"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M50.778,67.268C48.999,66.799 47.43,65.813 46.24,64.481"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M62.065,60.74C61.004,64.675 57.41,67.572 53.139,67.572"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M55.503,80.532H50.254L49.756,76.096H56.037L55.503,80.532Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M65.272,168.832H56.557C55.92,168.832 55.404,168.315 55.404,167.679V167.099C55.404,166.551 55.011,166.059 54.467,165.995C53.819,165.918 53.226,168.15 53.226,168.782H51.464L51.642,162.539L63.152,165.999C64.409,166.368 65.272,167.522 65.272,168.832Z"
      android:fillColor="#72BAE5"/>
  <path
      android:pathData="M54.597,164.031L54.908,161.238H51.642L51.647,162.589L52.279,163.3C52.88,163.875 53.709,164.147 54.534,164.039L54.597,164.031Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M47.527,87.238L48.873,82.221L52.049,82.06L51.414,87.145L47.527,87.238Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M37.911,168.832H46.626C47.263,168.832 47.779,168.315 47.779,167.679V167.099C47.779,166.551 48.173,166.059 48.717,165.995C49.364,165.918 49.957,168.15 49.957,168.782H51.719L51.647,162.589L40.032,165.999C38.775,166.368 37.911,167.522 37.911,168.832Z"
      android:fillColor="#72BAE5"/>
  <path
      android:pathData="M48.666,164.031L48.363,161.238H51.733L51.647,162.589L50.957,163.268C50.352,163.862 49.507,164.144 48.666,164.031Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M58.939,134.44C58.939,134.927 58.544,135.322 58.057,135.322C57.57,135.322 57.175,134.927 57.175,134.44"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M46.206,134.44C46.206,134.927 45.811,135.322 45.324,135.322C44.836,135.322 44.442,134.927 44.442,134.44"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M42.33,58.256C42.33,59.031 42.416,59.787 42.575,60.515C43.931,61.149 45.442,61.505 47.037,61.505C52.86,61.505 57.581,56.785 57.581,50.962C57.581,50.186 57.494,49.431 57.336,48.703C55.98,48.068 54.469,47.712 52.873,47.712C47.05,47.712 42.33,52.433 42.33,58.256Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M49.726,56.529C52.785,55.349 55.154,52.785 56.068,49.604"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M44.952,58.399C48.194,57.917 51.068,55.934 52.656,53.03"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M46.512,52.069C48.275,52.118 49.983,51.336 51.096,49.946"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M57.837,49.47C57.41,49.47 56.995,49.519 56.595,49.608C56.245,50.37 56.05,51.219 56.05,52.116C56.05,55.388 58.646,58.041 61.849,58.041C62.276,58.041 62.692,57.992 63.092,57.903C63.441,57.141 63.637,56.292 63.637,55.395C63.637,52.123 61.04,49.47 57.837,49.47Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M56.702,60.511C56.702,61.118 56.21,61.61 55.603,61.61C54.995,61.61 54.503,61.118 54.503,60.511"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M59.641,60.542C59.641,61.149 59.149,61.641 58.542,61.641C57.935,61.641 57.442,61.149 57.442,60.542"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M57.725,58.677L58.542,58.951"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M55.908,59.006L56.679,58.622"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M53.991,65.172C52.719,65.172 51.688,64.141 51.688,62.869"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M56.689,62.497C56.989,62.497 57.231,62.74 57.231,63.039C57.231,63.338 56.989,63.581 56.689,63.581"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M50.876,62.124C50.876,61.591 51.308,61.158 51.841,61.158"
      android:strokeLineJoin="round"
      android:strokeWidth="0.278719"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M44.952,67.246L43.639,67.661C41.459,68.349 40.537,70.927 41.786,72.842C42.867,74.499 42.243,76.729 40.459,77.585L37.699,78.909C35.965,79.741 35.244,81.828 36.094,83.552"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M37.505,69.787C36.956,68.116 37.859,66.316 39.526,65.755L39.746,65.681C40.787,65.331 41.576,64.472 41.836,63.405"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M59.043,70.36C58.495,68.689 59.398,66.888 61.065,66.328L61.284,66.254C62.326,65.904 63.114,65.045 63.374,63.978"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M30.338,83.507C29.789,81.836 30.692,80.035 32.359,79.474L32.579,79.401C33.62,79.05 34.409,78.191 34.669,77.124"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M56.317,80.315H51.331C50.869,80.315 50.495,79.941 50.495,79.479C50.495,79.017 50.869,78.643 51.331,78.643H56.317C56.778,78.643 57.153,79.017 57.153,79.479C57.153,79.941 56.778,80.315 56.317,80.315Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M56.317,81.988H51.331C50.869,81.988 50.495,81.613 50.495,81.151C50.495,80.69 50.869,80.315 51.331,80.315H56.317C56.778,80.315 57.153,80.69 57.153,81.151C57.153,81.613 56.778,81.988 56.317,81.988Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M56.317,83.638H51.331C50.869,83.638 50.495,83.263 50.495,82.802C50.495,82.34 50.869,81.965 51.331,81.965H56.317C56.778,81.965 57.153,82.34 57.153,82.802C57.153,83.263 56.778,83.638 56.317,83.638Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M99.4,87.093L94.474,86.325C94.018,86.254 93.705,85.826 93.776,85.37C93.848,84.914 94.275,84.602 94.731,84.673L99.658,85.44C100.114,85.511 100.426,85.939 100.355,86.395C100.284,86.852 99.857,87.164 99.4,87.093Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M99.143,88.745L94.216,87.977C93.76,87.906 93.448,87.479 93.519,87.022C93.59,86.566 94.018,86.254 94.474,86.325L99.4,87.093C99.857,87.164 100.169,87.591 100.098,88.048V88.048C100.027,88.504 99.599,88.816 99.143,88.745Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M98.889,90.376L94.34,89.667C93.884,89.596 93.572,89.168 93.643,88.712C93.714,88.256 94.142,87.943 94.598,88.015L99.146,88.723C99.602,88.794 99.915,89.222 99.844,89.678C99.773,90.134 99.345,90.447 98.889,90.376Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M98.665,91.814L94.836,91.217C94.379,91.146 94.067,90.718 94.138,90.262C94.209,89.806 94.637,89.494 95.093,89.565L98.922,90.161C99.378,90.232 99.691,90.66 99.619,91.116C99.549,91.573 99.121,91.885 98.665,91.814Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M52.322,83.638L48.804,84.649V80.371C48.804,79.416 49.578,78.643 50.532,78.643H52.322V83.638Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M56.836,76.096H48.957L49.138,74.702H56.608L56.836,76.096Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M55.681,74.702H50.112V74.427C50.112,74.156 50.333,73.936 50.604,73.936H55.19C55.461,73.936 55.681,74.156 55.681,74.427V74.702Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M67.112,87.5L58.484,86.2V74.702L66.527,80.041L67.112,87.5Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M98.644,91.811C97.823,91.683 97.261,90.913 97.389,90.092L97.919,86.692C98.047,85.871 98.816,85.309 99.637,85.437C100.458,85.565 101.02,86.334 100.892,87.155L100.362,90.556C100.234,91.377 99.465,91.938 98.644,91.811Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M48.103,85.093L51.653,85.264L51.636,85.514L47.788,86.267L48.103,85.093Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M46.239,103.687C44.564,103.454 43.394,101.907 43.628,100.231L47.446,86.337L51.893,85.288L49.696,101.076C49.462,102.751 47.915,103.921 46.239,103.687Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M46.185,90.925L44.805,95.948"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M51.72,86.275L49.939,99.621"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M58.571,85.261L51.04,84.219C50.703,84.172 50.469,83.862 50.515,83.526C50.562,83.19 50.872,82.955 51.208,83.002L58.739,84.044C59.076,84.09 59.31,84.4 59.264,84.737C59.217,85.073 58.907,85.307 58.571,85.261Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M53.824,81.965H56.317C56.778,81.965 57.153,82.34 57.153,82.802C57.153,83.263 56.778,83.638 56.317,83.638L55.764,83.632"
      android:strokeLineJoin="round"
      android:strokeWidth="0.278719"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M53.653,80.315H55.681"
      android:strokeLineJoin="round"
      android:strokeWidth="0.278719"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M60.634,83.579L60.265,89.327"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M48.698,69.012V66.596"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M54.768,69.089V67.428"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M42.575,60.515C43.931,61.149 45.442,61.505 47.038,61.505C52.861,61.505 57.581,56.785 57.581,50.962"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M56.78,54.997C57.771,56.813 59.67,58.041 61.849,58.041C62.276,58.041 62.692,57.992 63.092,57.903"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M62.065,50.688C62.834,52.113 63.268,53.73 63.268,55.445"
      android:strokeAlpha="0.300003"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.300003"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M59.657,47.711C60.13,48.117 60.566,48.562 60.959,49.04"
      android:strokeAlpha="0.300003"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.300003"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M41.682,55.445C41.682,51.668 43.801,48.366 46.962,46.57"
      android:strokeAlpha="0.300003"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.300003"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M58.303,54.172C59.271,55.646 60.89,56.599 62.667,56.717"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M49.757,76.096H56.037L55.977,76.599L49.97,77.996L49.757,76.096Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M56.928,74.702H48.698"
      android:strokeLineJoin="round"
      android:strokeWidth="0.278719"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M57.308,78.643H49.565"
      android:strokeLineJoin="round"
      android:strokeWidth="0.278719"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M52.049,72.92C52.049,73.06 52.163,73.174 52.303,73.174C52.443,73.174 52.557,73.06 52.557,72.92C52.557,72.78 52.443,72.666 52.303,72.666C52.163,72.666 52.049,72.78 52.049,72.92Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="72.6659"
          android:startX="52.3031"
          android:endY="73.1738"
          android:endX="52.3031"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M53.367,70.023C53.017,70.096 52.739,70.386 52.677,70.737C52.621,71.055 52.733,71.348 52.938,71.545C53.047,71.65 53.093,71.798 53.061,71.946C53.047,72.01 53.046,72.079 53.06,72.151C53.102,72.353 53.276,72.513 53.481,72.538C53.782,72.575 54.037,72.341 54.037,72.048C54.037,72.018 54.034,71.988 54.028,71.959C54.001,71.813 54.049,71.668 54.16,71.569C54.343,71.405 54.459,71.166 54.459,70.901C54.459,70.405 54.056,70.003 53.561,70.002C53.497,70.002 53.432,70.009 53.367,70.023">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="70.0025"
          android:startX="53.5606"
          android:endY="72.5419"
          android:endX="53.5606"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M87.877,22.335C88.341,22.335 88.717,22.711 88.717,23.174"
      android:strokeLineJoin="round"
      android:strokeWidth="0.278719"
      android:fillColor="#00000000"
      android:strokeColor="#B76452"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M73.048,23.174C73.048,22.711 73.424,22.335 73.887,22.335"
      android:strokeLineJoin="round"
      android:strokeWidth="0.278719"
      android:fillColor="#00000000"
      android:strokeColor="#B76452"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.665,91.814L94.836,91.217C94.379,91.146 94.067,90.718 94.138,90.262C94.209,89.806 94.637,89.494 95.093,89.565"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M95.833,88.026L98.496,88.398"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M96.396,86.624L94.474,86.325C94.018,86.254 93.705,85.826 93.776,85.37C93.847,84.914 94.275,84.601 94.731,84.673L99.658,85.44"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.247,86.913L98.837,87.004"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M51.646,168.592V162.94"
      android:strokeLineJoin="round"
      android:strokeWidth="0.278719"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M42.888,108.264C42.888,111.086 40.6,113.374 37.778,113.374"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M60.411,108.264C60.411,111.086 62.699,113.374 65.521,113.374"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M50.076,112.817L50.881,113.336C51.403,113.672 52.07,113.683 52.603,113.363L53.436,112.863"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M58.484,101.685L60.634,99.621"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M59.832,101.685L61.783,100.878"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M51.733,161.238H47.754L47.243,159.874H51.733V161.238Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.278719"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M51.646,161.238H55.626L56.137,159.874H51.646V161.238Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.278719"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M51.646,151.643V159.874H53.003L51.646,151.643Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M30.282,77.406C28.147,78.43 27.258,81.001 28.306,83.126L29.747,86.049"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M39.218,63.038L37.6,63.549C34.915,64.398 33.779,67.573 35.318,69.932C36.649,71.974 35.88,74.72 33.683,75.775"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M66.333,60.651L65.331,59.281C64.802,58.557 64.519,56.977 64.627,55.416C64.69,54.497 64.245,53.03 63.888,52.087"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M62.254,72.824C61.106,70.673 62.113,68.004 64.394,67.147H64.394C65.995,66.545 66.98,65.117 67.134,63.579"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M62.237,81.931C64.339,80.839 65.145,78.241 64.029,76.151"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M38.405,87.83L38.933,83.845L40.008,88.16L38.405,87.83Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M51.72,137.483V113.596"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M53.734,129.351L51.684,126.234"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M52.834,130.112L51.733,127.868"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M51.646,145.009V148.702"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M46.642,162.814C46.459,163.243 46.159,164.919 46.588,165.102C47.018,165.284 48.016,163.905 48.199,163.475C48.381,163.045 48.181,162.549 47.751,162.366C47.321,162.183 46.825,162.384 46.642,162.814Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.278719"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M44.115,164.97C44.544,165.152 46.22,165.453 46.403,165.023C46.586,164.593 45.206,163.596 44.776,163.413C44.346,163.23 43.85,163.431 43.667,163.861C43.484,164.29 43.685,164.787 44.115,164.97Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.278719"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M56.532,162.814C56.714,163.243 57.015,164.919 56.585,165.102C56.155,165.284 55.158,163.905 54.975,163.475C54.792,163.045 54.993,162.549 55.423,162.366C55.853,162.183 56.349,162.384 56.532,162.814Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.278719"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M59.059,164.97C58.629,165.152 56.953,165.453 56.771,165.023C56.588,164.593 57.967,163.596 58.397,163.413C58.827,163.23 59.324,163.431 59.506,163.861C59.689,164.29 59.488,164.787 59.059,164.97Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.278719"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M45.893,90.166L42.902,90.505C42.819,90.514 42.737,90.515 42.656,90.508C42.093,90.458 41.624,90.018 41.558,89.434C41.482,88.767 41.961,88.165 42.628,88.09L45.619,87.75C46.286,87.675 46.888,88.154 46.963,88.821C47.039,89.488 46.56,90.09 45.893,90.166Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M45.818,87.798L42.828,88.137C42.161,88.213 41.559,87.734 41.483,87.066C41.407,86.4 41.887,85.798 42.554,85.722L45.544,85.383C46.211,85.307 46.813,85.786 46.889,86.453V86.453C46.964,87.12 46.485,87.723 45.818,87.798Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M45.559,85.515L42.569,85.854C41.902,85.93 41.3,85.451 41.224,84.784C41.148,84.117 41.628,83.515 42.295,83.439L45.285,83.1C45.952,83.024 46.554,83.504 46.63,84.171C46.705,84.838 46.226,85.44 45.559,85.515Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M43.345,83.138L40.796,84.739C40.228,85.096 39.478,84.924 39.121,84.356C38.764,83.788 38.935,83.037 39.504,82.68L42.052,81.08C42.62,80.723 43.371,80.895 43.728,81.463C44.085,82.031 43.913,82.782 43.345,83.138Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M46.111,82.638L43.145,83.149C42.484,83.263 41.855,82.819 41.741,82.157C41.627,81.496 42.071,80.867 42.732,80.753L45.698,80.242C46.36,80.128 46.988,80.572 47.102,81.233C47.216,81.895 46.773,82.524 46.111,82.638Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M42.582,91.298C41.652,91.531 40.709,90.965 40.477,90.035L39.08,84.446C38.848,83.516 39.414,82.573 40.344,82.341C41.274,82.108 42.217,82.674 42.449,83.604L43.846,89.193C44.078,90.123 43.513,91.066 42.582,91.298Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M43.864,83.261L45.285,83.1C45.952,83.025 46.554,83.504 46.63,84.171C46.705,84.838 46.226,85.44 45.559,85.515L42.569,85.855"
      android:strokeLineJoin="round"
      android:strokeWidth="0.278719"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M46.889,86.453C46.965,87.12 46.485,87.722 45.818,87.798L43.027,88.044"
      android:strokeLineJoin="round"
      android:strokeWidth="0.278719"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.163,168.782H12.774"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M62.735,168.782H118.03"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M120.895,168.782H135.968"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M139.6,168.782H144.184"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M134.342,166.998L134.993,168.714L135.457,167.533"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M15.988,55.719C15.988,56.149 16.337,56.498 16.768,56.498C17.199,56.498 17.548,56.149 17.548,55.719C17.548,55.288 17.199,54.939 16.768,54.939C16.337,54.939 15.988,55.288 15.988,55.719Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M9.703,53.516C9.703,53.801 9.934,54.032 10.219,54.032C10.504,54.032 10.735,53.801 10.735,53.516C10.735,53.231 10.504,53.001 10.219,53.001C9.934,53.001 9.703,53.231 9.703,53.516Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M23.026,23.379C23.026,23.664 23.257,23.895 23.542,23.895C23.826,23.895 24.057,23.664 24.057,23.379C24.057,23.094 23.826,22.863 23.542,22.863C23.257,22.863 23.026,23.094 23.026,23.379Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M19.262,49.931H19.828V49.365C19.828,49.262 19.911,49.179 20.014,49.179C20.117,49.179 20.199,49.262 20.199,49.365V49.931H20.766C20.868,49.931 20.951,50.014 20.951,50.117C20.951,50.22 20.868,50.303 20.766,50.303H20.199V50.869C20.199,50.972 20.117,51.055 20.014,51.055C19.911,51.055 19.828,50.972 19.828,50.869V50.303H19.262C19.159,50.303 19.076,50.22 19.076,50.117C19.076,50.014 19.159,49.931 19.262,49.931"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M49.811,25.407H50.377V24.841C50.377,24.739 50.46,24.656 50.563,24.656C50.666,24.656 50.749,24.739 50.749,24.841V25.407H51.315C51.417,25.407 51.501,25.491 51.501,25.593C51.501,25.696 51.417,25.779 51.315,25.779H50.749V26.345C50.749,26.448 50.666,26.531 50.563,26.531C50.46,26.531 50.377,26.448 50.377,26.345V25.779H49.811C49.708,25.779 49.625,25.696 49.625,25.593C49.625,25.491 49.708,25.407 49.811,25.407"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M107.509,27.535H108.257V26.787C108.257,26.652 108.367,26.542 108.502,26.542C108.638,26.542 108.748,26.652 108.748,26.787V27.535H109.495C109.631,27.535 109.741,27.645 109.741,27.781C109.741,27.916 109.631,28.026 109.495,28.026H108.748V28.774C108.748,28.91 108.638,29.019 108.502,29.019C108.367,29.019 108.257,28.91 108.257,28.774V28.026H107.509C107.373,28.026 107.263,27.916 107.263,27.781C107.263,27.645 107.373,27.535 107.509,27.535Z"
      android:fillColor="#529ED6"/>
  <path
      android:pathData="M111.674,22.164C111.674,22.451 111.907,22.684 112.194,22.684C112.481,22.684 112.714,22.451 112.714,22.164C112.714,21.877 112.481,21.644 112.194,21.644C111.907,21.644 111.674,21.877 111.674,22.164Z"
      android:fillColor="#529ED6"/>
  <path
      android:pathData="M106.423,21.927C106.423,22.306 106.73,22.613 107.109,22.613C107.487,22.613 107.794,22.306 107.794,21.927C107.794,21.549 107.487,21.242 107.109,21.242C106.73,21.242 106.423,21.549 106.423,21.927Z"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#E4F4FC"/>
  <path
      android:pathData="M134.384,69.42C134.384,69.705 134.615,69.935 134.9,69.935C135.185,69.935 135.416,69.705 135.416,69.42C135.416,69.135 135.185,68.904 134.9,68.904C134.615,68.904 134.384,69.135 134.384,69.42Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.418078"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M187.236,68.975L199.535,63.076L209.714,51.957L210.769,70.095L187.861,79.477L187.236,68.975Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M195.807,84.359L194.5,79.477L192.717,80.541C190.139,82.081 187.379,82.591 184.661,82.033L182.596,81.609L181.784,84.359C179.828,90.991 179.247,98.56 180.137,105.828L180.204,106.376L181.433,122.622C181.586,124.639 182.011,126.568 182.675,128.257C182.872,128.76 183.011,129.323 183.084,129.916L187.297,164.909H190.195L194.507,129.916C194.58,129.323 194.719,128.76 194.916,128.257C195.58,126.568 196.005,124.639 196.158,122.622L197.387,106.376L197.454,105.828C198.344,98.56 197.763,90.991 195.807,84.359Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M199.798,117.158H177.452L174.458,93.211C173.549,85.94 178.832,79.477 185.683,79.477H191.259C198.049,79.477 203.313,85.83 202.503,93.048L199.798,117.158Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M198.464,82.458C192.559,86.178 184.971,86.272 178.971,82.699L178.796,82.594L179.589,50.053C179.894,45.737 183.596,42.386 188.061,42.386H194.164C199.081,42.386 202.971,46.421 202.636,51.175L198.464,82.458Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M198.664,80.956L197.939,75.992L199.355,75.775L198.664,80.956Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M204.164,31.927C205.55,35.089 204.111,38.775 200.949,40.161C197.788,41.547 194.101,40.107 192.716,36.946C191.33,33.785 192.769,30.098 195.93,28.712C199.092,27.326 202.778,28.766 204.164,31.927Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M188.227,36.816C188.227,40.667 185.105,43.788 181.255,43.788C177.404,43.788 174.282,40.667 174.282,36.816C174.282,32.965 177.404,29.844 181.255,29.844C185.105,29.844 188.227,32.965 188.227,36.816Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M193.93,44.152H187.861V35.548H193.93V44.152Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M193.93,38.534L187.861,40.216V35.548H193.93V38.534Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M202.516,30.885C202.516,31.56 201.968,32.108 201.293,32.108C200.617,32.108 200.07,31.56 200.07,30.885C200.07,30.209 200.617,29.662 201.293,29.662C201.968,29.662 202.516,30.209 202.516,30.885Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M201.293,28.784C201.293,33.762 197.257,37.797 192.279,37.797C187.301,37.797 183.265,33.762 183.265,28.784C183.265,23.805 187.301,19.77 192.279,19.77C197.257,19.77 201.293,23.805 201.293,28.784Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M184.523,33.348C183.724,32.003 183.265,30.432 183.265,28.753C183.265,23.775 187.301,19.739 192.279,19.739C197.257,19.739 201.293,23.775 201.293,28.753C201.293,29.29 201.246,29.815 201.156,30.325"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M189.978,37.471C188.243,37.014 186.712,36.053 185.553,34.754"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M200.983,31.106C199.948,34.943 196.444,37.767 192.279,37.767"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M192.866,29.782C192.866,30.106 192.603,30.369 192.279,30.369C191.955,30.369 191.692,30.106 191.692,29.782"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M196.292,29.799C196.292,30.123 196.029,30.385 195.705,30.385C195.381,30.385 195.118,30.123 195.118,29.799"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M194.322,27.222L195.118,27.489"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M192.467,27.487L193.264,27.223"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M190.802,34.912C189.561,34.912 188.556,33.907 188.556,32.667"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M193.763,31.106C194.055,31.106 194.292,31.343 194.292,31.634C194.292,31.926 194.055,32.163 193.763,32.163"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M187.765,31.94C187.765,31.42 188.187,30.999 188.706,30.999"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M187.949,44.081V38.534"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M178.257,41.014C176.15,41.014 174.396,39.355 174.299,37.229C173.997,30.577 177.69,24.438 183.708,21.588C183.753,21.567 183.797,21.545 183.84,21.523C185.002,20.924 185.795,19.691 185.909,18.306L185.967,17.602C186.148,15.42 188.065,13.797 190.246,13.977C192.428,14.158 194.051,16.073 193.871,18.256L193.812,18.959C193.475,23.045 191.046,26.729 187.475,28.571C187.351,28.634 187.227,28.696 187.1,28.756C183.98,30.233 182.064,33.418 182.221,36.868C182.32,39.056 180.628,40.91 178.44,41.009C178.379,41.012 178.318,41.014 178.257,41.014Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M185.05,30.885C185.05,31.802 184.306,32.546 183.389,32.546C182.471,32.546 181.728,31.802 181.728,30.885C181.728,29.967 182.471,29.224 183.389,29.224C184.306,29.224 185.05,29.967 185.05,30.885Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M184.659,31.955C184.355,32.316 183.898,32.546 183.389,32.546C182.471,32.546 181.728,31.802 181.728,30.885C181.728,29.968 182.471,29.224 183.389,29.224C184.135,29.224 184.766,29.716 184.975,30.393"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M187.496,21.939C187.496,23.823 185.968,25.351 184.083,25.351C182.199,25.351 180.671,23.823 180.671,21.939C180.671,20.054 182.199,18.526 184.083,18.526C185.968,18.526 187.496,20.054 187.496,21.939Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M184.083,18.207C184.083,17.048 185.023,16.109 186.182,16.109C187.341,16.109 188.281,17.048 188.281,18.207C188.281,19.366 187.341,20.306 186.182,20.306C185.023,20.306 184.083,19.366 184.083,18.207Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M202.227,30.34C201.826,30.34 201.418,30.264 201.024,30.102L199.325,29.402C195.746,27.929 193.276,24.481 193.034,20.618L192.961,19.469C192.852,17.726 194.176,16.225 195.919,16.115C197.662,16.005 199.164,17.33 199.274,19.073L199.346,20.222C199.438,21.687 200.375,22.995 201.732,23.553L203.432,24.253C205.046,24.917 205.817,26.766 205.152,28.381C204.65,29.602 203.47,30.34 202.227,30.34Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M201.512,21.939C201.512,23.325 200.388,24.449 199.002,24.449C197.615,24.449 196.491,23.325 196.491,21.939C196.491,20.552 197.615,19.428 199.002,19.428C200.388,19.428 201.512,20.552 201.512,21.939Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M200.568,20.15C200.568,21.325 199.615,22.278 198.44,22.278C197.264,22.278 196.311,21.325 196.311,20.15C196.311,18.974 197.264,18.021 198.44,18.021C199.615,18.021 200.568,18.974 200.568,20.15Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M193.59,29.822C193.59,30.749 192.839,31.5 191.912,31.5C190.986,31.5 190.235,30.749 190.235,29.822C190.235,28.896 190.986,28.145 191.912,28.145C192.839,28.145 193.59,28.896 193.59,29.822Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M197.382,29.822C197.382,30.749 196.631,31.5 195.705,31.5C194.778,31.5 194.027,30.749 194.027,29.822C194.027,28.896 194.778,28.145 195.705,28.145C196.631,28.145 197.382,28.896 197.382,29.822Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M193.604,29.73H194.009"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M194.003,125.861C194.003,126.87 193.232,127.689 192.281,127.689"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M185.226,127.689C184.275,127.689 183.503,126.87 183.503,125.861"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M202.723,168.971H189.071V164.909H199.227C201.158,164.909 202.723,166.474 202.723,168.404V168.971Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M174.868,168.971H189.071V164.909H178.364C176.433,164.909 174.868,166.474 174.868,168.404V168.971Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M192.402,163.055C192.402,163.531 191.54,164.909 191.54,164.909C191.54,164.909 190.677,163.531 190.677,163.055C190.677,162.578 191.063,162.192 191.54,162.192C192.016,162.192 192.402,162.578 192.402,163.055Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M193.394,165.771C192.918,165.771 191.54,164.909 191.54,164.909C191.54,164.909 192.918,164.047 193.394,164.047C193.87,164.047 194.256,164.433 194.256,164.909C194.256,165.385 193.87,165.771 193.394,165.771Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M185.924,163.055C185.924,163.531 186.787,164.909 186.787,164.909C186.787,164.909 187.649,163.531 187.649,163.055C187.649,162.578 187.263,162.192 186.787,162.192C186.31,162.192 185.924,162.578 185.924,163.055Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M184.933,165.771C185.409,165.771 186.787,164.909 186.787,164.909C186.787,164.909 185.409,164.047 184.933,164.047C184.457,164.047 184.071,164.433 184.071,164.909C184.071,165.385 184.457,165.771 184.933,165.771Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M202.723,168.404V168.971H189.071V164.909"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M196.896,164.909H199.227C200.634,164.909 201.847,165.74 202.402,166.939"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M183.568,164.909H178.364C176.433,164.909 174.868,166.474 174.868,168.404V168.971H185.429"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M187.298,168.971H189.071V164.909H187.298"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M190.393,164.909H189.071"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M191.905,168.971H189.071V164.909H189.543L191.905,168.971Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M188.795,164.909V157.057L189.543,164.909H188.795Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M194.164,42.386H188.061C186.879,42.386 185.75,42.623 184.724,43.049C185.291,46.157 188.011,48.514 191.283,48.514C194.506,48.514 197.196,46.227 197.817,43.187C196.713,42.675 195.475,42.386 194.164,42.386Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M194.214,43.342H192.219"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M190.804,43.342H189.427"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M191.008,43.809C191.008,44.044 191.198,44.234 191.433,44.234C191.668,44.234 191.858,44.044 191.858,43.809"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M196.571,117.158H181.019L181.26,120.333L196.496,118.148L196.571,117.158Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M207.056,68.975L207.649,69.153"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.439,28.248C177.439,28.982 176.844,29.577 176.11,29.577C175.377,29.577 174.782,28.982 174.782,28.248C174.782,27.514 175.377,26.92 176.11,26.92"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M203.51,37.994C203.997,38.543 203.947,39.383 203.398,39.87C202.849,40.357 202.009,40.306 201.522,39.758C201.035,39.209 201.085,38.369 201.634,37.882"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M201.064,38.747C201.551,39.296 201.501,40.135 200.952,40.622C200.403,41.109 199.563,41.059 199.076,40.51C198.589,39.961 198.639,39.121 199.188,38.634"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.129,31.94C177.129,32.674 176.534,33.268 175.8,33.268C175.067,33.268 174.472,32.674 174.472,31.94C174.472,31.206 175.067,30.611 175.8,30.611"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M196.447,16.879C196.447,17.612 195.852,18.207 195.118,18.207C194.384,18.207 193.789,17.612 193.789,16.879C193.789,16.145 194.384,15.55 195.118,15.55"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M191.067,15.265C190.568,15.935 190.706,16.882 191.375,17.381C192.045,17.88 192.992,17.742 193.491,17.073C193.99,16.403 193.852,15.456 193.183,14.957"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M176.589,38.873C176.589,39.871 175.78,40.68 174.782,40.68C173.783,40.68 172.974,39.871 172.974,38.873C172.974,37.874 173.783,37.065 174.782,37.065"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M188.795,150.738V117.159"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M198.152,76.185C196.481,76.315 195.169,74.779 195.558,73.149L200.092,54.171L203.714,55.3L201.571,72.876C201.206,74.57 199.856,75.876 198.152,76.185Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M201.571,72.876C201.347,73.919 200.748,74.815 199.924,75.422C199.409,75.8 198.807,76.066 198.152,76.185C196.481,76.315 195.169,74.779 195.558,73.149"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M194.452,61.794L184.823,61.131C183.906,61.068 183.157,60.373 183.027,59.462L182.948,58.909"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M196.416,61.929L195.494,61.866"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M182.982,44.02C184.402,42.992 186.163,42.386 188.061,42.386H188.474"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M201.526,46.512C202.337,47.874 202.755,49.479 202.635,51.175"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M193.396,42.386H194.164C196.748,42.386 199.049,43.501 200.602,45.249"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M193.797,42.25V37.639"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.957,41.216C176.62,40.213 175.756,38.615 175.756,36.816C175.756,36.145 175.876,35.502 176.096,34.908"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M183.573,41.803C182.869,42.131 182.083,42.314 181.255,42.314"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M199.739,28.385C198.57,27.904 197.537,27.181 196.692,26.288"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M182.977,30.289C183.497,30.289 183.937,30.632 184.084,31.104"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M189.977,29.376H186.356"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M197.753,29.55H199.173"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M195.489,80.349H198.53"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M197.714,81.346H198.53"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M180.682,70.137L178.795,68.188"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M179.998,70.468L178.796,69.721"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M195.045,47.352C196.448,46.392 197.465,44.91 197.817,43.187"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M185.528,45.214C186.685,47.188 188.828,48.514 191.283,48.514"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M164.333,169.194H218.251"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M221.045,169.194H235.742"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M239.284,169.194H243.753"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M234.157,167.455L234.791,169.128L235.244,167.976"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M169.241,69.858C170.353,70.529 171.684,70.401 172.28,69.567L181.923,56.068L185.956,50.423C186.847,49.174 186.195,47.273 184.532,46.27C182.938,45.309 181.032,45.549 180.273,46.806L168.138,66.93C167.586,67.847 168.079,69.158 169.241,69.858Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M181.589,83.018L178.674,83.349C178.592,83.358 178.512,83.359 178.433,83.352C177.884,83.303 177.427,82.874 177.362,82.305C177.289,81.655 177.756,81.068 178.406,80.994L181.322,80.663C181.973,80.589 182.56,81.057 182.633,81.707C182.707,82.357 182.24,82.945 181.589,83.018Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M181.517,80.71L178.601,81.04C177.95,81.114 177.363,80.647 177.29,79.996C177.216,79.346 177.683,78.759 178.334,78.685L181.25,78.354C181.9,78.281 182.487,78.748 182.561,79.399C182.634,80.049 182.167,80.636 181.517,80.71Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M181.264,78.484L178.349,78.815C177.698,78.888 177.111,78.421 177.037,77.771C176.964,77.12 177.431,76.533 178.081,76.46L180.997,76.129C181.648,76.055 182.235,76.522 182.308,77.173V77.173C182.382,77.823 181.915,78.41 181.264,78.484Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M179.105,76.166L176.62,77.727C176.066,78.075 175.334,77.908 174.986,77.353C174.638,76.799 174.805,76.068 175.359,75.72L177.845,74.159C178.399,73.811 179.13,73.978 179.478,74.532C179.827,75.087 179.659,75.818 179.105,76.166Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M181.802,75.678L178.91,76.176C178.265,76.287 177.652,75.854 177.541,75.21C177.43,74.564 177.863,73.952 178.508,73.84L181.399,73.342C182.044,73.231 182.658,73.664 182.769,74.309C182.88,74.954 182.447,75.567 181.802,75.678Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M178.362,84.123C177.455,84.349 176.535,83.798 176.309,82.891L174.947,77.441C174.72,76.534 175.272,75.615 176.179,75.388C177.086,75.162 178.005,75.713 178.232,76.62L179.594,82.07C179.82,82.977 179.269,83.896 178.362,84.123Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M178.349,76.429L180.997,76.129C181.648,76.055 182.235,76.522 182.309,77.173C182.382,77.823 181.915,78.41 181.264,78.484L178.349,78.815"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M182.561,79.398C182.634,80.049 182.167,80.636 181.517,80.71L178.795,80.95"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M169.325,54.481L168.012,49.589L164.916,49.432L165.535,54.39L169.325,54.481Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M168.463,51.269L168.012,49.589L164.916,49.432L165.304,52.54L168.463,51.269Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M170.581,70.521C172.215,70.294 173.355,68.785 173.127,67.151L169.405,53.603L165.068,52.58L167.211,67.975C167.438,69.608 168.947,70.749 170.581,70.521Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M170.434,57.348L172.153,63.605"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M163.861,48.624L164.006,44.245C164.021,43.782 164.409,43.42 164.872,43.435C165.335,43.45 165.697,43.838 165.682,44.301L165.537,48.68C165.521,49.143 165.134,49.505 164.671,49.49C164.208,49.475 163.846,49.087 163.861,48.624Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M163.861,48.624L164.006,44.245C164.021,43.782 164.409,43.42 164.872,43.435C165.335,43.45 165.697,43.838 165.682,44.301L165.537,48.68C165.521,49.143 165.134,49.505 164.671,49.49C164.208,49.475 163.846,49.087 163.861,48.624Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M165.517,46.273L167.211,45.045C168.427,44.163 169.291,42.874 169.644,41.414C169.785,40.83 170.373,40.472 170.956,40.613C171.54,40.754 171.899,41.341 171.757,41.925C171.283,43.887 170.121,45.62 168.487,46.805L166.793,48.033C166.6,48.173 166.377,48.24 166.156,48.24C165.819,48.24 165.488,48.084 165.275,47.791C164.922,47.305 165.031,46.625 165.517,46.273Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M165.517,46.273L167.211,45.045C168.427,44.163 169.291,42.874 169.644,41.414C169.785,40.83 170.372,40.472 170.956,40.613C171.54,40.754 171.899,41.341 171.757,41.925C171.283,43.887 170.121,45.62 168.487,46.805L166.793,48.033C166.6,48.173 166.377,48.24 166.156,48.24"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M173.062,45.526L168.208,47.882C167.643,48.156 166.962,47.92 166.688,47.355C166.414,46.79 166.65,46.109 167.215,45.835L172.068,43.479C172.634,43.205 173.314,43.441 173.588,44.006C173.863,44.571 173.627,45.252 173.062,45.526Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M167.215,45.835L172.069,43.479C172.634,43.205 173.314,43.441 173.589,44.006"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M173.302,47.736L168.012,48.797C167.397,48.92 166.797,48.521 166.673,47.905C166.55,47.289 166.949,46.69 167.565,46.566L172.855,45.505C173.471,45.381 174.07,45.781 174.194,46.396C174.317,47.013 173.918,47.612 173.302,47.736Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M168.059,50.233L166.826,50.832C165.815,51.322 164.597,50.901 164.107,49.89C163.616,48.879 164.038,47.661 165.049,47.171L166.282,46.572C167.293,46.081 168.511,46.503 169.001,47.514C169.492,48.525 169.07,49.742 168.059,50.233Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M174.193,46.396C174.317,47.013 173.918,47.612 173.302,47.736L168.012,48.797"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M171.979,45.681L167.912,46.59"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M166.826,50.832C165.815,51.322 164.597,50.901 164.107,49.889C163.725,49.103 163.895,48.192 164.466,47.595"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M168.964,69.33C169.1,69.896 169.669,70.244 170.235,70.108C170.8,69.971 171.148,69.402 171.012,68.837"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M169.945,54.481L166.215,53.47"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M179.411,63.076V57.347"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M176.917,63.076L179.188,58.2L179.381,59.725L176.917,63.076Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M176.661,115.68H196.683"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M196.256,85.995L195.806,84.359"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M197.247,85.177L196.657,84.052"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M181.778,86.636L182.948,85.177"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M174.947,77.441L176.309,82.891C176.535,83.798 177.455,84.349 178.362,84.123C178.821,84.008 179.19,83.715 179.412,83.337L181.151,83.068"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M224.739,164.909H218.354L205.229,92.398H227.733V96.537C227.733,99.222 226.628,101.755 224.739,103.398V164.909Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M224.739,140.683V164.909H218.353L207.948,107.42"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M206.853,101.368L205.229,92.398H227.733V96.537C227.733,99.222 226.628,101.755 224.739,103.398V137.663"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M206.084,97.119C213.367,98.385 220.876,97.563 227.733,94.729V92.398H205.229L206.084,97.119Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M203.062,93.903C211.02,96.684 219.685,96.684 227.643,93.903V56.639C227.643,53.844 226.717,51.265 225.156,49.193C222.895,46.191 219.301,44.251 215.255,44.251H211.298C205.886,54.781 203.062,66.45 203.062,78.29V93.903Z"
      android:strokeWidth="0.135887"
      android:fillColor="#9D9D9D"
      android:strokeColor="#161E24"/>
  <path
      android:pathData="M227.733,77.192V93.967C219.719,96.783 210.985,96.783 202.971,93.967V90.162"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M226.644,51.534C227.344,53.092 227.733,54.82 227.733,56.639V62.592V72.876"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M202.971,85.995V78.29C202.971,66.417 205.807,54.716 211.243,44.16H215.254C218.149,44.16 220.813,45.146 222.93,46.799"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M222.787,93.618C216.211,94.954 209.362,94.499 202.971,92.253"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M227.733,92.253C226.559,92.666 225.369,93.018 224.168,93.31"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M211.563,53.379C211.793,52.678 212.033,51.98 212.283,51.286"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M208.492,66.907C209.036,62.748 209.93,58.645 211.163,54.641"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M207.862,92.253V79.151"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M215.493,51.165C213.223,51.165 211.383,49.325 211.383,47.056V35.54H219.602V47.056C219.602,49.325 217.762,51.165 215.493,51.165Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M219.602,41.019L211.383,45.788V37.083H219.602V41.019Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M206.976,30.11C206.976,30.836 206.387,31.425 205.661,31.425C204.935,31.425 204.346,30.836 204.346,30.11C204.346,29.384 204.935,28.795 205.661,28.795C206.387,28.795 206.976,29.384 206.976,30.11Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M205.329,27.843C205.329,27.026 205.991,26.364 206.808,26.364C207.624,26.364 208.286,27.026 208.286,27.843C208.286,28.66 207.624,29.322 206.808,29.322C205.991,29.322 205.329,28.66 205.329,27.843Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M206.317,33.231C206.317,34.229 205.508,35.038 204.51,35.038C203.512,35.038 202.703,34.229 202.703,33.231C202.703,32.234 203.512,31.424 204.51,31.424C205.508,31.424 206.317,32.234 206.317,33.231Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M206.317,33.231C206.317,34.229 205.508,35.038 204.51,35.038C203.512,35.038 202.703,34.229 202.703,33.231C202.703,32.234 203.512,31.424 204.51,31.424C205.508,31.424 206.317,32.234 206.317,33.231Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M222.487,34.341C221.966,39.291 217.53,42.881 212.58,42.36C207.63,41.839 204.039,37.403 204.561,32.452C205.082,27.502 209.518,23.912 214.468,24.433C219.418,24.955 223.009,29.39 222.487,34.341Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M210.929,31.537C210.929,32.056 210.509,32.475 209.991,32.475C209.473,32.475 209.053,32.056 209.053,31.537"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M214.852,47.738C214.334,47.738 213.914,47.319 213.914,46.8C213.914,46.282 214.334,45.862 214.852,45.862"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M213.539,31.537C213.539,32.056 213.119,32.475 212.601,32.475C212.083,32.475 211.662,32.056 211.662,31.537"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M211.751,29.53C212.357,29.198 213.118,29.42 213.45,30.027"
      android:strokeLineJoin="round"
      android:strokeWidth="0.679436"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M209.215,29.826C209.409,29.162 210.104,28.782 210.768,28.976"
      android:strokeLineJoin="round"
      android:strokeWidth="0.679436"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M204.561,32.453C205.082,27.502 209.518,23.912 214.468,24.433C219.418,24.955 223.009,29.39 222.487,34.341C222.172,37.331 220.429,39.824 218.008,41.216"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M215.255,42.244C214.395,42.412 213.497,42.456 212.58,42.36C208.074,41.885 204.694,38.167 204.518,33.769"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M224.741,33.231C224.741,34.235 223.927,35.049 222.923,35.049C221.919,35.049 221.105,34.235 221.105,33.231C221.105,32.228 221.919,31.414 222.923,31.414C223.927,31.414 224.741,32.228 224.741,33.231Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M222.45,33.758C222.45,33.236 222.874,32.812 223.397,32.812"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M221.105,33.231C221.105,32.228 221.919,31.414 222.923,31.414C223.927,31.414 224.741,32.228 224.741,33.231C224.741,34.235 223.927,35.049 222.923,35.049C222.554,35.049 222.21,34.939 221.924,34.75"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M215.493,27.282C215.493,26.466 216.155,25.804 216.972,25.804C217.788,25.804 218.45,26.466 218.45,27.282C218.45,28.099 217.788,28.761 216.972,28.761C216.155,28.761 215.493,28.099 215.493,27.282Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M218.859,29.322C218.859,28.505 219.521,27.843 220.337,27.843C221.154,27.843 221.816,28.505 221.816,29.322C221.816,30.138 221.154,30.8 220.337,30.8C219.521,30.8 218.859,30.138 218.859,29.322Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M217.692,27.508C217.692,26.93 218.16,26.462 218.738,26.462C219.315,26.462 219.784,26.93 219.784,27.508C219.784,28.085 219.315,28.553 218.738,28.553C218.16,28.553 217.692,28.085 217.692,27.508Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M221.612,28.035C220.254,25.473 217.688,23.612 214.589,23.286C211.531,22.964 208.671,24.212 206.807,26.378L221.612,28.035Z"
      android:fillColor="#9D9D9D"/>
  <path
      android:pathData="M206.881,25.947L206.807,26.364L221.612,28.553L221.7,27.884C221.754,27.473 221.468,27.095 221.058,27.034L208.056,25.096C207.499,25.015 206.978,25.392 206.881,25.947Z"
      android:fillColor="#9D9D9D"/>
  <path
      android:pathData="M210.929,26.894C210.929,27.745 210.239,28.435 209.388,28.435C208.536,28.435 207.846,27.745 207.846,26.894C207.846,26.042 208.536,25.352 209.388,25.352C210.239,25.352 210.929,26.042 210.929,26.894Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M210.629,27.199C210.629,26.738 211.003,26.364 211.464,26.364C211.925,26.364 212.299,26.738 212.299,27.199C212.299,27.661 211.925,28.035 211.464,28.035C211.003,28.035 210.629,27.661 210.629,27.199Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M210.461,25.454L220.673,26.608L221.059,27.034L210.461,25.454Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M211.182,33.231V34.946C211.182,35.354 211.514,35.686 211.922,35.686H213.326"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M222.859,30.524C222.859,31.263 222.259,31.863 221.52,31.863C220.781,31.863 220.182,31.263 220.182,30.524C220.182,29.785 220.781,29.186 221.52,29.186C222.259,29.186 222.859,29.785 222.859,30.524Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M221.52,32.366C221.52,32.846 221.132,33.235 220.652,33.235C220.172,33.235 219.784,32.846 219.784,32.366C219.784,31.887 220.172,31.498 220.652,31.498C221.132,31.498 221.52,31.887 221.52,32.366Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M216.828,41.802H211.303C210.309,41.802 209.503,40.995 209.503,40.001C209.503,38.508 210.713,37.298 212.206,37.298H215.925C217.418,37.298 218.628,38.508 218.628,40.001C218.628,40.995 217.822,41.802 216.828,41.802Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M213.853,41.786C213.853,43.062 212.818,44.097 211.543,44.097C210.267,44.097 209.232,43.062 209.232,41.786C209.232,40.511 210.267,39.476 211.543,39.476C212.818,39.476 213.853,40.511 213.853,41.786Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M212.424,43.357C212.424,42.275 213.301,41.398 214.383,41.398C215.465,41.398 216.342,42.275 216.342,43.357C216.342,44.438 215.465,45.315 214.383,45.315C213.301,45.315 212.424,44.438 212.424,43.357Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M214.854,41.786C214.854,40.65 215.775,39.729 216.911,39.729C218.048,39.729 218.969,40.65 218.969,41.786C218.969,42.923 218.048,43.844 216.911,43.844C215.775,43.844 214.854,42.923 214.854,41.786Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M214.471,39.3C215.314,39.3 216.06,38.884 216.515,38.245"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M219.276,155.843L215.028,117.158"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M220.272,164.909L219.645,159.202"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M215.352,105.85L211.303,103.268"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M222.703,169.194H206.652C205.753,167.088 207.056,164.695 209.313,164.308L216.92,163.003C217.773,164.298 219.222,165.076 220.772,165.069L221.466,165.067L222.703,169.194Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M220.772,165.069L221.466,165.067L222.703,169.194H206.652C205.963,167.579 206.569,165.794 207.907,164.876"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M212.272,163.003L212.924,165.613"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M213.79,162.654L214.442,165.263"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M225.919,169.194H209.868C208.969,167.088 210.272,164.695 212.529,164.308L220.136,163.003C220.989,164.298 222.437,165.076 223.988,165.069L224.682,165.067L225.919,169.194Z"
      android:fillColor="#BEE3F7"/>
  <path
      android:pathData="M223.988,165.069L224.682,165.067L225.919,169.194H209.868C209.179,167.579 209.785,165.794 211.123,164.876"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M215.488,163.003L216.14,165.613"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M217.006,162.654L217.658,165.263"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M222.788,164.914C221.712,164.619 220.763,163.927 220.136,162.946L212.529,164.29"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M221.54,105.533L221.553,106.167"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M221.579,107.408L222.714,161.399"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M222.727,162.02L222.74,162.654"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M218.407,136.968C217.656,136.968 217.048,136.359 217.048,135.609"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M214.256,136.968C213.505,136.968 212.897,136.359 212.897,135.609"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M211.383,47.056V39.477"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M219.602,40.142V47.056C219.602,49.325 217.762,51.165 215.493,51.165C214.365,51.165 213.343,50.71 212.6,49.974"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M223.758,103.357C220.94,103.357 218.656,101.073 218.656,98.255"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M213.689,104.755L214.716,105.533L214.593,113.193L213.689,104.959"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M220.704,99.871V99.206H223.556V102.181"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M234.889,57.381H234.16V56.652C234.16,56.52 234.053,56.413 233.921,56.413C233.788,56.413 233.681,56.52 233.681,56.652V57.381H232.952C232.82,57.381 232.713,57.488 232.713,57.62C232.713,57.753 232.82,57.86 232.952,57.86H233.681V58.589C233.681,58.721 233.788,58.828 233.921,58.828C234.053,58.828 234.16,58.721 234.16,58.589V57.86H234.889C235.021,57.86 235.128,57.753 235.128,57.62C235.128,57.488 235.021,57.381 234.889,57.381Z"
      android:fillColor="#529ED6"/>
  <path
      android:pathData="M157.349,39.045C157.349,39.414 157.05,39.714 156.681,39.714C156.312,39.714 156.013,39.414 156.013,39.045C156.013,38.676 156.312,38.377 156.681,38.377C157.05,38.377 157.349,38.676 157.349,39.045Z"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#E4F4FC"/>
  <path
      android:pathData="M159.692,26.044C159.692,26.464 159.351,26.804 158.931,26.804C158.511,26.804 158.171,26.464 158.171,26.044C158.171,25.624 158.511,25.284 158.931,25.284C159.351,25.284 159.692,25.624 159.692,26.044Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M250.269,29.603C250.269,30.023 249.929,30.363 249.509,30.363C249.089,30.363 248.749,30.023 248.749,29.603C248.749,29.183 249.089,28.843 249.509,28.843C249.929,28.843 250.269,29.183 250.269,29.603Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M165.82,23.896C165.82,24.174 165.595,24.399 165.317,24.399C165.04,24.399 164.815,24.174 164.815,23.896C164.815,23.618 165.04,23.393 165.317,23.393C165.595,23.393 165.82,23.618 165.82,23.896Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M244.734,42.452C244.734,42.73 244.509,42.955 244.231,42.955C243.953,42.955 243.728,42.73 243.728,42.452C243.728,42.174 243.953,41.949 244.231,41.949C244.509,41.949 244.734,42.174 244.734,42.452Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M156.5,32.519H155.948V31.966C155.948,31.866 155.867,31.785 155.767,31.785C155.666,31.785 155.585,31.866 155.585,31.966V32.519H155.033C154.933,32.519 154.852,32.6 154.852,32.7C154.852,32.8 154.933,32.881 155.033,32.881H155.585V33.433C155.585,33.533 155.666,33.614 155.767,33.614C155.867,33.614 155.948,33.533 155.948,33.433V32.881H156.5C156.6,32.881 156.681,32.8 156.681,32.7C156.681,32.6 156.6,32.519 156.5,32.519"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M229.381,13.783H228.829V13.231C228.829,13.13 228.748,13.049 228.648,13.049C228.547,13.049 228.466,13.13 228.466,13.231V13.783H227.914C227.814,13.783 227.733,13.864 227.733,13.964C227.733,14.064 227.814,14.145 227.914,14.145H228.466V14.697C228.466,14.797 228.547,14.878 228.648,14.878C228.748,14.878 228.829,14.797 228.829,14.697V14.145H229.381C229.481,14.145 229.562,14.064 229.562,13.964C229.562,13.864 229.481,13.783 229.381,13.783"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M222.869,56.235C223.563,55.654 224.363,55.194 225.237,54.889"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M221.665,76.743L220.183,62.709C220.023,60.883 220.523,59.162 221.477,57.77"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M227.503,81.986C226.953,83.584 225.212,84.434 223.614,83.885L202.516,74.48L203.026,70.333L222.973,77.193"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M203.809,51.738C203.354,52.362 202.743,52.963 202.031,53.479C199.778,55.114 197.361,55.356 196.409,54.044C195.628,52.968 196.121,51.28 197.662,49.746C197.978,49.432 198.489,49.433 198.804,49.749C199.118,50.065 199.116,50.577 198.801,50.891C197.797,51.89 197.522,52.829 197.716,53.096C197.97,53.447 199.442,53.363 201.083,52.172C201.853,51.614 202.488,50.923 202.823,50.278C203.089,49.766 203.107,49.397 203.006,49.258C202.807,48.983 201.71,48.927 200.234,49.791C199.849,50.016 199.355,49.887 199.129,49.502C198.904,49.117 199.034,48.623 199.418,48.398C201.5,47.179 203.467,47.144 204.313,48.31C204.821,49.01 204.801,49.974 204.256,51.023C204.131,51.263 203.981,51.502 203.809,51.738Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M205.743,54.681L204.169,55.896L203.608,56.64C202.983,57.469 201.905,57.817 200.914,57.511C200.139,57.272 199.53,56.669 199.283,55.897L198.902,54.707L204.123,49.186L205.743,54.681Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M227.503,81.986L227.733,80.68"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M225.592,81.385C226.01,81.802 226.01,82.479 225.592,82.897C225.175,83.314 224.498,83.314 224.08,82.897"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M219.212,50.52C218.348,51.623 217.003,52.332 215.493,52.332C212.884,52.332 210.769,50.218 210.769,47.609"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M220.216,46.24V47.609C220.216,48.261 220.084,48.882 219.845,49.447"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M205.358,52.29H203.297C202.834,52.29 202.459,51.915 202.459,51.452C202.459,50.989 202.834,50.614 203.297,50.614H205.358C205.821,50.614 206.196,50.989 206.196,51.452C206.196,51.915 205.821,52.29 205.358,52.29Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M205.358,53.966H203.297C202.834,53.966 202.459,53.591 202.459,53.128C202.459,52.666 202.834,52.29 203.297,52.29H205.358C205.821,52.29 206.196,52.666 206.196,53.128C206.196,53.591 205.821,53.966 205.358,53.966Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M205.358,55.642H203.297C202.834,55.642 202.459,55.267 202.459,54.804C202.459,54.341 202.834,53.966 203.297,53.966H205.358C205.821,53.966 206.196,54.341 206.196,54.804C206.196,55.267 205.821,55.642 205.358,55.642Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M203.297,52.29C202.834,52.29 202.459,51.915 202.459,51.452C202.459,50.989 202.834,50.614 203.297,50.614H205.358"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M205.358,52.29H204.68"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M205.358,53.966H203.297C202.834,53.966 202.459,53.591 202.459,53.128C202.459,52.666 202.834,52.29 203.297,52.29"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M205.358,55.642H203.297C202.834,55.642 202.459,55.267 202.459,54.804C202.459,54.341 202.834,53.966 203.297,53.966"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M203.1,57.14L204.328,55.642"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M217.105,96.038V94.511"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M212.511,96.038V94.511"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M211.454,96.038V94.511"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M206.139,96.038V93.356"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M208.978,96.038V93.893"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M215.352,96.038V94.511"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M218.52,96.038V94.511"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M221.902,96.038V93.907"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M225.465,94.931V93.091"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M226.824,94.156V93.454"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M220,96.038V94.813"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M199.465,56.704L197.578,64.223"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M218.092,35.686C218.711,35.686 219.213,36.187 219.213,36.806"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M220.815,27.508V28.435"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M218.738,26.734V28.115"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M216.971,26.462V27.843"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M215.939,26.462V27.843"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M213.524,25.902V27.283"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M211.662,25.635V27.016"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M208.286,26.364L221.058,28.471Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M208.286,26.364L221.058,28.471"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M202.971,73.977L204.464,71.239L208.757,72.304L203.714,75.014L202.971,74.535V73.977Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M200.111,69.045C200.725,69.709 201.517,70.325 202.414,70.828C205.255,72.423 208.097,72.378 209.026,70.723C209.788,69.366 208.987,67.472 206.987,65.897C206.577,65.575 205.983,65.645 205.66,66.055C205.338,66.465 205.41,67.059 205.818,67.382C207.121,68.407 207.567,69.461 207.379,69.798C207.13,70.24 205.408,70.342 203.339,69.181C202.368,68.636 201.537,67.919 201.06,67.215C200.681,66.656 200.611,66.229 200.71,66.053C200.904,65.707 202.171,65.493 204.003,66.298C204.481,66.507 205.039,66.29 205.248,65.812C205.458,65.335 205.24,64.777 204.763,64.568C202.178,63.433 199.887,63.659 199.062,65.129C198.566,66.012 198.72,67.13 199.496,68.275C199.673,68.536 199.88,68.794 200.111,69.045Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M201.438,71.954C201.522,71.462 201.191,70.995 200.7,70.912L194.098,69.785C193.607,69.701 193.14,70.032 193.056,70.523C192.972,71.015 193.303,71.482 193.794,71.565L200.396,72.692C200.887,72.776 201.354,72.445 201.438,71.954Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M200.928,70.154C201.076,69.678 200.809,69.172 200.333,69.024L195.669,67.575L193.938,67.037C193.462,66.889 192.955,67.156 192.808,67.632C192.66,68.108 192.926,68.614 193.402,68.762L199.797,70.749C200.274,70.897 200.78,70.631 200.928,70.154Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M201.634,69.375C201.942,68.983 201.873,68.415 201.481,68.107L196.211,63.976C195.818,63.668 195.251,63.737 194.943,64.129V64.129C194.635,64.522 194.704,65.089 195.096,65.397L200.367,69.529C200.759,69.836 201.327,69.768 201.634,69.375Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M198.123,72.304L200.538,73.086L201.182,73.76C201.898,74.51 203.009,74.731 203.958,74.312C204.7,73.985 205.235,73.316 205.391,72.521L205.631,71.294L199.806,66.415L198.123,72.304Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M198.92,71.804L199.083,71.075"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M199.394,69.869L199.536,69.257"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M199.951,68.188L200.092,67.575"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M199.902,64.84L199.586,64.497"
      android:strokeLineJoin="round"
      android:strokeWidth="0.271774"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M198.123,72.304L200.538,73.086L201.182,73.76C201.898,74.51 203.009,74.731 203.958,74.312C204.7,73.985 205.235,73.316 205.391,72.521L205.631,71.664"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M217.629,13.96H216.537V12.868C216.537,12.67 216.377,12.51 216.179,12.51C215.981,12.51 215.821,12.67 215.821,12.868V13.96H214.729C214.531,13.96 214.371,14.12 214.371,14.318C214.371,14.516 214.531,14.677 214.729,14.677H215.821V15.768C215.821,15.967 215.981,16.127 216.179,16.127C216.377,16.127 216.537,15.967 216.537,15.768V14.677H217.629C217.827,14.677 217.988,14.516 217.988,14.318C217.988,14.12 217.827,13.96 217.629,13.96Z"
      android:fillColor="#E43C28"/>
  <path
      android:pathData="M221.776,11.64C221.776,12.059 221.436,12.399 221.017,12.399C220.598,12.399 220.258,12.059 220.258,11.64C220.258,11.221 220.598,10.881 221.017,10.881C221.436,10.881 221.776,11.221 221.776,11.64Z"
      android:fillColor="#E43C28"/>
  <path
      android:pathData="M211.226,5.107C211.326,2.667 209.246,0.685 206.804,0.685C205.547,0.685 204.414,1.213 203.609,2.056C202.804,1.213 201.672,0.685 200.415,0.685C197.973,0.685 196.087,2.667 195.993,5.107C195.755,11.298 203.603,13.846 203.603,13.846C203.603,13.846 210.945,11.908 211.226,5.107"
      android:fillColor="#E43C28"/>
  <path
      android:pathData="M202.48,16.494L203.608,14.092L204.737,16.494H202.48Z"
      android:fillColor="#E43C28"/>
  <path
      android:pathData="M209.429,3.698C208.84,2.893 207.865,2.368 206.794,2.368"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M206.759,10.88C208.233,9.945 209.895,8.297 210.007,5.581"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#FFFCEE"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M199.547,11.678C201.53,13.173 203.603,13.846 203.603,13.846C203.603,13.846 210.945,11.908 211.226,5.107C211.326,2.667 209.246,0.685 206.804,0.685C205.547,0.685 204.414,1.213 203.609,2.056C202.804,1.213 201.672,0.685 200.415,0.685C197.973,0.685 196.087,2.667 195.993,5.107C195.926,6.849 196.5,8.303 197.359,9.493"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M198.127,10.418C198.366,10.671 198.616,10.909 198.872,11.132"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M203.045,2.855L203.609,2.056"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M202.48,16.494L203.608,14.092L204.737,16.494H202.48Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M202.39,14.722L204.56,14.093"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M202.299,13.846L204.737,14.926"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M223.231,135.912H224.739"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M223.714,135.066H224.739"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M202.516,54.372C198.531,50.398 196.647,43.912 198.424,38.486L202.483,26.093C203.373,23.377 203.591,19.738 203.526,16.709"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M203.596,68.893C203.782,68.343 204.079,67.823 204.486,67.368C207.32,64.203 207.336,59.56 204.84,56.391L204.249,55.642"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M201.725,77.565L202.253,74.416"
      android:strokeLineJoin="round"
      android:strokeWidth="0.407661"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M200.596,49.124C200.596,49.564 200.239,49.921 199.799,49.921C199.358,49.921 199.002,49.564 199.002,49.124C199.002,48.684 199.358,48.327 199.799,48.327C200.239,48.327 200.596,48.684 200.596,49.124Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M207.271,66.585C207.271,67.103 206.851,67.523 206.333,67.523C205.815,67.523 205.395,67.103 205.395,66.585C205.395,66.067 205.815,65.646 206.333,65.646C206.851,65.646 207.271,66.067 207.271,66.585Z"
      android:fillColor="#F3D5CB"/>
</vector>
