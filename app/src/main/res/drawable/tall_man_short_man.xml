<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="185dp"
    android:height="185dp"
    android:viewportWidth="185"
    android:viewportHeight="185">
  <group>
    <clip-path android:pathData="M0,0h185v185h-185z M 0,0"/>
    <path
        android:pathData="M0,0h185v185h-185z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M92.5,92.5m-92.5,0a92.5,92.5 0,1 1,185 0a92.5,92.5 0,1 1,-185 0"
        android:fillColor="#E3F3FF"/>
    <path
        android:pathData="M103.354,128.842L107.032,151.369C106.301,151.635 105.769,152.344 105.769,153.186C105.769,153.939 106.19,154.581 106.81,154.891C107.342,155.179 107.74,155.689 107.829,156.287L110.044,173.143C110.288,175.026 111.905,176.444 113.787,176.444C115.87,176.444 117.575,174.76 117.575,172.656V128.842H103.354Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M117.553,171.349L109.911,172.257L110.022,173.055H117.553V171.349Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M109.269,159.344C109.668,159.344 109.978,159.654 109.978,160.053"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M114.297,159.344C114.696,159.344 115.006,159.654 115.006,160.053"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M112.126,157.727C112.193,157.35 112.569,157.084 112.968,157.151"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M114.23,154.914C114.297,154.537 114.673,154.271 115.072,154.338"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M110.842,154.758C110.531,155.002 110.066,154.936 109.845,154.603"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M107.408,153.895C107.098,154.138 106.633,154.072 106.411,153.74"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#E5B6AA"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M113.189,152.499C112.879,152.743 112.414,152.676 112.192,152.344"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M110.288,168.625C110.686,168.625 110.996,168.935 110.996,169.334"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M115.316,168.625C115.715,168.625 116.025,168.935 116.025,169.334"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M113.167,167.008C113.234,166.631 113.61,166.365 114.009,166.432"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M115.25,164.195C115.316,163.818 115.693,163.552 116.091,163.619"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M111.86,164.062C111.55,164.305 111.085,164.239 110.864,163.907"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M114.23,161.78C113.92,162.024 113.455,161.957 113.234,161.625"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M120.876,168.625C121.274,168.625 121.585,168.935 121.585,169.334"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M125.904,168.625C126.303,168.625 126.613,168.935 126.613,169.334"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M123.755,167.008C123.822,166.631 124.198,166.365 124.597,166.432"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M125.86,164.195C125.926,163.818 126.303,163.552 126.701,163.619"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M122.449,164.062C122.139,164.305 121.673,164.239 121.452,163.907"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M124.819,161.78C124.508,162.024 124.043,161.957 123.822,161.625"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M119.037,184.795H97.551L97.662,184.33C98.57,180.83 101.361,178.15 104.883,177.374L107.386,176.821C107.452,176.798 107.541,176.776 107.607,176.776C108.936,176.4 109.845,175.137 109.867,173.742L109.889,172.811H117.553L119.037,184.795Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M111.307,180.21C111.417,180.786 111.063,181.339 110.487,181.45C109.911,181.561 109.357,181.206 109.247,180.631C109.136,180.055 109.313,175.78 109.313,175.78C109.313,175.78 111.196,179.634 111.307,180.21Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M107.496,180.32C107.186,180.808 106.522,180.941 106.035,180.631C105.547,180.32 105.414,179.656 105.724,179.169C106.035,178.681 109.202,175.78 109.202,175.78C109.202,175.78 107.807,179.833 107.496,180.32Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M76.153,74.949L70.239,94.176L70.062,96.059C69.397,103.324 70.461,110.656 73.163,117.434L75.6,123.57C77.217,127.624 77.593,132.076 76.596,136.307C76.596,136.329 76.574,136.373 76.574,136.395C75.378,141.402 74.293,145.677 75.445,148.268C75.688,148.844 76.729,150.085 77.46,150.661C79.543,152.322 80.871,154.758 81.226,157.372L81.248,157.505C81.558,159.787 81.093,162.09 79.985,164.106C78.9,166.077 78.213,168.071 79.232,169.821C80.362,171.792 85.767,171.261 85.767,171.261H93.475L97.529,102.173L93.475,58.004L76.153,74.949Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M82.71,58.026L65.366,74.971L59.452,94.198L59.275,96.081C58.92,99.913 59.053,103.745 59.651,107.533C59.851,108.796 60.471,109.925 61.401,110.767C62.686,111.941 63.262,113.713 62.797,115.396C62.464,116.593 62.531,117.855 62.996,119.007L64.835,123.614C66.451,127.668 66.828,132.12 65.831,136.351C65.831,136.373 65.809,136.417 65.809,136.44C64.613,141.446 63.528,145.721 64.679,148.312C64.923,148.888 65.964,150.129 66.695,150.705C68.777,152.366 70.106,154.803 70.461,157.416L70.483,157.549C70.793,159.831 70.328,162.134 69.22,164.15C68.135,166.122 67.448,168.115 68.467,169.865C69.597,171.836 75.002,171.305 75.002,171.305H82.71L86.764,102.217L82.71,58.026Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M97.152,97.743L93.52,58.004L83.618,67.684L82.732,58.004L65.388,74.949L59.474,94.176L59.297,96.059C59.208,96.945 59.164,97.853 59.142,98.739C71.303,104.366 85.191,104.033 97.152,97.743Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M107.165,63.94C107.165,54.925 102.867,46.574 95.801,41.302C92.678,37.182 87.76,34.546 82.201,34.546H75.356C65.942,34.546 58.3,42.188 58.3,51.602V96.015L58.61,96.17C71.435,102.97 86.498,102.926 99.279,96.015V78.826L107.187,80.288V63.94H107.165Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M88.492,72.579L82.865,69.168L74.847,83.832C73.385,86.512 70.461,88.063 67.382,87.752C63.461,87.354 60.515,83.942 60.515,80V75.57H58.278V91.917C60.25,93.6 62.642,94.774 65.278,95.284C65.322,95.439 65.411,95.616 65.521,95.749C65.964,96.28 66.762,96.369 67.293,95.926C67.426,95.815 67.515,95.682 67.604,95.549C71.901,95.704 75.954,94.065 78.923,91.164C80.274,89.835 81.337,88.24 82.112,86.534L88.492,72.579Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M73.206,95.903C73.379,95.73 73.379,95.45 73.206,95.277C73.033,95.104 72.752,95.104 72.579,95.277C72.406,95.45 72.406,95.73 72.579,95.903C72.752,96.076 73.033,96.076 73.206,95.903Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M72.038,95.064C72.124,94.977 72.124,94.837 72.038,94.751C71.951,94.664 71.811,94.664 71.725,94.751C71.638,94.837 71.638,94.977 71.725,95.064C71.811,95.15 71.951,95.15 72.038,95.064Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M66.939,91.297C67.183,91.54 67.537,91.673 67.869,91.673C67.98,91.673 68.113,91.651 68.224,91.629C68.556,91.54 68.844,91.319 69.021,91.009L69.508,91.496C69.353,91.784 69.376,92.138 69.619,92.382C69.686,92.448 69.686,92.537 69.663,92.604C69.331,93.401 69.508,94.287 70.084,94.863L71.701,96.48C72.255,97.034 73.075,97.211 73.828,96.945C73.983,96.879 74.16,96.923 74.271,97.034C74.404,97.166 74.581,97.233 74.78,97.233H74.802C75.002,97.233 75.179,97.144 75.334,96.989C75.644,96.679 75.644,96.214 75.356,95.926C75.245,95.815 75.201,95.638 75.268,95.483C75.534,94.708 75.356,93.888 74.802,93.357L73.186,91.74C72.61,91.164 71.724,91.009 70.926,91.319C70.838,91.363 70.749,91.341 70.705,91.274C70.461,91.031 70.107,91.009 69.819,91.164L69.088,90.433L68.29,89.635C68.202,89.547 68.069,89.547 67.98,89.635C67.892,89.724 67.892,89.857 67.98,89.945L68.091,90.056C68.002,90.056 67.892,90.078 67.803,90.101C67.338,90.233 66.983,90.588 66.873,91.053C66.873,91.164 66.895,91.23 66.939,91.297ZM69.951,91.629C70.084,91.496 70.284,91.496 70.395,91.607C70.572,91.784 70.86,91.85 71.103,91.74C71.325,91.651 71.546,91.607 71.768,91.607C72.189,91.607 72.587,91.762 72.875,92.05L74.492,93.667C74.913,94.088 75.046,94.73 74.847,95.35C74.736,95.682 74.802,96.015 75.046,96.258C75.157,96.369 75.157,96.568 75.024,96.701C74.958,96.768 74.869,96.812 74.802,96.812C74.714,96.812 74.647,96.79 74.603,96.746C74.359,96.502 74.027,96.436 73.695,96.546C73.097,96.746 72.432,96.613 72.011,96.192L70.395,94.575C69.951,94.132 69.819,93.423 70.084,92.803C70.195,92.559 70.129,92.271 69.951,92.094C69.819,91.961 69.841,91.762 69.951,91.629ZM67.36,91.075C67.471,90.831 67.67,90.632 67.936,90.566C68.202,90.499 68.467,90.544 68.689,90.699C68.578,90.942 68.379,91.142 68.113,91.208C67.847,91.274 67.581,91.23 67.36,91.075Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M78.989,42.853C83.042,42.853 86.387,39.464 86.742,35.144C85.302,34.746 83.773,34.524 82.201,34.524H75.356C73.938,34.524 72.543,34.701 71.236,35.034C71.524,39.419 74.891,42.853 78.989,42.853Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M92.944,22.164C94.277,22.164 95.358,21.083 95.358,19.75C95.358,18.416 94.277,17.335 92.944,17.335C91.61,17.335 90.529,18.416 90.529,19.75C90.529,21.083 91.61,22.164 92.944,22.164Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M78.767,14.301C80.345,14.301 81.625,13.021 81.625,11.443C81.625,9.865 80.345,8.586 78.767,8.586C77.189,8.586 75.91,9.865 75.91,11.443C75.91,13.021 77.189,14.301 78.767,14.301Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M105.924,184.95C105.924,183.599 105.038,182.38 103.687,181.849L94.45,178.172C92.501,177.396 91.238,175.514 91.238,173.431V167.894H84.239L82.91,184.95H105.924Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M91.238,170.929L84.083,169.976L83.729,174.561L91.238,172.435V170.929Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M100.852,184.817C100.852,183.466 101.383,182.225 102.247,181.295"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M92.944,175.248C93.387,175.735 94.029,178.194 94.029,178.194C94.029,178.194 91.637,177.308 91.194,176.821C90.751,176.333 90.795,175.602 91.282,175.159C91.748,174.738 92.501,174.783 92.944,175.248Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M96.975,177.131C96.488,177.574 94.029,178.216 94.029,178.216C94.029,178.216 94.915,175.824 95.402,175.381C95.89,174.938 96.621,174.982 97.064,175.469C97.507,175.957 97.462,176.688 96.975,177.131Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M93.918,184.95C93.918,183.599 93.032,182.38 91.681,181.849L82.444,178.172C80.495,177.396 79.232,175.514 79.232,173.431V167.894H72.233L70.904,184.95H93.918Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M79.232,170.929L72.078,169.976L71.723,174.561L79.232,172.435V170.929Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M88.846,184.817C88.846,183.466 89.378,182.225 90.241,181.295"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M80.938,175.248C81.381,175.735 82.023,178.194 82.023,178.194C82.023,178.194 79.631,177.308 79.188,176.821C78.745,176.333 78.789,175.602 79.277,175.159C79.764,174.738 80.495,174.783 80.938,175.248Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M84.969,177.131C84.482,177.574 82.023,178.216 82.023,178.216C82.023,178.216 82.91,175.824 83.397,175.381C83.884,174.938 84.615,174.982 85.058,175.469C85.501,175.935 85.457,176.688 84.969,177.131Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M93.475,183.355C93.077,182.69 92.456,182.159 91.681,181.849L82.444,178.172"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M30.988,184.861H134.831"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M94.406,171.659H78.878L79.343,167.894H94.871L94.406,171.659Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M78.989,38.799C76.242,38.799 74.005,36.562 74.005,33.816V21.677H83.973V33.816C83.951,36.562 81.735,38.799 78.989,38.799Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M83.951,32.354V21.677H73.983V25.974C74.846,27.968 76.331,29.696 78.346,30.869C80.118,31.933 82.068,32.398 83.951,32.354Z"
        android:fillColor="#B76452"/>
    <path
        android:pathData="M74.005,35.189V21.677H83.951V35.189"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M92.974,24.507C95.958,19.421 94.254,12.879 89.168,9.896C84.082,6.912 77.541,8.616 74.557,13.702C71.573,18.788 73.277,25.329 78.363,28.313C83.449,31.296 89.991,29.592 92.974,24.507Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M79.454,28.876C79.454,28.854 79.432,28.832 79.41,28.81C84.216,30.67 89.776,28.832 92.479,24.269C95.425,19.24 93.785,12.795 88.846,9.76C88.912,9.782 88.957,9.826 89.023,9.849C94.118,12.839 95.801,19.373 92.833,24.446C90.108,29.098 84.394,30.914 79.543,28.92C79.476,28.92 79.454,28.898 79.454,28.876C79.454,28.898 79.454,28.898 79.454,28.876Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M79.454,28.898C79.432,28.898 79.41,28.876 79.454,28.898V28.898Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M75.334,25.664C72.72,22.319 72.277,17.579 74.559,13.703C77.549,8.608 84.084,6.925 89.156,9.893C94.229,12.861 95.934,19.418 92.966,24.49C89.998,29.563 83.441,31.268 78.369,28.3"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.552,22.895C83.22,23.471 83.508,24.246 84.172,24.645C84.859,25.044 85.678,24.911 86.011,24.335L87.295,22.142"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M88.159,17.247C87.65,17.646 86.941,17.734 86.321,17.402"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M90.463,19.838C89.931,19.484 89.621,18.82 89.754,18.155"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M80.318,25.265C78.812,24.379 78.324,22.452 79.188,20.968"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.552,22.895C83.353,23.25 82.887,23.36 82.533,23.161C82.178,22.962 82.068,22.497 82.267,22.142"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M87.295,25.088C87.096,25.442 86.631,25.553 86.276,25.354C85.922,25.154 85.811,24.689 86.011,24.335"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M76.404,14.213C76.973,13.242 76.648,11.994 75.677,11.424C74.706,10.855 73.458,11.18 72.888,12.151C72.319,13.122 72.644,14.37 73.615,14.94C74.586,15.509 75.834,15.184 76.404,14.213Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M74.005,12.484C74.47,12.551 74.78,12.994 74.714,13.437"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M74.869,15.209C74.448,15.253 74.005,15.187 73.606,14.943C72.632,14.367 72.322,13.127 72.875,12.152C73.429,11.177 74.692,10.867 75.666,11.421C76.641,11.997 76.951,13.238 76.397,14.212"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M80.362,12.086C81.94,12.086 83.22,10.806 83.22,9.228C83.22,7.65 81.94,6.371 80.362,6.371C78.784,6.371 77.505,7.65 77.505,9.228C77.505,10.806 78.784,12.086 80.362,12.086Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M80.362,14.079C81.365,14.079 82.179,13.266 82.179,12.263C82.179,11.26 81.365,10.446 80.362,10.446C79.359,10.446 78.546,11.26 78.546,12.263C78.546,13.266 79.359,14.079 80.362,14.079Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M83.951,11.067C85.26,11.067 86.321,10.005 86.321,8.697C86.321,7.388 85.26,6.326 83.951,6.326C82.642,6.326 81.581,7.388 81.581,8.697C81.581,10.005 82.642,11.067 83.951,11.067Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M83.951,13.946C85.26,13.946 86.321,12.826 86.321,11.443C86.321,10.061 85.26,8.94 83.951,8.94C82.642,8.94 81.581,10.061 81.581,11.443C81.581,12.826 82.642,13.946 83.951,13.946Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M91.138,9.645C91.201,8.068 89.974,6.739 88.397,6.676C86.82,6.614 85.491,7.841 85.428,9.418C85.365,10.995 86.592,12.324 88.169,12.387C89.746,12.45 91.075,11.222 91.138,9.645Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M86.852,13.437C87.648,13.437 88.292,12.792 88.292,11.997C88.292,11.202 87.648,10.557 86.852,10.557C86.057,10.557 85.413,11.202 85.413,11.997C85.413,12.792 86.057,13.437 86.852,13.437Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M90.108,15.253C91.687,15.253 92.966,13.974 92.966,12.396C92.966,10.818 91.687,9.538 90.108,9.538C88.53,9.538 87.251,10.818 87.251,12.396C87.251,13.974 88.53,15.253 90.108,15.253Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M92.944,13.725C93.678,13.725 94.273,13.13 94.273,12.396C94.273,11.662 93.678,11.067 92.944,11.067C92.21,11.067 91.615,11.662 91.615,12.396C91.615,13.13 92.21,13.725 92.944,13.725Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M92.102,16.715C92.995,16.715 93.719,15.991 93.719,15.098C93.719,14.205 92.995,13.481 92.102,13.481C91.209,13.481 90.485,14.205 90.485,15.098C90.485,15.991 91.209,16.715 92.102,16.715Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M94.605,16.206C95.523,16.206 96.267,15.462 96.267,14.544C96.267,13.627 95.523,12.883 94.605,12.883C93.688,12.883 92.944,13.627 92.944,14.544C92.944,15.462 93.688,16.206 94.605,16.206Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M94.45,19.838C95.783,19.838 96.864,18.757 96.864,17.424C96.864,16.09 95.783,15.009 94.45,15.009C93.116,15.009 92.035,16.09 92.035,17.424C92.035,18.757 93.116,19.838 94.45,19.838Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M87.273,20.237C87.007,20.725 86.409,20.924 85.922,20.658C85.435,20.392 85.235,19.794 85.501,19.307"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M90.308,21.876C90.042,22.364 89.444,22.563 88.957,22.297C88.469,22.031 88.27,21.433 88.536,20.946"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M66.828,93.933C58.832,93.246 52.763,86.423 52.763,78.405V74.019H60.515V78.449C60.515,82.392 63.439,85.803 67.382,86.202C70.461,86.512 73.385,84.939 74.847,82.281L82.865,67.617L88.492,71.029L82.134,84.961C81.359,86.689 80.296,88.262 78.945,89.591C75.777,92.67 71.391,94.331 66.828,93.933Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M90.463,71.472L90.02,72.623C88.979,75.348 85.922,76.721 83.198,75.68C80.473,74.639 79.1,71.582 80.141,68.858L80.584,67.706C81.625,64.981 84.682,63.608 87.406,64.649C90.131,65.69 91.504,68.747 90.463,71.472Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M88.89,59.068L83.906,67.95L81.381,66.266L86.985,57.894C87.34,57.384 88.026,57.229 88.558,57.583C89.023,57.894 89.178,58.558 88.89,59.068Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M92.301,60.95L86.387,69.257L84.061,67.308L90.529,59.599C90.928,59.112 91.637,59.067 92.124,59.466C92.567,59.821 92.656,60.485 92.301,60.95Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M94.804,63.498L87.96,71.051L85.878,68.836L93.187,61.903C93.63,61.482 94.361,61.504 94.782,61.947C95.203,62.39 95.203,63.055 94.804,63.498Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M96.311,66.576L88.536,73.155L86.764,70.697L94.938,64.804C95.447,64.45 96.156,64.561 96.51,65.048C96.842,65.535 96.754,66.2 96.311,66.576Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M83.065,70.431L80.761,73.155L76.464,66.621C76.109,66.067 76.265,65.336 76.796,64.981C77.283,64.649 77.948,64.738 78.347,65.181L83.065,70.431Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M85.058,64.317C85.833,64.251 86.631,64.361 87.406,64.649C89.267,65.358 90.507,67.019 90.773,68.858"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M90.751,70.873L90.131,72.358"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M86.543,52.472C86.716,52.299 86.716,52.018 86.543,51.845C86.37,51.672 86.089,51.672 85.916,51.845C85.743,52.018 85.743,52.299 85.916,52.472C86.089,52.645 86.37,52.645 86.543,52.472Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M87.408,51.621C87.494,51.534 87.494,51.394 87.408,51.307C87.321,51.221 87.181,51.221 87.094,51.307C87.008,51.394 87.008,51.534 87.094,51.621C87.181,51.707 87.321,51.707 87.408,51.621Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M92.235,47.638C92.124,47.173 91.748,46.818 91.305,46.685C91.216,46.663 91.128,46.641 91.017,46.641L91.128,46.53C91.216,46.442 91.216,46.309 91.128,46.22C91.039,46.131 90.906,46.131 90.817,46.22L90.02,47.017L89.289,47.748C89.001,47.593 88.647,47.616 88.403,47.859C88.337,47.926 88.27,47.926 88.182,47.903C87.384,47.571 86.498,47.748 85.922,48.324L84.305,49.941C83.751,50.495 83.574,51.315 83.84,52.068C83.906,52.223 83.862,52.4 83.751,52.511C83.463,52.799 83.485,53.264 83.773,53.574C83.906,53.707 84.106,53.796 84.305,53.818H84.327C84.527,53.818 84.704,53.751 84.837,53.618C84.948,53.508 85.125,53.463 85.28,53.53C86.055,53.796 86.875,53.618 87.406,53.065L89.023,51.448C89.599,50.872 89.754,49.986 89.444,49.188C89.422,49.1 89.422,49.011 89.488,48.967C89.732,48.723 89.754,48.369 89.599,48.081L90.087,47.593C90.264,47.881 90.552,48.125 90.884,48.214C90.995,48.236 91.128,48.258 91.238,48.258C91.593,48.258 91.925,48.125 92.169,47.881C92.235,47.815 92.257,47.726 92.235,47.638ZM89.178,48.634C89.001,48.812 88.935,49.1 89.045,49.343C89.311,49.964 89.178,50.672 88.735,51.115L87.118,52.732C86.697,53.153 86.055,53.286 85.435,53.087C85.103,52.976 84.77,53.042 84.527,53.286C84.482,53.33 84.394,53.375 84.327,53.353C84.239,53.353 84.15,53.308 84.106,53.242C83.973,53.109 83.973,52.91 84.084,52.799C84.327,52.555 84.394,52.223 84.283,51.891C84.084,51.27 84.216,50.628 84.637,50.207L86.254,48.59C86.542,48.302 86.941,48.147 87.362,48.147C87.583,48.147 87.805,48.191 88.026,48.28C88.27,48.391 88.558,48.324 88.735,48.147C88.846,48.036 89.045,48.036 89.178,48.169C89.289,48.324 89.311,48.524 89.178,48.634ZM91.017,47.771C90.751,47.704 90.552,47.505 90.441,47.261C90.662,47.106 90.928,47.062 91.194,47.128C91.46,47.195 91.659,47.394 91.77,47.638C91.548,47.793 91.283,47.837 91.017,47.771Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M68.935,79.414C69.108,79.241 69.108,78.96 68.935,78.787C68.762,78.614 68.481,78.614 68.308,78.787C68.135,78.96 68.135,79.241 68.308,79.414C68.481,79.587 68.762,79.587 68.935,79.414Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M69.799,78.562C69.886,78.476 69.886,78.336 69.799,78.249C69.713,78.163 69.573,78.163 69.486,78.249C69.4,78.336 69.4,78.476 69.486,78.562C69.573,78.649 69.713,78.649 69.799,78.562Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M74.647,74.573C74.537,74.108 74.16,73.753 73.717,73.62C73.629,73.598 73.54,73.576 73.429,73.576L73.54,73.465C73.629,73.377 73.629,73.244 73.54,73.155C73.451,73.066 73.318,73.066 73.23,73.155L72.432,73.952L71.701,74.684C71.413,74.528 71.059,74.551 70.815,74.794C70.749,74.861 70.683,74.861 70.594,74.839C69.796,74.506 68.91,74.684 68.335,75.259L66.717,76.876C66.164,77.43 65.987,78.25 66.252,79.003C66.319,79.158 66.274,79.335 66.164,79.446C65.876,79.734 65.898,80.199 66.186,80.509C66.319,80.642 66.518,80.731 66.717,80.753H66.74C66.939,80.753 67.116,80.686 67.249,80.553C67.36,80.443 67.537,80.398 67.692,80.465C68.467,80.731 69.287,80.553 69.819,80L71.436,78.383C72.011,77.807 72.167,76.921 71.856,76.123C71.834,76.035 71.834,75.946 71.901,75.902C72.144,75.658 72.166,75.304 72.011,75.016L72.499,74.528C72.676,74.816 72.964,75.06 73.296,75.149C73.407,75.171 73.54,75.193 73.651,75.193C74.005,75.193 74.337,75.06 74.581,74.816C74.647,74.75 74.67,74.661 74.647,74.573ZM71.591,75.57C71.413,75.747 71.347,76.035 71.458,76.278C71.724,76.898 71.591,77.607 71.148,78.05L69.531,79.667C69.11,80.088 68.467,80.221 67.847,80.022C67.515,79.911 67.183,79.978 66.939,80.221C66.895,80.266 66.806,80.31 66.74,80.288C66.651,80.288 66.562,80.243 66.518,80.177C66.385,80.044 66.385,79.845 66.496,79.734C66.74,79.49 66.806,79.158 66.695,78.826C66.496,78.205 66.629,77.563 67.05,77.142L68.667,75.525C68.955,75.237 69.353,75.082 69.774,75.082C69.996,75.082 70.217,75.127 70.439,75.215C70.683,75.326 70.97,75.259 71.148,75.082C71.258,74.971 71.458,74.971 71.591,75.104C71.701,75.259 71.701,75.459 71.591,75.57ZM73.407,74.706C73.141,74.639 72.942,74.44 72.831,74.196C73.053,74.041 73.318,73.997 73.584,74.063C73.85,74.13 74.049,74.329 74.16,74.573C73.961,74.728 73.673,74.772 73.407,74.706Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M76.263,62.378C76.436,62.205 76.436,61.925 76.263,61.752C76.09,61.579 75.81,61.579 75.637,61.752C75.464,61.925 75.464,62.205 75.637,62.378C75.81,62.551 76.09,62.551 76.263,62.378Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M75.111,61.523C75.198,61.436 75.198,61.296 75.111,61.21C75.024,61.123 74.884,61.123 74.798,61.21C74.711,61.296 74.711,61.436 74.798,61.523C74.884,61.609 75.024,61.609 75.111,61.523Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M70.018,57.761C70.261,58.004 70.616,58.137 70.948,58.137C71.059,58.137 71.192,58.115 71.302,58.093C71.635,58.004 71.923,57.783 72.1,57.472L72.587,57.96C72.432,58.248 72.454,58.602 72.698,58.846C72.764,58.912 72.764,59.001 72.742,59.067C72.41,59.865 72.587,60.751 73.163,61.327L74.78,62.944C75.334,63.498 76.153,63.675 76.907,63.409C77.062,63.342 77.239,63.387 77.35,63.498C77.482,63.63 77.66,63.697 77.859,63.697H77.881C78.08,63.697 78.258,63.608 78.413,63.453C78.723,63.143 78.723,62.678 78.435,62.39C78.324,62.279 78.28,62.102 78.346,61.947C78.612,61.172 78.435,60.352 77.881,59.82L76.264,58.203C75.688,57.628 74.802,57.472 74.005,57.783C73.916,57.827 73.827,57.805 73.783,57.738C73.54,57.495 73.185,57.472 72.897,57.628L72.166,56.897L71.369,56.099C71.28,56.011 71.147,56.011 71.059,56.099C70.97,56.188 70.97,56.321 71.059,56.409L71.17,56.52C71.081,56.52 70.97,56.542 70.882,56.564C70.416,56.697 70.062,57.052 69.951,57.517C69.929,57.628 69.951,57.716 70.018,57.761ZM73.03,58.115C73.163,57.982 73.362,57.982 73.473,58.093C73.65,58.27 73.938,58.336 74.182,58.226C74.404,58.137 74.625,58.093 74.846,58.093C75.267,58.093 75.666,58.248 75.954,58.536L77.571,60.153C77.992,60.574 78.125,61.216 77.925,61.836C77.815,62.168 77.881,62.501 78.125,62.744C78.235,62.855 78.235,63.055 78.103,63.187C78.036,63.254 77.948,63.298 77.881,63.298C77.793,63.298 77.726,63.276 77.682,63.232C77.438,62.988 77.106,62.922 76.774,63.032C76.175,63.232 75.511,63.099 75.09,62.678L73.473,61.061C73.03,60.618 72.897,59.909 73.163,59.289C73.274,59.045 73.207,58.757 73.03,58.58C72.897,58.425 72.897,58.226 73.03,58.115ZM70.416,57.539C70.527,57.295 70.727,57.096 70.992,57.029C71.258,56.963 71.524,57.007 71.745,57.162C71.635,57.406 71.435,57.605 71.17,57.672C70.926,57.738 70.638,57.694 70.416,57.539Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M91.013,89.346C91.187,89.174 91.187,88.893 91.013,88.72C90.84,88.547 90.56,88.547 90.387,88.72C90.214,88.893 90.214,89.174 90.387,89.346C90.56,89.52 90.84,89.52 91.013,89.346Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M89.846,88.507C89.932,88.421 89.932,88.28 89.846,88.194C89.759,88.107 89.619,88.107 89.532,88.194C89.446,88.28 89.446,88.421 89.532,88.507C89.619,88.593 89.759,88.593 89.846,88.507Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M84.748,84.74C84.992,84.984 85.346,85.117 85.678,85.117C85.789,85.117 85.922,85.094 86.033,85.072C86.365,84.984 86.653,84.762 86.83,84.452L87.317,84.939C87.162,85.227 87.184,85.582 87.428,85.825C87.494,85.892 87.494,85.98 87.472,86.047C87.14,86.844 87.317,87.73 87.893,88.306L89.51,89.923C90.064,90.477 90.884,90.654 91.637,90.388C91.792,90.322 91.969,90.366 92.08,90.477C92.213,90.61 92.39,90.676 92.589,90.676H92.611C92.811,90.676 92.988,90.588 93.143,90.433C93.453,90.123 93.453,89.658 93.165,89.369C93.054,89.259 93.01,89.082 93.076,88.926C93.342,88.151 93.165,87.332 92.611,86.8L90.994,85.205C90.418,84.629 89.532,84.474 88.735,84.784C88.646,84.829 88.558,84.807 88.513,84.74C88.27,84.496 87.915,84.474 87.627,84.629L86.896,83.898L86.099,83.101C86.01,83.012 85.878,83.012 85.789,83.101C85.7,83.189 85.7,83.322 85.789,83.411L85.9,83.522C85.811,83.522 85.7,83.544 85.612,83.566C85.147,83.699 84.792,84.053 84.681,84.519C84.681,84.607 84.703,84.696 84.748,84.74ZM87.76,85.094C87.893,84.962 88.093,84.962 88.203,85.072C88.381,85.249 88.669,85.316 88.912,85.205C89.134,85.117 89.355,85.072 89.577,85.072C89.998,85.072 90.396,85.227 90.684,85.515L92.301,87.132C92.722,87.553 92.855,88.196 92.656,88.816C92.545,89.148 92.611,89.48 92.855,89.724C92.966,89.835 92.966,90.034 92.833,90.167C92.766,90.233 92.678,90.278 92.611,90.278C92.523,90.278 92.456,90.256 92.412,90.211C92.168,89.968 91.836,89.901 91.504,90.012C90.906,90.211 90.241,90.078 89.82,89.658L88.203,88.04C87.76,87.598 87.627,86.889 87.893,86.268C88.004,86.025 87.938,85.737 87.76,85.56C87.627,85.405 87.65,85.205 87.76,85.094ZM85.169,84.519C85.28,84.275 85.479,84.076 85.745,84.009C86.01,83.943 86.276,83.987 86.498,84.142C86.387,84.386 86.188,84.585 85.922,84.651C85.656,84.718 85.39,84.674 85.169,84.519Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M84.637,75.725C84.97,75.902 85.103,76.323 84.947,76.655C84.77,76.987 84.349,77.12 84.017,76.965"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M86.188,112.008L83.397,159.698"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.951,112.008C85.413,112.008 86.808,111.52 87.96,110.612L90.507,108.574"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M82.245,140.538C81.359,141.424 79.897,141.424 79.011,140.538"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M93.741,140.538C92.855,141.424 91.393,141.424 90.507,140.538"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M82.422,171.659H66.894L67.337,167.894H82.865L82.422,171.659Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M83.729,93.179V95.062"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M68.755,84.031V85.936"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M93.165,78.361V79.557"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M93.298,55.678V56.874"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M73.141,46.264V47.46"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M81.669,56.276V57.472"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M68.268,65.602V66.798"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M68.268,39.796V40.992"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M98.592,46.264V47.46"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M69.686,168.868L69.353,171.659"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M79.055,168.868L78.723,171.659"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M71.878,169.71L71.546,171.659"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M92.811,168.868L92.479,171.659"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M85.656,169.71L85.324,171.659"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.663,171.659H82.422L82.444,167.894H85.324L83.663,171.659Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M82.422,171.659H66.894L67.337,167.894H82.865L82.422,171.659Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M91.172,98.274C80.85,101.397 69.774,100.556 59.873,95.705"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M98.681,95.195C97.418,95.86 96.133,96.436 94.849,96.967"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M67.302,94.36C67.839,93.916 67.914,93.12 67.469,92.583C67.025,92.046 66.229,91.97 65.692,92.415C65.155,92.859 65.079,93.655 65.524,94.192C65.968,94.73 66.764,94.805 67.302,94.36Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M67.758,93.6C67.714,93.888 67.559,94.154 67.315,94.353C66.784,94.796 65.986,94.73 65.543,94.176C65.277,93.844 65.189,93.445 65.3,93.069"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.729,171.659H94.406"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M82.91,137.702C82.267,138.655 80.982,138.921 80.03,138.3"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M94.583,137.702C93.941,138.655 92.656,138.921 91.703,138.3"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M72.388,34.989C72.388,38.622 75.334,41.568 78.967,41.568C82.599,41.568 85.545,38.622 85.545,34.989V34.879C84.46,34.657 83.33,34.546 82.178,34.546H75.334C74.315,34.546 73.34,34.635 72.366,34.812V34.989H72.388Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M84.039,40.815C85.258,39.663 86.144,38.135 86.542,36.363"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M71.214,35.034C71.524,39.397 74.869,42.853 78.967,42.853C79.941,42.853 80.872,42.653 81.713,42.299"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M85.324,167.894H83.33V160.828L85.324,167.894Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M141.565,184.95H145.707"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M149.982,184.95H154.124"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M81.425,31.091C81.758,31.091 82.024,31.357 82.024,31.689C82.024,32.021 81.758,32.287 81.425,32.287"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M52.763,78.56C52.785,80.11 53.028,81.594 53.449,83.012L60.582,79.468C60.537,79.136 60.515,78.803 60.515,78.471V76.943L52.763,78.56Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M64.347,77.43L48.953,80.288V63.94C48.953,53.33 54.911,43.628 64.347,38.799V77.43Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M63.527,52.469C63.7,52.296 63.7,52.015 63.527,51.842C63.354,51.669 63.073,51.669 62.9,51.842C62.727,52.015 62.727,52.296 62.9,52.469C63.073,52.642 63.354,52.642 63.527,52.469Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M64.392,51.618C64.479,51.531 64.479,51.391 64.392,51.304C64.305,51.218 64.165,51.218 64.079,51.304C63.992,51.391 63.992,51.531 64.079,51.618C64.165,51.704 64.305,51.704 64.392,51.618Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M69.22,47.638C69.109,47.173 68.733,46.818 68.29,46.685C68.201,46.663 68.113,46.641 68.002,46.641L68.113,46.53C68.201,46.442 68.201,46.309 68.113,46.22C68.024,46.131 67.891,46.131 67.802,46.22L67.005,47.017L66.274,47.748C65.986,47.593 65.632,47.616 65.388,47.859C65.322,47.926 65.255,47.926 65.167,47.903C64.369,47.571 63.483,47.748 62.907,48.324L61.29,49.941C60.736,50.495 60.559,51.315 60.825,52.068C60.891,52.223 60.847,52.4 60.736,52.511C60.449,52.799 60.471,53.264 60.759,53.574C60.891,53.707 61.091,53.796 61.29,53.818H61.312C61.512,53.818 61.689,53.751 61.822,53.618C61.933,53.508 62.11,53.463 62.265,53.53C63.04,53.796 63.86,53.618 64.391,53.065L66.008,51.448C66.584,50.872 66.739,49.986 66.429,49.188C66.407,49.1 66.407,49.011 66.474,48.967C66.717,48.723 66.739,48.369 66.584,48.081L67.072,47.593C67.249,47.881 67.537,48.125 67.869,48.214C67.98,48.236 68.113,48.258 68.223,48.258C68.578,48.258 68.91,48.125 69.154,47.881C69.22,47.815 69.242,47.726 69.22,47.638ZM66.163,48.634C65.986,48.812 65.92,49.1 66.031,49.343C66.296,49.964 66.163,50.672 65.72,51.115L64.103,52.732C63.682,53.153 63.04,53.286 62.42,53.087C62.088,52.976 61.755,53.042 61.512,53.286C61.467,53.33 61.379,53.375 61.312,53.353C61.224,53.353 61.135,53.308 61.091,53.242C60.958,53.109 60.958,52.91 61.069,52.799C61.312,52.555 61.379,52.223 61.268,51.891C61.069,51.27 61.202,50.628 61.623,50.207L63.239,48.59C63.528,48.302 63.926,48.147 64.347,48.147C64.568,48.147 64.79,48.191 65.012,48.28C65.255,48.391 65.543,48.324 65.72,48.147C65.831,48.036 66.031,48.036 66.163,48.169C66.274,48.324 66.274,48.524 66.163,48.634ZM68.002,47.771C67.736,47.704 67.537,47.505 67.426,47.261C67.647,47.106 67.913,47.062 68.179,47.128C68.445,47.195 68.644,47.394 68.755,47.638C68.534,47.793 68.246,47.837 68.002,47.771Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M57.43,64.651C57.603,64.478 57.603,64.197 57.43,64.024C57.257,63.851 56.977,63.851 56.804,64.024C56.631,64.197 56.631,64.478 56.804,64.651C56.977,64.824 57.257,64.824 57.43,64.651Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M56.262,63.811C56.349,63.725 56.349,63.584 56.262,63.498C56.176,63.411 56.036,63.411 55.949,63.498C55.863,63.584 55.863,63.725 55.949,63.811C56.036,63.898 56.176,63.898 56.262,63.811Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M51.167,60.042C51.411,60.286 51.765,60.418 52.098,60.418C52.208,60.418 52.341,60.396 52.452,60.374C52.784,60.286 53.072,60.064 53.25,59.754L53.737,60.241C53.582,60.529 53.604,60.884 53.847,61.127C53.914,61.194 53.914,61.282 53.892,61.349C53.56,62.146 53.737,63.032 54.313,63.608L55.93,65.225C56.484,65.779 57.303,65.956 58.056,65.69C58.211,65.624 58.388,65.668 58.499,65.779C58.632,65.912 58.809,65.978 59.009,65.978H59.031C59.23,65.978 59.407,65.89 59.562,65.735C59.873,65.425 59.873,64.959 59.585,64.672C59.474,64.561 59.43,64.383 59.496,64.228C59.762,63.453 59.585,62.634 59.031,62.102L57.414,60.485C56.838,59.909 55.952,59.754 55.154,60.064C55.066,60.108 54.977,60.086 54.933,60.02C54.689,59.776 54.335,59.754 54.047,59.909L53.316,59.178L52.519,58.381C52.43,58.292 52.297,58.292 52.208,58.381C52.12,58.469 52.12,58.602 52.208,58.691L52.319,58.801C52.23,58.801 52.12,58.824 52.031,58.846C51.566,58.979 51.212,59.333 51.101,59.798C51.101,59.909 51.123,59.998 51.167,60.042ZM54.18,60.396C54.313,60.264 54.512,60.264 54.623,60.374C54.8,60.551 55.088,60.618 55.332,60.507C55.553,60.418 55.775,60.374 55.996,60.374C56.417,60.374 56.816,60.529 57.104,60.817L58.721,62.434C59.141,62.855 59.274,63.498 59.075,64.118C58.964,64.45 59.031,64.782 59.274,65.026C59.385,65.137 59.385,65.336 59.252,65.469C59.186,65.535 59.097,65.58 59.031,65.58C58.942,65.58 58.876,65.558 58.831,65.513C58.588,65.27 58.256,65.203 57.923,65.314C57.325,65.513 56.661,65.38 56.24,64.959L54.623,63.342C54.18,62.899 54.047,62.191 54.313,61.57C54.423,61.327 54.357,61.039 54.18,60.861C54.047,60.707 54.069,60.529 54.18,60.396ZM51.588,59.82C51.699,59.577 51.898,59.377 52.164,59.311C52.43,59.244 52.696,59.289 52.917,59.444C52.806,59.688 52.607,59.887 52.341,59.953C52.076,60.02 51.81,59.975 51.588,59.82Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M63.063,77.674V63.608L65.233,62.434L63.063,77.674Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M59.142,73.421V74.617"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M56.639,52.998V54.172"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M47.668,78.937C48.199,79.49 49.041,79.712 49.816,79.446C50.392,79.247 50.835,78.804 51.057,78.272L53.626,77.762C54.402,77.607 54.911,76.832 54.756,76.057C54.623,75.414 54.091,74.971 53.493,74.905C54.158,74.661 54.579,73.975 54.424,73.266C54.291,72.646 53.781,72.203 53.183,72.114C53.693,71.804 54.003,71.206 53.87,70.586C53.715,69.81 52.939,69.301 52.164,69.456L49.462,70.01L49.861,69.7C49.883,69.677 49.905,69.677 49.905,69.655L53.095,68.814C53.87,68.614 54.335,67.817 54.136,67.042C53.936,66.266 53.139,65.801 52.364,66.001L48.886,66.909C48.62,66.975 48.399,67.108 48.221,67.285C48.177,67.307 48.133,67.329 48.111,67.374L47.646,67.728V78.937H47.668Z"
        android:strokeAlpha="0.5"
        android:fillColor="#161E24"
        android:fillAlpha="0.5"/>
    <path
        android:pathData="M62.287,63.608L65.809,61.903"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M48.953,79.224C47.867,79.601 46.693,79.025 46.317,77.94L44.079,71.45C43.703,70.364 44.279,69.19 45.364,68.814C46.45,68.437 47.624,69.013 48,70.098L50.237,76.589C50.614,77.674 50.038,78.848 48.953,79.224Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M52.762,77.541L49.24,78.25C48.465,78.405 47.69,77.896 47.535,77.12C47.38,76.345 47.889,75.57 48.664,75.415L52.186,74.706C52.962,74.551 53.737,75.06 53.892,75.836C54.069,76.611 53.56,77.386 52.762,77.541Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M52.43,74.728L48.908,75.459C48.133,75.614 47.358,75.104 47.203,74.329C47.048,73.554 47.557,72.778 48.332,72.623L51.854,71.914C52.63,71.759 53.405,72.269 53.56,73.044C53.737,73.82 53.228,74.573 52.43,74.728Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M51.899,72.048L48.377,72.757C47.601,72.911 46.826,72.402 46.671,71.627C46.516,70.852 47.026,70.076 47.801,69.921L51.323,69.212C52.098,69.057 52.873,69.567 53.028,70.342C53.183,71.14 52.674,71.893 51.899,72.048Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M49.019,69.456L46.161,71.627C45.519,72.114 44.611,71.981 44.124,71.339C43.636,70.697 43.769,69.788 44.412,69.301L47.269,67.13C47.911,66.643 48.82,66.776 49.307,67.418C49.794,68.061 49.661,68.969 49.019,69.456Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M52.253,68.57L48.775,69.478C48,69.677 47.202,69.212 47.003,68.437C46.804,67.662 47.269,66.864 48.044,66.665L51.522,65.757C52.297,65.558 53.095,66.023 53.294,66.798C53.493,67.595 53.028,68.371 52.253,68.57Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M47.114,68.681L47.513,69.833"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#B76452"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M48.111,71.582L48.51,72.734"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#B76452"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M49.085,74.373L49.484,75.525"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#B76452"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M49.241,78.25L52.143,77.674"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M50.614,75.104L52.43,74.728"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M50.259,72.38L52.341,71.893"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M52.43,79.557L63.063,77.585"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M141.587,29.518H140.922V28.854C140.922,28.721 140.833,28.632 140.701,28.632C140.568,28.632 140.479,28.721 140.479,28.854V29.518H139.815C139.682,29.518 139.593,29.607 139.593,29.74C139.593,29.873 139.682,29.961 139.815,29.961H140.479V30.626C140.479,30.759 140.568,30.847 140.701,30.847C140.833,30.847 140.922,30.759 140.922,30.626V29.961H141.587C141.719,29.961 141.808,29.873 141.808,29.74C141.808,29.607 141.719,29.518 141.587,29.518Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M132.195,37.382H130.866V36.031C130.866,35.787 130.666,35.588 130.423,35.588C130.179,35.588 129.98,35.787 129.98,36.031V37.382H128.628C128.385,37.382 128.185,37.581 128.185,37.825C128.185,38.069 128.385,38.268 128.628,38.268H129.98V39.619C129.98,39.863 130.179,40.062 130.423,40.062C130.666,40.062 130.866,39.863 130.866,39.619V38.268H132.217C132.46,38.268 132.66,38.069 132.66,37.825C132.66,37.581 132.438,37.382 132.195,37.382Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M111.329,11.82C111.329,11 110.664,10.336 109.845,10.336C109.025,10.336 108.361,11 108.361,11.82C108.361,11.931 108.361,11.931 108.361,11.82C108.361,11 107.696,10.336 106.877,10.336C106.057,10.336 105.393,11 105.393,11.82C105.393,12.44 106.256,13.215 106.256,13.215L108.361,15.32L110.465,13.215C110.487,13.215 111.329,12.44 111.329,11.82Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#F0917A"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M110.797,9.804V8.896"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M112.481,9.494L111.816,10.137"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M113.632,8.564L113.322,8.896"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M112.015,11.377H112.924"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M52.297,31.756H51.411V30.869C51.411,30.714 51.278,30.581 51.123,30.581C50.968,30.581 50.835,30.714 50.835,30.869V31.756H49.949C49.794,31.756 49.661,31.888 49.661,32.043C49.661,32.199 49.794,32.332 49.949,32.332H50.835V33.217C50.835,33.373 50.968,33.505 51.123,33.505C51.278,33.505 51.411,33.373 51.411,33.217V32.332H52.297C52.452,32.332 52.585,32.199 52.585,32.043C52.585,31.888 52.474,31.756 52.297,31.756Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M46.715,25.974C47.058,25.974 47.335,25.697 47.335,25.354C47.335,25.012 47.058,24.734 46.715,24.734C46.373,24.734 46.095,25.012 46.095,25.354C46.095,25.697 46.373,25.974 46.715,25.974Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M52.784,25.885C53.237,25.885 53.604,25.519 53.604,25.066C53.604,24.613 53.237,24.246 52.784,24.246C52.332,24.246 51.965,24.613 51.965,25.066C51.965,25.519 52.332,25.885 52.784,25.885Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M140.523,52.289C140.976,52.289 141.343,51.922 141.343,51.47C141.343,51.017 140.976,50.65 140.523,50.65C140.071,50.65 139.704,51.017 139.704,51.47C139.704,51.922 140.071,52.289 140.523,52.289Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M114.098,128.842L117.775,151.369C117.044,151.635 116.512,152.344 116.512,153.186C116.512,153.939 116.933,154.581 117.553,154.891C118.085,155.179 118.484,155.689 118.572,156.287L120.765,173.143C121.009,175.026 122.626,176.444 124.509,176.444C126.591,176.444 128.297,174.76 128.297,172.656V128.842H114.098Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M128.297,171.061L120.677,172.368L120.788,173.21L128.297,173.896V171.061Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M128.119,137.547L128.318,137.437V128.842H117.575H114.12H103.377L104.573,136.174C107.696,138.3 114.784,137.702 114.784,137.702C119.259,140.072 123.667,140.028 128.119,137.547Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M132.062,112.14C132.062,106.292 127.853,101.574 122.67,101.574C120.433,101.574 118.395,102.46 116.778,103.922L98.393,101.884L98.415,102.372C98.836,113.093 100.098,123.769 102.225,134.291C105.37,135.753 108.981,135.775 112.148,134.379L112.326,134.291L113.81,135.066C118.306,137.436 123.645,137.37 128.097,134.911L129.227,134.291L129.758,119.073C131.198,117.19 132.062,114.776 132.062,112.14Z"
        android:fillColor="#0D9AFF"/>
    <path
        android:pathData="M131.065,107.4C129.515,103.944 126.347,101.574 122.67,101.574C120.433,101.574 118.395,102.46 116.778,103.922L98.393,101.884L98.415,102.372C98.437,103.014 98.459,103.634 98.504,104.277C108.914,108.662 120.61,111.431 131.065,107.4Z"
        android:fillColor="#0071C2"/>
    <path
        android:pathData="M99.257,96.015V78.826L107.164,80.287V63.94C107.164,58.092 105.348,52.511 102.136,47.859C99.877,49.144 98.083,51.226 97.174,53.773C96.111,56.741 95.58,59.865 95.58,63.01V97.764C96.82,97.255 98.061,96.657 99.257,96.015Z"
        android:strokeAlpha="0.5"
        android:fillColor="#EDA257"
        android:fillAlpha="0.5"/>
    <path
        android:pathData="M131.176,105.717C120.765,110.213 109.025,107.688 98.481,103.347C97.595,102.97 97.02,102.106 97.02,101.154V65.424C97.02,62.279 97.551,59.156 98.614,56.188C100.187,51.757 104.396,48.789 109.092,48.789H111.262C122.249,48.789 131.176,57.694 131.176,68.703V105.717Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M120.854,159.343C121.252,159.343 121.563,159.653 121.563,160.052"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M125.882,159.343C126.281,159.343 126.591,159.653 126.591,160.052"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M123.733,157.726C123.8,157.35 124.176,157.084 124.575,157.15"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M125.815,154.913C125.882,154.536 126.258,154.271 126.657,154.337"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M122.426,154.758C122.116,155.002 121.651,154.935 121.43,154.603"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M124.775,152.499C124.465,152.742 123.999,152.676 123.778,152.344"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M120.854,168.713C121.252,168.713 121.563,169.023 121.563,169.422"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M125.882,168.713C126.281,168.713 126.591,169.023 126.591,169.422"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M123.733,167.096C123.8,166.72 124.176,166.454 124.575,166.52"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M125.815,164.261C125.882,163.884 126.258,163.619 126.657,163.685"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M122.426,164.128C122.116,164.371 121.651,164.305 121.43,163.973"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M124.775,161.846C124.465,162.09 123.999,162.024 123.778,161.691"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M129.78,184.795H108.294L108.405,184.329C109.313,180.83 112.104,178.149 115.626,177.374L118.129,176.82C118.196,176.798 118.284,176.776 118.351,176.776C119.68,176.399 120.566,175.159 120.632,173.786L120.676,173.01L128.318,172.877L129.78,184.795Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M114.762,137.702L117.775,151.347C117.044,151.613 116.512,152.322 116.512,153.163C116.512,153.916 116.933,154.559 117.553,154.869C118.085,155.157 118.484,155.666 118.572,156.264"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M122.05,180.209C122.161,180.785 121.806,181.339 121.23,181.45C120.655,181.56 120.101,181.206 119.99,180.63C119.879,180.054 120.056,175.779 120.056,175.779C120.056,175.779 121.939,179.633 122.05,180.209Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M118.24,180.32C117.93,180.807 117.265,180.94 116.778,180.63C116.29,180.32 116.157,179.655 116.468,179.168C116.778,178.681 119.945,175.779 119.945,175.779C119.945,175.779 118.55,179.833 118.24,180.32Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M108.294,184.795L108.405,184.329C109.313,180.83 112.104,178.149 115.626,177.374"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M110.266,183.953H128.296"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#EDA257"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M111.683,130.37L105.57,117.634L108.228,119.383L111.683,130.37Z"
        android:fillColor="#0071C2"/>
    <path
        android:pathData="M128.296,121.156L129.78,119.051"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M103.798,115.153L104.551,116.172C105.703,117.766 107.298,119.007 109.114,119.716"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M111.285,129.13L108.228,119.383"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M105.747,50.074L109.136,54.548L104.307,57.362L105.747,50.074Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M115.05,50.273L109.424,54.548L114.164,58.114L115.05,50.273Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M110.465,54.748C108.361,54.748 106.544,53.33 106.035,51.292L102.203,35.986H111.617L114.895,49.077C115.626,51.935 113.455,54.748 110.465,54.748Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M112.569,39.752L111.617,35.964H102.203L105.304,48.302C109.114,47.128 112.015,43.827 112.569,39.752Z"
        android:fillColor="#D1A299"/>
    <path
        android:pathData="M109.136,55.368V60.861"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M110.465,54.748C108.361,54.748 106.544,53.33 106.035,51.292L102.203,35.986H111.617L114.895,49.077C115.626,51.935 113.455,54.748 110.465,54.748Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M106.943,33.262C109.194,33.262 111.019,31.268 111.019,28.809C111.019,26.35 109.194,24.357 106.943,24.357C104.692,24.357 102.867,26.35 102.867,28.809C102.867,31.268 104.692,33.262 106.943,33.262Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M95.159,33.217C97.006,33.217 98.504,31.571 98.504,29.54C98.504,27.51 97.006,25.863 95.159,25.863C93.312,25.863 91.814,27.51 91.814,29.54C91.814,31.571 93.312,33.217 95.159,33.217Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M93.033,36.761C94.88,36.761 96.377,35.115 96.377,33.084C96.377,31.053 94.88,29.407 93.033,29.407C91.185,29.407 89.688,31.053 89.688,33.084C89.688,35.115 91.185,36.761 93.033,36.761Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M102.757,30.537C104.604,30.537 106.101,28.891 106.101,26.86C106.101,24.829 104.604,23.183 102.757,23.183C100.909,23.183 99.412,24.829 99.412,26.86C99.412,28.891 100.909,30.537 102.757,30.537Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M101.871,45.046C107.657,45.046 112.348,40.355 112.348,34.568C112.348,28.782 107.657,24.091 101.871,24.091C96.084,24.091 91.393,28.782 91.393,34.568C91.393,40.355 96.084,45.046 101.871,45.046Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M105.766,30.692C106.64,29.818 107.082,28.843 106.753,28.515C106.424,28.186 105.45,28.628 104.576,29.501C103.702,30.375 103.26,31.35 103.589,31.678C103.918,32.007 104.893,31.565 105.766,30.692Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M111.383,30.597C111.753,30.315 111.446,29.29 110.698,28.307C109.949,27.323 109.043,26.755 108.673,27.036C108.303,27.318 108.61,28.343 109.358,29.326C110.106,30.309 111.013,30.878 111.383,30.597Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M143.846,184.662C143.625,184.174 140.812,178.26 142.362,177.086C143.913,175.912 144.577,182.513 144.577,182.513C144.577,182.513 144.511,178.88 145.53,178.969C146.571,179.08 145.973,184.086 145.973,184.086C145.973,184.086 146.416,181.893 147.235,182.004C148.055,182.114 147.235,184.662 147.235,184.662"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M49.085,181.937C49.085,181.937 48.997,177.308 50.304,177.441C51.611,177.573 50.857,183.953 50.857,183.953C50.857,183.953 51.411,181.162 52.452,181.295C53.493,181.428 52.452,184.684 52.452,184.684"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M101.871,36.739C101.383,37.337 100.519,37.426 99.921,36.961"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M101.051,43.207C100.564,43.805 99.7,43.894 99.102,43.429"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M97.108,36.739C96.621,37.337 95.757,37.426 95.159,36.961"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M102.269,44.935C96.488,44.935 91.792,40.239 91.792,34.458C91.792,29.54 95.159,25.42 99.722,24.291C94.849,25.177 91.15,29.452 91.15,34.591C91.15,40.372 95.846,45.068 101.627,45.068C102.358,45.068 103.089,44.979 103.798,44.846C103.288,44.891 102.779,44.935 102.269,44.935Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M107.12,43.495C110.111,41.657 112.104,38.334 112.104,34.568C112.104,28.787 107.408,24.091 101.627,24.091C95.846,24.091 91.15,28.787 91.15,34.568C91.15,40.35 95.846,45.046 101.627,45.046C102.38,45.046 103.133,44.957 103.842,44.802"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M112.24,33.297C113.148,32.965 113.615,31.961 113.284,31.053C112.953,30.145 111.949,29.677 111.041,30.008C110.133,30.34 109.665,31.344 109.996,32.252C110.327,33.16 111.332,33.628 112.24,33.297Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M112.281,31.157C111.883,31.135 111.55,31.467 111.528,31.866"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M110.908,33.239C111.307,33.439 111.794,33.461 112.237,33.306C113.145,32.973 113.61,31.977 113.278,31.069C112.946,30.16 111.949,29.695 111.041,30.027C110.554,30.205 110.199,30.581 110.022,31.024"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M98.349,35.831L97.662,40.239C97.64,40.461 97.684,40.66 97.773,40.815C98.038,41.236 98.636,41.302 99.035,40.97L99.678,40.416C99.877,40.682 100.254,40.726 100.497,40.505L100.519,40.483"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M102.054,29.597C103.104,28.291 103.39,26.778 102.694,26.219C101.998,25.66 100.582,26.266 99.533,27.572C98.483,28.879 98.197,30.392 98.893,30.951C99.59,31.51 101.005,30.904 102.054,29.597Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M107.098,33.682C107.588,33.682 107.984,32.621 107.984,31.312C107.984,30.003 107.588,28.942 107.098,28.942C106.609,28.942 106.212,30.003 106.212,31.312C106.212,32.621 106.609,33.682 107.098,33.682Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M92.52,32.461C92.989,30.993 92.991,29.681 92.525,29.532C92.059,29.384 91.3,30.453 90.831,31.922C90.362,33.39 90.36,34.701 90.826,34.85C91.292,34.999 92.05,33.929 92.52,32.461Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M106.703,30.745C107.127,29.742 107.252,28.836 106.981,28.722C106.711,28.608 106.148,29.328 105.724,30.331C105.3,31.333 105.175,32.239 105.446,32.353C105.716,32.468 106.279,31.747 106.703,30.745Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M105.537,25.676C105.927,25.64 106.166,24.781 106.071,23.758C105.976,22.735 105.584,21.934 105.194,21.971C104.804,22.007 104.565,22.866 104.66,23.889C104.755,24.912 105.148,25.712 105.537,25.676Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M106.501,24.508C106.76,23.692 106.791,22.973 106.569,22.903C106.348,22.832 105.958,23.437 105.699,24.253C105.439,25.069 105.409,25.788 105.63,25.858C105.852,25.928 106.242,25.324 106.501,24.508Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M109.269,34.635C109.978,34.635 110.554,33.018 110.554,31.024C110.554,29.03 109.978,27.414 109.269,27.414C108.559,27.414 107.984,29.03 107.984,31.024C107.984,33.018 108.559,34.635 109.269,34.635Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M101.83,27.67C104.151,27.4 105.941,26.394 105.828,25.421C105.715,24.449 103.741,23.88 101.42,24.15C99.1,24.42 97.31,25.427 97.423,26.399C97.536,27.371 99.509,27.94 101.83,27.67Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M106.232,29.042C108.035,27.556 108.993,25.739 108.37,24.984C107.748,24.229 105.781,24.821 103.978,26.307C102.175,27.793 101.218,29.61 101.84,30.365C102.463,31.121 104.429,30.528 106.232,29.042Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M109.48,28.49C109.557,26.706 108.828,25.225 107.85,25.183C106.872,25.14 106.017,26.552 105.939,28.337C105.862,30.121 106.591,31.602 107.569,31.645C108.547,31.687 109.403,30.275 109.48,28.49Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M94.381,31.506C94.893,29.427 94.827,27.624 94.233,27.477C93.639,27.331 92.743,28.897 92.23,30.975C91.717,33.054 91.783,34.858 92.377,35.004C92.971,35.151 93.868,33.584 94.381,31.506Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M99.197,29.507C101.059,27.189 101.632,24.559 100.478,23.632C99.324,22.705 96.879,23.832 95.018,26.15C93.156,28.468 92.583,31.098 93.737,32.025C94.891,32.952 97.336,31.825 99.197,29.507Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M112.038,132.342L112.348,134.269"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M106.721,35.809C106.856,35.809 106.965,35.7 106.965,35.565C106.965,35.431 106.856,35.321 106.721,35.321C106.587,35.321 106.478,35.431 106.478,35.565C106.478,35.7 106.587,35.809 106.721,35.809Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M97.839,34.059C97.906,34.258 97.817,34.48 97.618,34.546C97.418,34.613 96.289,34.856 96.222,34.657C96.156,34.458 97.152,33.882 97.352,33.815C97.551,33.771 97.773,33.859 97.839,34.059Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M99.39,34.059C99.323,34.258 99.412,34.48 99.611,34.546C99.811,34.613 100.94,34.856 101.007,34.657C101.073,34.458 100.076,33.882 99.877,33.815C99.678,33.771 99.456,33.859 99.39,34.059Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M118.284,153.894C117.974,154.138 117.509,154.072 117.288,153.739"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#E5B6AA"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M106.057,107.688V113.27C106.057,113.89 105.725,114.466 105.193,114.776L104.573,115.153V107.333"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M121.496,110.501C121.496,113.824 124.198,116.526 127.521,116.526"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M113.832,135.044C118.329,137.414 123.667,137.348 128.119,134.889"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M102.181,134.291L102.225,134.313C103,134.778 103.886,135.066 104.772,135.111"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M132.705,100.356C132.328,101.043 131.597,101.486 130.778,101.441C130.157,101.419 129.626,101.109 129.271,100.666L126.657,100.865C125.86,100.932 125.173,100.334 125.107,99.536C125.063,98.894 125.439,98.318 126.015,98.074C125.306,98.03 124.73,97.476 124.664,96.745C124.62,96.103 124.974,95.549 125.528,95.306C124.952,95.15 124.487,94.641 124.442,94.021C124.376,93.223 124.974,92.537 125.771,92.47L128.518,92.249L128.053,92.072C128.031,92.072 128.009,92.049 127.987,92.049L124.686,92.116C123.889,92.138 123.224,91.496 123.202,90.698C123.18,89.901 123.822,89.236 124.62,89.214L128.208,89.148C128.474,89.148 128.717,89.214 128.939,89.325C128.983,89.347 129.028,89.347 129.072,89.369L129.626,89.591L132.705,100.356Z"
        android:strokeAlpha="0.5"
        android:fillColor="#161E24"
        android:fillAlpha="0.5"/>
    <path
        android:pathData="M131.553,100.998C132.705,101.065 133.679,100.179 133.723,99.049L134.1,92.182C134.166,91.031 133.28,90.056 132.151,90.012C130.999,89.945 130.024,90.831 129.98,91.961L129.603,98.827C129.537,99.979 130.401,100.954 131.553,100.998Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M127.411,100.422L130.977,100.134C131.774,100.068 132.372,99.381 132.306,98.584C132.239,97.786 131.553,97.188 130.755,97.255L127.189,97.543C126.392,97.609 125.794,98.296 125.86,99.093C125.926,99.891 126.613,100.489 127.411,100.422Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M126.968,97.632L130.534,97.344C131.331,97.277 131.929,96.59 131.863,95.793C131.797,94.996 131.11,94.398 130.312,94.464L126.746,94.752C125.949,94.818 125.351,95.505 125.417,96.302C125.461,97.1 126.17,97.698 126.968,97.632Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M126.746,94.907L130.312,94.619C131.11,94.552 131.708,93.866 131.641,93.068C131.575,92.271 130.888,91.673 130.091,91.739L126.525,92.027C125.727,92.094 125.129,92.78 125.195,93.578C125.262,94.375 125.949,94.973 126.746,94.907Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M128.806,91.629L132.151,92.935C132.904,93.223 133.746,92.847 134.034,92.116C134.322,91.363 133.945,90.521 133.214,90.233L129.87,88.926C129.116,88.638 128.275,89.015 127.987,89.746C127.699,90.499 128.053,91.34 128.806,91.629Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M125.461,91.673L129.05,91.606C129.847,91.584 130.49,90.92 130.468,90.122C130.445,89.325 129.781,88.683 128.983,88.705L125.395,88.771C124.598,88.793 123.955,89.458 123.977,90.255C124,91.053 124.664,91.695 125.461,91.673Z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M130.423,90.366L130.357,91.584"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#B76452"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M130.246,93.423L130.18,94.641"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#B76452"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M130.091,96.369L130.024,97.587"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#B76452"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M130.999,100.156L128.053,100.378"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M128.828,97.476L126.968,97.631"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M128.407,94.774L126.281,94.863"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M105.459,39.508C105.415,39.309 105.348,39.109 105.26,38.932C104.817,39.486 104.241,39.951 103.576,40.283C103.222,40.46 102.912,40.704 102.668,41.014L102.535,41.169V40.682C102.07,40.793 101.605,40.859 101.14,40.837C99.789,40.793 98.991,42.366 99.988,43.097C100.608,43.54 101.472,43.783 102.646,43.495C104.064,43.163 105.127,42.033 105.437,40.793L105.016,40.881L105.06,40.837C105.393,40.483 105.548,40.018 105.437,39.552C105.459,39.53 105.459,39.53 105.459,39.508Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M98.149,41.723C97.817,41.9 97.463,42.011 97.064,42.077C96.887,42.1 96.709,42.122 96.532,42.122L96.864,42.365L96.51,42.21C96.355,42.144 96.2,42.122 96.023,42.1C95.535,42.055 95.048,41.922 94.627,41.723C94.627,41.878 94.649,42.033 94.672,42.188C94.959,43.495 96.466,44.381 97.95,44.071C98.88,43.872 99.39,43.451 99.678,42.919C100.143,42.1 99.079,41.258 98.149,41.723Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M131.176,102.416V105.716"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M116.313,49.432C124.863,51.669 131.198,59.444 131.198,68.703V87.376"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M106.854,46.907C106.522,46.995 106.212,46.796 106.123,46.486C106.035,46.153 106.234,45.843 106.544,45.755"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M104.861,49.631L104.794,49.653C103.82,49.941 102.911,50.473 102.203,51.204"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M111.329,87.398C108.161,87.708 105.171,86.114 103.687,83.367L97.042,78.87V87.62C97.219,87.797 98.415,89.68 99.523,90.876C101.826,93.357 105.06,94.797 108.449,95.24C108.494,95.439 108.56,95.638 108.715,95.815C109.158,96.347 109.956,96.436 110.487,95.993C110.687,95.838 110.819,95.616 110.886,95.395C111.218,95.395 111.573,95.372 111.905,95.35C120.078,94.642 127.72,86.911 127.72,75.946L119.791,75.991C120.256,82.88 115.338,86.999 111.329,87.398Z"
        android:strokeAlpha="0.5"
        android:fillColor="#161E24"
        android:fillAlpha="0.5"/>
    <path
        android:pathData="M111.905,93.733C120.078,93.024 127.72,85.294 127.72,74.329L119.79,74.373C120.278,81.307 115.36,85.404 111.351,85.825C108.183,86.135 105.193,84.541 103.709,81.794L95.491,66.798L89.732,70.298L96.244,84.563C97.042,86.313 98.127,87.952 99.522,89.303C102.712,92.448 107.209,94.154 111.905,93.733Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M86.299,69.345L86.742,70.541C87.805,73.332 90.928,74.75 93.719,73.687C96.51,72.624 97.928,69.5 96.865,66.71L96.422,65.513C95.358,62.722 92.235,61.305 89.444,62.368C86.631,63.409 85.235,66.532 86.299,69.345Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M87.893,56.631L93.01,65.735L95.602,64.007L89.865,55.435C89.51,54.903 88.801,54.77 88.27,55.125C87.76,55.435 87.605,56.099 87.893,56.631Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M84.416,58.58L90.485,67.064L92.877,65.07L86.254,57.162C85.856,56.675 85.125,56.609 84.637,57.029C84.15,57.406 84.061,58.07 84.416,58.58Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M81.824,61.172L88.824,68.902L90.973,66.643L83.486,59.555C83.021,59.112 82.289,59.134 81.846,59.599C81.448,60.042 81.426,60.729 81.824,61.172Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M80.296,64.317L88.27,71.051L90.086,68.526L81.713,62.501C81.204,62.124 80.473,62.235 80.096,62.767C79.764,63.254 79.83,63.941 80.296,64.317Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M93.874,68.26L96.222,71.051L100.63,64.362C101.007,63.808 100.852,63.055 100.276,62.678C99.766,62.346 99.102,62.434 98.703,62.877L93.874,68.26Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M95.956,64.561L96.731,66.001"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M93.054,74.927C92.7,75.104 92.567,75.525 92.744,75.88C92.922,76.234 93.342,76.367 93.697,76.19"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M129.16,76.079H116.689L118.174,64.162C118.594,60.684 121.208,58.093 124.287,58.093C128.053,58.093 130.932,61.881 130.401,66.133L129.16,76.079Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M81.049,64.937L86.299,69.345"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M86.299,69.345L86.742,70.541C87.65,72.911 90.02,74.285 92.434,73.997"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M89.444,62.346C87.694,63.01 86.498,64.472 86.099,66.156"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M96.731,70.497L97.861,68.57"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M110.478,94.402C111.015,93.958 111.091,93.162 110.646,92.624C110.202,92.087 109.406,92.012 108.868,92.457C108.331,92.901 108.256,93.697 108.7,94.234C109.145,94.772 109.941,94.847 110.478,94.402Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M110.93,93.645C110.886,93.933 110.731,94.198 110.487,94.398C109.956,94.841 109.158,94.774 108.715,94.221C108.449,93.888 108.361,93.49 108.471,93.113"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M127.565,76.788C127.61,76.345 127.654,75.924 127.676,75.481L119.79,75.127C119.813,76.5 119.613,77.741 119.259,78.87L127.565,76.788Z"
        android:fillColor="#E5B6AA"/>
    <path
        android:pathData="M129.16,76.079H116.689L118.174,64.162C118.594,60.684 121.208,58.093 124.287,58.093C128.053,58.093 130.932,61.881 130.401,66.133L129.16,76.079Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M125.395,59.798C125.527,59.931 125.66,60.064 125.771,60.197"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M126.524,61.283C127.322,62.656 127.698,64.361 127.477,66.133L126.701,72.313"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M126.613,72.956L126.547,73.51"
        android:strokeLineJoin="round"
        android:strokeWidth="0.664521"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M97.042,91.319V99.072"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M88.801,58.226L91.216,62.545"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M96.731,82.37C97.086,82.193 97.507,82.326 97.684,82.68"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M101.228,80.111C101.583,79.933 102.004,80.066 102.181,80.421"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M98.57,79.623C98.459,79.247 98.681,78.848 99.057,78.737"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M99.168,76.168C99.058,75.791 99.279,75.393 99.656,75.282"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M96.067,77.563C95.89,77.918 95.469,78.05 95.115,77.873"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M97.153,74.462C96.976,74.817 96.555,74.95 96.2,74.772"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M95.602,72.579C95.425,72.934 95.004,73.067 94.65,72.89"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M101.383,82.835C101.206,83.19 100.785,83.323 100.431,83.145"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.443014"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M125.328,78.715L124.885,78.626C124.774,78.604 124.664,78.671 124.642,78.782C124.531,79.38 123.999,79.8 123.401,79.778L123.778,77.807L124.176,77.873C124.287,77.896 124.398,77.829 124.42,77.718C124.442,77.607 124.376,77.497 124.265,77.475L123.866,77.408L124.021,76.589C124.287,76.544 124.531,76.345 124.597,76.057C124.664,75.703 124.42,75.348 124.066,75.282C123.711,75.215 123.357,75.459 123.29,75.813C123.246,76.101 123.379,76.367 123.623,76.522L123.468,77.342L123.069,77.275C122.958,77.253 122.847,77.32 122.825,77.43C122.803,77.541 122.869,77.652 122.98,77.674L123.379,77.74L123.002,79.712C122.449,79.512 122.094,78.914 122.227,78.316C122.249,78.206 122.183,78.095 122.072,78.073L121.563,77.984C121.452,77.962 121.341,78.028 121.319,78.139C121.297,78.25 121.363,78.361 121.474,78.383L121.784,78.449C121.74,79.202 122.205,79.889 122.914,80.11L122.869,80.376C122.847,80.487 122.914,80.598 123.025,80.62C123.135,80.642 123.246,80.576 123.268,80.465L123.313,80.199C124.066,80.243 124.752,79.778 124.974,79.07L125.217,79.114C125.328,79.136 125.439,79.07 125.461,78.959C125.483,78.848 125.439,78.737 125.328,78.715ZM123.977,75.703C124.11,75.725 124.199,75.858 124.176,75.991C124.154,76.123 124.021,76.212 123.888,76.19C123.756,76.168 123.667,76.035 123.689,75.902C123.711,75.769 123.844,75.68 123.977,75.703Z"
        android:fillColor="#161E24"/>
  </group>
</vector>
