<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="200dp"
    android:height="200dp"
    android:viewportWidth="200"
    android:viewportHeight="200">
  <group>
    <clip-path android:pathData="M0,0H200V200H0V0Z M 0,0"/>
    <path
        android:pathData="M0,0H200V200H0V0Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M99.966,200C154.436,200 198.592,156.053 198.592,101.843C198.592,47.632 154.436,3.685 99.966,3.685C45.496,3.685 1.339,47.632 1.339,101.843C1.339,156.053 45.496,200 99.966,200Z"
        android:fillColor="#E3F3FF"/>
    <path
        android:pathData="M84.842,191.308C82.772,191.308 81.094,189.638 81.094,187.579L82.112,167.217H91.989L88.563,188.012C88.341,189.89 86.742,191.306 84.842,191.308Z"
        android:fillColor="#FFCCAE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M81.327,182.948L89.762,180.75L90.004,179.286L81.493,179.642L81.327,182.948Z"
        android:fillColor="#E7B191"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M105.984,191.308C106.978,191.308 107.931,190.915 108.634,190.215C109.337,189.516 109.732,188.568 109.732,187.579L112.308,167.217H101.903L102.263,188.012C102.484,189.891 104.083,191.307 105.984,191.308Z"
        android:fillColor="#FFCCAE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M110.545,181.157L110.815,179.024H102.108L102.184,183.431L110.545,181.157Z"
        android:fillColor="#E7B191"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M90.028,194.913H64.992C64.327,193.366 64.41,191.601 65.217,190.122C66.023,188.643 67.466,187.613 69.132,187.325L80.999,185.3C82.328,187.311 84.589,188.518 87.008,188.509H88.098L90.028,194.913Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M63.608,32.609L65.271,32.951L62.25,61.298C61.743,63.249 57.315,64.997 55.354,64.489C53.396,63.982 52.221,61.992 52.728,60.043L59.18,35.351C59.688,33.41 61.634,32.205 63.608,32.609Z"
        android:fillColor="#FFCCAE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M60.83,56.24L86.724,66.709L80.645,77.463L55.718,65.28C54.513,64.606 53.626,63.483 53.254,62.158C52.881,60.834 53.053,59.416 53.731,58.217C54.408,57.018 55.537,56.136 56.868,55.765C58.2,55.394 59.625,55.565 60.83,56.24Z"
        android:fillColor="#FFCCAE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M59.627,41.196L57.422,59.026C57.238,60.506 57.585,62.003 58.402,63.254C59.195,64.465 60.38,65.371 61.76,65.823L68.476,68.444L68.693,71.624L69.436,71.989L70.635,61.101L59.627,41.196Z"
        android:fillColor="#E7B191"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M69.231,75.371L70.29,58.213L89.52,64.119C93.834,65.608 95.995,71.102 94.496,75.392C93.777,77.453 92.264,79.145 90.291,80.096C88.317,81.047 86.045,81.179 83.974,80.463L69.231,75.371Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M59.196,42.278V60.728C59.196,61.208 59.388,61.669 59.729,62.009C60.071,62.349 60.534,62.54 61.018,62.54H96.247V40.693H60.791C60.368,40.693 59.962,40.859 59.663,41.157C59.364,41.454 59.196,41.857 59.196,42.278Z"
        android:fillColor="#72BAE5"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M77.721,57.259h16.445v3.256h-16.445z"
        android:fillColor="#529ED6"/>
    <path
        android:pathData="M115.477,111.455L120.911,111.455A1.304,1.304 0,0 1,122.216 112.759L122.216,112.759A1.304,1.304 0,0 1,120.911 114.063L115.477,114.063A1.304,1.304 0,0 1,114.173 112.759L114.173,112.759A1.304,1.304 0,0 1,115.477 111.455z"
        android:fillColor="#E7B191"/>
    <path
        android:pathData="M116.829,114.74L112.362,111.921L125.288,86.486C126.458,84.843 131.221,84.769 132.873,85.933C133.666,86.492 134.204,87.342 134.368,88.297C134.531,89.251 134.307,90.23 133.744,91.019L116.829,114.74Z"
        android:fillColor="#FFCCAE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M124.019,120.134H113.974L113.427,112.431L124.961,117.378C125.432,117.57 125.743,118.023 125.752,118.529C125.717,119.448 124.943,120.165 124.019,120.134Z"
        android:fillColor="#FFCCAE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M112.217,116.667C110.935,115.825 110.582,114.101 111.431,112.822C112.273,111.553 113.984,111.203 115.258,112.04L119.856,115.061C121.138,115.904 121.491,117.628 120.642,118.906C119.8,120.176 118.089,120.525 116.816,119.689L112.217,116.667Z"
        android:fillColor="#FFCCAE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M117.075,120.134C116.918,119.429 115.192,109.766 115.192,109.766L114.349,118.156L114.467,120.142L117.075,120.134Z"
        android:fillColor="#E7B191"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M124.815,91.707L104.333,72.78L114.214,64.578L132.827,85.057C133.714,86.112 134.144,87.475 134.021,88.845C133.899,90.215 133.234,91.481 132.173,92.364C131.111,93.25 129.738,93.68 128.357,93.556C126.976,93.433 125.701,92.767 124.815,91.707Z"
        android:fillColor="#FFCCAE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M119.886,87.151L122.483,89.551L129.242,81.12L127.935,79.685L119.886,87.151Z"
        android:fillColor="#E7B191"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M83.493,109.766L79.571,179.912H91.178L99.399,120.485L100.053,120.533L101.77,179.912H113.127L114.467,109.766H83.493Z"
        android:fillColor="#0D9AFF"/>
    <path
        android:pathData="M83.212,114.755L83.007,118.423L114.467,118.564L114.186,117.062L83.212,114.755Z"
        android:fillColor="#E4F4FC"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M116.036,112.168L120.348,115.481"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M121.446,118.969L126.185,118.039"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M114.802,118.373L82.591,115.338L80.294,72.242C80.113,68.828 81.349,65.49 83.714,63.009C86.078,60.528 89.363,59.124 92.799,59.124H104.389C107.803,59.123 111.068,60.51 113.43,62.962C115.792,65.414 117.046,68.719 116.901,72.112L114.802,118.373Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M63.74,36.71L93.48,36.71A0.687,0.687 0,0 1,94.166 37.397L94.166,38.504A0.687,0.687 0,0 1,93.48 39.191L63.74,39.191A0.687,0.687 0,0 1,63.053 38.504L63.053,37.397A0.687,0.687 0,0 1,63.74 36.71z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M66.205,42.742h27.96v2.48h-27.96z"
        android:fillColor="#529ED6"/>
    <path
        android:pathData="M68.477,60.459C72.224,60.459 75.26,57.437 75.26,53.71C75.26,49.983 72.224,46.961 68.477,46.961C64.731,46.961 61.694,49.983 61.694,53.71C61.694,57.437 64.731,60.459 68.477,60.459Z"
        android:fillColor="#BEE3F7"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M78.789,46.961L93.097,46.961A1.068,1.068 0,0 1,94.166 48.029L94.166,52.071A1.068,1.068 0,0 1,93.097 53.14L78.789,53.14A1.068,1.068 0,0 1,77.721 52.071L77.721,48.029A1.068,1.068 0,0 1,78.789 46.961z"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#529ED6"/>
    <path
        android:pathData="M77.721,54.336h2.728v1.947h-2.728z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M81.201,54.336h2.728v1.947h-2.728z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M84.678,54.336h2.728v1.947h-2.728z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M88.158,54.336h2.728v1.947h-2.728z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M91.636,54.336h2.728v1.947h-2.728z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M85.944,59.98C86.551,59.98 87.043,59.491 87.043,58.887C87.043,58.284 86.551,57.794 85.944,57.794C85.338,57.794 84.846,58.284 84.846,58.887C84.846,59.491 85.338,59.98 85.944,59.98Z"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M81.547,59.98C82.153,59.98 82.645,59.491 82.645,58.887C82.645,58.284 82.153,57.794 81.547,57.794C80.94,57.794 80.449,58.284 80.449,58.887C80.449,59.491 80.94,59.98 81.547,59.98Z"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M62.823,45.586C63.714,45.586 64.436,44.868 64.436,43.982C64.436,43.096 63.714,42.378 62.823,42.378C61.933,42.378 61.211,43.096 61.211,43.982C61.211,44.868 61.933,45.586 62.823,45.586Z"
        android:fillColor="#529ED6"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M63.052,37.95H61.377V40.461"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M131.5,78.321L118.993,89.333L105.499,77.903C103.829,76.489 102.792,74.472 102.616,72.298C102.44,70.123 103.14,67.967 104.562,66.306C107.521,62.847 112.738,62.43 116.215,65.374L131.5,78.321Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M97.654,118.969L97.885,119.347C98.326,120.069 99.113,120.509 99.962,120.509V120.509C100.579,120.51 101.173,120.277 101.624,119.858L102.121,119.392"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M91.178,179.913L97.046,137.489L87.62,179.913H91.178Z"
        android:fillColor="#3A9CE2"/>
    <path
        android:pathData="M86.833,179.912H77.976V176.37H79.08H85.781H86.833V179.912Z"
        android:fillColor="#0D9AFF"/>
    <path
        android:pathData="M78.836,176.37H85.972"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M108.793,179.912H99.935V176.37H101.04H107.74H108.793V179.912Z"
        android:fillColor="#0D9AFF"/>
    <path
        android:pathData="M100.796,176.37H107.932"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M76.361,77.834L81.327,79.099"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M116.25,86.948L115.385,108.086"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M82.096,112.431L88.794,113.083"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M81.92,110.443L86.903,110.928"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M93.514,48.96H103.537V60.867C103.537,63.62 101.293,65.853 98.525,65.853C95.758,65.853 93.514,63.62 93.514,60.867V48.96Z"
        android:fillColor="#FFCCAE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M93.516,57.315L103.539,52.398V48.96H93.516V57.315Z"
        android:fillColor="#E7B191"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M96.247,52.214C102.401,52.214 107.39,47.25 107.39,41.127C107.39,35.003 102.401,30.039 96.247,30.039C90.093,30.039 85.104,35.003 85.104,41.127C85.104,47.25 90.093,52.214 96.247,52.214Z"
        android:fillColor="#FFCCAE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M101.125,48.001C102.186,47.82 102.901,46.823 102.729,45.766V45.749"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M91.226,43.702C91.226,44.51 90.567,45.166 89.755,45.166C88.943,45.166 88.284,44.51 88.284,43.702"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M98.044,43.702C98.044,44.51 97.385,45.166 96.573,45.166C95.761,45.166 95.102,44.51 95.102,43.702"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M96.31,41.714C95.51,41.856 94.746,41.326 94.604,40.53"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M92.37,40.797C92,41.517 91.115,41.802 90.391,41.435"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M92.424,43.143V46.917C92.424,47.516 92.912,48.001 93.514,48.001V48.001C94.116,48.001 94.604,47.516 94.604,46.917V46.115"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M92.797,41.742C92.277,41.742 91.798,41.458 91.552,41.001C91.306,40.545 91.331,39.991 91.618,39.559L93.558,36.639H96.948L93.976,41.109C93.715,41.504 93.272,41.742 92.797,41.742Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M100.038,40.312C99.517,40.311 99.039,40.027 98.793,39.57C98.547,39.114 98.572,38.56 98.859,38.128L100.798,35.208H104.191L101.221,39.681C100.958,40.075 100.514,40.312 100.038,40.312Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M103.496,40.177C102.975,40.177 102.497,39.892 102.251,39.436C102.004,38.98 102.03,38.426 102.317,37.994L104.256,35.076H107.646L104.674,39.546C104.413,39.94 103.97,40.178 103.496,40.177Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M109.131,40.797L100.378,36.639"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M98.525,67.759C100.156,67.757 101.719,67.111 102.871,65.962C104.023,64.813 104.669,63.257 104.668,61.634V59.112C104.574,59.112 104.483,59.112 104.389,59.112H92.799C92.66,59.112 92.522,59.112 92.383,59.112V61.632C92.381,63.255 93.026,64.812 94.178,65.961C95.33,67.11 96.894,67.757 98.525,67.759Z"
        android:fillColor="#FFCCAE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M98.525,68.979C100.916,68.981 103.138,67.751 104.395,65.727"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M105.124,64.118C105.325,63.464 105.427,62.783 105.427,62.098"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M72.864,58.076C70.295,60.632 66.157,60.719 63.481,58.273C63.547,58.343 63.612,58.414 63.68,58.49C65.372,60.29 67.916,61.029 70.316,60.418C72.716,59.806 74.59,57.942 75.204,55.554C75.819,53.166 75.076,50.635 73.267,48.951C73.2,48.884 73.13,48.819 73.049,48.754C75.504,51.41 75.422,55.518 72.864,58.076Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M61.019,62.54H85.343"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M59.196,57.794V60.728"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M59.196,46.961V55.31"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.014,143.845C82.957,144.431 83.22,145.003 83.703,145.343C84.186,145.682 84.816,145.738 85.352,145.489C85.888,145.239 86.248,144.723 86.296,144.136"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M107.154,147.538C107.097,148.124 107.36,148.695 107.843,149.035C108.327,149.375 108.956,149.43 109.492,149.181C110.028,148.931 110.388,148.415 110.436,147.828"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M103.537,50.785V59.98"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M93.514,52.52V59.98"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M69.142,187.327L81.009,185.302V185.302C81.976,186.764 83.449,187.82 85.149,188.27"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M87.019,188.511H88.108L90.028,194.913H64.992C63.956,192.499 64.777,189.693 66.953,188.209"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M73.76,185.302L74.775,189.354"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M76.128,184.76L77.146,188.812"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M100.794,194.913H125.835C126.5,193.364 126.416,191.598 125.606,190.119C124.797,188.639 123.352,187.61 121.684,187.325L109.815,185.3C108.486,187.311 106.226,188.518 103.807,188.509H102.718L100.794,194.913Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M121.684,187.327L109.815,185.302V185.302C108.849,186.765 107.375,187.821 105.675,188.27"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M103.808,188.511H102.718L100.788,194.917H125.835C126.87,192.503 126.049,189.698 123.874,188.214"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M117.069,185.302L116.051,189.354"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M114.701,184.76L113.683,188.812"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M124.383,88.383L121.988,92.973"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M132.591,89.333H134.395"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#D1A299"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M52.611,62.104H53.89"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#D1A299"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M96.928,52.092C91.298,52.088 86.554,47.911 85.864,42.351C85.173,36.792 88.754,31.591 94.213,30.224C90.268,30.95 87.02,33.731 85.709,37.504C84.399,41.277 85.228,45.46 87.88,48.455C90.533,51.451 94.599,52.796 98.525,51.978C97.996,52.053 97.463,52.092 96.928,52.092Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M84.607,42.239C84.039,42.236 83.518,41.926 83.248,41.429C82.978,40.932 83.003,40.328 83.312,39.854L85.452,36.639H89.156L85.888,41.547C85.603,41.975 85.123,42.234 84.607,42.239Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M88.163,43.29C87.49,43.292 86.87,42.926 86.551,42.336C86.232,41.746 86.265,41.03 86.637,40.472L89.006,36.634H93.691L89.679,42.473C89.343,42.981 88.774,43.288 88.163,43.29Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M102.079,50.575C106.996,47.566 108.779,41.313 106.183,36.184C103.587,31.055 97.476,28.757 92.119,30.895C86.762,33.034 83.943,38.897 85.63,44.389C87.317,49.881 92.95,53.174 98.593,51.967"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M108.499,40.582L89.925,31.652L91.466,27.923C93,23.994 97.446,22.045 101.395,23.572L106.189,25.424C110.138,26.95 112.096,31.374 110.562,35.303L108.499,40.582Z"
        android:fillColor="#0D9AFF"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M108.499,40.582L89.925,31.652L91.466,27.923C93,23.994 97.446,22.045 101.395,23.572L106.189,25.424C110.138,26.95 112.096,31.374 110.562,35.303L108.499,40.582Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M105.62,26.037C109.108,27.386 110.837,31.293 109.481,34.763"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M109.131,35.895L108.597,37.005"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M92.925,25.567L100.378,36.639L96.752,23.286C95.278,23.654 93.947,24.448 92.925,25.567Z"
        android:fillColor="#BEE3F7"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M107.198,44.752C108.672,44.752 109.867,43.563 109.867,42.096C109.867,40.629 108.672,39.44 107.198,39.44C105.724,39.44 104.529,40.629 104.529,42.096C104.529,43.563 105.724,44.752 107.198,44.752Z"
        android:fillColor="#FFCCAE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M108.383,41.714C107.816,41.489 107.172,41.763 106.945,42.328"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M105.309,43.973C106.083,44.743 107.25,44.964 108.254,44.533C109.258,44.101 109.896,43.104 109.864,42.016C109.831,40.928 109.135,39.97 108.106,39.598C107.078,39.227 105.926,39.517 105.2,40.331"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M100.378,36.639H85.67L79.301,25.914C78.575,24.691 78.563,23.174 79.271,21.941C79.979,20.707 81.296,19.945 82.724,19.945C87.52,19.946 91.96,22.466 94.401,26.575L100.378,36.639Z"
        android:fillColor="#B9E2FF"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M91.677,23.303C92.753,24.244 93.672,25.348 94.401,26.575L100.377,36.639H85.67"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M79.301,25.914C78.575,24.691 78.563,23.174 79.271,21.941C79.979,20.707 81.296,19.945 82.724,19.945V19.945C84.969,19.946 87.18,20.502 89.156,21.562"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M117.259,87.871L115.051,85.791"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M80.685,79.323L81.355,95.926"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M133.587,178.284C133.609,178.54 133.622,178.796 133.622,179.056C133.625,182.593 131.546,185.804 128.307,187.262C125.068,188.721 121.271,188.157 118.603,185.82C118.95,189.858 121.966,193.167 125.971,193.905C129.976,194.643 133.983,192.628 135.761,188.982C137.538,185.336 136.649,180.957 133.587,178.284Z"
        android:fillColor="#F3D5CB"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M116.023,175.179C115.019,175.18 114.056,174.783 113.346,174.077C112.636,173.371 112.238,172.412 112.238,171.414V163.773H116.215"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M133.217,194.681H119.034C117.31,194.681 115.668,193.945 114.526,192.659C113.383,191.374 112.849,189.663 113.06,187.96L115.546,167.86C115.546,163.682 115.415,157.133 119.995,157.133C124.571,157.133 128.286,160.519 128.286,164.695L133.217,194.681Z"
        android:fillColor="#EDA257"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M107.054,153.876L121,157.366C124.735,158.301 127.001,162.071 126.061,165.787L124.323,172.662L117.278,170.899C109.731,169.01 105.151,161.391 107.05,153.882L107.054,153.876Z"
        android:fillColor="#EDA257"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M110.157,154.271L106.235,153.215C105.88,153.12 105.5,153.22 105.24,153.478C104.979,153.736 104.876,154.113 104.971,154.466C105.065,154.82 105.341,155.097 105.696,155.193L106.66,155.451L106.612,155.626C106.515,155.979 106.616,156.357 106.875,156.616C107.135,156.876 107.513,156.978 107.869,156.884C108.224,156.79 108.503,156.515 108.599,156.162L108.647,155.988L109.61,156.246C110.154,156.384 110.709,156.062 110.856,155.522C111.004,154.983 110.688,154.426 110.148,154.271H110.157Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M118.886,158.247C118.623,158.637 118.099,158.754 117.693,158.514C117.287,158.273 117.141,157.759 117.361,157.343"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M121.359,159.711C121.206,160.001 120.905,160.183 120.576,160.185C120.247,160.186 119.944,160.006 119.788,159.718C119.633,159.429 119.65,159.078 119.834,158.807"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M121.568,192.029L120.797,179.834L120.81,179.784C120.948,179.245 121.433,178.866 121.992,178.86C122.552,178.855 123.045,179.224 123.194,179.76V179.76V192.029"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M119.489,192.68H118.006C117.472,192.68 116.961,192.891 116.583,193.266C116.206,193.641 115.994,194.15 115.994,194.681V194.681"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M117.962,192.68H116.478C115.945,192.68 115.433,192.891 115.056,193.266C114.679,193.641 114.467,194.15 114.467,194.681V194.681"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M120.316,192.68H119.226C118.411,192.68 117.749,193.575 117.749,194.681V194.681"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M125.299,192.68H123.813C123.279,192.68 122.768,192.891 122.391,193.266C122.015,193.641 121.803,194.15 121.804,194.681V194.681"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M123.769,192.68H122.277C121.743,192.68 121.232,192.891 120.855,193.266C120.478,193.641 120.266,194.15 120.266,194.681V194.681"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M126.115,192.68H125.026C124.211,192.68 123.551,193.575 123.551,194.681V194.681"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M127.281,188.398L127.989,183.303"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M115.7,179.455L117.232,190.366"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M118.542,171.201L115.256,170.213"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M116.929,163.284C115.2,165.58 112.139,166.441 109.458,165.385"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M127.634,174.252V174.252C125.547,174.252 123.853,172.571 123.847,170.495V162.833"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M110.02,157.601C110.163,157.601 110.279,157.486 110.279,157.343C110.279,157.201 110.163,157.085 110.02,157.085C109.877,157.085 109.761,157.201 109.761,157.343C109.761,157.486 109.877,157.601 110.02,157.601Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M108.852,158.115C108.995,158.115 109.111,158 109.111,157.857C109.111,157.715 108.995,157.599 108.852,157.599C108.709,157.599 108.593,157.715 108.593,157.857C108.593,158 108.709,158.115 108.852,158.115Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M109.76,159.065C109.903,159.065 110.02,158.949 110.02,158.807C110.02,158.664 109.903,158.549 109.76,158.549C109.617,158.549 109.501,158.664 109.501,158.807C109.501,158.949 109.617,159.065 109.76,159.065Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M131.153,182.124L131.774,186.169"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M49.758,69.985C51.235,69.985 52.432,68.794 52.432,67.325C52.432,65.856 51.235,64.665 49.758,64.665C48.282,64.665 47.084,65.856 47.084,67.325C47.084,68.794 48.282,69.985 49.758,69.985Z"
        android:fillColor="#F3D5CB"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M44.908,68.205C47.345,68.205 49.32,66.24 49.32,63.815C49.32,61.39 47.345,59.425 44.908,59.425C42.471,59.425 40.495,61.39 40.495,63.815C40.495,66.24 42.471,68.205 44.908,68.205Z"
        android:fillColor="#EDA257"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M40.045,82.666C46.241,82.666 51.264,77.668 51.264,71.503C51.264,65.338 46.241,60.34 40.045,60.34C33.848,60.34 28.825,65.338 28.825,71.503C28.825,77.668 33.848,82.666 40.045,82.666Z"
        android:fillColor="#F3D5CB"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M49.674,130.297L44.581,162.75C44.355,164.197 44.437,165.674 44.823,167.087C45.462,169.437 46.418,171.262 45.024,173.168C44.744,173.498 44.437,173.804 44.104,174.081C43.065,174.948 42.546,175.704 42.361,176.936V176.992C42.202,178.071 42.438,179.172 43.026,180.092C43.599,181.023 43.963,181.948 43.426,182.774C42.829,183.696 39.96,183.449 39.96,183.449H35.859V130.297H49.674Z"
        android:fillColor="#0D9AFF"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M57.125,130.297L52.582,162.75C52.383,164.197 52.457,165.667 52.8,167.087C53.369,169.437 53.875,171.438 53.334,172.658C53.085,173.085 52.763,173.464 52.382,173.779C51.404,174.566 50.767,175.696 50.602,176.936V176.992C50.459,178.062 50.667,179.149 51.194,180.092C51.707,181.023 52.029,181.948 51.554,182.774C51.02,183.696 48.466,183.449 48.466,183.449H44.819V130.297H57.125Z"
        android:fillColor="#0D9AFF"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M59.328,131.117L32.939,134.833V97.797C32.939,89.933 38.848,83.559 46.137,83.559C53.426,83.559 59.335,89.933 59.335,97.797V131.117H59.328Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M48.938,119.945L54.493,126.05L59.328,128.834V122.091L48.938,116.886V119.945Z"
        android:fillColor="#E4F4FC"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M45.453,183.106h5.066v2.638h-5.066z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M50.519,184.173L45.453,185.744V183.106H50.519V184.173Z"
        android:fillColor="#F0917A"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M62.067,194.959H44.873V185.745H51.025L51.447,186.423C52.348,187.865 53.935,188.74 55.642,188.734H57.043C59.671,188.734 61.926,190.731 62.054,193.341C62.054,193.43 62.054,193.519 62.054,193.61L62.067,194.959Z"
        android:fillColor="#BA7D41"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M53.264,185.066C53.264,185.56 52.863,187.316 52.366,187.316C51.869,187.316 51.466,185.56 51.466,185.066C51.49,184.589 51.885,184.215 52.365,184.215C52.845,184.215 53.24,184.589 53.264,185.066V185.066Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M54.617,188.21C54.12,188.21 52.355,187.811 52.355,187.317C52.355,186.822 54.12,186.421 54.617,186.421C55.097,186.445 55.473,186.838 55.473,187.315C55.473,187.793 55.097,188.186 54.617,188.21Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M57.043,188.73C59.671,188.73 61.926,190.727 62.054,193.337C62.054,193.426 62.054,193.515 62.054,193.606V194.959H44.873"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M44.873,185.745H51.025L51.447,186.423C52.348,187.865 53.935,188.74 55.642,188.734"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M44.037,193.55H59.562"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M57.801,189.356C59.71,189.738 61.123,191.347 61.248,193.281"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M36.438,183.106h5.066v2.638h-5.066z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M41.504,184.173L36.438,185.744V183.106H41.504V184.173Z"
        android:fillColor="#F0917A"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M53.051,194.959H35.859V185.745H42.008L42.433,186.423C43.332,187.865 44.919,188.74 46.625,188.734H48.028C50.654,188.734 52.909,190.731 53.04,193.341C53.04,193.43 53.04,193.519 53.04,193.61L53.051,194.959Z"
        android:fillColor="#BA7D41"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M44.25,185.066C44.25,185.56 43.847,187.316 43.35,187.316C42.853,187.316 42.452,185.56 42.452,185.066C42.476,184.589 42.871,184.215 43.351,184.215C43.831,184.215 44.226,184.589 44.25,185.066Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M45.601,188.21C45.106,188.21 43.341,187.811 43.341,187.316C43.341,186.822 45.106,186.421 45.601,186.421C45.932,186.405 46.246,186.572 46.417,186.855C46.588,187.138 46.588,187.492 46.417,187.776C46.246,188.059 45.932,188.226 45.601,188.21Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M48.028,188.73C50.654,188.73 52.909,190.727 53.04,193.337C53.04,193.426 53.04,193.515 53.04,193.606V194.959H35.859"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M35.859,185.745H42.008L42.433,186.423C43.332,187.865 44.919,188.74 46.625,188.734"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M35.022,193.55H50.547"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M48.784,189.356C50.693,189.737 52.107,191.347 52.231,193.281"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M37.24,182.818V182.276"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M37.24,181.183V153.337"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M37.24,152.791V152.249"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M42.354,177.285V177.339C42.195,178.367 42.433,179.416 43.019,180.277C43.592,181.157 43.956,182.035 43.42,182.818"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M48.322,141.248L44.581,163.839C44.353,165.213 44.436,166.62 44.823,167.958C45.461,170.185 46.418,171.917 45.023,173.721"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M52.432,108.726L53.925,113.712C54.311,115.001 54.222,116.385 53.672,117.614L53.214,118.638L56.156,120.806H67.214C67.214,120.806 69.339,118.963 69.339,118.8C69.339,118.638 69.665,114.681 69.774,114.302C69.883,113.922 67.813,110.996 66.996,110.67C66.179,110.345 58.226,109.424 58.226,109.424L52.432,108.726Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M64.316,96.984C57.127,96.602 50.851,101.783 49.902,108.884C51.127,110.812 53.229,112.015 55.519,112.099L62.71,112.368C64.358,112.43 65.859,113.331 66.682,114.753C67.319,115.847 67.17,117.226 66.314,118.161C65.122,119.462 63.514,119.661 61.59,119.572L58.603,119.41C57.121,119.193 55.665,118.976 54.299,118.38C52.377,117.411 50.726,115.835 48.954,114.603V118.796C49.877,120.294 51.17,121.532 52.711,122.391C53.558,122.875 54.479,123.221 55.437,123.414C55.998,123.532 56.571,123.584 57.145,123.57C57.6,123.557 58.062,123.529 58.439,123.529H64.316C71.563,123.529 77.518,117.66 77.39,110.451C77.39,110.295 77.39,110.139 77.374,109.983C76.991,102.97 71.364,97.368 64.316,96.984Z"
        android:fillColor="#0D9AFF"/>
    <path
        android:pathData="M77.368,109.977C77.139,105.607 74.83,101.609 71.152,99.21C67.474,96.812 62.868,96.301 58.749,97.836C63.8,99.323 67.881,103.042 69.813,107.917C71.744,112.793 71.312,118.282 68.64,122.798C73.78,121.014 77.486,116.153 77.384,110.441C77.39,110.289 77.375,110.133 77.368,109.977Z"
        android:fillColor="#108DE6"/>
    <path
        android:pathData="M51.078,110.276C50.806,109.978 50.561,109.657 50.344,109.317C51.022,104.192 54.54,99.882 59.443,98.17C64.346,96.458 69.799,97.636 73.548,101.215C69.886,97.394 64.345,96.003 59.3,97.639C54.255,99.276 50.602,103.649 49.908,108.884C50.235,109.397 50.629,109.865 51.078,110.276Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M41.94,66.501C41.573,67.092 40.887,67.407 40.197,67.303"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M42.899,65.099C43.522,64.776 44.278,64.856 44.819,65.302"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M25.877,64.943C27.325,63.836 29.4,64.105 30.514,65.545"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M25.877,61.66C27.699,61.68 29.161,63.164 29.145,64.977"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M45.967,56.814C46.491,58.115 46.474,59.57 45.92,60.859C45.366,62.147 44.32,63.164 43.012,63.685C40.288,64.769 30.246,66.43 29.154,63.722C28.062,61.014 36.345,54.961 39.062,53.875C41.784,52.79 44.875,54.106 45.967,56.814Z"
        android:fillColor="#EDA257"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M33.878,74.768C32.037,75.915 29.669,75.451 28.588,73.734L26.513,70.436C25.302,68.511 25.99,65.909 28.052,64.626C30.449,63.132 33.532,64.108 34.371,66.623L35.606,70.332C36.149,71.958 35.432,73.801 33.878,74.768Z"
        android:fillColor="#EDA257"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M29.978,79.607C31.455,79.607 32.652,78.416 32.652,76.947C32.652,75.478 31.455,74.287 29.978,74.287C28.501,74.287 27.305,75.478 27.305,76.947C27.305,78.416 28.501,79.607 29.978,79.607Z"
        android:fillColor="#F3D5CB"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M28.737,77.09C28.942,76.88 29.223,76.759 29.517,76.755C29.811,76.75 30.095,76.862 30.306,77.066"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M42.335,69.159C42.552,69.159 42.729,68.841 42.729,68.448C42.729,68.055 42.552,67.737 42.335,67.737C42.117,67.737 41.94,68.055 41.94,68.448C41.94,68.841 42.117,69.159 42.335,69.159Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M44.052,68.86C44.27,68.86 44.446,68.542 44.446,68.149C44.446,67.756 44.27,67.438 44.052,67.438C43.834,67.438 43.657,67.756 43.657,68.149C43.657,68.542 43.834,68.86 44.052,68.86Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M51.024,69.56C51.083,69.831 51.113,70.107 51.114,70.384C51.111,71.65 50.493,72.836 49.453,73.566C48.414,74.296 47.083,74.479 45.884,74.057C46.28,75.896 47.938,77.193 49.827,77.141C51.717,77.09 53.301,75.704 53.594,73.846C53.888,71.988 52.807,70.186 51.024,69.56Z"
        android:fillColor="#EDA257"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M40.493,78.495C38.711,78.838 36.883,78.19 35.72,76.803C34.556,75.417 34.243,73.511 34.9,71.828C32.538,72.272 30.83,74.327 30.834,76.718C30.839,79.109 32.555,81.158 34.918,81.593C37.281,82.028 39.62,80.726 40.484,78.495H40.493Z"
        android:fillColor="#EDA257"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M55.123,83.91C56.561,82.381 56.95,80.148 56.113,78.227C55.275,76.305 53.371,75.061 51.266,75.061H51.227C49.528,73.563 47.288,72.821 45.026,73.005C42.764,73.188 40.675,74.283 39.243,76.034C38.009,75.533 36.605,75.676 35.499,76.415C34.392,77.155 33.728,78.394 33.728,79.72C33.729,80.133 33.793,80.544 33.919,80.938C33.455,82.61 33.849,84.402 34.973,85.728C36.096,87.054 37.804,87.744 39.539,87.572C39.323,88.011 39.211,88.493 39.212,88.982C39.212,89.966 39.663,90.897 40.438,91.51C41.213,92.122 42.227,92.349 43.191,92.125C43.557,94.3 45.132,96.079 47.253,96.716C49.375,97.353 51.676,96.737 53.189,95.126C54.703,93.516 55.167,91.19 54.386,89.127C55.289,88.616 55.902,87.718 56.045,86.695C56.189,85.672 55.846,84.641 55.118,83.904L55.123,83.91Z"
        android:fillColor="#EDA257"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M43.359,77.367C42.416,77.363 41.519,76.96 40.893,76.257"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M45.919,60.86C45.365,62.147 44.319,63.161 43.012,63.679C41.201,64.4 36.153,65.376 32.653,65.153"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#F3D5CB"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M43.501,83.598C43.502,84.214 43.091,84.755 42.496,84.923C41.901,85.09 41.266,84.844 40.942,84.32C40.618,83.795 40.685,83.121 41.104,82.668"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#EDA257"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M45.762,84.153C45.603,84.675 45.148,85.054 44.604,85.118C44.059,85.183 43.528,84.921 43.25,84.45"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#EDA257"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M33.91,68.968C33.751,69.491 33.297,69.87 32.752,69.935C32.208,69.999 31.676,69.738 31.398,69.267"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#EDA257"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M35.433,70.397C35.274,70.919 34.82,71.299 34.275,71.363C33.731,71.428 33.199,71.166 32.921,70.696"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#EDA257"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M48.41,89.844C48.771,89.481 49.271,89.287 49.785,89.311"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#EDA257"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M45.576,89.814C46.129,89.26 46.959,89.085 47.691,89.367C48.422,89.649 48.916,90.335 48.95,91.115"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#EDA257"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M43.657,62.104C43.288,62.412 42.873,62.659 42.426,62.837"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M44.947,56.973C45.558,58.477 45.308,60.193 44.293,61.463"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M27.068,70.332C26.842,69.973 26.685,69.575 26.604,69.159C26.363,67.602 27.079,66.051 28.424,65.218"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M55.765,81.172C55.759,82.817 54.42,84.148 52.767,84.153"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M45.731,61.985C48.462,63.607 50.373,66.306 50.989,69.411"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M32.538,72.981C31.441,73.941 30.821,75.331 30.843,76.784C30.865,78.237 31.526,79.608 32.651,80.535"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M56.55,80.318C56.55,77.414 54.184,75.061 51.266,75.061H51.227"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M50.191,74.285C46.627,72.025 41.928,72.778 39.257,76.036"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#F3D5CB"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M43.437,69.459L44.908,74.607L41.639,73.233"
        android:fillColor="#F3D5CB"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M43.437,69.459L44.908,74.607L41.639,73.233"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M48.961,114.597L52.434,117.218"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M66.318,118.154C65.126,119.455 63.518,119.655 61.594,119.566L58.607,119.403"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M76.146,110.428C76.146,110.287 76.146,110.144 76.13,109.994C76.082,109.014 75.915,108.043 75.633,107.102"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M50.588,119.02V119.02C51.636,120.235 52.98,121.163 54.491,121.714"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M74.52,104.504C72.402,100.774 68.521,98.375 64.222,98.139"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M49.429,114.933L48.959,114.597V118.79C49.107,119.032 49.732,119.858 49.896,120.09L49.429,114.933Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M54.304,118.373C52.382,117.404 50.73,115.828 48.959,114.597V118.79V118.79C49.881,120.288 51.175,121.525 52.715,122.384C53.563,122.869 54.483,123.214 55.441,123.407V123.407C56.002,123.525 56.576,123.578 57.149,123.564C57.605,123.551 58.067,123.522 58.444,123.522H64.316C71.563,123.522 77.518,117.653 77.39,110.445C77.39,110.289 77.39,110.133 77.374,109.977C76.987,102.904 71.256,97.286 64.143,97.005C57.03,96.724 50.869,101.873 49.92,108.893C51.144,110.821 53.246,112.024 55.537,112.108L62.728,112.377"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M28.709,122.081C27.437,121.989 26.261,121.372 25.465,120.38C24.669,119.388 24.325,118.111 24.517,116.856L27.875,94.712C28.13,93.316 28.933,92.079 30.105,91.273C31.278,90.466 32.724,90.157 34.126,90.412C37.046,90.944 38.981,93.729 38.449,96.635L33.516,118.532C33.021,120.738 30.975,122.249 28.709,122.081Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M74.367,122.273C73.832,122.994 72.815,123.147 72.091,122.616C71.363,122.081 71.208,121.055 71.746,120.329L74.962,115.993C75.497,115.271 76.514,115.118 77.238,115.649C77.967,116.184 78.121,117.21 77.583,117.936L74.367,122.273Z"
        android:fillColor="#F3D5CB"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M73.642,121.367C73.338,121.916 72.647,122.116 72.097,121.814L69.611,120.451C69.057,120.147 68.856,119.452 69.162,118.899C69.466,118.35 70.157,118.151 70.707,118.452L73.193,119.816C73.746,120.119 73.948,120.815 73.642,121.367Z"
        android:fillColor="#F3D5CB"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M74.556,119.344C74.252,119.893 73.562,120.093 73.011,119.791L70.525,118.428C69.972,118.124 69.77,117.429 70.076,116.876C70.38,116.328 71.071,116.128 71.621,116.429L74.107,117.793C74.661,118.096 74.862,118.792 74.556,119.344Z"
        android:fillColor="#F3D5CB"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M75.6,117.46C75.296,118.009 74.606,118.208 74.056,117.907L71.569,116.543C71.016,116.24 70.815,115.544 71.12,114.992C71.424,114.443 72.115,114.243 72.665,114.545L75.151,115.908C75.705,116.212 75.906,116.908 75.6,117.46Z"
        android:fillColor="#F3D5CB"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M77.59,117.839C77.078,118.203 76.368,118.084 76.001,117.573L74.359,115.281C73.991,114.766 74.111,114.05 74.627,113.684C75.14,113.32 75.85,113.44 76.216,113.951L77.858,116.243C78.227,116.757 78.107,117.473 77.59,117.839Z"
        android:fillColor="#F3D5CB"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M76.099,115C75.764,115.531 75.063,115.69 74.531,115.357L72.129,113.854C71.594,113.519 71.433,112.813 71.77,112.279C72.106,111.748 72.807,111.589 73.338,111.922L75.74,113.425C76.276,113.76 76.437,114.466 76.099,115Z"
        android:fillColor="#F3D5CB"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M75.971,115.134L75.396,115.908"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#F0917A"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M74.529,117.077L73.954,117.853"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#F0917A"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M73.15,118.939L72.574,119.713"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#F0917A"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M72.097,121.81L70.055,120.689"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M47.66,141.248L50.968,139.782"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M34.126,90.412C35.075,90.584 35.959,91.006 36.687,91.635"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M24.52,116.856L27.875,94.714"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M36.981,103.154L33.517,118.532"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M38.768,135.75C36.693,129.81 32.257,117.12 32.257,117.12C31.463,115.029 29.116,113.974 27.014,114.763C24.918,115.558 23.864,117.892 24.659,119.98L34.072,139.058L38.768,135.75Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M36.092,103.396L38.539,102.384"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M40.478,140.934L37.005,142.61L35.268,138.204L38.508,135.932L40.478,140.934Z"
        android:fillColor="#F3D5CB"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M38.768,135.75C36.693,129.81 32.257,117.12 32.257,117.12"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M27.014,114.763V114.763C24.918,115.558 23.864,117.892 24.659,119.98L30.86,132.554"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M41.084,139.709L39.308,141.792L36.397,142.727L36.066,142.107C35.548,141.136 35.368,140.022 35.554,138.939L38.977,137.135L39.143,137.246C39.866,137.738 40.447,138.411 40.827,139.197L41.084,139.709Z"
        android:fillColor="#F3D5CB"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M41.729,136.836C41.729,140.091 39.077,142.729 35.806,142.729"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M41.076,146.959C39.351,146.837 37.766,145.971 36.739,144.587"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M37.402,147.145C36.746,147.128 36.098,147.002 35.484,146.774"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M38.63,136.242L35.558,138.939L35.268,138.204L38.508,135.932L38.63,136.242Z"
        android:fillColor="#F0917A"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M35.809,138.373L35.486,139.648"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M35.249,118.156L37.874,119.325"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M36.112,120.368L38.286,120.812"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M33.215,119.863L36.981,103.154L36.111,116.314L40.478,133.771L38.286,134.367L33.215,119.863Z"
        android:fillColor="#E4F4FC"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M39.018,132.546L60.025,129.701"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M31.07,79.375C29.926,79.884 28.58,79.535 27.832,78.534C27.084,77.534 27.135,76.15 27.955,75.207C28.775,74.264 30.143,74.014 31.246,74.605"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M43.32,161.283L45.124,160.569"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M51.516,160.925L53.318,160.214"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M59.328,124.335V127.642"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M55.539,83.535L55.556,83.555C55.628,83.466 55.698,83.373 55.774,83.277C55.692,83.366 55.617,83.453 55.539,83.535Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M49.355,96.589C46.514,96.586 44.089,94.544 43.616,91.757C42.398,92.039 41.126,91.6 40.346,90.627C39.566,89.655 39.417,88.323 39.964,87.204C39.79,87.221 39.614,87.23 39.439,87.23C36.519,87.23 34.495,85.129 34.057,82.547C33.97,82.046 33.66,82.183 33.68,82.67C33.774,85.09 36.199,87.657 39.127,87.657C39.306,87.657 39.548,87.553 39.72,87.535C39.173,88.654 39.322,89.986 40.102,90.959C40.882,91.931 42.154,92.371 43.372,92.088C43.764,94.4 45.519,96.249 47.816,96.772C50.114,97.294 52.501,96.386 53.864,94.473C52.757,95.816 51.101,96.593 49.355,96.589Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M52.432,95.807C54.501,94.261 55.3,91.544 54.393,89.133"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M39.016,87.605C39.192,87.605 39.368,87.596 39.543,87.579C39.327,88.018 39.215,88.5 39.216,88.988C39.216,89.973 39.667,90.904 40.442,91.516C41.217,92.129 42.231,92.355 43.195,92.132C43.666,94.921 46.094,96.964 48.937,96.964"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M33.625,81.172C34.275,81.491 34.986,81.666 35.71,81.684"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M33.732,82.347C33.735,84.397 34.935,86.259 36.807,87.117"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M43.978,91.635H43.967C43.752,91.822 43.513,91.979 43.256,92.104V92.104"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M40.326,87.26H40.313C40.067,87.403 39.804,87.513 39.529,87.587V87.587"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M125.76,118.457C124.13,116.835 124.13,114.197 125.76,112.575L139.138,99.264C140.757,97.653 143.373,97.653 144.991,99.264C146.622,100.886 146.622,103.524 144.991,105.146L131.613,118.457C129.994,120.068 127.379,120.068 125.76,118.457Z"
        android:fillColor="#CB765E"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M135.051,115.034L136.698,113.395L130.767,107.592L130.122,108.233L135.051,115.034Z"
        android:fillColor="#B76452"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M163.746,105.145C162.116,103.523 162.116,100.884 163.746,99.262C165.364,97.652 167.98,97.652 169.599,99.262L182.977,112.573C184.607,114.195 184.607,116.834 182.977,118.456C181.359,120.066 178.743,120.066 177.124,118.456L163.746,105.145Z"
        android:fillColor="#CB765E"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M178.828,108.446L177.713,107.333L171.903,113.261L174.154,115.5L178.828,108.446Z"
        android:fillColor="#B76452"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M194.654,103.286L192.07,101.89L196.669,99.232C196.919,99.104 197.216,99.114 197.456,99.258C197.679,99.391 197.825,99.623 197.846,99.881C197.867,100.139 197.762,100.391 197.563,100.559L194.654,103.286Z"
        android:fillColor="#B76452"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M179.336,106.939L171.505,114.733L163.75,107.017L159.778,103.069C157.616,100.917 157.616,97.429 159.778,95.277C161.941,93.126 165.446,93.126 167.609,95.277L179.336,106.939Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M184.812,104.489L191.074,103.037L186.037,115.958C185.449,117.47 184.25,118.666 182.734,119.257C181.217,119.848 179.52,119.778 178.057,119.065C177.639,118.861 177.326,118.492 177.196,118.047C177.065,117.602 177.129,117.123 177.371,116.728L177.931,115.806L179.977,112.437L184.812,104.489Z"
        android:fillColor="#CB765E"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M178.671,105.365L171.372,112.628"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M163.589,190.464C161.765,190.463 160.261,189.042 160.165,187.23L158.156,148.617H167.016V187.054C167.016,188.938 165.482,190.464 163.589,190.464Z"
        android:fillColor="#CB765E"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M167.018,154.254V151.529L158.366,152.606L158.527,155.741L167.018,154.254Z"
        android:fillColor="#B76452"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M142.197,190.094C144.016,190.258 145.646,188.975 145.902,187.175L148.776,166.954L155.095,148.11L144.437,147.316L140.91,166.007L139.088,186.386C139.005,187.288 139.286,188.186 139.87,188.882C140.453,189.577 141.291,190.013 142.197,190.094Z"
        android:fillColor="#CB765E"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M142.235,158.976L152.535,155.724L153.57,152.632L143.213,153.777L142.235,158.976Z"
        android:fillColor="#B76452"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M182.633,194.959H158.156L160.222,188.272L167.016,186.627L180.253,191.676C181.655,192.172 182.602,193.479 182.633,194.959Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M141.63,129.755V155.555L153.779,154.087L157.07,147.993L157.429,153.647L168.544,152.303V129.755H141.63Z"
        android:fillColor="#0D9AFF"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M168.544,132.971L166.01,130.386L141.627,134.315V100.138C141.627,96.372 144.75,93.32 148.6,93.32H161.567C165.419,93.32 168.539,96.372 168.539,100.138V132.971H168.544Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M167.657,166.406C167.657,167.311 166.919,168.045 166.01,168.045C165.1,168.045 164.362,167.311 164.362,166.406"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M143.599,145.764V145.764C145.386,144.151 146.588,141.997 147.022,139.635L147.037,139.546"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M167.729,141.959V141.959C165.942,140.346 164.739,138.192 164.306,135.83L164.291,135.741"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M129.399,106.939L137.23,114.733L144.985,107.017L148.957,103.069C149.996,102.036 150.58,100.635 150.58,99.173C150.58,97.712 149.996,96.31 148.957,95.277C146.794,93.126 143.289,93.126 141.126,95.277L129.399,106.939Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M155.087,98.616C152.468,98.616 150.345,96.504 150.345,93.898V84.225H159.828V93.89C159.83,95.142 159.332,96.344 158.442,97.231C157.553,98.118 156.346,98.616 155.087,98.616Z"
        android:fillColor="#CB765E"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M150.345,90.551L159.828,87.568V84.216H155.285L150.345,84.715V90.551Z"
        android:fillColor="#B76452"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M146.035,72.426C146.848,72.426 147.506,71.771 147.506,70.963C147.506,70.155 146.848,69.499 146.035,69.499C145.223,69.499 144.565,70.155 144.565,70.963C144.565,71.771 145.223,72.426 146.035,72.426Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M147.503,64.498L147.503,64.498A2.185,2.185 0,0 1,149.689 66.683L149.689,75.242A2.185,2.185 0,0 1,147.503 77.428L147.503,77.428A2.185,2.185 0,0 1,145.318 75.242L145.318,66.683A2.185,2.185 0,0 1,147.503 64.498z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M151.876,62.438L151.876,62.438A2.185,2.185 0,0 1,154.062 64.624L154.062,75.242A2.185,2.185 0,0 1,151.876 77.428L151.876,77.428A2.185,2.185 0,0 1,149.691 75.242L149.691,64.624A2.185,2.185 0,0 1,151.876 62.438z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M156.247,62.438L156.247,62.438A2.185,2.185 0,0 1,158.433 64.624L158.433,75.242A2.185,2.185 0,0 1,156.247 77.428L156.247,77.428A2.185,2.185 0,0 1,154.062 75.242L154.062,64.624A2.185,2.185 0,0 1,156.247 62.438z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M161.688,64.498L161.688,64.498A3.255,3.255 0,0 1,164.944 67.753L164.944,77.056A3.255,3.255 0,0 1,161.688 80.311L161.688,80.311A3.255,3.255 0,0 1,158.433 77.056L158.433,67.753A3.255,3.255 0,0 1,161.688 64.498z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M154.446,87.054C160.245,87.054 164.946,82.377 164.946,76.606C164.946,70.836 160.245,66.159 154.446,66.159C148.646,66.159 143.945,70.836 143.945,76.606C143.945,82.377 148.646,87.054 154.446,87.054Z"
        android:fillColor="#CB765E"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M153.24,86.996C153.265,86.985 153.288,86.973 153.31,86.959C147.968,86.072 144.181,81.287 144.567,75.912C144.953,70.537 149.384,66.336 154.798,66.213H154.607C149.088,66.21 144.508,70.458 144.126,75.936C143.743,81.414 147.687,86.252 153.153,87.009L153.24,87.022C153.24,87.022 153.23,87 153.24,86.996Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M158.801,67.316C159.614,67.316 160.272,66.661 160.272,65.853C160.272,65.045 159.614,64.389 158.801,64.389C157.989,64.389 157.331,65.045 157.331,65.853C157.331,66.661 157.989,67.316 158.801,67.316Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M154.062,65.961C154.875,65.961 155.533,65.306 155.533,64.498C155.533,63.69 154.875,63.034 154.062,63.034C153.25,63.034 152.592,63.69 152.592,64.498C152.592,65.306 153.25,65.961 154.062,65.961Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M149.569,67.015C150.381,67.015 151.04,66.36 151.04,65.551C151.04,64.743 150.381,64.088 149.569,64.088C148.757,64.088 148.098,64.743 148.098,65.551C148.098,66.36 148.757,67.015 149.569,67.015Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M158.326,86.317C163.362,84.328 166.053,78.856 164.539,73.679C163.026,68.503 157.804,65.321 152.482,66.331C147.159,67.341 143.482,72.213 143.99,77.58C144.498,82.948 149.025,87.05 154.443,87.054"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M164.103,76.567C165.482,76.567 166.6,75.455 166.6,74.083C166.6,72.711 165.482,71.598 164.103,71.598C162.724,71.598 161.606,72.711 161.606,74.083C161.606,75.455 162.724,76.567 164.103,76.567Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M146.035,69.942C146.848,69.942 147.506,69.286 147.506,68.478C147.506,67.67 146.848,67.015 146.035,67.015C145.223,67.015 144.565,67.67 144.565,68.478C144.565,69.286 145.223,69.942 146.035,69.942Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M150.345,86.317V92.784"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M148.525,79.323C148.525,79.973 149.338,80.493 150.345,80.493C151.351,80.493 152.164,79.969 152.164,79.323"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M151.639,72.854C151.881,73.461 152.464,73.866 153.121,73.883"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M148.39,73.905C149.049,73.867 149.62,73.439 149.839,72.821"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M154.306,83.501C156.015,83.501 157.4,82.122 157.4,80.422"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M152.766,80.407C153.152,80.386 153.454,80.068 153.454,79.684C153.454,79.299 153.152,78.982 152.766,78.961"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M149.569,76.606C149.819,76.606 150.022,76.306 150.022,75.936C150.022,75.566 149.819,75.266 149.569,75.266C149.319,75.266 149.116,75.566 149.116,75.936C149.116,76.306 149.319,76.606 149.569,76.606Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M149.798,75.722C149.857,75.722 149.904,75.674 149.904,75.616C149.904,75.557 149.857,75.509 149.798,75.509C149.739,75.509 149.691,75.557 149.691,75.616C149.691,75.674 149.739,75.722 149.798,75.722Z"
        android:fillColor="#FFF9E9"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M150.881,76.606C151.131,76.606 151.334,76.306 151.334,75.936C151.334,75.566 151.131,75.266 150.881,75.266C150.63,75.266 150.427,75.566 150.427,75.936C150.427,76.306 150.63,76.606 150.881,76.606Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M151.11,75.722C151.169,75.722 151.217,75.674 151.217,75.616C151.217,75.557 151.169,75.509 151.11,75.509C151.051,75.509 151.003,75.557 151.003,75.616C151.003,75.674 151.051,75.722 151.11,75.722Z"
        android:fillColor="#FFF9E9"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M161.061,73.114V76.014C161.054,76.619 161.288,77.201 161.712,77.634C162.136,78.066 162.715,78.314 163.323,78.321H164.802C164.922,77.743 164.994,77.157 165.02,76.567C165.02,70.651 160.24,65.853 154.439,65.853C151.441,65.858 148.592,67.155 146.628,69.408H157.424C158.401,69.419 159.333,69.816 160.015,70.511C160.697,71.206 161.073,72.142 161.061,73.114Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M164.946,80.312C166.051,80.312 166.946,79.421 166.946,78.321C166.946,77.222 166.051,76.331 164.946,76.331C163.841,76.331 162.946,77.222 162.946,78.321C162.946,79.421 163.841,80.312 164.946,80.312Z"
        android:fillColor="#CB765E"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M165.835,78.041C165.41,77.872 164.927,78.078 164.757,78.501"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M164.031,72.088C164.843,72.088 165.502,71.433 165.502,70.625C165.502,69.817 164.843,69.161 164.031,69.161C163.219,69.161 162.56,69.817 162.56,70.625C162.56,71.433 163.219,72.088 164.031,72.088Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M163.738,79.91C164.479,80.467 165.508,80.443 166.222,79.853C166.936,79.263 167.148,78.261 166.734,77.435C166.32,76.608 165.389,76.173 164.486,76.384C163.582,76.596 162.943,77.398 162.943,78.321"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M148.602,93.313C148.331,93.314 148.061,93.329 147.793,93.361C146.915,96.398 148.064,99.655 150.656,101.481C153.249,103.307 156.718,103.302 159.305,101.468C161.892,99.634 163.031,96.373 162.144,93.339C161.953,93.322 161.762,93.313 161.568,93.313H148.602Z"
        android:fillColor="#CB765E"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M150.532,97.773C151.148,97.988 151.833,97.817 152.275,97.339"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M156.921,95.698C157.185,96.299 157.787,96.683 158.446,96.669"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M154.559,99.132C154.245,99.706 154.306,100.411 154.714,100.923"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M160.298,168.997L159.137,170.974"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M171.666,187.28L170.395,190.026"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M172.936,188.047L171.665,190.794"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M174.209,188.351L172.936,191.095"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M177.235,194.815C177.232,193.428 177.767,192.094 178.73,191.091"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M159.719,190.794L162.586,193.857"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M159.719,193.567H175.952"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M159.606,176.476L159.671,177.727L160.166,187.229C160.259,189.078 161.818,190.514 163.677,190.467C165.536,190.419 167.018,188.905 167.016,187.054V176.476H159.606Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M159.753,178.898H167.018"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#529ED6"/>
    <path
        android:pathData="M159.753,180.216H167.018"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#E43C28"/>
    <path
        android:pathData="M123.348,194.497H147.824L145.758,187.813L138.962,186.169L125.727,191.219C124.326,191.713 123.378,193.018 123.348,194.497Z"
        android:fillColor="#161E24"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M140.206,166.334C140.149,166.92 140.412,167.492 140.895,167.831C141.379,168.171 142.008,168.227 142.544,167.977C143.08,167.728 143.44,167.212 143.488,166.625"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M147.388,169.001L148.371,171.071"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M134.314,186.82L135.587,189.567"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M133.044,187.587L134.314,190.334"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M131.774,187.889L133.044,190.636"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M128.745,194.356C128.746,192.971 128.211,191.638 127.25,190.635"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M146.264,190.334L143.394,193.395"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M146.264,193.107H130.031"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M147.41,176.511L145.884,187.175C145.619,189.001 143.942,190.286 142.1,190.075C140.258,189.863 138.919,188.233 139.077,186.395L139.964,176.476L147.41,176.511Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M147.161,178.271H139.814"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#529ED6"/>
    <path
        android:pathData="M146.933,179.583H139.698"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#E43C28"/>
    <path
        android:pathData="M154.237,153.239L158.156,145.218"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M141.844,135.741L165.434,131.96"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M157.42,133.634V144.739"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M158.156,144.738V144.738C158.797,144.146 159.153,143.309 159.135,142.438V132.97"
        android:strokeLineJoin="round"
        android:strokeWidth="0.626474"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M156.05,143.847L158.791,145.63"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M143.599,148.617L141.63,150.785"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M142.83,151.548L140.86,153.716"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M141.844,123.36L141.629,129.756L125.289,117.84L130.634,111.739L132.634,113.814L135.375,116.654L141.844,123.36Z"
        android:fillColor="#CB765E"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M142.485,121.651L142.485,121.651A1.634,1.634 0,0 1,144.119 123.285L144.119,128.663A1.634,1.634 0,0 1,142.485 130.297L142.485,130.297A1.634,1.634 0,0 1,140.851 128.663L140.851,123.285A1.634,1.634 0,0 1,142.485 121.651z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M141.971,128.373C142.054,127.75 142.626,127.312 143.248,127.395L146.067,127.767C146.691,127.849 147.13,128.422 147.047,129.046C146.963,129.669 146.392,130.106 145.769,130.024L142.95,129.652C142.326,129.569 141.888,128.996 141.971,128.373Z"
        android:fillColor="#CB765E"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M142.446,126.205C142.529,125.583 143.101,125.145 143.723,125.227L146.542,125.599C147.166,125.682 147.605,126.255 147.522,126.878C147.439,127.501 146.867,127.939 146.244,127.856L143.425,127.484C142.801,127.402 142.363,126.829 142.446,126.205Z"
        android:fillColor="#CB765E"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M142.732,124.071C142.815,123.449 143.386,123.011 144.009,123.093L146.828,123.466C147.452,123.548 147.89,124.121 147.807,124.745C147.724,125.367 147.152,125.805 146.53,125.723L143.711,125.35C143.087,125.268 142.648,124.695 142.732,124.071Z"
        android:fillColor="#CB765E"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M140.915,123.197C140.718,122.598 141.045,121.953 141.645,121.758L144.346,120.88C144.943,120.686 145.584,121.011 145.78,121.607C145.977,122.206 145.65,122.851 145.05,123.046L142.349,123.924C141.752,124.118 141.111,123.793 140.915,123.197Z"
        android:fillColor="#CB765E"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M143.808,121.793C143.855,121.166 144.401,120.696 145.027,120.743L147.864,120.952C148.491,120.999 148.962,121.545 148.915,122.172C148.868,122.799 148.322,123.269 147.696,123.223L144.86,123.013C144.232,122.967 143.762,122.42 143.808,121.793Z"
        android:fillColor="#CB765E"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M135.373,118.545C135.989,118.76 136.675,118.59 137.116,118.111"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M137.125,119.826C137.74,120.041 138.426,119.871 138.868,119.392"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M132.109,112.899L135.768,117.062"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M124.237,116.06L125.799,116.277"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M140.239,111.739L144.987,107.533"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M130.065,105.365L137.364,112.628"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M140.851,134.308L159.828,131.377"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M194.818,103.475L187.427,106.447C186.29,106.737 185.131,106.059 184.834,104.929C184.685,104.352 184.786,103.739 185.114,103.24C185.442,102.742 185.966,102.403 186.557,102.308L194.184,101.083L194.818,103.475Z"
        android:fillColor="#CB765E"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M194.835,103.485L192.146,102.603L195,97.933C195.146,97.696 195.405,97.55 195.684,97.549C195.944,97.547 196.188,97.67 196.34,97.879C196.493,98.087 196.535,98.356 196.454,98.601L194.835,103.485Z"
        android:fillColor="#CB765E"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M184.567,98.59C186.219,99.91 186.907,102.09 186.311,104.112L186.204,104.476"
        android:strokeLineJoin="round"
        android:strokeWidth="2.63119"
        android:fillColor="#00000000"
        android:strokeColor="#CB765E"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M187.809,101.216L188.824,101.216A1.871,1.871 0,0 1,190.695 103.087L190.695,103.087A1.871,1.871 0,0 1,188.824 104.958L187.809,104.958A1.871,1.871 0,0 1,185.938 103.087L185.938,103.087A1.871,1.871 0,0 1,187.809 101.216z"
        android:fillColor="#CB765E"/>
    <path
        android:pathData="M189.115,105.771L192.07,104.58"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M180.017,112.899L182.787,107.819"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M139.991,124.405L140.93,122.412"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M143.826,121.99V122.954V121.99Z"
        android:fillColor="#B76452"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M143.826,121.99V122.954"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#B76452"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M143.826,124.405V125.37V124.405Z"
        android:fillColor="#B76452"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M143.826,124.405V125.37"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#B76452"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M143.826,126.718V127.681V126.718Z"
        android:fillColor="#B76452"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M143.826,126.718V127.681"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#B76452"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M142.961,129.649L145.271,129.955"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M59.544,35.117C59.061,34.104 59.493,32.89 60.508,32.41L66.622,29.522C67.632,29.045 68.837,29.474 69.318,30.482C69.802,31.496 69.37,32.709 68.354,33.189L62.241,36.077C61.231,36.554 60.025,36.125 59.544,35.117Z"
        android:fillColor="#FFCCAE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M60.529,35.304C60.287,34.554 60.7,33.749 61.45,33.509C62.198,33.269 62.998,33.68 63.24,34.427L64.318,37.759C64.56,38.509 64.148,39.314 63.397,39.554C62.649,39.793 61.849,39.383 61.607,38.636L60.529,35.304Z"
        android:fillColor="#FFCCAE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M63.194,34.333C62.949,33.585 63.358,32.779 64.107,32.536C64.854,32.294 65.655,32.701 65.899,33.446L66.99,36.772C67.235,37.52 66.826,38.326 66.077,38.569C65.331,38.811 64.53,38.404 64.285,37.659L63.194,34.333Z"
        android:fillColor="#FFCCAE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M65.798,33.854C65.555,33.104 65.968,32.3 66.718,32.059C67.466,31.82 68.266,32.231 68.508,32.978L69.586,36.31C69.828,37.06 69.416,37.864 68.665,38.105C67.917,38.344 67.117,37.933 66.875,37.187L65.798,33.854Z"
        android:fillColor="#FFCCAE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M66.672,31.558C66.129,30.988 66.152,30.084 66.725,29.543C67.294,29.006 68.19,29.029 68.73,29.596L71.159,32.143C71.703,32.713 71.679,33.617 71.106,34.158C70.537,34.695 69.642,34.671 69.102,34.105L66.672,31.558Z"
        android:fillColor="#FFCCAE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M69.042,33.882C68.756,33.147 69.122,32.32 69.858,32.037C70.59,31.755 71.412,32.118 71.697,32.849L72.966,36.115C73.252,36.85 72.886,37.677 72.151,37.96C71.418,38.242 70.596,37.879 70.312,37.147L69.042,33.882Z"
        android:fillColor="#FFCCAE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M69.652,32.166L68.563,32.682L69.652,32.166Z"
        android:fillColor="#B76452"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M69.652,32.166L68.563,32.682"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#B76452"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M66.922,33.458L65.832,33.972L66.922,33.458Z"
        android:fillColor="#B76452"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M66.922,33.458L65.832,33.972"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#B76452"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M64.304,34.694L63.215,35.208L64.304,34.694Z"
        android:fillColor="#B76452"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M64.304,34.694L63.215,35.208"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#B76452"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M60.527,35.29L61.416,38.039"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M168.543,107.819V124.335"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M63.76,36.264L64.311,38.002"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M66.376,35.633L67.093,37.597"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M167.018,174.85V186.629"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M159.989,183.806L160.298,188.812"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M139.55,181.226L139.089,186.395"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M146.026,185.69L145.501,188.35"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M185.684,195.393H118.193"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M188.517,195.393H190.842"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M111.214,195.393H29.202"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M182.5,84.006H181.629V83.138C181.629,82.98 181.5,82.852 181.341,82.852C181.182,82.852 181.054,82.98 181.054,83.138V84.006H180.182C180.023,84.006 179.894,84.134 179.894,84.292C179.894,84.45 180.023,84.578 180.182,84.578H181.054V85.445C181.054,85.603 181.182,85.731 181.341,85.731C181.5,85.731 181.629,85.603 181.629,85.445V84.578H182.5C182.659,84.578 182.788,84.45 182.788,84.292C182.788,84.134 182.659,84.006 182.5,84.006Z"
        android:fillColor="#529ED6"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M22.948,90.616H22.077V89.749C22.077,89.591 21.948,89.463 21.789,89.463C21.63,89.463 21.502,89.591 21.502,89.749V90.616H20.63C20.48,90.629 20.365,90.754 20.365,90.903C20.365,91.053 20.48,91.178 20.63,91.191H21.502V92.058C21.502,92.16 21.556,92.255 21.646,92.306C21.735,92.357 21.844,92.357 21.933,92.306C22.022,92.255 22.077,92.16 22.077,92.058V91.191H22.948C23.098,91.178 23.213,91.053 23.213,90.903C23.213,90.754 23.098,90.629 22.948,90.616Z"
        android:fillColor="#529ED6"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M177.007,78.347C177.344,78.347 177.617,78.075 177.617,77.74C177.617,77.405 177.344,77.133 177.007,77.133C176.67,77.133 176.396,77.405 176.396,77.74C176.396,78.075 176.67,78.347 177.007,78.347Z"
        android:fillColor="#529ED6"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M182.971,78.263C183.415,78.263 183.775,77.904 183.775,77.463C183.775,77.021 183.415,76.663 182.971,76.663C182.527,76.663 182.167,77.021 182.167,77.463C182.167,77.904 182.527,78.263 182.971,78.263Z"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#E4F4FC"/>
    <path
        android:pathData="M126.564,43.076C127.07,43.076 127.479,42.668 127.479,42.165C127.479,41.662 127.07,41.254 126.564,41.254C126.059,41.254 125.649,41.662 125.649,42.165C125.649,42.668 126.059,43.076 126.564,43.076Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M46.041,43.652C46.546,43.652 46.956,43.245 46.956,42.742C46.956,42.239 46.546,41.831 46.041,41.831C45.535,41.831 45.125,42.239 45.125,42.742C45.125,43.245 45.535,43.652 46.041,43.652Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M134.245,40.196C134.578,40.196 134.848,39.928 134.848,39.596C134.848,39.264 134.578,38.995 134.245,38.995C133.911,38.995 133.641,39.264 133.641,39.596C133.641,39.928 133.911,40.196 134.245,40.196Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M22.206,50.651C22.539,50.651 22.809,50.382 22.809,50.05C22.809,49.719 22.539,49.45 22.206,49.45C21.872,49.45 21.602,49.719 21.602,50.05C21.602,50.382 21.872,50.651 22.206,50.651Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:fillType="evenOdd"
        android:strokeColor="#252944"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M123.64,35.412H122.986V34.75C122.986,34.631 122.888,34.534 122.768,34.534C122.648,34.534 122.55,34.631 122.55,34.75V35.412H121.886C121.765,35.412 121.668,35.509 121.668,35.629C121.668,35.748 121.765,35.845 121.886,35.845H122.55V36.496C122.55,36.616 122.648,36.713 122.768,36.713C122.888,36.713 122.986,36.616 122.986,36.496V35.845H123.64C123.76,35.845 123.858,35.748 123.858,35.629C123.858,35.509 123.76,35.412 123.64,35.412Z"
        android:fillColor="#F0917A"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M48.096,25.82H47.431V25.17C47.431,25.05 47.333,24.953 47.213,24.953C47.093,24.953 46.995,25.05 46.995,25.17V25.82H46.341C46.221,25.82 46.124,25.917 46.124,26.037C46.124,26.157 46.221,26.254 46.341,26.254H46.995V26.904C46.995,27.024 47.093,27.121 47.213,27.121C47.333,27.121 47.431,27.024 47.431,26.904V26.254H48.096C48.216,26.254 48.313,26.157 48.313,26.037C48.313,25.917 48.216,25.82 48.096,25.82Z"
        android:fillColor="#F0917A"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M131.324,77.977L115.234,64.614"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M131.244,78.324L119.054,89.219"
        android:strokeLineJoin="round"
        android:strokeWidth="0.789357"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
  </group>
</vector>
