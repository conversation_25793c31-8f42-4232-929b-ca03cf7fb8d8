<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="177dp"
    android:height="177dp"
    android:viewportWidth="177"
    android:viewportHeight="177">
  <path
      android:pathData="M28.204,74.79H31.699"
      android:strokeLineJoin="round"
      android:strokeWidth="0.807692"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M30.409,69.441L32.882,71.914"
      android:strokeLineJoin="round"
      android:strokeWidth="0.807692"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M35.543,67.693V71.188"
      android:strokeLineJoin="round"
      android:strokeWidth="0.807692"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M158.608,58.688C159.706,58.688 160.597,57.798 160.597,56.699C160.597,55.6 159.706,54.71 158.608,54.71C157.509,54.71 156.618,55.6 156.618,56.699C156.618,57.798 157.509,58.688 158.608,58.688Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.807692"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M51.833,41.887C52.932,41.887 53.823,40.996 53.823,39.898C53.823,38.799 52.932,37.908 51.833,37.908C50.735,37.908 49.844,38.799 49.844,39.898C49.844,40.996 50.735,41.887 51.833,41.887Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.807692"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M161.403,17.048H159.306V14.952C159.306,14.575 159.011,14.253 158.608,14.253C158.231,14.253 157.909,14.548 157.909,14.952V17.048H155.812C155.435,17.048 155.113,17.344 155.113,17.747C155.113,18.124 155.409,18.446 155.812,18.446H157.909V20.543C157.909,20.919 158.204,21.242 158.608,21.242C158.984,21.242 159.306,20.946 159.306,20.543V18.446H161.403C161.78,18.446 162.102,18.15 162.102,17.747C162.075,17.371 161.78,17.048 161.403,17.048Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M175.516,35.005H174.414V33.903C174.414,33.715 174.253,33.554 174.065,33.554C173.876,33.554 173.715,33.715 173.715,33.903V35.005H172.613C172.425,35.005 172.263,35.167 172.263,35.355C172.263,35.543 172.425,35.704 172.613,35.704H173.715V36.806C173.715,36.995 173.876,37.156 174.065,37.156C174.253,37.156 174.414,36.995 174.414,36.806V35.704H175.516C175.704,35.704 175.866,35.543 175.866,35.355C175.866,35.167 175.731,35.005 175.516,35.005Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M31.78,133.984H30.409V132.613C30.409,132.371 30.194,132.156 29.952,132.156C29.71,132.156 29.495,132.371 29.495,132.613V133.984H28.151C27.909,133.984 27.694,134.199 27.694,134.441C27.694,134.683 27.909,134.898 28.151,134.898H29.522V136.269C29.522,136.511 29.737,136.726 29.979,136.726C30.221,136.726 30.435,136.511 30.435,136.269V134.871H31.806C32.048,134.871 32.264,134.656 32.264,134.414C32.264,134.172 32.048,133.984 31.78,133.984Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M145.382,154.898C141.914,157.882 138.177,160.597 134.253,163.016"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#EDA257"/>
  <path
      android:pathData="M48.984,10.489C60.839,4.468 74.253,1.081 88.473,1.081C136.753,1.081 175.919,40.22 175.919,88.527C175.919,109.898 168.258,129.495 155.516,144.683"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#EDA257"/>
  <path
      android:pathData="M112.425,172.64C104.817,174.817 96.753,175.973 88.446,175.973C40.167,175.973 1,136.833 1,88.527C1,63.581 11.457,41.081 28.204,25.14"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#EDA257"/>
  <path
      android:pathData="M128.231,40.167C130.059,37.129 133.715,35.946 136.538,37.505C139.36,39.064 140.274,42.828 138.634,45.973L116.941,90.086C117.425,91.968 117.667,93.93 117.667,95.946V114.172C117.667,117.694 116.914,121.054 115.543,124.064V156.78H78.097V137.505L67.371,134.817C60.328,133.043 54.011,129.226 49.172,123.823L39.925,113.473C38.07,111.403 36.968,108.769 36.806,105.973L35.543,83.258C35.436,81.296 36.995,79.629 38.984,79.629H39.091C39.495,79.629 39.898,79.683 40.274,79.763C39.414,78.607 39.549,76.995 40.597,76L57.747,61.672C60.167,59.629 63.715,59.683 66.081,61.779L76,70.57L77.559,13.903C77.667,9.925 80.597,6.78 84.199,6.78C87.801,6.78 90.731,9.925 90.839,13.903L92.398,70.462L105.328,18.769C106.296,14.925 109.871,12.479 113.366,13.258C116.887,14.038 119.065,17.747 118.312,21.645L108.258,73.688L128.231,40.167ZM57.398,109.629C59.737,110.301 62.237,109.656 64.091,108.097L69.495,103.554V95.973C69.495,93.392 69.898,90.892 70.651,88.554L59.817,73.769L48.446,81.511C47.156,82.398 45.624,82.693 44.199,82.452C44.629,83.07 44.925,83.796 45.086,84.575L47.613,96.941C48.285,100.22 49.898,103.258 52.21,105.704L54.065,107.64C55.006,108.607 56.161,109.28 57.398,109.629Z"
      android:fillColor="#F3D5CB"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M79.441,156.806L81.269,137.935L68.527,132.855C61.511,131.108 55.167,127.263 50.328,121.86L41.054,111.484C39.199,109.414 38.097,106.753 37.936,103.984L36.672,81.269C36.645,81 36.672,80.758 36.726,80.489C35.946,81.161 35.462,82.183 35.543,83.285L36.806,106C36.968,108.796 38.07,111.43 39.925,113.5L49.172,123.849C54.011,129.253 60.328,133.07 67.371,134.844L78.097,137.532V156.806H79.441Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M38.312,79.763L38.285,79.736C38.258,79.736 38.231,79.763 38.204,79.763C38.231,79.763 38.285,79.763 38.312,79.763Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M48.5,81.484L60.005,73.742L59.414,73.258L46.484,81.484C45.221,82.398 42.882,80.919 41.457,80.677C41.86,81.323 42.962,83.796 43.124,84.575L45.651,96.941C46.323,100.247 47.909,103.285 50.247,105.704L52.102,107.64C53.043,108.608 54.199,109.306 55.436,109.629C56.376,109.898 57.371,109.952 58.312,109.817C58.016,109.763 57.747,109.71 57.452,109.629C56.215,109.28 55.059,108.608 54.118,107.64L52.264,105.704C49.952,103.258 48.339,100.22 47.667,96.941L45.14,84.575C44.979,83.796 44.683,83.07 44.253,82.452C45.678,82.693 47.21,82.398 48.5,81.484Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M111.43,13.258C114.925,14.038 117.129,17.747 116.376,21.645L107.398,76H108.151L118.392,21.645C119.145,17.747 116.968,14.038 113.446,13.258C112.667,13.097 111.887,13.07 111.134,13.204C111.215,13.204 111.323,13.231 111.43,13.258Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M136.591,37.505C135.436,36.86 134.145,36.672 132.882,36.914C133.473,37.021 134.038,37.21 134.575,37.505C137.398,39.064 138.312,42.828 136.672,45.973L115.436,91.968C115.893,93.85 115.677,93.93 115.677,95.946V114.172C115.677,117.694 115.14,123.231 113.796,126.242L113.581,156.806H115.597V124.065C116.968,121.054 117.72,117.694 117.72,114.172V95.946C117.72,93.93 117.479,91.968 116.995,90.086L138.688,45.973C140.328,42.828 139.414,39.064 136.591,37.505Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M88.876,13.93L92.075,73.742L92.452,70.462L90.893,13.903C90.785,9.925 87.855,6.78 84.253,6.78C83.903,6.78 83.581,6.806 83.258,6.86C86.376,7.425 88.796,10.328 88.876,13.93Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M64.118,61.806L76.564,71.538L76.699,71.188L66.134,61.833C64.656,60.516 62.72,60.005 60.866,60.301C62.021,60.462 63.177,60.973 64.118,61.806Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M73.876,92.936L69.226,86.591L59.817,73.769L48.446,81.511C47.183,82.425 45.651,82.72 44.199,82.479"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M125.57,44.548L128.231,40.167C130.059,37.102 133.742,35.919 136.538,37.505V37.505C139.36,39.064 140.274,42.828 138.634,45.973L115.543,92.935"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M109.226,71.403L120.005,53.688"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M76.699,45.462L77.559,13.93C77.667,9.952 80.597,6.806 84.199,6.806V6.806C87.801,6.806 90.731,9.952 90.839,13.93L92.237,65.247L92.64,73.769"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M76.564,50.812L76.645,47.855"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M76.108,67.102L76.296,60.194"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M93.715,65.22L105.355,18.796C106.323,14.951 109.871,12.505 113.393,13.285V13.285C116.887,14.064 119.091,17.774 118.339,21.672L108.097,76"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M69.495,108.957V103.527V95.946C69.495,94.333 69.656,92.747 69.952,91.242"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M112.317,129.306C115.651,125.167 117.667,119.898 117.667,114.172V95.946V95.919C117.667,94.226 117.317,92.559 116.699,91L116.591,90.785"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M115.543,124.548V156.806H78.097V139.602"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M80.946,138.258L67.371,134.871C60.355,133.124 54.011,129.28 49.172,123.876L39.925,113.527C38.07,111.457 36.968,108.796 36.806,106.027L35.543,83.312C35.436,81.35 36.995,79.683 38.984,79.683H39.091C41.995,79.683 44.522,81.726 45.086,84.575L47.613,96.941C48.285,100.247 49.871,103.285 52.21,105.704L54.065,107.64C55.006,108.608 56.161,109.307 57.398,109.629C59.737,110.274 62.237,109.656 64.091,108.097L68.231,104.602"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M60.086,68.903L59.817,73.742"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M58.178,115.14V109.898"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M61.027,112.505L59.817,109.898"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M46.779,101.833L48.984,101"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M38.419,84.226L38.796,87.505C38.984,89.064 38.124,90.597 36.672,91.242V91.242"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M81.645,13.123C81.645,13.823 81.833,14.495 82.156,15.086C82.828,16.242 84.091,17.048 85.543,17.048H86.753"
      android:strokeLineJoin="round"
      android:strokeWidth="1.07692"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M110.919,17.747C110.704,18.419 110.704,19.118 110.866,19.763C111.188,21.081 112.156,22.183 113.554,22.613L114.71,22.962"
      android:strokeLineJoin="round"
      android:strokeWidth="1.07692"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M132.344,41.645C132.102,42.129 132.022,42.64 132.048,43.124C132.102,44.118 132.694,45.086 133.661,45.57L134.468,45.973"
      android:strokeLineJoin="round"
      android:strokeWidth="1.07692"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M80.677,143.876L113.876,140.65L113.634,144.683L80.677,143.876Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M118.473,165.919L73.876,160.274V143.876H118.473V165.919Z"
      android:fillColor="#2BA7FF"/>
  <path
      android:pathData="M71.565,143.876H116.726"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M107.075,155.919C108.887,155.919 110.355,154.451 110.355,152.64C110.355,150.828 108.887,149.36 107.075,149.36C105.264,149.36 103.796,150.828 103.796,152.64C103.796,154.451 105.264,155.919 107.075,155.919Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M107.075,155.919C105.247,155.919 103.796,154.441 103.796,152.64C103.796,152.075 103.93,151.538 104.199,151.081"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M105.624,149.683C106.054,149.468 106.565,149.333 107.075,149.333C108.903,149.333 110.355,150.812 110.355,152.613C110.355,153.742 109.79,154.763 108.903,155.355"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M88.446,162.129L121.027,165.919"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M84.199,45.946H90.597"
      android:strokeLineJoin="round"
      android:strokeWidth="1.07692"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M106.108,45.946L112.317,47.855"
      android:strokeLineJoin="round"
      android:strokeWidth="1.07692"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M124.925,62.559L127.505,64.871"
      android:strokeLineJoin="round"
      android:strokeWidth="1.07692"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M60.866,63.097L61.699,60.462"
      android:strokeLineJoin="round"
      android:strokeWidth="1.07692"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M40.14,79.575C39.441,78.446 39.602,76.941 40.597,76L57.747,61.672C60.167,59.656 63.715,59.683 66.081,61.78L76.645,71.134"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M41.43,106.376L39.71,107.855"
      android:strokeLineJoin="round"
      android:strokeWidth="1.07692"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M116.269,146.753V162.129"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34615"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
</vector>
