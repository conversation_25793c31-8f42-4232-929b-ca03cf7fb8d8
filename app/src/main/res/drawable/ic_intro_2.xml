<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="272dp"
    android:height="309dp"
    android:viewportWidth="272"
    android:viewportHeight="309">
  <path
      android:pathData="M17.556,266.72V278H16.356L16.372,269.264L12.708,276.208H11.78L8.1,269.328L8.116,278H6.9V266.72H7.988L12.26,274.896L16.484,266.72H17.556ZM26.689,274.304H20.993C21.036,275.211 21.281,275.899 21.729,276.368C22.177,276.827 22.812,277.056 23.633,277.056C24.54,277.056 25.372,276.747 26.129,276.128L26.561,277.072C26.22,277.392 25.782,277.648 25.249,277.84C24.716,278.021 24.172,278.112 23.617,278.112C22.401,278.112 21.441,277.755 20.737,277.04C20.044,276.325 19.697,275.339 19.697,274.08C19.697,273.28 19.852,272.576 20.161,271.968C20.47,271.349 20.902,270.869 21.457,270.528C22.022,270.187 22.662,270.016 23.377,270.016C24.412,270.016 25.222,270.357 25.809,271.04C26.396,271.712 26.689,272.64 26.689,273.824V274.304ZM23.393,271.024C22.71,271.024 22.161,271.237 21.745,271.664C21.34,272.091 21.1,272.693 21.025,273.472H25.553C25.532,272.683 25.334,272.08 24.961,271.664C24.588,271.237 24.065,271.024 23.393,271.024ZM32.518,270.016C34.385,270.016 35.318,271.045 35.318,273.104V278H34.022V273.184C34.022,272.459 33.878,271.931 33.59,271.6C33.313,271.269 32.865,271.104 32.246,271.104C31.532,271.104 30.961,271.323 30.534,271.76C30.108,272.197 29.894,272.789 29.894,273.536V278H28.598V270.208H29.878V271.488C30.134,271.008 30.492,270.645 30.95,270.4C31.409,270.144 31.932,270.016 32.518,270.016ZM45.897,270.912C46.58,270.912 47.188,271.061 47.721,271.36C48.255,271.659 48.671,272.08 48.969,272.624C49.268,273.168 49.417,273.792 49.417,274.496C49.417,275.189 49.263,275.813 48.953,276.368C48.644,276.923 48.212,277.355 47.657,277.664C47.103,277.973 46.479,278.128 45.785,278.128C44.42,278.128 43.364,277.653 42.617,276.704C41.881,275.755 41.513,274.4 41.513,272.64C41.513,271.371 41.684,270.283 42.025,269.376C42.377,268.469 42.879,267.781 43.529,267.312C44.18,266.832 44.958,266.592 45.865,266.592C46.516,266.592 47.151,266.715 47.769,266.96C48.388,267.195 48.932,267.536 49.401,267.984L48.905,268.992C48.372,268.544 47.86,268.219 47.369,268.016C46.889,267.803 46.398,267.696 45.897,267.696C44.926,267.696 44.169,268.128 43.625,268.992C43.092,269.845 42.825,271.056 42.825,272.624V272.864C43.07,272.267 43.465,271.792 44.009,271.44C44.553,271.088 45.182,270.912 45.897,270.912ZM45.721,277.024C46.446,277.024 47.028,276.795 47.465,276.336C47.903,275.877 48.121,275.269 48.121,274.512C48.121,273.765 47.897,273.163 47.449,272.704C47.012,272.245 46.431,272.016 45.705,272.016C44.969,272.016 44.367,272.251 43.897,272.72C43.438,273.179 43.209,273.776 43.209,274.512C43.209,275.259 43.438,275.867 43.897,276.336C44.367,276.795 44.974,277.024 45.721,277.024ZM51.187,266.72H52.723L52.403,271.152H51.539L51.187,266.72ZM58.527,278.128C57.236,278.128 56.255,277.637 55.583,276.656C54.911,275.675 54.575,274.245 54.575,272.368C54.575,270.48 54.911,269.045 55.583,268.064C56.255,267.083 57.236,266.592 58.527,266.592C59.817,266.592 60.799,267.083 61.471,268.064C62.143,269.035 62.479,270.464 62.479,272.352C62.479,274.24 62.143,275.675 61.471,276.656C60.799,277.637 59.817,278.128 58.527,278.128ZM58.527,277.024C59.423,277.024 60.084,276.645 60.511,275.888C60.937,275.131 61.151,273.952 61.151,272.352C61.151,270.741 60.937,269.568 60.511,268.832C60.095,268.085 59.433,267.712 58.527,267.712C57.631,267.712 56.969,268.085 56.543,268.832C56.116,269.579 55.903,270.752 55.903,272.352C55.903,273.952 56.116,275.131 56.543,275.888C56.969,276.645 57.631,277.024 58.527,277.024ZM64.312,266.72H65.848L65.528,271.152H64.664L64.312,266.72ZM67.512,266.72H69.048L68.728,271.152H67.864L67.512,266.72ZM83.396,278L82.084,276.576C81.625,277.077 81.108,277.461 80.532,277.728C79.956,277.984 79.326,278.112 78.644,278.112C77.929,278.112 77.294,277.989 76.74,277.744C76.196,277.499 75.769,277.152 75.46,276.704C75.161,276.256 75.012,275.744 75.012,275.168C75.012,274.485 75.214,273.877 75.62,273.344C76.036,272.8 76.702,272.251 77.62,271.696C77.129,271.131 76.788,270.656 76.596,270.272C76.404,269.888 76.308,269.488 76.308,269.072C76.308,268.325 76.558,267.728 77.06,267.28C77.572,266.832 78.244,266.608 79.076,266.608C79.854,266.608 80.468,266.816 80.916,267.232C81.374,267.637 81.604,268.187 81.604,268.88C81.604,269.435 81.438,269.936 81.108,270.384C80.777,270.821 80.185,271.312 79.332,271.856L82.004,274.784C82.484,273.835 82.74,272.699 82.772,271.376H83.908C83.865,273.051 83.492,274.469 82.788,275.632L84.948,278H83.396ZM79.092,267.6C78.622,267.6 78.244,267.733 77.956,268C77.678,268.267 77.54,268.624 77.54,269.072C77.54,269.381 77.614,269.68 77.764,269.968C77.913,270.245 78.201,270.624 78.628,271.104C79.332,270.667 79.812,270.288 80.068,269.968C80.334,269.648 80.468,269.307 80.468,268.944C80.468,268.539 80.34,268.213 80.084,267.968C79.838,267.723 79.508,267.6 79.092,267.6ZM78.676,277.056C79.209,277.056 79.705,276.949 80.164,276.736C80.622,276.512 81.022,276.197 81.364,275.792L78.324,272.464C77.577,272.923 77.049,273.355 76.74,273.76C76.441,274.165 76.292,274.624 76.292,275.136C76.292,275.723 76.505,276.192 76.932,276.544C77.369,276.885 77.95,277.056 78.676,277.056ZM96.69,270.208V278H95.426V276.672C95.18,277.141 94.834,277.499 94.386,277.744C93.948,277.989 93.452,278.112 92.898,278.112C91.97,278.112 91.271,277.856 90.802,277.344C90.332,276.821 90.098,276.053 90.098,275.04V270.208H91.394V275.008C91.394,275.701 91.532,276.213 91.81,276.544C92.087,276.864 92.524,277.024 93.122,277.024C93.804,277.024 94.354,276.805 94.77,276.368C95.186,275.92 95.394,275.333 95.394,274.608V270.208H96.69ZM103.018,270.016C103.701,270.016 104.304,270.187 104.826,270.528C105.349,270.869 105.754,271.349 106.042,271.968C106.33,272.576 106.474,273.285 106.474,274.096C106.474,274.907 106.33,275.616 106.042,276.224C105.754,276.821 105.349,277.285 104.826,277.616C104.314,277.947 103.712,278.112 103.018,278.112C102.41,278.112 101.877,277.979 101.418,277.712C100.97,277.445 100.629,277.067 100.394,276.576V280.88H99.098V270.208H100.394V271.552C100.629,271.061 100.97,270.683 101.418,270.416C101.877,270.149 102.41,270.016 103.018,270.016ZM102.762,277.04C103.52,277.04 104.106,276.784 104.522,276.272C104.938,275.76 105.146,275.035 105.146,274.096C105.146,273.157 104.938,272.427 104.522,271.904C104.106,271.371 103.52,271.104 102.762,271.104C102.005,271.104 101.418,271.36 101.002,271.872C100.597,272.384 100.394,273.115 100.394,274.064C100.394,275.013 100.597,275.749 101.002,276.272C101.418,276.784 102.005,277.04 102.762,277.04ZM115.704,270.016C116.696,270.016 117.432,270.272 117.912,270.784C118.403,271.285 118.648,272.053 118.648,273.088V278H117.384V276.672C117.181,277.131 116.872,277.488 116.456,277.744C116.04,277.989 115.56,278.112 115.016,278.112C114.515,278.112 114.051,278.011 113.624,277.808C113.208,277.605 112.877,277.323 112.632,276.96C112.387,276.597 112.264,276.197 112.264,275.76C112.264,275.173 112.413,274.72 112.712,274.4C113.011,274.069 113.501,273.835 114.184,273.696C114.867,273.547 115.816,273.472 117.032,273.472H117.384V272.944C117.384,272.293 117.251,271.824 116.984,271.536C116.717,271.237 116.285,271.088 115.688,271.088C114.792,271.088 113.901,271.365 113.016,271.92L112.584,270.976C112.989,270.688 113.475,270.459 114.04,270.288C114.616,270.107 115.171,270.016 115.704,270.016ZM115.192,277.088C115.832,277.088 116.355,276.875 116.76,276.448C117.176,276.011 117.384,275.451 117.384,274.768V274.304H117.096C116.157,274.304 115.443,274.347 114.952,274.432C114.461,274.507 114.109,274.64 113.896,274.832C113.693,275.013 113.592,275.285 113.592,275.648C113.592,276.064 113.741,276.411 114.04,276.688C114.349,276.955 114.733,277.088 115.192,277.088ZM124.94,270.016C126.807,270.016 127.74,271.045 127.74,273.104V278H126.444V273.184C126.444,272.459 126.3,271.931 126.012,271.6C125.735,271.269 125.287,271.104 124.668,271.104C123.954,271.104 123.383,271.323 122.956,271.76C122.53,272.197 122.316,272.789 122.316,273.536V278H121.02V270.208H122.3V271.488C122.556,271.008 122.914,270.645 123.372,270.4C123.831,270.144 124.354,270.016 124.94,270.016ZM137.026,266.72V278H135.73V276.576C135.495,277.067 135.149,277.445 134.69,277.712C134.242,277.979 133.714,278.112 133.106,278.112C132.423,278.112 131.821,277.941 131.298,277.6C130.775,277.259 130.37,276.784 130.082,276.176C129.794,275.557 129.65,274.843 129.65,274.032C129.65,273.232 129.794,272.528 130.082,271.92C130.37,271.312 130.775,270.843 131.298,270.512C131.821,270.181 132.423,270.016 133.106,270.016C133.714,270.016 134.242,270.149 134.69,270.416C135.149,270.683 135.495,271.061 135.73,271.552V266.72H137.026ZM133.362,277.04C134.119,277.04 134.701,276.784 135.106,276.272C135.522,275.749 135.73,275.013 135.73,274.064C135.73,273.115 135.522,272.384 135.106,271.872C134.701,271.36 134.119,271.104 133.362,271.104C132.605,271.104 132.013,271.36 131.586,271.872C131.17,272.384 130.962,273.104 130.962,274.032C130.962,274.981 131.17,275.723 131.586,276.256C132.013,276.779 132.605,277.04 133.362,277.04ZM155.424,270.208L152.528,278H151.28L149.088,271.968L146.88,278H145.648L142.752,270.208H144.096L146.304,276.464L148.56,270.208H149.68L151.904,276.464L154.128,270.208H155.424ZM160.159,278.112C159.423,278.112 158.772,277.947 158.207,277.616C157.652,277.285 157.22,276.816 156.911,276.208C156.612,275.589 156.463,274.875 156.463,274.064C156.463,273.253 156.612,272.544 156.911,271.936C157.22,271.317 157.652,270.843 158.207,270.512C158.772,270.181 159.423,270.016 160.159,270.016C160.895,270.016 161.545,270.181 162.111,270.512C162.676,270.843 163.108,271.317 163.407,271.936C163.716,272.544 163.871,273.253 163.871,274.064C163.871,274.875 163.716,275.589 163.407,276.208C163.108,276.816 162.676,277.285 162.111,277.616C161.545,277.947 160.895,278.112 160.159,278.112ZM160.159,277.04C160.927,277.04 161.513,276.789 161.919,276.288C162.335,275.776 162.543,275.035 162.543,274.064C162.543,273.115 162.335,272.384 161.919,271.872C161.503,271.349 160.916,271.088 160.159,271.088C159.401,271.088 158.815,271.349 158.399,271.872C157.983,272.384 157.775,273.115 157.775,274.064C157.775,275.024 157.977,275.76 158.383,276.272C158.799,276.784 159.391,277.04 160.159,277.04ZM174.474,270.016C176.202,270.016 177.066,271.045 177.066,273.104V278H175.77V273.168C175.77,272.453 175.642,271.931 175.386,271.6C175.14,271.269 174.74,271.104 174.186,271.104C173.535,271.104 173.023,271.323 172.65,271.76C172.276,272.197 172.09,272.795 172.09,273.552V278H170.794V273.168C170.794,272.443 170.666,271.92 170.41,271.6C170.154,271.269 169.748,271.104 169.194,271.104C168.543,271.104 168.031,271.323 167.658,271.76C167.284,272.197 167.098,272.795 167.098,273.552V278H165.802V270.208H167.082V271.44C167.316,270.981 167.642,270.629 168.058,270.384C168.474,270.139 168.954,270.016 169.498,270.016C170.703,270.016 171.498,270.528 171.882,271.552C172.106,271.072 172.447,270.699 172.906,270.432C173.364,270.155 173.887,270.016 174.474,270.016ZM185.955,274.304H180.259C180.301,275.211 180.547,275.899 180.995,276.368C181.443,276.827 182.077,277.056 182.899,277.056C183.805,277.056 184.637,276.747 185.395,276.128L185.827,277.072C185.485,277.392 185.048,277.648 184.515,277.84C183.981,278.021 183.437,278.112 182.883,278.112C181.667,278.112 180.707,277.755 180.003,277.04C179.309,276.325 178.963,275.339 178.963,274.08C178.963,273.28 179.117,272.576 179.427,271.968C179.736,271.349 180.168,270.869 180.723,270.528C181.288,270.187 181.928,270.016 182.643,270.016C183.677,270.016 184.488,270.357 185.075,271.04C185.661,271.712 185.955,272.64 185.955,273.824V274.304ZM182.659,271.024C181.976,271.024 181.427,271.237 181.011,271.664C180.605,272.091 180.365,272.693 180.291,273.472H184.819C184.797,272.683 184.6,272.08 184.227,271.664C183.853,271.237 183.331,271.024 182.659,271.024ZM191.784,270.016C193.651,270.016 194.584,271.045 194.584,273.104V278H193.288V273.184C193.288,272.459 193.144,271.931 192.856,271.6C192.579,271.269 192.131,271.104 191.512,271.104C190.797,271.104 190.227,271.323 189.8,271.76C189.373,272.197 189.16,272.789 189.16,273.536V278H187.864V270.208H189.144V271.488C189.4,271.008 189.757,270.645 190.216,270.4C190.675,270.144 191.197,270.016 191.784,270.016ZM205.163,270.928C205.877,270.928 206.496,271.077 207.019,271.376C207.552,271.675 207.963,272.096 208.251,272.64C208.539,273.173 208.683,273.792 208.683,274.496C208.683,275.211 208.523,275.84 208.203,276.384C207.893,276.928 207.451,277.355 206.875,277.664C206.299,277.963 205.627,278.112 204.859,278.112C204.176,278.112 203.499,277.989 202.827,277.744C202.165,277.488 201.605,277.147 201.147,276.72L201.643,275.712C202.688,276.576 203.76,277.008 204.859,277.008C205.648,277.008 206.267,276.784 206.715,276.336C207.163,275.888 207.387,275.28 207.387,274.512C207.387,273.765 207.163,273.163 206.715,272.704C206.277,272.245 205.691,272.016 204.955,272.016C203.877,272.016 203.019,272.453 202.379,273.328H201.467V266.72H208.235V267.808H202.763V271.824C203.061,271.536 203.413,271.317 203.819,271.168C204.235,271.008 204.683,270.928 205.163,270.928ZM210.469,266.72H212.005L211.685,271.152H210.821L210.469,266.72ZM219.76,272.208C220.421,272.411 220.933,272.757 221.296,273.248C221.669,273.739 221.856,274.325 221.856,275.008C221.856,275.979 221.504,276.741 220.8,277.296C220.096,277.84 219.098,278.112 217.808,278.112C216.517,278.112 215.52,277.84 214.816,277.296C214.112,276.741 213.76,275.979 213.76,275.008C213.76,274.315 213.946,273.723 214.32,273.232C214.693,272.741 215.21,272.4 215.872,272.208C215.306,271.995 214.858,271.659 214.528,271.2C214.197,270.741 214.032,270.203 214.032,269.584C214.032,268.667 214.362,267.941 215.024,267.408C215.696,266.864 216.624,266.592 217.808,266.592C218.992,266.592 219.914,266.864 220.576,267.408C221.248,267.941 221.584,268.667 221.584,269.584C221.584,270.203 221.418,270.747 221.088,271.216C220.757,271.675 220.314,272.005 219.76,272.208ZM215.328,269.68C215.328,270.341 215.536,270.848 215.952,271.2C216.378,271.552 216.997,271.728 217.808,271.728C218.618,271.728 219.232,271.552 219.648,271.2C220.074,270.848 220.288,270.341 220.288,269.68C220.288,269.051 220.074,268.56 219.648,268.208C219.232,267.856 218.618,267.68 217.808,267.68C216.997,267.68 216.378,267.856 215.952,268.208C215.536,268.56 215.328,269.051 215.328,269.68ZM217.808,277.024C219.642,277.024 220.56,276.325 220.56,274.928C220.56,273.531 219.642,272.832 217.808,272.832C215.973,272.832 215.056,273.531 215.056,274.928C215.056,276.325 215.973,277.024 217.808,277.024ZM223.594,266.72H225.13L224.81,271.152H223.946L223.594,266.72ZM226.794,266.72H228.33L228.01,271.152H227.146L226.794,266.72ZM242.677,278L241.365,276.576C240.906,277.077 240.389,277.461 239.813,277.728C239.237,277.984 238.608,278.112 237.925,278.112C237.21,278.112 236.576,277.989 236.021,277.744C235.477,277.499 235.05,277.152 234.741,276.704C234.442,276.256 234.293,275.744 234.293,275.168C234.293,274.485 234.496,273.877 234.901,273.344C235.317,272.8 235.984,272.251 236.901,271.696C236.41,271.131 236.069,270.656 235.877,270.272C235.685,269.888 235.589,269.488 235.589,269.072C235.589,268.325 235.84,267.728 236.341,267.28C236.853,266.832 237.525,266.608 238.357,266.608C239.136,266.608 239.749,266.816 240.197,267.232C240.656,267.637 240.885,268.187 240.885,268.88C240.885,269.435 240.72,269.936 240.389,270.384C240.058,270.821 239.466,271.312 238.613,271.856L241.285,274.784C241.765,273.835 242.021,272.699 242.053,271.376H243.189C243.146,273.051 242.773,274.469 242.069,275.632L244.229,278H242.677ZM238.373,267.6C237.904,267.6 237.525,267.733 237.237,268C236.96,268.267 236.821,268.624 236.821,269.072C236.821,269.381 236.896,269.68 237.045,269.968C237.194,270.245 237.482,270.624 237.909,271.104C238.613,270.667 239.093,270.288 239.349,269.968C239.616,269.648 239.749,269.307 239.749,268.944C239.749,268.539 239.621,268.213 239.365,267.968C239.12,267.723 238.789,267.6 238.373,267.6ZM237.957,277.056C238.49,277.056 238.986,276.949 239.445,276.736C239.904,276.512 240.304,276.197 240.645,275.792L237.605,272.464C236.858,272.923 236.33,273.355 236.021,273.76C235.722,274.165 235.573,274.624 235.573,275.136C235.573,275.723 235.786,276.192 236.213,276.544C236.65,276.885 237.232,277.056 237.957,277.056ZM255.971,270.208V278H254.707V276.672C254.462,277.141 254.115,277.499 253.667,277.744C253.23,277.989 252.734,278.112 252.179,278.112C251.251,278.112 250.552,277.856 250.083,277.344C249.614,276.821 249.379,276.053 249.379,275.04V270.208H250.675V275.008C250.675,275.701 250.814,276.213 251.091,276.544C251.368,276.864 251.806,277.024 252.403,277.024C253.086,277.024 253.635,276.805 254.051,276.368C254.467,275.92 254.675,275.333 254.675,274.608V270.208H255.971ZM262.3,270.016C262.982,270.016 263.585,270.187 264.108,270.528C264.63,270.869 265.036,271.349 265.324,271.968C265.612,272.576 265.756,273.285 265.756,274.096C265.756,274.907 265.612,275.616 265.324,276.224C265.036,276.821 264.63,277.285 264.108,277.616C263.596,277.947 262.993,278.112 262.3,278.112C261.692,278.112 261.158,277.979 260.7,277.712C260.252,277.445 259.91,277.067 259.676,276.576V280.88H258.38V270.208H259.676V271.552C259.91,271.061 260.252,270.683 260.7,270.416C261.158,270.149 261.692,270.016 262.3,270.016ZM262.044,277.04C262.801,277.04 263.388,276.784 263.804,276.272C264.22,275.76 264.428,275.035 264.428,274.096C264.428,273.157 264.22,272.427 263.804,271.904C263.388,271.371 262.801,271.104 262.044,271.104C261.286,271.104 260.7,271.36 260.284,271.872C259.878,272.384 259.676,273.115 259.676,274.064C259.676,275.013 259.878,275.749 260.284,276.272C260.7,276.784 261.286,277.04 262.044,277.04ZM72.644,301.92C72.644,302.88 72.41,303.6 71.94,304.08C71.471,304.56 70.746,304.832 69.764,304.896L69.044,304.944L68.948,303.936L69.668,303.888C70.788,303.813 71.348,303.216 71.348,302.096V294.208H72.644V301.92ZM72.788,290.832V292.288H71.22V290.832H72.788ZM78.299,302.112C77.563,302.112 76.912,301.947 76.347,301.616C75.793,301.285 75.36,300.816 75.051,300.208C74.753,299.589 74.603,298.875 74.603,298.064C74.603,297.253 74.753,296.544 75.051,295.936C75.36,295.317 75.793,294.843 76.347,294.512C76.912,294.181 77.563,294.016 78.299,294.016C79.035,294.016 79.686,294.181 80.251,294.512C80.816,294.843 81.248,295.317 81.547,295.936C81.856,296.544 82.011,297.253 82.011,298.064C82.011,298.875 81.856,299.589 81.547,300.208C81.248,300.816 80.816,301.285 80.251,301.616C79.686,301.947 79.035,302.112 78.299,302.112ZM78.299,301.04C79.067,301.04 79.654,300.789 80.059,300.288C80.475,299.776 80.683,299.035 80.683,298.064C80.683,297.115 80.475,296.384 80.059,295.872C79.643,295.349 79.057,295.088 78.299,295.088C77.542,295.088 76.955,295.349 76.539,295.872C76.123,296.384 75.915,297.115 75.915,298.064C75.915,299.024 76.118,299.76 76.523,300.272C76.939,300.784 77.531,301.04 78.299,301.04ZM83.942,294.208H85.238V302H83.942V294.208ZM85.382,290.832V292.288H83.814V290.832H85.382ZM91.581,294.016C93.448,294.016 94.381,295.045 94.381,297.104V302H93.085V297.184C93.085,296.459 92.941,295.931 92.653,295.6C92.376,295.269 91.928,295.104 91.309,295.104C90.594,295.104 90.024,295.323 89.597,295.76C89.17,296.197 88.957,296.789 88.957,297.536V302H87.661V294.208H88.941V295.488C89.197,295.008 89.554,294.645 90.013,294.4C90.472,294.144 90.994,294.016 91.581,294.016ZM104.048,294.016C105.04,294.016 105.776,294.272 106.256,294.784C106.746,295.285 106.992,296.053 106.992,297.088V302H105.728V300.672C105.525,301.131 105.216,301.488 104.8,301.744C104.384,301.989 103.904,302.112 103.36,302.112C102.858,302.112 102.394,302.011 101.968,301.808C101.552,301.605 101.221,301.323 100.976,300.96C100.73,300.597 100.608,300.197 100.608,299.76C100.608,299.173 100.757,298.72 101.056,298.4C101.354,298.069 101.845,297.835 102.528,297.696C103.21,297.547 104.16,297.472 105.376,297.472H105.728V296.944C105.728,296.293 105.594,295.824 105.328,295.536C105.061,295.237 104.629,295.088 104.032,295.088C103.136,295.088 102.245,295.365 101.36,295.92L100.928,294.976C101.333,294.688 101.818,294.459 102.384,294.288C102.96,294.107 103.514,294.016 104.048,294.016ZM103.536,301.088C104.176,301.088 104.698,300.875 105.104,300.448C105.52,300.011 105.728,299.451 105.728,298.768V298.304H105.44C104.501,298.304 103.786,298.347 103.296,298.432C102.805,298.507 102.453,298.64 102.24,298.832C102.037,299.013 101.936,299.285 101.936,299.648C101.936,300.064 102.085,300.411 102.384,300.688C102.693,300.955 103.077,301.088 103.536,301.088ZM112.004,302.112C110.692,302.112 109.652,301.776 108.884,301.104L109.316,300.144C109.732,300.475 110.153,300.715 110.58,300.864C111.017,301.003 111.508,301.072 112.052,301.072C112.628,301.072 113.065,300.976 113.364,300.784C113.673,300.581 113.828,300.293 113.828,299.92C113.828,299.611 113.727,299.365 113.524,299.184C113.321,299.003 112.985,298.859 112.516,298.752L111.204,298.448C110.543,298.299 110.036,298.043 109.684,297.68C109.332,297.317 109.156,296.869 109.156,296.336C109.156,295.643 109.428,295.083 109.972,294.656C110.516,294.229 111.241,294.016 112.148,294.016C112.692,294.016 113.209,294.107 113.7,294.288C114.191,294.459 114.601,294.709 114.932,295.04L114.5,295.984C113.785,295.376 113.001,295.072 112.148,295.072C111.604,295.072 111.177,295.179 110.868,295.392C110.569,295.595 110.42,295.883 110.42,296.256C110.42,296.565 110.511,296.816 110.692,297.008C110.884,297.189 111.188,297.328 111.604,297.424L112.932,297.744C113.657,297.915 114.196,298.176 114.548,298.528C114.9,298.869 115.076,299.317 115.076,299.872C115.076,300.555 114.799,301.099 114.244,301.504C113.7,301.909 112.953,302.112 112.004,302.112ZM122.617,292.464V294.096H120.985V292.944C120.985,292.496 121.059,292.085 121.209,291.712C121.358,291.339 121.609,290.976 121.961,290.624L122.569,291.104C122.153,291.563 121.918,292.016 121.865,292.464H122.617ZM125.273,292.464V294.096H123.641V292.944C123.641,292.496 123.715,292.085 123.865,291.712C124.014,291.339 124.265,290.976 124.617,290.624L125.225,291.104C124.809,291.563 124.574,292.016 124.521,292.464H125.273ZM138.337,290.72V302H137.137L137.153,293.264L133.489,300.208H132.561L128.881,293.328L128.897,302H127.681V290.72H128.769L133.041,298.896L137.265,290.72H138.337ZM147.47,298.304H141.774C141.817,299.211 142.062,299.899 142.51,300.368C142.958,300.827 143.593,301.056 144.414,301.056C145.321,301.056 146.153,300.747 146.91,300.128L147.342,301.072C147.001,301.392 146.564,301.648 146.03,301.84C145.497,302.021 144.953,302.112 144.398,302.112C143.182,302.112 142.222,301.755 141.518,301.04C140.825,300.325 140.478,299.339 140.478,298.08C140.478,297.28 140.633,296.576 140.942,295.968C141.252,295.349 141.684,294.869 142.238,294.528C142.804,294.187 143.444,294.016 144.158,294.016C145.193,294.016 146.004,294.357 146.59,295.04C147.177,295.712 147.47,296.64 147.47,297.824V298.304ZM144.174,295.024C143.492,295.024 142.942,295.237 142.526,295.664C142.121,296.091 141.881,296.693 141.806,297.472H146.334C146.313,296.683 146.116,296.08 145.742,295.664C145.369,295.237 144.846,295.024 144.174,295.024ZM158.052,294.016C159.78,294.016 160.644,295.045 160.644,297.104V302H159.348V297.168C159.348,296.453 159.22,295.931 158.964,295.6C158.718,295.269 158.318,295.104 157.764,295.104C157.113,295.104 156.601,295.323 156.228,295.76C155.854,296.197 155.668,296.795 155.668,297.552V302H154.372V297.168C154.372,296.443 154.244,295.92 153.988,295.6C153.732,295.269 153.326,295.104 152.772,295.104C152.121,295.104 151.609,295.323 151.236,295.76C150.862,296.197 150.676,296.795 150.676,297.552V302H149.38V294.208H150.66V295.44C150.894,294.981 151.22,294.629 151.636,294.384C152.052,294.139 152.532,294.016 153.076,294.016C154.281,294.016 155.076,294.528 155.46,295.552C155.684,295.072 156.025,294.699 156.484,294.432C156.942,294.155 157.465,294.016 158.052,294.016ZM166.925,294.016C167.607,294.016 168.21,294.181 168.733,294.512C169.255,294.843 169.661,295.312 169.949,295.92C170.237,296.528 170.381,297.232 170.381,298.032C170.381,298.843 170.237,299.557 169.949,300.176C169.661,300.784 169.255,301.259 168.733,301.6C168.21,301.941 167.607,302.112 166.925,302.112C166.317,302.112 165.783,301.979 165.325,301.712C164.877,301.445 164.535,301.067 164.301,300.576V302H163.005V290.72H164.301V295.552C164.535,295.061 164.877,294.683 165.325,294.416C165.783,294.149 166.317,294.016 166.925,294.016ZM166.669,301.04C167.426,301.04 168.013,300.779 168.429,300.256C168.845,299.723 169.053,298.981 169.053,298.032C169.053,297.104 168.845,296.384 168.429,295.872C168.013,295.36 167.426,295.104 166.669,295.104C165.911,295.104 165.325,295.36 164.909,295.872C164.503,296.384 164.301,297.115 164.301,298.064C164.301,299.013 164.503,299.749 164.909,300.272C165.325,300.784 165.911,301.04 166.669,301.04ZM178.845,298.304H173.149C173.192,299.211 173.437,299.899 173.885,300.368C174.333,300.827 174.968,301.056 175.789,301.056C176.696,301.056 177.528,300.747 178.285,300.128L178.717,301.072C178.376,301.392 177.939,301.648 177.405,301.84C176.872,302.021 176.328,302.112 175.773,302.112C174.557,302.112 173.597,301.755 172.893,301.04C172.2,300.325 171.853,299.339 171.853,298.08C171.853,297.28 172.008,296.576 172.317,295.968C172.627,295.349 173.059,294.869 173.613,294.528C174.179,294.187 174.819,294.016 175.533,294.016C176.568,294.016 177.379,294.357 177.965,295.04C178.552,295.712 178.845,296.64 178.845,297.824V298.304ZM175.549,295.024C174.867,295.024 174.317,295.237 173.901,295.664C173.496,296.091 173.256,296.693 173.181,297.472H177.709C177.688,296.683 177.491,296.08 177.117,295.664C176.744,295.237 176.221,295.024 175.549,295.024ZM185.171,295.136L184.323,295.216C183.523,295.291 182.941,295.541 182.579,295.968C182.227,296.395 182.051,296.923 182.051,297.552V302H180.755V294.208H182.019V295.568C182.456,294.661 183.315,294.155 184.595,294.048L185.059,294.016L185.171,295.136ZM188.957,302.112C187.645,302.112 186.605,301.776 185.837,301.104L186.269,300.144C186.685,300.475 187.107,300.715 187.533,300.864C187.971,301.003 188.461,301.072 189.005,301.072C189.581,301.072 190.019,300.976 190.317,300.784C190.627,300.581 190.781,300.293 190.781,299.92C190.781,299.611 190.68,299.365 190.477,299.184C190.275,299.003 189.939,298.859 189.469,298.752L188.157,298.448C187.496,298.299 186.989,298.043 186.637,297.68C186.285,297.317 186.109,296.869 186.109,296.336C186.109,295.643 186.381,295.083 186.925,294.656C187.469,294.229 188.195,294.016 189.101,294.016C189.645,294.016 190.163,294.107 190.653,294.288C191.144,294.459 191.555,294.709 191.885,295.04L191.453,295.984C190.739,295.376 189.955,295.072 189.101,295.072C188.557,295.072 188.131,295.179 187.821,295.392C187.523,295.595 187.373,295.883 187.373,296.256C187.373,296.565 187.464,296.816 187.645,297.008C187.837,297.189 188.141,297.328 188.557,297.424L189.885,297.744C190.611,297.915 191.149,298.176 191.501,298.528C191.853,298.869 192.029,299.317 192.029,299.872C192.029,300.555 191.752,301.099 191.197,301.504C190.653,301.909 189.907,302.112 188.957,302.112ZM195.367,300.368V302H193.735V300.368H195.367ZM198.179,290.72V291.872C198.179,292.32 198.104,292.731 197.955,293.104C197.816,293.477 197.571,293.84 197.219,294.192L196.611,293.712C197.027,293.253 197.262,292.8 197.315,292.352H196.547V290.72H198.179ZM200.835,290.72V291.872C200.835,292.32 200.76,292.731 200.611,293.104C200.472,293.477 200.227,293.84 199.875,294.192L199.267,293.712C199.683,293.253 199.918,292.8 199.971,292.352H199.203V290.72H200.835Z"
      android:fillColor="#646C70"/>
  <path
      android:pathData="M44.455,231.31H49.387C50.767,231.31 51.955,231.562 52.951,232.066C53.947,232.57 54.709,233.296 55.237,234.244C55.765,235.192 56.029,236.326 56.029,237.646C56.029,238.966 55.765,240.106 55.237,241.066C54.721,242.014 53.965,242.74 52.969,243.244C51.973,243.748 50.779,244 49.387,244H44.455V231.31ZM49.189,241.732C51.889,241.732 53.239,240.37 53.239,237.646C53.239,234.934 51.889,233.578 49.189,233.578H47.263V241.732H49.189ZM61.892,234.946C63.212,234.946 64.184,235.258 64.808,235.882C65.444,236.506 65.762,237.472 65.762,238.78V244H63.188V242.686C63.008,243.142 62.708,243.502 62.288,243.766C61.868,244.018 61.376,244.144 60.812,244.144C60.212,244.144 59.666,244.024 59.174,243.784C58.694,243.544 58.31,243.208 58.022,242.776C57.746,242.344 57.608,241.864 57.608,241.336C57.608,240.688 57.77,240.178 58.094,239.806C58.43,239.434 58.964,239.164 59.696,238.996C60.428,238.828 61.43,238.744 62.702,238.744H63.17V238.42C63.17,237.892 63.056,237.52 62.828,237.304C62.6,237.088 62.204,236.98 61.64,236.98C61.208,236.98 60.728,237.058 60.2,237.214C59.672,237.37 59.168,237.586 58.688,237.862L57.968,236.044C58.472,235.732 59.09,235.474 59.822,235.27C60.566,235.054 61.256,234.946 61.892,234.946ZM61.406,242.272C61.934,242.272 62.36,242.098 62.684,241.75C63.008,241.39 63.17,240.928 63.17,240.364V240.058H62.864C61.892,240.058 61.208,240.136 60.812,240.292C60.428,240.448 60.236,240.73 60.236,241.138C60.236,241.462 60.344,241.732 60.56,241.948C60.788,242.164 61.07,242.272 61.406,242.272ZM72.69,242.146C72.966,242.146 73.254,242.128 73.554,242.092L73.41,244.072C73.062,244.12 72.714,244.144 72.366,244.144C71.022,244.144 70.038,243.85 69.414,243.262C68.802,242.674 68.496,241.78 68.496,240.58V237.196H66.822V235.162H68.496V232.57H71.214V235.162H73.428V237.196H71.214V240.562C71.214,241.618 71.706,242.146 72.69,242.146ZM74.755,235.162H77.473V244H74.755V235.162ZM77.581,230.914V233.452H74.665V230.914H77.581ZM85.098,234.946C86.154,234.946 86.94,235.258 87.456,235.882C87.972,236.494 88.23,237.424 88.23,238.672V244H85.512V238.798C85.512,238.198 85.398,237.76 85.17,237.484C84.954,237.208 84.612,237.07 84.144,237.07C83.58,237.07 83.124,237.25 82.776,237.61C82.44,237.97 82.272,238.45 82.272,239.05V244H79.554V235.162H82.2V236.422C82.512,235.942 82.914,235.576 83.406,235.324C83.91,235.072 84.474,234.946 85.098,234.946ZM99.277,235.162V243.046C99.277,244.474 98.869,245.554 98.053,246.286C97.237,247.018 96.031,247.384 94.435,247.384C93.643,247.384 92.881,247.288 92.149,247.096C91.429,246.904 90.793,246.634 90.241,246.286L90.961,244.414C92.101,245.038 93.235,245.35 94.363,245.35C95.851,245.35 96.595,244.648 96.595,243.244V242.272C96.343,242.728 95.965,243.088 95.461,243.352C94.957,243.616 94.393,243.748 93.769,243.748C93.001,243.748 92.317,243.568 91.717,243.208C91.129,242.848 90.667,242.338 90.331,241.678C89.995,241.006 89.827,240.232 89.827,239.356C89.827,238.48 89.995,237.712 90.331,237.052C90.667,236.38 91.129,235.864 91.717,235.504C92.317,235.132 93.001,234.946 93.769,234.946C94.381,234.946 94.933,235.078 95.425,235.342C95.929,235.594 96.313,235.948 96.577,236.404V235.162H99.277ZM94.561,241.678C95.197,241.678 95.689,241.474 96.037,241.066C96.397,240.658 96.577,240.088 96.577,239.356C96.577,238.624 96.397,238.054 96.037,237.646C95.689,237.226 95.197,237.016 94.561,237.016C93.925,237.016 93.427,237.226 93.067,237.646C92.707,238.054 92.527,238.624 92.527,239.356C92.527,240.076 92.707,240.646 93.067,241.066C93.427,241.474 93.925,241.678 94.561,241.678ZM106.612,231.31H115.18V233.524H109.384V236.44H114.82V238.654H109.384V244H106.612V231.31ZM120.769,244.144C119.833,244.144 119.011,243.958 118.303,243.586C117.595,243.202 117.049,242.668 116.665,241.984C116.281,241.288 116.089,240.472 116.089,239.536C116.089,238.6 116.281,237.79 116.665,237.106C117.049,236.41 117.595,235.876 118.303,235.504C119.011,235.132 119.833,234.946 120.769,234.946C121.705,234.946 122.527,235.132 123.235,235.504C123.943,235.876 124.489,236.41 124.873,237.106C125.257,237.79 125.449,238.6 125.449,239.536C125.449,240.472 125.257,241.288 124.873,241.984C124.489,242.668 123.943,243.202 123.235,243.586C122.527,243.958 121.705,244.144 120.769,244.144ZM120.769,242.074C122.089,242.074 122.749,241.228 122.749,239.536C122.749,238.684 122.575,238.048 122.227,237.628C121.891,237.208 121.405,236.998 120.769,236.998C119.449,236.998 118.789,237.844 118.789,239.536C118.789,241.228 119.449,242.074 120.769,242.074ZM133.421,237.178L131.891,237.34C131.135,237.412 130.601,237.628 130.289,237.988C129.977,238.336 129.821,238.804 129.821,239.392V244H127.103V235.162H129.713V236.656C130.157,235.636 131.075,235.078 132.467,234.982L133.259,234.928L133.421,237.178ZM142.505,233.596H138.527V231.31H149.291V233.596H145.313V244H142.505V233.596ZM153.034,234.946C154.354,234.946 155.326,235.258 155.95,235.882C156.586,236.506 156.904,237.472 156.904,238.78V244H154.33V242.686C154.15,243.142 153.85,243.502 153.43,243.766C153.01,244.018 152.518,244.144 151.954,244.144C151.354,244.144 150.808,244.024 150.316,243.784C149.836,243.544 149.452,243.208 149.164,242.776C148.888,242.344 148.75,241.864 148.75,241.336C148.75,240.688 148.912,240.178 149.236,239.806C149.572,239.434 150.106,239.164 150.838,238.996C151.57,238.828 152.572,238.744 153.844,238.744H154.312V238.42C154.312,237.892 154.198,237.52 153.97,237.304C153.742,237.088 153.346,236.98 152.782,236.98C152.35,236.98 151.87,237.058 151.342,237.214C150.814,237.37 150.31,237.586 149.83,237.862L149.11,236.044C149.614,235.732 150.232,235.474 150.964,235.27C151.708,235.054 152.398,234.946 153.034,234.946ZM152.548,242.272C153.076,242.272 153.502,242.098 153.826,241.75C154.15,241.39 154.312,240.928 154.312,240.364V240.058H154.006C153.034,240.058 152.35,240.136 151.954,240.292C151.57,240.448 151.378,240.73 151.378,241.138C151.378,241.462 151.486,241.732 151.702,241.948C151.93,242.164 152.212,242.272 152.548,242.272ZM158.937,231.31H161.655V244H158.937V231.31ZM163.735,231.31H166.453V244H163.735V231.31ZM173.778,231.31H179.61C180.99,231.31 182.064,231.658 182.832,232.354C183.6,233.05 183.984,234.022 183.984,235.27C183.984,236.518 183.6,237.49 182.832,238.186C182.064,238.882 180.99,239.23 179.61,239.23H176.586V244H173.778V231.31ZM179.25,237.106C180.654,237.106 181.356,236.5 181.356,235.288C181.356,234.664 181.182,234.202 180.834,233.902C180.486,233.602 179.958,233.452 179.25,233.452H176.586V237.106H179.25ZM193.189,240.094H187.429C187.513,240.802 187.741,241.318 188.113,241.642C188.497,241.954 189.037,242.11 189.733,242.11C190.189,242.11 190.639,242.038 191.083,241.894C191.539,241.738 191.953,241.522 192.325,241.246L193.045,243.064C192.613,243.4 192.085,243.664 191.461,243.856C190.849,244.048 190.231,244.144 189.607,244.144C188.119,244.144 186.943,243.736 186.079,242.92C185.227,242.092 184.801,240.97 184.801,239.554C184.801,238.654 184.987,237.856 185.359,237.16C185.731,236.464 186.247,235.924 186.907,235.54C187.567,235.144 188.317,234.946 189.157,234.946C190.393,234.946 191.371,235.348 192.091,236.152C192.823,236.956 193.189,238.048 193.189,239.428V240.094ZM189.229,236.818C188.713,236.818 188.299,236.986 187.987,237.322C187.687,237.646 187.501,238.12 187.429,238.744H190.903C190.867,238.108 190.705,237.628 190.417,237.304C190.141,236.98 189.745,236.818 189.229,236.818ZM199.079,244.144C198.143,244.144 197.321,243.958 196.613,243.586C195.905,243.202 195.359,242.668 194.975,241.984C194.591,241.288 194.399,240.472 194.399,239.536C194.399,238.6 194.591,237.79 194.975,237.106C195.359,236.41 195.905,235.876 196.613,235.504C197.321,235.132 198.143,234.946 199.079,234.946C200.015,234.946 200.837,235.132 201.545,235.504C202.253,235.876 202.799,236.41 203.183,237.106C203.567,237.79 203.759,238.6 203.759,239.536C203.759,240.472 203.567,241.288 203.183,241.984C202.799,242.668 202.253,243.202 201.545,243.586C200.837,243.958 200.015,244.144 199.079,244.144ZM199.079,242.074C200.399,242.074 201.059,241.228 201.059,239.536C201.059,238.684 200.885,238.048 200.537,237.628C200.201,237.208 199.715,236.998 199.079,236.998C197.759,236.998 197.099,237.844 197.099,239.536C197.099,241.228 197.759,242.074 199.079,242.074ZM210.885,234.946C211.641,234.946 212.313,235.144 212.901,235.54C213.501,235.924 213.963,236.47 214.287,237.178C214.623,237.874 214.791,238.678 214.791,239.59C214.791,240.502 214.623,241.306 214.287,242.002C213.963,242.686 213.507,243.214 212.919,243.586C212.331,243.958 211.653,244.144 210.885,244.144C210.285,244.144 209.739,244.024 209.247,243.784C208.767,243.532 208.395,243.184 208.131,242.74V247.24H205.413V235.162H208.095V236.404C208.359,235.948 208.737,235.594 209.229,235.342C209.721,235.078 210.273,234.946 210.885,234.946ZM210.093,242.074C210.729,242.074 211.221,241.864 211.569,241.444C211.917,241.012 212.091,240.394 212.091,239.59C212.091,238.774 211.917,238.144 211.569,237.7C211.221,237.244 210.729,237.016 210.093,237.016C209.457,237.016 208.965,237.238 208.617,237.682C208.269,238.114 208.095,238.738 208.095,239.554C208.095,240.358 208.269,240.982 208.617,241.426C208.965,241.858 209.457,242.074 210.093,242.074ZM216.452,231.31H219.17V244H216.452V231.31ZM229.207,240.094H223.447C223.531,240.802 223.759,241.318 224.131,241.642C224.515,241.954 225.055,242.11 225.751,242.11C226.207,242.11 226.657,242.038 227.101,241.894C227.557,241.738 227.971,241.522 228.343,241.246L229.063,243.064C228.631,243.4 228.103,243.664 227.479,243.856C226.867,244.048 226.249,244.144 225.625,244.144C224.137,244.144 222.961,243.736 222.097,242.92C221.245,242.092 220.819,240.97 220.819,239.554C220.819,238.654 221.005,237.856 221.377,237.16C221.749,236.464 222.265,235.924 222.925,235.54C223.585,235.144 224.335,234.946 225.175,234.946C226.411,234.946 227.389,235.348 228.109,236.152C228.841,236.956 229.207,238.048 229.207,239.428V240.094ZM225.247,236.818C224.731,236.818 224.317,236.986 224.005,237.322C223.705,237.646 223.519,238.12 223.447,238.744H226.921C226.885,238.108 226.723,237.628 226.435,237.304C226.159,236.98 225.763,236.818 225.247,236.818Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M36,0H236V200H36V0Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M136.084,195.645C188.861,195.645 231.645,152.861 231.645,100.084C231.645,47.307 188.861,4.522 136.084,4.522C83.307,4.522 40.523,47.307 40.523,100.084C40.523,152.861 83.307,195.645 136.084,195.645Z"
      android:fillColor="#E3F3FF"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M125,63.56L145.738,80.993L143.94,90.221L118.797,78.756L125,63.56Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M134.585,85.812L143.054,89.818L144.216,88.808L145.738,80.993L141.323,77.282L134.585,85.812Z"
      android:fillColor="#ECECEC"/>
  <path
      android:pathData="M123.456,33.575C124.926,33.575 126.118,32.382 126.118,30.909C126.118,29.436 124.926,28.242 123.456,28.242C121.986,28.242 120.795,29.436 120.795,30.909C120.795,32.382 121.986,33.575 123.456,33.575Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M124.587,40.182C126.184,40.182 127.479,38.884 127.479,37.284C127.479,35.683 126.184,34.386 124.587,34.386C122.989,34.386 121.694,35.683 121.694,37.284C121.694,38.884 122.989,40.182 124.587,40.182Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M125.402,40.066L126.735,35.347C126.108,34.646 125.18,34.295 124.247,34.405C123.314,34.515 122.493,35.074 122.045,35.901L123.48,39.963C124.09,40.214 124.766,40.25 125.398,40.066H125.402Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M124.587,40.182C126.184,40.182 127.479,38.884 127.479,37.284C127.479,35.683 126.184,34.386 124.587,34.386C122.989,34.386 121.694,35.683 121.694,37.284C121.694,38.884 122.989,40.182 124.587,40.182Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M104.507,41.82h10.864v15.73h-10.864z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M115.371,49.052L104.507,51.222V41.82H115.371V49.052Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M113.216,48.104C119.92,48.104 125.355,42.658 125.355,35.941C125.355,29.224 119.92,23.779 113.216,23.779C106.512,23.779 101.077,29.224 101.077,35.941C101.077,42.658 106.512,48.104 113.216,48.104Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M102.299,35.407C102.743,31.14 104.712,25.894 108.271,24.093C110.271,23.076 113.236,24.331 115.643,24.585C117.282,24.756 118.87,25.261 120.308,26.067C116.653,23.439 111.847,23.05 107.819,25.059C103.792,27.067 101.203,31.143 101.093,35.649C100.982,40.155 103.369,44.353 107.293,46.556C103.75,44.009 101.843,39.753 102.299,35.407Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M102.976,21.496C103.649,21.51 104.288,21.791 104.753,22.277C105.218,22.764 105.471,23.415 105.456,24.089"
      android:strokeLineJoin="round"
      android:strokeWidth="0.981094"
      android:fillColor="#00000000"
      android:strokeColor="#EDA257"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M106.438,20.051C105.858,20.395 105.439,20.955 105.272,21.608C105.105,22.262 105.205,22.955 105.549,23.535"
      android:strokeLineJoin="round"
      android:strokeWidth="0.981094"
      android:fillColor="#00000000"
      android:strokeColor="#EDA257"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M85.92,195.269H110.876L108.877,186.501L101.841,186.724L88.346,191.908C86.917,192.415 85.951,193.757 85.92,195.275L85.92,195.269Z"
      android:fillColor="#E43C28"/>
  <path
      android:pathData="M110.877,195.266L109.104,187.491L106.224,195.266H110.877Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M132.843,195.269H107.887L109.886,186.501L116.92,186.724L130.417,191.908C131.846,192.415 132.812,193.757 132.843,195.275V195.269Z"
      android:fillColor="#E43C28"/>
  <path
      android:pathData="M113.461,189.584C112.512,189.584 111.603,189.206 110.932,188.534C110.262,187.862 109.885,186.95 109.886,186V180.384L117.315,179.563L117.038,186.152C116.956,188.071 115.378,189.584 113.461,189.584Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M117.238,181.368L109.886,183.665V180.384L117.315,179.563L117.238,181.368Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M118.879,185.101C118.879,185.715 118.382,187.899 117.769,187.899C117.155,187.899 116.658,185.715 116.658,185.101C116.658,184.486 117.155,183.988 117.769,183.988C118.382,183.988 118.879,184.486 118.879,185.101V185.101Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M120.55,189.012C119.937,189.012 117.757,188.513 117.757,187.899C117.757,187.284 119.937,186.786 120.55,186.786C121.164,186.786 121.661,187.284 121.661,187.899C121.661,188.513 121.164,189.012 120.55,189.012V189.012Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M127.34,195.122C127.338,193.7 127.884,192.332 128.864,191.304"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M109.477,193.839H126.031"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M105.301,189.584C106.249,189.584 107.159,189.206 107.83,188.534C108.501,187.862 108.878,186.951 108.878,186V180.384L101.448,179.563L101.728,186.152C101.809,188.07 103.384,189.583 105.301,189.584Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M101.622,183.665L108.878,181.991V180.384L101.448,179.563L101.622,183.665Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M99.884,185.101C99.884,185.715 100.379,187.899 100.995,187.899C101.61,187.899 102.105,185.715 102.105,185.101C102.105,184.486 101.608,183.988 100.995,183.988C100.381,183.988 99.884,184.486 99.884,185.101H99.884Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.213,189.012C98.827,189.012 101.006,188.513 101.006,187.899C101.006,187.284 98.827,186.786 98.213,186.786C97.6,186.786 97.103,187.284 97.103,187.899C97.103,188.513 97.6,189.012 98.213,189.012V189.012Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M91.424,195.122C91.425,193.7 90.88,192.332 89.899,191.304"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M105.929,193.839H92.732"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M118.853,155.069L124.629,110.376H94.136L99.737,155.069C99.987,156.87 99.897,158.702 99.471,160.469C98.769,163.392 98.138,165.88 98.804,167.398C99.113,167.928 99.51,168.4 99.98,168.794C101.188,169.771 101.976,171.176 102.179,172.718L102.19,172.79C102.367,174.12 102.109,175.472 101.457,176.645C100.826,177.8 100.428,178.953 101.013,179.983C101.679,181.132 104.827,180.823 104.827,180.823H113.588C113.588,180.823 116.842,181.132 117.52,179.983C118.127,178.957 117.713,177.804 117.062,176.645C116.392,175.479 116.128,174.123 116.309,172.79L116.32,172.718C116.537,171.163 117.353,169.754 118.593,168.794C119.075,168.403 119.486,167.931 119.806,167.398C120.493,165.88 119.848,163.392 119.126,160.469C118.688,158.704 118.596,156.87 118.853,155.069Z"
      android:fillColor="#0D9AFF"/>
  <path
      android:pathData="M108.966,110.376H94.138L94.596,114.027L124.474,111.547L124.627,110.376H109.328H108.966Z"
      android:fillColor="#529ED6"/>
  <path
      android:pathData="M113.927,180.818H109.435"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M123.969,115.618L119.037,155.069C118.787,156.87 118.877,158.702 119.303,160.469C120.003,163.392 120.636,165.88 119.97,167.398C119.661,167.929 119.263,168.401 118.792,168.794C117.585,169.772 116.798,171.176 116.593,172.718V172.789C116.417,174.12 116.673,175.471 117.324,176.645C117.957,177.8 118.355,178.953 117.768,179.983"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M117.708,152.797L119.935,151.911"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M109.435,121.472V168.582"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M106.596,116.682L109.435,118.18L112.668,116.682"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M96.369,128.192L95.809,123.718L94.138,110.376"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M104.836,180.818C104.836,180.818 101.679,181.128 101.021,179.979C100.435,178.953 100.832,177.8 101.466,176.64C102.118,175.468 102.375,174.116 102.199,172.785L102.188,172.714C101.984,171.172 101.197,169.767 99.988,168.79C99.519,168.396 99.121,167.924 98.813,167.394C98.146,165.876 98.771,163.388 99.479,160.465C99.905,158.698 99.996,156.866 99.746,155.065L96.927,132.64"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M101.054,152.797L98.828,151.911"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M125.925,110.376H93.348L90.715,72.784C90.472,69.325 91.674,65.921 94.033,63.384C96.392,60.848 99.696,59.406 103.156,59.404H116.107C119.567,59.407 122.871,60.849 125.23,63.385C127.589,65.922 128.791,69.326 128.548,72.784L125.925,110.376Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M115.36,109.437L98.808,106.341L100.557,99.165L113.865,102.423L115.36,109.437Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M124.998,38.873C123.534,44.76 117.965,48.667 111.943,48.033C111.29,47.962 110.644,47.841 110.01,47.67"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M125.351,35.588C125.229,29.747 120.992,24.812 115.246,23.817C109.499,22.823 103.855,26.048 101.785,31.51C99.714,36.972 101.797,43.138 106.753,46.216L107.287,46.557"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M100.901,31.619C101.945,31.619 102.792,30.771 102.792,29.725C102.792,28.679 101.945,27.831 100.901,27.831C99.857,27.831 99.01,28.679 99.01,29.725C99.01,30.771 99.857,31.619 100.901,31.619Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M100.981,28.721C101.697,28.721 102.276,28.14 102.276,27.423C102.276,26.706 101.697,26.125 100.981,26.125C100.266,26.125 99.686,26.706 99.686,27.423C99.686,28.14 100.266,28.721 100.981,28.721Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M117.74,30.704C117.326,31.341 116.572,31.668 115.825,31.534"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M118.921,32.859C119.679,32.894 120.343,33.381 120.605,34.094"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M126.771,25.329C126.552,26.84 125.743,28.202 124.522,29.115C123.301,30.028 121.767,30.418 120.259,30.199C117.117,29.74 106.811,25.845 107.269,22.698C107.726,19.55 118.773,18.346 121.912,18.805C123.42,19.024 124.779,19.834 125.691,21.058C126.602,22.281 126.99,23.818 126.771,25.329Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M104.982,22.791C106.022,22.79 107.02,23.203 107.757,23.938C108.493,24.674 108.907,25.673 108.908,26.715C108.908,28.885 107.391,36.509 105.227,36.509C103.063,36.509 101.068,28.89 101.066,26.724C101.064,25.682 101.476,24.682 102.211,23.945C102.946,23.207 103.942,22.792 104.982,22.791Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M101.132,37.413C102.967,37.413 104.454,35.923 104.454,34.085C104.454,32.247 102.967,30.757 101.132,30.757C99.298,30.757 97.811,32.247 97.811,34.085C97.811,35.923 99.298,37.413 101.132,37.413Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M100.497,33.242C100.862,33.249 101.21,33.402 101.463,33.666C101.716,33.931 101.853,34.285 101.846,34.651"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M119.791,35.076C119.899,34.662 119.803,34.278 119.575,34.218C119.347,34.158 119.074,34.445 118.966,34.858C118.857,35.272 118.953,35.656 119.181,35.716C119.409,35.776 119.682,35.49 119.791,35.076Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M124.521,29.112C123.301,30.027 121.767,30.417 120.259,30.196C118.171,29.891 112.914,28.066 109.76,25.952"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#F3D5CB"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M121.723,29.048C121.209,29.138 120.684,29.145 120.168,29.068"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M125.731,24.912C125.479,26.662 124.307,28.14 122.663,28.783"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M120.148,41.384C119.432,41.106 118.767,40.709 118.182,40.211C116.567,38.831 114.147,40.369 114.694,42.423C115.029,43.69 115.871,44.872 117.582,45.604C120.341,46.799 123.607,45.297 124.707,42.457C124.837,42.123 124.934,41.778 124.996,41.426C123.429,42.006 121.704,41.991 120.148,41.384Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M111.315,39.1C112.158,39.235 113.018,39.226 113.858,39.073C116.18,38.65 117.646,41.484 115.976,43.151C114.945,44.182 113.483,44.838 111.43,44.593C108.113,44.197 105.811,40.927 106.338,37.582C106.401,37.19 106.502,36.804 106.638,36.431C107.819,37.864 109.482,38.813 111.315,39.1Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M117.948,34.194L117.777,39.113L115.347,39.167"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M117.948,34.194L117.777,39.113L115.347,39.167"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M115.645,33.544L116.702,35.325L116.651,35.281C115.82,34.578 114.734,34.255 113.654,34.39V34.39"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.372,44.961C113.383,44.703 112.551,44.036 112.083,43.126"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M110.735,25.54C112.767,25.54 114.414,23.89 114.414,21.854C114.414,19.818 112.767,18.168 110.735,18.168C108.703,18.168 107.056,19.818 107.056,21.854C107.056,23.89 108.703,25.54 110.735,25.54Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M117.164,21.658C118.289,21.658 119.201,20.744 119.201,19.617C119.201,18.49 118.289,17.576 117.164,17.576C116.039,17.576 115.127,18.49 115.127,19.617C115.127,20.744 116.039,21.658 117.164,21.658Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M122.225,24.32C124.33,24.32 126.036,22.611 126.036,20.503C126.036,18.395 124.33,16.685 122.225,16.685C120.121,16.685 118.415,18.395 118.415,20.503C118.415,22.611 120.121,24.32 122.225,24.32Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M107.493,30.693C108.711,30.693 109.699,29.703 109.699,28.483C109.699,27.262 108.711,26.272 107.493,26.272C106.274,26.272 105.287,27.262 105.287,28.483C105.287,29.703 106.274,30.693 107.493,30.693Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M107.391,33.026C108.153,33.026 108.771,32.407 108.771,31.643C108.771,30.88 108.153,30.261 107.391,30.261C106.629,30.261 106.011,30.88 106.011,31.643C106.011,32.407 106.629,33.026 107.391,33.026Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M106.824,24.928C107.748,24.928 108.497,24.177 108.497,23.252C108.497,22.326 107.748,21.576 106.824,21.576C105.9,21.576 105.151,22.326 105.151,23.252C105.151,24.177 105.9,24.928 106.824,24.928Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M102.394,37.164C102.192,37.247 101.982,37.31 101.767,37.35C100.286,37.64 98.795,36.892 98.139,35.53C97.484,34.167 97.828,32.533 98.977,31.552C100.126,30.571 101.791,30.489 103.03,31.354C104.269,32.219 104.77,33.812 104.249,35.231"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M111.774,32.77L112.552,33.353"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M112.628,34.036L111.343,33.954"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M119.672,34.799C119.746,34.799 119.806,34.739 119.806,34.666C119.806,34.592 119.746,34.532 119.672,34.532C119.599,34.532 119.539,34.592 119.539,34.666C119.539,34.739 119.599,34.799 119.672,34.799Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M102.47,51.948h14.934v9.237h-14.934z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M83.361,104.206C80.346,103.231 78.729,99.866 79.753,96.69L89.417,66.685C90.664,62.821 94.647,60.648 98.315,61.835C102.201,63.092 104.132,67.578 102.496,71.553L90.499,100.692C89.308,103.596 86.202,105.126 83.361,104.206Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M118.515,77.621V85.643"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M112.792,85.46V81.349"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M118.515,76.392C120.138,76.392 121.454,75.074 121.454,73.447C121.454,71.821 120.138,70.502 118.515,70.502C116.891,70.502 115.576,71.821 115.576,73.447C115.576,75.074 116.891,76.392 118.515,76.392Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M118.716,78.286C119.586,77.679 119.086,75.454 117.6,73.316C116.114,71.179 114.204,69.938 113.334,70.545C112.464,71.152 112.964,73.377 114.45,75.515C115.936,77.652 117.846,78.893 118.716,78.286Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M122.665,75.502C124.151,73.364 124.65,71.139 123.78,70.532C122.91,69.925 121,71.166 119.514,73.303C118.028,75.441 117.529,77.665 118.399,78.273C119.268,78.88 121.178,77.639 122.665,75.502Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M118.798,78.755C119.566,77.985 118.613,75.781 116.667,73.832C114.722,71.883 112.522,70.928 111.753,71.698C110.985,72.468 111.938,74.672 113.884,76.621C115.829,78.57 118.029,79.525 118.798,78.755Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M123.235,76.623C125.181,74.674 126.134,72.47 125.366,71.7C124.597,70.93 122.397,71.885 120.451,73.834C118.506,75.783 117.553,77.987 118.321,78.757C119.09,79.527 121.29,78.572 123.235,76.623Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M124.7,84.022C125.813,84.022 126.715,83.118 126.715,82.003C126.715,80.888 125.813,79.984 124.7,79.984C123.587,79.984 122.685,80.888 122.685,82.003C122.685,83.118 123.587,84.022 124.7,84.022Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M124.838,85.324C125.434,84.908 125.091,83.383 124.072,81.917C123.053,80.452 121.744,79.601 121.148,80.017C120.552,80.433 120.895,81.958 121.914,83.424C122.933,84.889 124.242,85.74 124.838,85.324Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M127.545,83.406C128.564,81.94 128.906,80.415 128.31,79.999C127.714,79.583 126.405,80.434 125.386,81.9C124.367,83.365 124.024,84.891 124.62,85.307C125.216,85.723 126.525,84.872 127.545,83.406Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M124.894,85.642C125.421,85.115 124.767,83.604 123.433,82.268C122.099,80.932 120.591,80.276 120.065,80.804C119.538,81.332 120.192,82.842 121.526,84.178C122.859,85.515 124.367,86.17 124.894,85.642Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M127.937,84.178C129.271,82.841 129.925,81.331 129.398,80.803C128.872,80.275 127.364,80.931 126.03,82.267C124.696,83.603 124.042,85.114 124.569,85.641C125.096,86.169 126.604,85.514 127.937,84.178Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M112.925,80.517C114.038,80.517 114.94,79.613 114.94,78.498C114.94,77.383 114.038,76.479 112.925,76.479C111.812,76.479 110.91,77.383 110.91,78.498C110.91,79.613 111.812,80.517 112.925,80.517Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M113.063,81.816C113.659,81.4 113.316,79.875 112.297,78.409C111.278,76.943 109.968,76.092 109.372,76.508C108.776,76.924 109.119,78.45 110.138,79.915C111.157,81.381 112.467,82.232 113.063,81.816Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M115.837,79.816C115.138,80.832 114.086,81.551 112.887,81.834C112.853,81.675 112.828,81.514 112.811,81.351C112.715,80.343 112.975,79.333 113.547,78.498C114.246,77.482 115.298,76.762 116.497,76.479C116.744,77.635 116.505,78.841 115.837,79.816Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M113.118,82.136C113.645,81.609 112.991,80.098 111.657,78.762C110.323,77.426 108.815,76.771 108.289,77.298C107.762,77.826 108.416,79.337 109.75,80.673C111.083,82.009 112.591,82.664 113.118,82.136Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M116.161,80.672C117.495,79.336 118.149,77.825 117.622,77.298C117.095,76.77 115.587,77.425 114.254,78.761C112.92,80.098 112.266,81.608 112.793,82.136C113.32,82.664 114.828,82.008 116.161,80.672Z"
      android:fillColor="#EDA257"/>
  <path
      android:pathData="M118.726,116.682L107.447,85.643H129.673L118.726,116.682Z"
      android:fillColor="#BDE3FF"/>
  <path
      android:pathData="M121.992,93.992L107.447,85.643H129.673L121.992,93.992Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M126.597,94.366L118.726,116.682"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M113.778,103.067L129.672,85.643L127.906,90.653"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M118.566,84.555C118.866,84.254 118.494,83.394 117.735,82.633C116.976,81.873 116.117,81.5 115.817,81.801C115.517,82.102 115.889,82.962 116.648,83.723C117.407,84.483 118.266,84.856 118.566,84.555Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M120.608,79.542C121.368,78.781 121.74,77.921 121.44,77.62C121.139,77.319 120.281,77.692 119.521,78.453C118.762,79.213 118.39,80.074 118.69,80.374C118.991,80.675 119.849,80.302 120.608,79.542Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M121.123,108.516L117.633,109.395C116.855,109.59 116.065,109.117 115.869,108.338C115.674,107.558 116.146,106.767 116.924,106.57L120.415,105.682C121.193,105.487 121.982,105.96 122.179,106.739C122.275,107.115 122.218,107.514 122.019,107.847C121.821,108.181 121.499,108.421 121.123,108.516Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M115.4,105.542C115.204,104.763 115.677,103.972 116.456,103.776L119.942,102.898C120.722,102.702 121.514,103.175 121.709,103.956C121.905,104.735 121.432,105.525 120.653,105.721L117.167,106.599C116.387,106.796 115.596,106.322 115.4,105.542Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M114.731,102.873C114.535,102.094 115.008,101.303 115.787,101.107L119.273,100.229C120.053,100.033 120.845,100.506 121.04,101.287C121.236,102.066 120.763,102.856 119.984,103.052L116.498,103.93C115.718,104.127 114.927,103.653 114.731,102.873Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M112.173,102.688C111.66,102.071 111.744,101.156 112.36,100.642L115.126,98.336C115.744,97.821 116.662,97.905 117.176,98.523C117.689,99.141 117.605,100.056 116.989,100.57L114.223,102.876C113.606,103.391 112.687,103.307 112.173,102.688Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M114.931,99.664C114.691,98.898 115.117,98.082 115.883,97.841L119.313,96.763C120.081,96.521 120.898,96.948 121.139,97.716C121.379,98.483 120.952,99.299 120.186,99.539L116.756,100.618C115.989,100.859 115.171,100.432 114.931,99.664Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M112.138,102.807C111.716,101.742 112.236,100.536 113.301,100.112C114.367,99.687 115.576,100.209 115.999,101.276L118.544,107.693C118.967,108.759 118.447,109.965 117.382,110.389C116.316,110.813 115.107,110.292 114.684,109.225L112.138,102.807Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M112.174,102.695L114.019,107.543"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M111.381,108.693L98.808,106.341L100.557,99.165L110.821,101.678L111.381,108.693Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M108.008,108.693L84.98,104.645C83.587,104.399 82.363,103.57 81.616,102.367C80.868,101.163 80.666,99.698 81.061,98.336C81.835,95.69 84.603,94.174 87.244,94.949L110.112,101.515L108.008,108.693Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M117.622,100.641L119.279,100.224C119.653,100.129 120.049,100.187 120.38,100.385C120.711,100.583 120.949,100.905 121.043,101.279V101.279C121.137,101.654 121.079,102.051 120.881,102.382C120.683,102.714 120.362,102.953 119.988,103.047L116.5,103.926"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M121.714,103.948V103.948C121.808,104.323 121.75,104.72 121.552,105.051C121.354,105.382 121.033,105.622 120.659,105.716L117.391,106.452"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M117.391,109.437L121.088,108.48"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M79.753,96.69L89.42,66.685"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.026,79.984L91.905,97.284"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M93.043,95.982L107.084,100.612"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M110.261,101.161L108.611,106.639"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M93.045,106.062L108.009,108.693L93.305,109.766L93.045,106.062Z"
      android:fillColor="#E7E7E7"/>
  <path
      android:pathData="M92.747,110.376H114.018"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M109.381,172.979V180.818"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M97.875,79.985L100.515,79.493"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M81.65,100.843C82.009,101.461 81.8,102.253 81.184,102.612C80.567,102.972 79.776,102.763 79.417,102.145"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M116.026,51.945H117.404V61.183"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.47,61.183V51.945H114.371"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.675,59.898L102.47,59.462"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M119.777,59.95C119.448,59.816 117.404,59.469 117.404,59.469"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M104.485,53.653V60.108"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#A1D9FF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M111.752,53.653V60.108"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#A1D9FF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.94,53.653V57.548"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#A1D9FF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M107.04,56.232V60.108"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#A1D9FF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M107.04,53.653V54.452"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#A1D9FF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M109.641,60.108V55.954"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#A1D9FF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M126.469,75.891V80.118"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M131.035,84.336L139.231,87.742"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M125.84,64.067L127.48,66.189"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M124.418,42.27C123.735,43.901 122.226,45.036 120.472,45.239"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M108.444,39.084C107.931,38.79 107.476,38.406 107.1,37.949"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M115.274,43.443L115.5,44.645"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#F3D5CB"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M173.668,172.72L180.029,181.746L178.127,185.953L168.714,175.569L173.668,172.72Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M173.674,181.043L176.307,176.467L175.143,174.815L171.217,178.332L173.674,181.043Z"
      android:fillColor="#B76452"/>
  <path
      android:pathData="M135.171,129.637L133.095,128.13C131.605,127.048 130.546,125.47 130.11,123.678C130.008,123.204 129.657,122.822 129.194,122.681C128.73,122.54 128.227,122.662 127.879,123C127.531,123.337 127.393,123.837 127.519,124.306C128.102,126.718 129.524,128.843 131.529,130.3L133.606,131.809C134.202,132.187 134.989,132.034 135.401,131.461C135.813,130.888 135.708,130.092 135.162,129.646L135.171,129.637Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M133.735,130.965C133.398,131.66 132.561,131.95 131.866,131.612L125.919,128.72C125.226,128.383 124.936,127.548 125.273,126.854C125.609,126.159 126.446,125.869 127.141,126.207L133.088,129.099C133.782,129.436 134.071,130.271 133.735,130.965Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M133.088,129.1L127.136,126.207C126.804,126.044 126.421,126.021 126.071,126.142C125.721,126.263 125.434,126.519 125.272,126.852V126.852"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M133.741,131.649C133.589,132.406 132.852,132.897 132.095,132.745L125.615,131.443C124.859,131.291 124.368,130.555 124.52,129.798C124.671,129.041 125.408,128.55 126.166,128.702L132.646,130.004C133.402,130.156 133.893,130.893 133.741,131.649Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M137.274,132.874C136.672,134.117 135.175,134.636 133.933,134.032L132.425,133.298C131.185,132.696 130.668,131.203 131.269,129.962C131.871,128.719 133.368,128.2 134.611,128.804L136.118,129.537C137.358,130.14 137.875,131.633 137.274,132.874Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M127.248,128.909L132.235,130.026"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M133.088,130.057L134.27,121.612L139.275,123.729L136.725,132.054L133.088,130.057Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M180.8,90.844L182.41,84.834L186.207,84.641L185.447,90.733L180.8,90.844Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M181.857,86.898L182.411,84.834L186.207,84.641L185.73,88.458L181.857,86.898Z"
      android:fillColor="#B76452"/>
  <path
      android:pathData="M154.983,82.744C152.959,82.744 151.317,81.1 151.317,79.072V70.199H158.649V79.072C158.649,81.1 157.008,82.744 154.983,82.744Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M151.318,73.536L158.649,76.024V70.21H151.318V73.536Z"
      android:fillColor="#B76452"/>
  <path
      android:pathData="M151.318,72.163V78.805"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M158.671,72.672V78.551"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M140.24,66.718C141.168,66.718 141.92,65.965 141.92,65.035C141.92,64.106 141.168,63.353 140.24,63.353C139.313,63.353 138.561,64.106 138.561,65.035C138.561,65.965 139.313,66.718 140.24,66.718Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M140.122,65.325C140.091,65.142 139.988,64.98 139.836,64.873C139.685,64.767 139.498,64.725 139.316,64.757"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M141.766,69.32C141.28,70.269 140.178,70.724 139.166,70.393C138.154,70.062 137.532,69.043 137.699,67.989C137.867,66.936 138.773,66.16 139.838,66.16"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M151.633,73.445C157.546,73.445 162.339,68.642 162.339,62.718C162.339,56.794 157.546,51.992 151.633,51.992C145.72,51.992 140.926,56.794 140.926,62.718C140.926,68.642 145.72,73.445 151.633,73.445Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M141.698,62.723C141.698,56.951 146.256,52.215 152.013,52.005C151.884,52.005 151.755,52.005 151.624,52.005C145.711,52.005 140.918,56.808 140.918,62.732C140.918,68.656 145.711,73.458 151.624,73.458C151.755,73.458 151.884,73.458 152.013,73.447C146.253,73.239 141.693,68.497 141.698,62.723Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M173.775,195.032H179.551L182.717,189.546C183.044,188.979 182.854,188.253 182.291,187.921L181.522,187.464C180.949,187.139 180.707,186.437 180.958,185.826C181.103,185.491 181.386,185.234 181.734,185.121C182.082,185.009 182.461,185.052 182.775,185.239L183.617,185.735C183.879,185.891 184.192,185.936 184.487,185.86C184.783,185.784 185.035,185.593 185.19,185.33L186.27,183.498L179.234,180.729L176.639,190.986L175.306,191.981C174.344,192.699 173.777,193.83 173.777,195.032H173.775Z"
      android:fillColor="#E43C28"/>
  <path
      android:pathData="M177.245,188.742C178.018,188.966 178.826,188.52 179.051,187.745L180.355,183.227C180.621,182.303 180.23,181.316 179.404,180.827L179.167,180.604L177.214,188.736L177.245,188.742Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M157.541,176.898L156.508,187.899L160.46,189.626L163.253,176.898H157.541Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M157.54,176.898L157.167,180.856L162.817,178.877L163.252,176.898H157.54Z"
      android:fillColor="#B76452"/>
  <path
      android:pathData="M172.93,195.384H162.508C161.747,195.383 161.131,194.764 161.131,194.002V193.307C161.142,192.646 160.661,192.08 160.009,191.985C159.646,191.941 159.283,192.055 159.009,192.297C158.736,192.54 158.58,192.888 158.58,193.254V193.922C158.58,194.728 157.928,195.382 157.123,195.382H155.305L156.507,187.903L170.395,191.99C171.899,192.431 172.931,193.814 172.93,195.384Z"
      android:fillColor="#E43C28"/>
  <path
      android:pathData="M157.856,117.432L157.516,132.633L156.508,178.04H163.55L175.621,132.882C176.435,129.84 176.214,126.614 174.994,123.711L172.595,118.32C171.486,115.829 169.873,113.595 167.858,111.761L157.856,117.432Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M148.755,114.027L146.585,118.183C144.774,122.475 143.687,127.04 143.37,131.689L142.299,147.339C142.1,150.237 143.196,153.074 145.29,155.083L171.224,179.979L176.111,175.527L159.286,145.616L162.803,117.432L148.755,114.027Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M138.502,129.986L132.588,125.283L140.231,82.101C140.598,80.022 142.877,78.691 145.321,79.125C147.765,79.559 149.447,81.593 149.078,83.672L147.3,91.483L138.502,129.986Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M180.902,109.735C179.54,110.559 177.907,110.403 177.176,109.377L160.406,85.858C159.313,84.325 160.113,81.988 162.153,80.757C164.106,79.575 166.445,79.866 167.374,81.424L182.259,106.147C182.932,107.264 182.326,108.874 180.902,109.735Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M163.266,118.632H153.111C150.272,118.631 147.798,116.693 147.112,113.933L140.425,87.069C139.909,85.002 140.374,82.812 141.683,81.133C142.992,79.454 145.001,78.473 147.127,78.473H161.391C165.844,78.473 169.453,82.09 169.453,86.551V112.435C169.453,114.079 168.802,115.656 167.641,116.818C166.481,117.98 164.907,118.633 163.266,118.632Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M160.791,79.71C160.79,79.284 160.701,78.863 160.531,78.473L154.874,78.332L149.014,78.473C148.845,78.864 148.757,79.285 148.756,79.71C148.756,82.061 151.449,83.966 154.772,83.966C158.096,83.966 160.791,82.061 160.791,79.71Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M154.773,83.966C158.105,83.966 160.791,82.061 160.791,79.71"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M148.756,79.71C148.782,80.698 149.223,81.629 149.969,82.275"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M151.677,78.573H146.438"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M178.887,57.603C178.887,57.603 179.416,59.582 180.849,60.628C181.324,60.958 181.865,61.18 182.435,61.28C182.903,61.364 183.292,61.691 183.454,62.139C183.617,62.587 183.529,63.087 183.224,63.453C182.4,64.439 181.292,65.145 180.051,65.474C176.785,66.304 173.104,64.416 173.042,62.066C172.979,59.715 173.557,56.526 173.557,56.526L178.887,57.603Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M148.607,49.327C149.989,49.328 151.269,50.053 151.982,51.239C152.607,49.537 154.282,48.456 156.088,48.59C157.893,48.725 159.391,50.042 159.758,51.818C161.846,50.992 164.228,51.623 165.636,53.375C167.044,55.127 167.15,57.593 165.898,59.46C164.647,61.328 162.328,62.162 160.176,61.519C158.025,60.877 156.541,58.906 156.514,56.657C156.274,56.701 156.031,56.723 155.788,56.724C154.349,56.726 153.017,55.964 152.288,54.721C151.566,56.578 149.576,57.606 147.647,57.118C145.718,56.629 144.454,54.778 144.7,52.799C144.946,50.821 146.624,49.336 148.614,49.336L148.607,49.327Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M155.781,56.724C158.023,56.724 159.84,54.903 159.84,52.658C159.84,50.411 158.023,48.591 155.781,48.591C153.539,48.591 151.722,50.411 151.722,52.658C151.722,54.903 153.539,56.724 155.781,56.724Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M161.635,61.736C164.469,61.736 166.765,59.436 166.765,56.597C166.765,53.759 164.469,51.458 161.635,51.458C158.802,51.458 156.506,53.759 156.506,56.597C156.506,59.436 158.802,61.736 161.635,61.736Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M162.804,66.925C163.732,66.925 164.483,66.172 164.483,65.242C164.483,64.313 163.732,63.56 162.804,63.56C161.876,63.56 161.124,64.313 161.124,65.242C161.124,66.172 161.876,66.925 162.804,66.925Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M144.512,61.632C144.512,62.232 144.998,62.718 145.596,62.718C146.195,62.718 146.681,62.232 146.681,61.632"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M145.596,65.505C145.596,66.173 146.427,66.709 147.452,66.709C148.476,66.709 149.307,66.17 149.307,65.505"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M148.442,61.632C148.442,62.232 148.928,62.718 149.527,62.718C150.125,62.718 150.611,62.232 150.611,61.632"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M163.55,65.007C163.193,64.864 162.787,65.038 162.644,65.396"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M148.774,58.865C149.019,59.489 149.613,59.905 150.282,59.92"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M145.459,59.943C146.127,59.907 146.709,59.474 146.936,58.843"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M151.486,69.797C153.229,69.797 154.641,68.381 154.641,66.636"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M162.344,64.058C162.843,60.097 161.106,56.186 157.834,53.907C154.561,51.627 150.297,51.358 146.764,53.207C143.232,55.055 141.017,58.717 141.016,62.709C141.016,63.005 141.027,63.299 141.051,63.589"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M149.918,66.62C150.328,66.62 150.66,66.287 150.66,65.877C150.66,65.466 150.328,65.133 149.918,65.133"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M143.206,59.306C144.703,59.306 145.916,58.09 145.916,56.59C145.916,55.091 144.703,53.875 143.206,53.875C141.709,53.875 140.495,55.091 140.495,56.59C140.495,58.09 141.709,59.306 143.206,59.306Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M141.016,63.099C142.63,63.099 143.939,61.788 143.939,60.17C143.939,58.552 142.63,57.241 141.016,57.241C139.401,57.241 138.092,58.552 138.092,60.17C138.092,61.788 139.401,63.099 141.016,63.099Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M161.92,64.092C163.744,64.092 165.223,62.61 165.223,60.782C165.223,58.954 163.744,57.472 161.92,57.472C160.095,57.472 158.616,58.954 158.616,60.782C158.616,62.61 160.095,64.092 161.92,64.092Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M161.92,64.092C163.744,64.092 165.223,62.61 165.223,60.782C165.223,58.954 163.744,57.472 161.92,57.472C160.095,57.472 158.616,58.954 158.616,60.782C158.616,62.61 160.095,64.092 161.92,64.092Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M160.953,69.349C161.439,70.298 162.541,70.753 163.553,70.422C164.565,70.09 165.187,69.071 165.02,68.018C164.852,66.965 163.946,66.189 162.881,66.188"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M145.736,56.232C146.806,57.404 148.484,57.797 149.961,57.223C151.438,56.649 152.412,55.224 152.413,53.637C152.413,53.543 152.413,53.447 152.402,53.354"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#CB765E"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M158.974,59.642C158.265,60.837 158.376,62.348 159.251,63.426"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#CB765E"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M151.991,51.947C154.782,51.947 157.045,49.68 157.045,46.884C157.045,44.087 154.782,41.82 151.991,41.82C149.199,41.82 146.936,44.087 146.936,46.884C146.936,49.68 149.199,51.947 151.991,51.947Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M145.217,51.992C147,51.992 148.445,50.544 148.445,48.758C148.445,46.971 147,45.523 145.217,45.523C143.434,45.523 141.989,46.971 141.989,48.758C141.989,50.544 143.434,51.992 145.217,51.992Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M142.246,56.789C144.02,56.789 145.459,55.348 145.459,53.57C145.459,51.793 144.02,50.352 142.246,50.352C140.472,50.352 139.034,51.793 139.034,53.57C139.034,55.348 140.472,56.789 142.246,56.789Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M140.193,59.272C141.326,59.272 142.244,58.353 142.244,57.218C142.244,56.083 141.326,55.164 140.193,55.164C139.061,55.164 138.143,56.083 138.143,57.218C138.143,58.353 139.061,59.272 140.193,59.272Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M142.735,51.834C143.512,51.834 144.142,51.203 144.142,50.425C144.142,49.646 143.512,49.016 142.735,49.016C141.959,49.016 141.329,49.646 141.329,50.425C141.329,51.203 141.959,51.834 142.735,51.834Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M158.138,52.09C160.069,52.09 161.635,50.521 161.635,48.586C161.635,46.652 160.069,45.083 158.138,45.083C156.207,45.083 154.641,46.652 154.641,48.586C154.641,50.521 156.207,52.09 158.138,52.09Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M161.878,53.354C163.003,53.354 163.915,52.44 163.915,51.313C163.915,50.186 163.003,49.272 161.878,49.272C160.752,49.272 159.84,50.186 159.84,51.313C159.84,52.44 160.752,53.354 161.878,53.354Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M164.808,45.504C166.936,45.504 168.662,43.775 168.662,41.642C168.662,39.509 166.936,37.78 164.808,37.78C162.679,37.78 160.953,39.509 160.953,41.642C160.953,43.775 162.679,45.504 164.808,45.504Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M170.973,43.861C173.489,43.861 175.528,41.818 175.528,39.298C175.528,36.778 173.489,34.735 170.973,34.735C168.458,34.735 166.419,36.778 166.419,39.298C166.419,41.818 168.458,43.861 170.973,43.861Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M177.527,48.359C179.92,48.359 181.86,46.416 181.86,44.019C181.86,41.621 179.92,39.678 177.527,39.678C175.134,39.678 173.195,41.621 173.195,44.019C173.195,46.416 175.134,48.359 177.527,48.359Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M176.883,41.085C178.12,41.085 179.122,40.081 179.122,38.841C179.122,37.602 178.12,36.598 176.883,36.598C175.646,36.598 174.643,37.602 174.643,38.841C174.643,40.081 175.646,41.085 176.883,41.085Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M163.55,52.758C166.387,52.758 168.687,50.454 168.687,47.611C168.687,44.769 166.387,42.465 163.55,42.465C160.713,42.465 158.413,44.769 158.413,47.611C158.413,50.454 160.713,52.758 163.55,52.758Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M176.581,54.293C178.883,54.293 180.749,52.424 180.749,50.118C180.749,47.811 178.883,45.942 176.581,45.942C174.279,45.942 172.413,47.811 172.413,50.118C172.413,52.424 174.279,54.293 176.581,54.293Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M170.711,48.963C172.882,48.963 174.641,47.2 174.641,45.025C174.641,42.85 172.882,41.087 170.711,41.087C168.54,41.087 166.781,42.85 166.781,45.025C166.781,47.2 168.54,48.963 170.711,48.963Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M170.926,60.063C173.467,60.063 175.527,57.999 175.527,55.453C175.527,52.907 173.467,50.843 170.926,50.843C168.385,50.843 166.325,52.907 166.325,55.453C166.325,57.999 168.385,60.063 170.926,60.063Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M172.928,52.51C174.364,52.51 175.528,51.344 175.528,49.906C175.528,48.468 174.364,47.302 172.928,47.302C171.493,47.302 170.329,48.468 170.329,49.906C170.329,51.344 171.493,52.51 172.928,52.51Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M177.629,57.893C179.352,57.893 180.749,56.494 180.749,54.768C180.749,53.042 179.352,51.643 177.629,51.643C175.907,51.643 174.51,53.042 174.51,54.768C174.51,56.494 175.907,57.893 177.629,57.893Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M173.455,64.23C175.181,64.23 176.581,62.828 176.581,61.098C176.581,59.368 175.181,57.966 173.455,57.966C171.728,57.966 170.329,59.368 170.329,61.098C170.329,62.828 171.728,64.23 173.455,64.23Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M177.185,60.127C178.662,60.127 179.86,58.928 179.86,57.447C179.86,55.967 178.662,54.768 177.185,54.768C175.708,54.768 174.51,55.967 174.51,57.447C174.51,58.928 175.708,60.127 177.185,60.127Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M168.667,55.42C171.02,55.42 172.928,53.508 172.928,51.151C172.928,48.793 171.02,46.881 168.667,46.881C166.314,46.881 164.406,48.793 164.406,51.151C164.406,53.508 166.314,55.42 168.667,55.42Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M143.077,97.731L145.752,108.48"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#262944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M156.748,167.207L158.332,130.705"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M155.874,128.083L158.329,130.705L161.217,128.775"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M168.978,157.733L165.248,171.957"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M169.082,114.55C168.192,117 165.868,118.631 163.266,118.632H153.11"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#818181"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M167.03,95.147L172.209,102.408"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M179.26,110.55C177.257,110.27 175.858,108.417 176.134,106.41L180.7,89.765L186.019,88.509L183.391,107.429C183.107,109.432 181.26,110.827 179.26,110.55Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M179.44,94.366L177.332,102.052"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M186.264,77.276C186.831,77.257 187.306,77.702 187.325,78.269L187.504,83.654C187.522,84.222 187.078,84.697 186.511,84.715C185.943,84.734 185.468,84.29 185.45,83.723L185.271,78.337C185.252,77.77 185.697,77.295 186.264,77.276Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M186.264,77.276C186.831,77.257 187.306,77.702 187.325,78.269L187.504,83.654C187.522,84.222 187.078,84.697 186.511,84.715V84.715C185.943,84.734 185.468,84.29 185.45,83.723L185.271,78.337C185.252,77.77 185.697,77.295 186.264,77.276V77.276Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M185.465,80.752L183.388,79.245C181.897,78.159 180.839,76.577 180.404,74.782C180.318,74.293 179.967,73.892 179.494,73.742C179.021,73.592 178.504,73.717 178.152,74.068C177.8,74.418 177.672,74.935 177.818,75.41C178.398,77.82 179.816,79.945 181.817,81.404L183.895,82.913C184.491,83.291 185.277,83.138 185.689,82.565C186.101,81.992 185.996,81.196 185.45,80.75L185.465,80.752Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M185.465,80.752L183.388,79.245C181.897,78.159 180.839,76.577 180.404,74.782C180.318,74.293 179.967,73.892 179.494,73.742C179.021,73.592 178.504,73.717 178.152,74.068C177.8,74.418 177.672,74.935 177.818,75.41C178.398,77.82 179.816,79.945 181.817,81.404L183.895,82.913C184.122,83.078 184.394,83.167 184.674,83.167"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M184.034,82.087C183.698,82.782 182.861,83.072 182.166,82.734L176.218,79.842C175.525,79.505 175.236,78.67 175.572,77.976C175.909,77.281 176.746,76.991 177.441,77.329L183.388,80.221C184.081,80.558 184.371,81.393 184.034,82.087Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M183.388,80.222L177.436,77.329C177.104,77.166 176.72,77.143 176.371,77.264C176.021,77.385 175.734,77.641 175.572,77.974V77.974"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M184.041,82.775C183.889,83.532 183.152,84.023 182.395,83.871L175.914,82.569C175.158,82.417 174.668,81.681 174.819,80.924C174.971,80.167 175.708,79.676 176.465,79.828L182.945,81.13C183.702,81.282 184.192,82.019 184.041,82.775Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M187.2,85.198C186.597,86.441 185.101,86.96 183.858,86.356L182.35,85.623C181.111,85.02 180.593,83.527 181.194,82.286C181.797,81.043 183.293,80.524 184.536,81.128L186.044,81.862C187.283,82.464 187.801,83.957 187.2,85.198Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M174.83,80.913V80.913C174.678,81.669 175.168,82.405 175.923,82.558L182.408,83.868"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.545,80.031L182.533,81.149"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M183.863,86.361C185.103,86.962 186.593,86.444 187.196,85.204V85.204C187.651,84.263 187.474,83.138 186.752,82.384"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M181.244,109.085C181.136,109.534 180.797,109.892 180.355,110.023C179.912,110.154 179.434,110.038 179.099,109.72C178.765,109.402 178.625,108.929 178.733,108.479"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M167.03,110.262L169.621,108.479"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M168.324,111.155L169.62,110.583"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M143.077,151.911H144.415"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M163.914,160.347H166.305"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M164.406,190.264C164.31,190.64 164.069,190.963 163.735,191.16C163.401,191.357 163.003,191.413 162.628,191.315L158.085,190.142C157.156,189.901 156.507,189.062 156.508,188.101V187.907L164.415,190.233L164.406,190.264Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M155.639,176.898L161.971,176.095"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M178.289,184.228L177.652,187.019"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M159.569,188.8L162.23,189.581"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M157.354,178.877L156.748,185.332"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M178.783,179.979L180.029,181.403"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M179.407,193.659L182.577,187.898"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M160.9,194.291H166.692"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M183.644,185.748C184.189,186.06 184.883,185.873 185.199,185.33L186.279,183.498L184.008,182.607L183.644,185.748Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M155.794,192.328L155.305,195.377H157.123C157.928,195.377 158.58,194.723 158.58,193.917V193.516L155.794,192.328Z"
      android:fillColor="#161E24"/>
  <path
      android:pathData="M157.112,71.936C161.956,69.028 163.698,62.842 161.086,57.827C158.473,52.812 152.412,50.706 147.261,53.025C142.11,55.343 139.657,61.282 141.665,66.57C143.673,71.857 149.446,74.66 154.833,72.962"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M133.173,125.75L141.071,82.146C141.293,80.912 142.182,79.942 143.379,79.448C143.839,79.259 143.792,79.116 143.295,79.243C141.788,79.628 140.629,80.705 140.364,82.137L132.602,125.187L133.173,125.75Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M132.588,125.283L140.231,82.101C140.495,80.601 141.755,79.49 143.355,79.149"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M180.04,90.844L184.614,89.6"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#262944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M136.627,131.697C137.05,132.075 137.088,132.724 136.712,133.148L133.141,137.173C132.764,137.598 132.113,137.637 131.688,137.258C131.265,136.881 131.226,136.232 131.603,135.808L135.173,131.783C135.551,131.358 136.202,131.319 136.627,131.697Z"
      android:fillColor="#CB765E"/>
  <path
      android:pathData="M133.939,125.283L139.276,128.909"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#262944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M134.388,132.666L132.78,134.478"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M150.213,99.63C147.91,99.628 145.96,97.925 145.645,95.639C145.614,95.842 145.598,96.047 145.596,96.253C145.595,98.425 147.191,100.265 149.337,100.568C151.484,100.872 153.525,99.545 154.123,97.457C153.282,98.809 151.804,99.63 150.213,99.63Z"
      android:fillColor="#C7C7C7"/>
  <path
      android:pathData="M161.155,99.63C163.459,99.628 165.409,97.925 165.725,95.639C165.756,95.842 165.773,96.047 165.774,96.253C165.764,98.417 164.17,100.245 162.031,100.547C159.893,100.85 157.857,99.534 157.249,97.457C158.091,98.807 159.567,99.628 161.155,99.63Z"
      android:fillColor="#C7C7C7"/>
  <path
      android:pathData="M172.413,176.495L174.746,172.72"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M168.358,88.097L171.127,85.794C171.203,85.731 171.272,85.661 171.333,85.584C171.765,85.045 171.759,84.276 171.318,83.744C171.071,83.447 170.717,83.26 170.334,83.224C169.95,83.189 169.568,83.308 169.271,83.554L166.51,85.871C166.213,86.118 166.026,86.472 165.99,86.857C165.955,87.241 166.073,87.624 166.319,87.922C166.836,88.53 167.745,88.609 168.358,88.097Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M164.338,85.894C163.827,85.276 163.912,84.361 164.529,83.848L167.298,81.546C167.916,81.031 168.835,81.117 169.348,81.736C169.86,82.354 169.774,83.27 169.157,83.783L166.389,86.085C165.77,86.599 164.852,86.514 164.338,85.894Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M162.586,83.774C162.074,83.156 162.159,82.24 162.776,81.727L165.545,79.425C166.163,78.911 167.082,78.996 167.595,79.616C168.107,80.233 168.022,81.149 167.405,81.662L164.636,83.964C164.017,84.479 163.099,84.393 162.586,83.774Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M162.654,79.005C162.459,78.225 162.933,77.436 163.712,77.24L167.199,76.367C167.979,76.171 168.77,76.646 168.965,77.426C169.16,78.206 168.686,78.995 167.906,79.191L164.419,80.064C163.639,80.26 162.848,79.785 162.654,79.005Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M159.656,81.729C159.181,81.082 159.318,80.173 159.964,79.696L162.86,77.558C163.507,77.08 164.419,77.218 164.896,77.866C165.371,78.513 165.234,79.422 164.588,79.899L161.692,82.037C161.045,82.515 160.133,82.377 159.656,81.729Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M165.556,79.695C164.948,78.724 165.24,77.443 166.21,76.833C167.182,76.221 168.466,76.515 169.076,77.488L172.741,83.334C173.35,84.305 173.057,85.585 172.087,86.196C171.115,86.807 169.831,86.514 169.221,85.541L165.556,79.695Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M164.091,80.626L162.776,81.718C162.479,81.965 162.292,82.319 162.256,82.704C162.22,83.088 162.338,83.471 162.584,83.768V83.768C162.83,84.066 163.184,84.253 163.568,84.289C163.952,84.325 164.334,84.206 164.631,83.96L167.399,81.656"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M164.339,85.887V85.887C164.586,86.185 164.94,86.371 165.323,86.407C165.707,86.442 166.089,86.324 166.386,86.077L168.91,83.869"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M71.101,195.348H81.141"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M74.771,194.892C74.771,194.522 71.883,186.296 73.735,185.735C75.588,185.174 76.122,194.892 76.122,194.892C76.122,194.892 76.402,189.374 77.659,189.151C78.917,188.929 77.141,193.919 77.141,193.919"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M72.105,191.402L73.514,193.334"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M84.6,195.348H150.716"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M141.88,195.021C141.658,194.523 138.845,188.586 140.401,187.4C141.956,186.213 142.622,192.853 142.622,192.853C142.622,192.853 142.547,189.196 143.584,189.292C144.622,189.387 144.029,194.431 144.029,194.431C144.029,194.431 144.473,192.239 145.288,192.339C146.104,192.439 145.288,195.01 145.288,195.01"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M154.657,195.348H196.703"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M203.239,195.348H208.72"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M192.131,193.211L192.908,195.266L193.464,193.852"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M198.327,83.537C198.842,83.537 199.26,83.119 199.26,82.602C199.26,82.086 198.842,81.668 198.327,81.668C197.811,81.668 197.394,82.086 197.394,82.602C197.394,83.119 197.811,83.537 198.327,83.537Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M206.156,80.581C206.496,80.581 206.771,80.305 206.771,79.965C206.771,79.624 206.496,79.348 206.156,79.348C205.816,79.348 205.541,79.624 205.541,79.965C205.541,80.305 205.816,80.581 206.156,80.581Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M190.226,44.478C190.566,44.478 190.842,44.202 190.842,43.861C190.842,43.521 190.566,43.245 190.226,43.245C189.887,43.245 189.611,43.521 189.611,43.861C189.611,44.202 189.887,44.478 190.226,44.478Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M195.346,75.671H194.668V74.992C194.668,74.869 194.569,74.77 194.446,74.77C194.323,74.77 194.224,74.869 194.224,74.992V75.671H193.557C193.434,75.671 193.335,75.771 193.335,75.894C193.335,76.017 193.434,76.116 193.557,76.116H194.224V76.784C194.224,76.907 194.323,77.007 194.446,77.007C194.569,77.007 194.668,76.907 194.668,76.784V76.116H195.346C195.468,76.116 195.568,76.017 195.568,75.894C195.568,75.771 195.468,75.671 195.346,75.671Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M140.618,34.428H139.951V33.76C139.951,33.637 139.852,33.538 139.729,33.538C139.606,33.538 139.507,33.637 139.507,33.76V34.428H138.829C138.707,34.428 138.607,34.528 138.607,34.651C138.607,34.773 138.707,34.873 138.829,34.873H139.507V35.541C139.507,35.664 139.606,35.764 139.729,35.764C139.852,35.764 139.951,35.664 139.951,35.541V34.873H140.618C140.74,34.873 140.84,34.773 140.84,34.651C140.84,34.528 140.74,34.428 140.618,34.428Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M162.017,23.964C162.038,23.42 161.76,22.907 161.293,22.629C160.825,22.35 160.243,22.35 159.776,22.629C159.308,22.907 159.03,23.42 159.051,23.964V23.964C159.051,23.143 158.387,22.477 157.567,22.477C156.748,22.477 156.083,23.143 156.083,23.964C156.083,24.583 156.941,25.367 156.941,25.367L159.051,27.479L161.16,25.367C161.16,25.367 162.017,24.583 162.017,23.964Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M161.471,21.952V21.026"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M163.155,21.625L162.502,22.282"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M164.326,20.697L163.997,21.026"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M162.693,23.519H163.619"
      android:strokeLineJoin="round"
      android:strokeWidth="0.547486"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M83.658,42.564H82.769V41.673C82.769,41.511 82.637,41.379 82.476,41.379C82.314,41.379 82.182,41.511 82.182,41.673V42.564H81.294C81.132,42.564 81,42.695 81,42.857C81,43.02 81.132,43.151 81.294,43.151H82.182V44.042C82.182,44.204 82.314,44.335 82.476,44.335C82.637,44.335 82.769,44.204 82.769,44.042V43.151H83.658C83.82,43.151 83.951,43.02 83.951,42.857C83.951,42.695 83.82,42.564 83.658,42.564Z"
      android:fillColor="#529ED6"/>
  <path
      android:pathData="M78.057,36.752C78.401,36.752 78.679,36.473 78.679,36.129C78.679,35.784 78.401,35.505 78.057,35.505C77.714,35.505 77.435,35.784 77.435,36.129C77.435,36.473 77.714,36.752 78.057,36.752Z"
      android:fillColor="#529ED6"/>
  <path
      android:pathData="M84.138,36.667C84.59,36.667 84.957,36.299 84.957,35.846C84.957,35.392 84.59,35.024 84.138,35.024C83.685,35.024 83.318,35.392 83.318,35.846C83.318,36.299 83.685,36.667 84.138,36.667Z"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#E4F4FC"/>
  <path
      android:pathData="M68.175,85.173C68.515,85.173 68.79,84.897 68.79,84.557C68.79,84.216 68.515,83.94 68.175,83.94C67.835,83.94 67.559,84.216 67.559,84.557C67.559,84.897 67.835,85.173 68.175,85.173Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.735821"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
</vector>
