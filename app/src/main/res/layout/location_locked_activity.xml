<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:focusable="true"
    android:background="@color/white">

    <ImageView
        android:id="@+id/icon_empty_magnifying_glass_image_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="24dp"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_location_locked"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/waitlist_text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="18dp"
        android:fontFamily="@font/nunitosans_regular"
        android:gravity="center"
        android:lineSpacingMultiplier="0.97"
        android:text="@string/you_are_on_waitlist"
        android:textColor="@color/grey2"
        android:textSize="16sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/icon_empty_magnifying_glass_image_view" />

    <ImageView
        android:id="@+id/location_pin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_location_love_heart_pin"
        app:layout_constraintEnd_toStartOf="@+id/title_text_view"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/waitlist_text_view" />

    <TextView
        android:id="@+id/title_text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:fontFamily="@font/font_nunitosans_extrabold"
        android:gravity="center"
        android:lineSpacingMultiplier="0.97"
        android:text="San Francisco"
        android:textColor="@color/grey1"
        android:textSize="24sp"
        android:layout_marginStart="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/location_pin"
        app:layout_constraintTop_toBottomOf="@+id/waitlist_text_view" />

    <TextView
        android:id="@+id/desc_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="24dp"
        android:fontFamily="@font/font_nunitosans_extrabold"
        android:gravity="center"
        android:lineSpacingMultiplier="0.97"
        android:text="@string/location_locked_activity_dateup_is_not_yet_li_text_view_text"
        android:textColor="@color/grey1"
        android:textSize="14sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title_text_view" />

    <TextView
        android:id="@+id/browse_unlocked_city_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:layout_marginTop="32dp"
        android:gravity="center"
        android:fontFamily="@font/font_nunitosans_regular"
        android:textSize="16sp"
        android:textColor="@color/grey2"
        android:visibility="gone"
        android:text="Browse in an unlocked city:"
        app:layout_constraintTop_toBottomOf="@+id/desc_text_view"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/sub_desc_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:fontFamily="@font/font_nunitosans_regular"
        android:gravity="center"
        android:lineSpacingMultiplier="0.97"
        android:text="@string/location_locked_activity_we_ll_let_you_know_atext_view_text"
        android:textColor="@color/grey2"
        android:textSize="14sp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintVertical_bias="1"
        app:layout_constraintTop_toBottomOf="@+id/desc_text_view"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/share_with_friends" />

    <android.widget.Button
        android:id="@+id/share_with_friends"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="0dp"
        android:layout_height="@dimen/enable_location_activity_button_large_active_button_height"
        android:layout_marginStart="24dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="24dp"
        android:layout_marginBottom="16dp"
        app:layout_goneMarginBottom="48dp"
        android:background="@drawable/enable_location_activity_button_large_active_button_selector"
        android:text="Share with Friends"
        android:visibility="gone"
        android:textSize="16sp"
        android:textColor="@color/enable_location_activity_button_large_active_button_text_color"
        android:theme="@style/BottomCTAButton"
        app:layout_constraintVertical_bias="1"
        app:layout_constraintBottom_toTopOf="@+id/brand_ambassador"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/brand_ambassador"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="48dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="48dp"
        android:layout_marginBottom="48dp"
        android:fontFamily="@font/font_nunitosans_bold"
        android:gravity="center"
        android:visibility="gone"
        android:lineSpacingMultiplier="1.1"
        android:text="Become a Brand Ambassador"
        android:textColor="@color/code_verification_activity_i_didn_tget_acode_text_view_text_color"
        android:textSize="@dimen/code_verification_activity_i_didn_tget_acode_text_view_text_size"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>