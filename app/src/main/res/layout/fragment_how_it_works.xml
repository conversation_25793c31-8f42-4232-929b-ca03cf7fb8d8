<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/welcome_activity_welcome_constraint_layout_background_color">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header_normal_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="@dimen/settings_settings_supernova_activity_header_normal_constraint_layout_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/account_header_left_image_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/preferences_men_secopy2supernova_activity_tail_left_image_view_margin_start"
            android:layout_marginTop="4dp"
            android:background="@null"
            android:elevation="2dp"
            android:padding="10dp"
            android:scaleType="center"
            android:src="@drawable/ic_icon_arrow_left"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/welcome_to_dateup_text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="72dp"
        android:fontFamily="@font/font_nunitosans_extrabold"
        android:gravity="start"
        android:text="@string/how_it_works"
        android:textColor="@color/welcome_activity_welcome_to_dateup_text_view_text_color"
        android:textSize="32sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/dateup_is_acommunit_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:layout_marginStart="16dp"
        app:layout_constraintHorizontal_bias="0"
        android:layout_marginEnd="24dp"
        android:fontFamily="@font/font_nunitosans_bold"
        android:gravity="start"
        android:text="@string/how_it_works_point_1"
        android:textColor="@color/grey1"
        android:textSize="18sp"
        app:layout_constraintStart_toEndOf="@+id/group8_image_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/welcome_to_dateup_text_view" />

    <TextView
        android:id="@+id/how_it_work_1_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginStart="16dp"
        app:layout_constraintHorizontal_bias="0"
        android:layout_marginEnd="24dp"
        android:fontFamily="@font/font_nunitosans_regular"
        android:gravity="start"
        android:text="@string/how_it_works_point_1_desc"
        android:textColor="@color/grey2"
        android:textSize="18sp"
        app:layout_constraintStart_toEndOf="@+id/group8_image_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dateup_is_acommunit_text_view" />

    <TextView
        android:id="@+id/tall_people_on_dateu_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        app:layout_constraintHorizontal_bias="0"
        android:layout_marginEnd="24dp"
        android:layout_marginTop="@dimen/welcome_activity_tall_people_on_dateu_text_view_margin_top"
        android:fontFamily="@font/font_nunitosans_bold"
        android:gravity="start"
        android:textColor="@color/grey1"
        android:textSize="18sp"
        android:text="@string/how_it_works_point_2"
        app:layout_constraintStart_toEndOf="@+id/group7_image_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/how_it_work_1_desc" />

    <TextView
        android:id="@+id/how_it_work_2_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginStart="16dp"
        app:layout_constraintHorizontal_bias="0"
        android:layout_marginEnd="24dp"
        android:fontFamily="@font/font_nunitosans_regular"
        android:gravity="start"
        android:text="@string/how_it_works_point_2_desc"
        android:textColor="@color/grey2"
        android:textSize="18sp"
        app:layout_constraintStart_toEndOf="@+id/group7_image_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tall_people_on_dateu_text_view" />

    <ImageView
        android:id="@+id/group8_image_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginTop="4dp"
        android:scaleType="center"
        android:src="@drawable/ic_icon_number_1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/dateup_is_acommunit_text_view" />

    <ImageView
        android:id="@+id/group7_image_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:scaleType="center"
        android:layout_marginTop="4dp"
        android:src="@drawable/ic_icon_number_2"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tall_people_on_dateu_text_view" />

    <ImageView
        android:id="@+id/number3_image_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:scaleType="center"
        android:layout_marginTop="4dp"
        android:src="@drawable/ic_icon_number3"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/third_point" />

    <TextView
        android:id="@+id/third_point"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        app:layout_constraintHorizontal_bias="0"
        android:layout_marginEnd="24dp"
        android:layout_marginTop="@dimen/welcome_activity_tall_people_on_dateu_text_view_margin_top"
        android:fontFamily="@font/font_nunitosans_bold"
        android:gravity="start"
        android:text="@string/how_it_works_point_3"
        android:textColor="@color/grey1"
        android:textSize="18sp"
        app:layout_constraintStart_toEndOf="@+id/group8_image_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/how_it_work_2_desc" />

    <TextView
        android:id="@+id/how_it_work_3_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginStart="16dp"
        app:layout_constraintHorizontal_bias="0"
        android:layout_marginEnd="24dp"
        android:fontFamily="@font/font_nunitosans_regular"
        android:gravity="start"
        android:text="@string/how_it_works_point_3_desc"
        android:textColor="@color/grey2"
        android:textSize="18sp"
        app:layout_constraintStart_toEndOf="@+id/group8_image_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/third_point" />

</androidx.constraintlayout.widget.ConstraintLayout>