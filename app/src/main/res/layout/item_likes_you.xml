<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/userProfilePhoto"
        android:layout_width="match_parent"
        android:layout_height="156dp"
        android:scaleType="centerCrop"
        android:background="@drawable/rounded_outline"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/userName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:fontFamily="@font/font_nunitosans_bold"
        android:textAlignment="center"
        android:textColor="@color/grey1"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="@id/userProfilePhoto"
        app:layout_constraintStart_toStartOf="@id/userProfilePhoto"
        app:layout_constraintTop_toBottomOf="@+id/userProfilePhoto" />

    <TextView
        android:id="@+id/userHeight"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:fontFamily="@font/font_nunitosans_regular"
        android:gravity="center"
        android:textAlignment="center"
        android:textColor="@color/grey2"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="@id/userProfilePhoto"
        app:layout_constraintStart_toStartOf="@id/userName"
        app:layout_constraintTop_toBottomOf="@id/userName" />

</androidx.constraintlayout.widget.ConstraintLayout>