<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
	android:layout_height="match_parent">

	<TextView
		android:id="@+id/should_we_show_you_gtext_view"
		android:layout_width="0dp"
		android:layout_height="wrap_content"
		android:layout_marginStart="24dp"
		android:layout_marginTop="24dp"
		android:layout_marginEnd="24dp"
		android:fontFamily="@font/font_nunitosans_regular"
		android:gravity="center"
		android:lineSpacingMultiplier="0.96"
		android:textColor="@color/guest_matchmaking2_activity_should_we_show_you_gtext_view_text_color"
		android:textSize="18sp"
		app:layout_constraintBottom_toTopOf="@+id/guests_are_men_unde_text_view"
		app:layout_constraintEnd_toEndOf="parent"
		app:layout_constraintStart_toStartOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/guests_match_making_image_view"
		app:layout_constraintVertical_bias="0"
		tools:text="@string/guest_matchmaking2_activity_should_we_show_you_gtext_view_text" />

	<android.widget.Button
		android:id="@+id/button_large_active_button"
		style="?android:attr/borderlessButtonStyle"
		android:layout_width="0dp"
		android:layout_height="@dimen/guest_matchmaking2_activity_button_large_active_button_height"
		android:layout_marginStart="24dp"
		android:layout_marginEnd="24dp"
		android:layout_marginBottom="24dp"
		android:background="@drawable/guest_matchmaking2_activity_button_large_active_button_ripple"
		android:text="@string/guest_matchmaking2_activity_button_large_active_button_text"
		android:textColor="@color/guest_matchmaking2_activity_button_large_active_button_text_color"
		android:textSize="16sp"
		android:theme="@style/BottomCTAButton"
		app:layout_constraintBottom_toTopOf="@+id/group7_constraint_layout"
		app:layout_constraintEnd_toEndOf="parent"
		app:layout_constraintStart_toStartOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/button_large_active_two_button" />

	<android.widget.Button
		android:id="@+id/button_large_active_two_button"
		style="?android:attr/borderlessButtonStyle"
		android:layout_width="0dp"
		android:layout_height="@dimen/guest_matchmaking2_activity_button_large_active_two_button_height"
		android:layout_marginStart="24dp"
		android:layout_marginEnd="24dp"
		android:layout_marginBottom="12dp"
		android:background="@drawable/guest_matchmaking2_activity_button_large_active_two_button_ripple"
		android:text="@string/guest_matchmaking2_activity_button_large_active_two_button_text"
		android:textColor="@color/guest_matchmaking2_activity_button_large_active_two_button_text_color"
		android:textSize="16sp"
		android:theme="@style/BottomCTAButton"
		app:layout_constraintBottom_toTopOf="@+id/button_large_active_button"
		app:layout_constraintEnd_toEndOf="parent"
		app:layout_constraintStart_toStartOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/guests_are_men_unde_text_view" />

	<ImageView
		android:id="@+id/guests_match_making_image_view"
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:layout_marginTop="@dimen/toolbar_height"
		android:layout_marginBottom="12dp"
		android:scaleType="centerCrop"
		android:src="@drawable/ic_tall_man_with_short_women"
		app:layout_constraintBottom_toTopOf="@+id/should_we_show_you_gtext_view"
		app:layout_constraintEnd_toEndOf="parent"
		app:layout_constraintStart_toStartOf="parent"
		app:layout_constraintTop_toTopOf="parent"
		app:layout_constraintVertical_bias="1.0" />

	<TextView
		android:id="@+id/guests_are_men_unde_text_view"
		android:layout_width="0dp"
		android:layout_height="wrap_content"
		android:layout_marginStart="24dp"
		android:layout_marginTop="45dp"
		android:layout_marginEnd="24dp"
		android:layout_marginBottom="12dp"
		android:fontFamily="@font/font_nunitosans_regular"
		android:gravity="center"
		android:textStyle="italic"
		android:lineSpacingMultiplier="1.18"
		android:text="@string/guest_matchmaking2_activity_guests_are_men_unde_text_view_text"
		android:textColor="@color/grey2"
		android:textSize="12sp"
		app:layout_constraintBottom_toTopOf="@+id/button_large_active_two_button"
		app:layout_constraintEnd_toEndOf="parent"
		app:layout_constraintStart_toStartOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/should_we_show_you_gtext_view" />

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group7_constraint_layout"
		android:layout_width="@dimen/lottie_imageview_width"
		android:layout_height="@dimen/lottie_imageview_height"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent">

		<com.airbnb.lottie.LottieAnimationView
            android:layout_width="match_parent"
			android:layout_height="match_parent"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintEnd_toEndOf="parent"
			app:layout_constraintStart_toStartOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			app:lottie_autoPlay="true"
			app:lottie_loop="true"
			app:lottie_rawRes="@raw/clouds" />
	</androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>