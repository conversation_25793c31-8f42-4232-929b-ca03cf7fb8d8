<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".heightVerification.ui.HeightVerificationScan">

    <TextView
        android:id="@+id/cancel_text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="16dp"
        android:padding="10dp"
        android:text="@string/cancel"
        android:textColor="#646C70"
        android:textSize="16sp"
        app:layout_constraintBottom_toTopOf="@+id/image_view_height_bar_code"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        app:layout_constraintVertical_chainStyle="spread_inside" />

    <ImageView
        android:id="@+id/image_view_height_bar_code"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_licence_scan"
        app:layout_constraintBottom_toTopOf="@+id/verify_height"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cancel_text_view" />

    <TextView
        android:id="@+id/verify_height"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="18dp"
        android:fontFamily="@font/font_nunitosans_bold"
        android:text="@string/verify_height"
        android:textColor="@color/black"
        android:textSize="20sp"
        app:layout_constraintBottom_toTopOf="@+id/text_view_scan"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/image_view_height_bar_code" />

    <TextView
        android:id="@+id/text_view_scan"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="24dp"
        android:layout_marginTop="12dp"
        android:layout_marginRight="24dp"
        android:fontFamily="@font/font_nunitosans_regular"
        android:gravity="center"
        android:lineSpacingMultiplier="1.09"
        android:text="@string/height_verification_scan_desc"
        android:textColor="@color/enable_location_activity_enable_your_location_text_view_text_color"
        android:textSize="@dimen/enable_location_activity_enable_your_location_text_view_text_size"
        app:layout_constrainedHeight="true"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/verify_height" />

    <android.widget.Button
        android:id="@+id/button_scan"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="0dp"
        android:layout_height="@dimen/hometown_activity_button_large_active_button_height"
        android:layout_marginStart="24dp"
        android:layout_marginTop="32dp"
        android:layout_marginEnd="24dp"
        android:layout_marginBottom="56dp"
        android:background="@drawable/phone_number_activity_button_large_active_button_selector"
        android:text="@string/scan_now"
        android:textColor="@color/hometown_activity_button_large_active_button_text_color"
        android:theme="@style/BottomCTAButton"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/text_view_scan"
        app:layout_constraintVertical_bias="1.0" />

    <include
        android:id="@+id/layout_enable_camera"
        layout="@layout/fragment_blank_camera_permission"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cancel_text_view" />

</androidx.constraintlayout.widget.ConstraintLayout>