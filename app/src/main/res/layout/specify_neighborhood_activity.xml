<androidx.constraintlayout.widget.ConstraintLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/specify_neighborhood_activity_specify_neighborhood_constraint_layout_background_color">

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/button_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="@dimen/specify_neighborhood_activity_button_constraint_layout_height"
		android:layout_marginBottom="@dimen/specify_neighborhood_activity_button_constraint_layout_margin_bottom"
		android:layout_marginStart="@dimen/specify_neighborhood_activity_button_constraint_layout_margin_start"
		android:layout_marginEnd="@dimen/specify_neighborhood_activity_button_constraint_layout_margin_end"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		tools:layout_editor_absoluteX="24dp"
		tools:layout_editor_absoluteY="495dp">
	
		<android.widget.Button
			android:id="@+id/button_enable_location"
			android:theme="@style/BottomCTAButton"
			android:layout_width="0dp"
			android:layout_height="@dimen/specify_neighborhood_activity_button_large_active_button_height"
			android:layout_marginEnd="@dimen/specify_neighborhood_activity_button_large_active_button_margin_end"
			android:background="@drawable/specify_neighborhood_activity_button_large_active_button_selector"
			android:text="@string/specify_neighborhood_activity_button_large_active_button_text"
			android:textColor="@color/specify_neighborhood_activity_button_large_active_button_text_color"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="0dp"
			tools:layout_editor_absoluteY="0dp"/>
	</androidx.constraintlayout.widget.ConstraintLayout>

	<androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
		android:layout_height="0dp"
		android:layout_marginBottom="@dimen/specify_neighborhood_activity_map_plus_pin_constraint_layout_margin_bottom"
		app:layout_constraintBottom_toTopOf="@+id/group2_constraint_layout"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toTopOf="parent"
		tools:layout_editor_absoluteX="0dp"
		tools:layout_editor_absoluteY="0dp">
	
		<com.google.android.gms.maps.MapView
            android:layout_width="0dp"
			android:layout_height="0dp"
			android:layout_marginBottom="@dimen/specify_neighborhood_activity_rectangle13_map_view_margin_bottom"
			android:layout_marginLeft="@dimen/specify_neighborhood_activity_rectangle13_map_view_margin_start"
			android:layout_marginRight="@dimen/specify_neighborhood_activity_rectangle13_map_view_margin_end"
			android:layout_marginTop="@dimen/specify_neighborhood_activity_rectangle13_map_view_margin_top"
			android:background="@drawable/specify_neighborhood_activity_rectangle13_map_view_background"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="-1dp"
			tools:layout_editor_absoluteY="-1dp"/>
	
		<ImageView
            android:layout_width="0dp"
			android:layout_height="0dp"
			android:layout_marginBottom="@dimen/specify_neighborhood_activity_pin_and_current_locaton_image_view_margin_bottom"
			android:layout_marginLeft="@dimen/specify_neighborhood_activity_pin_and_current_locaton_image_view_margin_start"
			android:layout_marginRight="@dimen/specify_neighborhood_activity_pin_and_current_locaton_image_view_margin_end"
			android:layout_marginTop="@dimen/specify_neighborhood_activity_pin_and_current_locaton_image_view_margin_top"
			android:src="@drawable/ic_icon_location_pin"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="147dp"
			tools:layout_editor_absoluteY="112dp"/>
	</androidx.constraintlayout.widget.ConstraintLayout>

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group2_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="@dimen/specify_neighborhood_activity_group2_constraint_layout_height"
		android:layout_marginBottom="@dimen/specify_neighborhood_activity_group2_constraint_layout_margin_bottom"
		android:layout_marginLeft="@dimen/specify_neighborhood_activity_group2_constraint_layout_margin_start"
		android:layout_marginRight="@dimen/specify_neighborhood_activity_group2_constraint_layout_margin_end"
		app:layout_constraintBottom_toTopOf="@+id/button_constraint_layout"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		tools:layout_editor_absoluteX="12dp"
		tools:layout_editor_absoluteY="329dp">
	
		<TextView
			android:id="@+id/pinch_and_drag_the_mtext_view"
			android:layout_width="@dimen/specify_neighborhood_activity_pinch_and_drag_the_mtext_view_width"
			android:layout_height="wrap_content"
			android:layout_marginLeft="@dimen/specify_neighborhood_activity_pinch_and_drag_the_mtext_view_margin_start"
			android:layout_marginRight="@dimen/specify_neighborhood_activity_pinch_and_drag_the_mtext_view_margin_end"
			android:layout_marginTop="@dimen/specify_neighborhood_activity_pinch_and_drag_the_mtext_view_margin_top"
			android:fontFamily="@font/font_nunitosans_regular"
			android:gravity="center"
			android:lineSpacingMultiplier="1.09"
			android:text="@string/specify_neighborhood_activity_pinch_and_drag_the_mtext_view_text"
			android:textColor="@color/specify_neighborhood_activity_pinch_and_drag_the_mtext_view_text_color"
			android:textSize="@dimen/specify_neighborhood_activity_pinch_and_drag_the_mtext_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/set_your_neighborhoo_text_view"
			tools:layout_editor_absoluteX="12dp"
			tools:layout_editor_absoluteY="40dp"/>
	
		<TextView
			android:id="@+id/only_neighborhood_ntext_view"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginBottom="@dimen/specify_neighborhood_activity_only_neighborhood_ntext_view_margin_bottom"
			android:layout_marginLeft="@dimen/specify_neighborhood_activity_only_neighborhood_ntext_view_margin_start"
			android:layout_marginRight="@dimen/specify_neighborhood_activity_only_neighborhood_ntext_view_margin_end"
			android:fontFamily="@font/font_nunitosans_regular"
			android:gravity="center"
			android:lineSpacingMultiplier="1.2"
			android:text="@string/specify_neighborhood_activity_only_neighborhood_ntext_view_text"
			android:textColor="@color/specify_neighborhood_activity_only_neighborhood_ntext_view_text_color"
			android:textSize="@dimen/specify_neighborhood_activity_only_neighborhood_ntext_view_text_size"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			tools:layout_editor_absoluteX="19dp"
			tools:layout_editor_absoluteY="127dp"/>
	
		<TextView
			android:id="@+id/set_your_neighborhoo_text_view"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginLeft="@dimen/specify_neighborhood_activity_set_your_neighborhoo_text_view_margin_start"
			android:layout_marginRight="@dimen/specify_neighborhood_activity_set_your_neighborhoo_text_view_margin_end"
			android:layout_marginTop="@dimen/specify_neighborhood_activity_set_your_neighborhoo_text_view_margin_top"
			android:fontFamily="@font/font_nunitosans_extrabold"
			android:gravity="center"
			android:lineSpacingMultiplier="0.97"
			android:text="@string/specify_neighborhood_activity_set_your_neighborhoo_text_view_text"
			android:textColor="@color/specify_neighborhood_activity_set_your_neighborhoo_text_view_text_color"
			android:textSize="@dimen/specify_neighborhood_activity_set_your_neighborhoo_text_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="15dp"
			tools:layout_editor_absoluteY="-1dp"/>
	</androidx.constraintlayout.widget.ConstraintLayout>

	<TextView
		android:id="@+id/sun_valley_nv_text_view"
		android:layout_width="@dimen/specify_neighborhood_activity_sun_valley_nv_text_view_width"
		android:layout_height="wrap_content"
		android:layout_marginBottom="@dimen/specify_neighborhood_activity_sun_valley_nv_text_view_margin_bottom"
		android:layout_marginLeft="@dimen/specify_neighborhood_activity_sun_valley_nv_text_view_margin_start"
		android:layout_marginRight="@dimen/specify_neighborhood_activity_sun_valley_nv_text_view_margin_end"
		android:fontFamily="@font/font_nunitosans_extrabold"
		android:gravity="center"
		android:letterSpacing="0.08"
		android:lineSpacingMultiplier="0.7"
		android:textColor="@color/specify_neighborhood_activity_sun_valley_nv_text_view_text_color"
		android:textSize="@dimen/specify_neighborhood_activity_sun_valley_nv_text_view_text_size"
		app:layout_constraintBottom_toTopOf="@+id/group2_constraint_layout"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		tools:layout_editor_absoluteX="24dp"
		tools:layout_editor_absoluteY="292dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>