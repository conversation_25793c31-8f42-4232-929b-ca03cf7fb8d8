<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header_normal_constraint_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/close_button"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="18dp"
            android:background="@null"
            android:elevation="2dp"
            android:padding="10dp"
            android:scaleType="center"
            android:src="@drawable/ic_close"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/grey3" />

        <TextView
            android:id="@+id/subscription_title_header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:fontFamily="@font/nunitosans_bold"
            android:gravity="center"
            android:lineSpacingMultiplier="1"
            android:text="@string/offer_title"
            android:textColor="@color/grey2"
            android:textSize="14sp"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/subscription_sub_title_text"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0"
            app:layout_constraintVertical_chainStyle="spread_inside" />

        <TextView
            android:id="@+id/subscription_sub_title_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:fontFamily="@font/nunitosans_bold"
            android:gravity="center"
            android:lineSpacingMultiplier="1"
            android:text="@string/influencer_offer_title"
            android:textColor="@color/grey2"
            android:textSize="14sp"
            android:visibility="gone"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/subscription_title_header"
            app:layout_constraintVertical_bias="0.5" />

        <View
            android:id="@+id/view_divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#F2F2F2"
            android:layout_marginTop="6dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/close_button" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Main content area -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/subscription_view_pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@+id/header_normal_constraint_layout"
        app:layout_constraintBottom_toTopOf="@id/dots_indicator" />

    <com.tbuonomo.viewpagerdotsindicator.WormDotsIndicator
        android:id="@+id/dots_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:dotsColor="@color/grey1"
        app:dotsSize="8dp"
        app:dotsSpacing="4dp"
        app:layout_constraintBottom_toTopOf="@id/content_bottom_area"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <!-- This is a container for the bottom section - either buttons or trial offer will be shown -->
    <FrameLayout
        android:id="@+id/content_bottom_area"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- Bottom Container for Buttons -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/bottom_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:elevation="8dp">

            <android.widget.Button
                android:id="@+id/button_join_dateup_select"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="0dp"
                android:layout_height="@dimen/intro1_activity_button_large_active_button_height"
                android:layout_marginStart="24dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="24dp"
                android:background="@drawable/button_gold"
                android:text="Upgrade to DateUp Select"
                android:textColor="@color/white"
                android:theme="@style/BottomCTAButton"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <android.widget.Button
                android:id="@+id/button_join_dateup_plus"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="0dp"
                android:layout_height="@dimen/intro1_activity_button_large_active_button_height"
                android:layout_marginStart="24dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="24dp"
                android:layout_marginBottom="8dp"
                android:background="@drawable/enable_location_activity_button_large_active_button_background"
                android:text="Upgrade to DateUp Plus"
                android:textColor="@color/white"
                android:theme="@style/BottomCTAButton"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/button_join_dateup_select"
                app:layout_constraintBottom_toTopOf="@+id/restore_text_view"/>

            <TextView
                android:id="@+id/restore_text_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="24dp"
                android:layout_marginBottom="16dp"
                android:fontFamily="@font/nunitosans_bold"
                android:gravity="center"
                android:lineSpacingMultiplier="1.18"
                android:text="@string/subscription_restore_title"
                android:textColor="@color/grey1"
                android:textSize="14sp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/button_join_dateup_plus" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Trial offer container - will show instead of bottom_container when active -->
        <ScrollView
            android:id="@+id/trail_offer_scroll_layout"
            android:layout_width="match_parent"
            android:layout_height="180dp"
            android:visibility="gone"
            android:background="@android:color/white"
            android:elevation="8dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <View
                    android:id="@+id/view_divider_trail"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#F2F2F2"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/offer_trail_price"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="24dp"
                    android:fontFamily="@font/font_nunitosans_regular"
                    android:gravity="center"
                    android:lineSpacingMultiplier="1.18"
                    android:text="@string/offer_pricing_title"
                    android:textColor="@color/grey3"
                    android:textColorLink="@color/grey3"
                    android:textSize="12sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/view_divider_trail" />

                <android.widget.Button
                    android:id="@+id/button_start_trail"
                    style="?android:attr/borderlessButtonStyle"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/intro1_activity_button_large_active_button_height"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="24dp"
                    android:background="@drawable/intro1_activity_button_large_active_button_selector"
                    android:text="@string/offer_action_button_title"
                    android:textColor="@color/intro1_activity_button_large_active_button_text_color"
                    android:theme="@style/BottomCTAButton"
                    android:textSize="16sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/offer_trail_price" />

                <TextView
                    android:id="@+id/restore_text_view_offer_trail"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginStart="24dp"
                    android:layout_marginEnd="24dp"
                    android:fontFamily="@font/font_nunitosans_regular"
                    android:gravity="center"
                    android:visibility="gone"
                    android:lineSpacingMultiplier="1.18"
                    android:text="@string/subscription_restore_title"
                    android:textColor="@color/images3_activity_please_upload_at_lea_text_view_text_color"
                    android:textSize="14sp"
                    app:layout_constraintTop_toBottomOf="@+id/button_start_trail"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <TextView
                    android:id="@+id/subscription_terms_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="24dp"
                    android:layout_marginBottom="16dp"
                    android:fontFamily="@font/font_nunitosans_regular"
                    android:gravity="center"
                    android:lineSpacingMultiplier="1.18"
                    android:text="@string/subscription_terms"
                    android:textColor="@color/grey3"
                    android:textColorLink="@color/grey3"
                    android:textSize="12sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/restore_text_view_offer_trail" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </ScrollView>
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>