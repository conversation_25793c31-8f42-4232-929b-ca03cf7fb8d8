<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/enable_location_activity_enable_location_constraint_layout_background_color">

    <TextView
        android:id="@+id/scan_status_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/enable_location_activity_where_are_you_locate_text_view_margin_top"
        android:fontFamily="@font/nunitosans_bold"
        android:gravity="center"
        android:lineSpacingMultiplier="0.97"
        android:textColor="@color/enable_location_activity_where_are_you_locate_text_view_text_color"
        android:textSize="20sp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintBottom_toTopOf="@+id/scan_status_desc_text_view"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/icon_empty_magnifying_glass_image_view"
        tools:text="Scan Status" />

    <TextView
        android:id="@+id/scan_status_desc_text_view"
        android:layout_width="@dimen/enable_location_activity_enable_your_location_text_view_width"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/enable_location_activity_enable_your_location_text_view_margin_top"
        android:fontFamily="@font/font_nunitosans_regular"
        android:gravity="center"
        android:lineSpacingMultiplier="1.09"
        android:textColor="@color/enable_location_activity_enable_your_location_text_view_text_color"
        android:textSize="@dimen/enable_location_activity_enable_your_location_text_view_text_size"
        app:layout_constraintBottom_toTopOf="@+id/button_accept_verify"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/scan_status_text_view"
        tools:text="Scan Status Description" />

    <ImageView
        android:id="@+id/icon_empty_magnifying_glass_image_view"
        android:layout_width="150dp"
        android:layout_height="180dp"
        android:scaleType="centerCrop"
        tools:src="@drawable/ic_success_check"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/scan_status_text_view"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <android.widget.Button
        android:id="@+id/button_accept_verify"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="0dp"
        android:layout_height="@dimen/enable_location_activity_button_large_active_button_height"
        android:layout_marginTop="36dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:background="@drawable/enable_location_activity_button_large_active_button_selector"
        android:textColor="@color/enable_location_activity_button_large_active_button_text_color"
        android:theme="@style/BottomCTAButton"
        app:layout_constraintBottom_toTopOf="@+id/cancel_verify_text_view"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="Accept verification"
        app:layout_constraintTop_toBottomOf="@+id/scan_status_desc_text_view" />

    <TextView
        android:id="@+id/cancel_verify_text_view"
        android:layout_width="@dimen/enable_location_activity_enable_your_location_text_view_width"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:fontFamily="@font/nunitosans_bold"
        android:gravity="center"
        android:lineSpacingMultiplier="1.09"
        android:text="Cancel verification"
        android:textColor="@color/color_primary_dark"
        android:textSize="18sp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/group3_constraint_layout"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/button_accept_verify" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group3_constraint_layout"
        android:layout_width="@dimen/lottie_imageview_width"
        android:layout_height="@dimen/lottie_imageview_height"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:layout_editor_absoluteX="-184dp"
        tools:layout_editor_absoluteY="514dp">

        <com.airbnb.lottie.LottieAnimationView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/clouds" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        android:id="@+id/progress"
        layout="@layout/dateup_progress_bar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>