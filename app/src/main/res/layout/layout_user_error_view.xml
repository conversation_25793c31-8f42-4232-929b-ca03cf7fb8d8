<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blank_enable_location_activity_blank_enable_location_constraint_layout_background_color">


    <TextView
        android:id="@+id/turn_on_location_ser_text_view"
        android:layout_width="@dimen/blank_enable_location_activity_turn_on_location_ser_text_view_width"
        android:layout_height="wrap_content"
        android:fontFamily="@font/font_nunitosans_regular"
        android:gravity="center"
        android:lineSpacingMultiplier="1.09"
        android:text="Sorry, we have encountered an error. Please try again in few minutes"
        android:textColor="@color/blank_enable_location_activity_turn_on_location_ser_text_view_text_color"
        android:textSize="@dimen/blank_screen_desc_text_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
