<androidx.appcompat.widget.Toolbar xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="0dp"
    android:layout_height="50dp"
    android:background="@color/white"
    android:layout_alignParentStart="true"
    android:layout_alignParentEnd="true"
    android:layout_alignParentTop="true"
    android:layout_marginTop="-24dp">

    <TextView
        android:id="@+id/browse_text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:text="Browse"
        android:textSize="16sp"
        android:visibility="gone"
        android:fontFamily="@font/font_nunitosans_bold"
        android:textColor="#25333D"/>

    <co.ceryle.segmentedbutton.SegmentedButtonGroup
        android:id="@+id/segment_control_group"
        android:layout_width="160dp"
        android:layout_height="29dp"
        android:layout_margin="4dp"
        app:sbg_animateSelector="fastOutSlowIn"
        app:sbg_animateSelectorDuration="1000"
        app:sbg_backgroundColor="@color/members_color"
        app:sbg_position="0"
        app:sbg_radius="14dp"
        app:sbg_rippleColor="@color/white"
        app:sbg_selectorColor="@color/white"
        android:layout_gravity="center">

        <co.ceryle.segmentedbutton.SegmentedButton
            android:id="@+id/member_button"
            android:layout_width="0dp"
            android:layout_height="25dp"
            android:layout_weight="1"
            android:layout_margin="2dp"
            app:sb_drawableGravity="right"
            app:sb_text="Members"
            app:sb_textSize="12sp"
            app:sb_textColor="#000"
            android:fontFamily="@font/font_nunitosans_extrabold"
            app:sb_drawableTint="@color/white"
            app:sb_drawableTint_onSelection="#000"/>

        <co.ceryle.segmentedbutton.SegmentedButton
            android:id="@+id/guest_button"
            android:layout_width="0dp"
            android:layout_height="25dp"
            android:layout_weight="1"
            app:sb_drawableGravity="right"
            android:layout_margin="2dp"
            app:sb_textColor="#000"
            app:sb_text="Guests"
            app:sb_textSize="12sp"
            android:fontFamily="@font/font_nunitosans_extrabold"
            app:sb_drawableTint="@color/white"
            app:sb_drawableTint_onSelection="#000"/>

    </co.ceryle.segmentedbutton.SegmentedButtonGroup>

    <ImageView
        android:id="@+id/settings_top_image_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_filter"
        android:padding="10dp"
        android:layout_gravity="end"/>



</androidx.appcompat.widget.Toolbar>