<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blank_enable_location_activity_blank_enable_location_constraint_layout_background_color">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group3_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        app:layout_constraintBottom_toTopOf="@+id/button_constraint_layout"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/group2_constraint_layout"
            android:layout_width="0dp"
            android:layout_height="@dimen/blank_enable_location_activity_group2_constraint_layout_height"
            android:layout_marginStart="@dimen/blank_enable_location_activity_group2_constraint_layout_margin_start"
            android:layout_marginTop="@dimen/blank_enable_location_activity_group2_constraint_layout_margin_top"
            android:layout_marginEnd="@dimen/blank_enable_location_activity_group2_constraint_layout_margin_end"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/icon_empty_magnifying_glass_image_view"
            tools:layout_editor_absoluteX="24dp"
            tools:layout_editor_absoluteY="166dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/group_constraint_layout"
                android:layout_width="0dp"
                android:layout_height="@dimen/blank_enable_location_activity_group_constraint_layout_height"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:layout_editor_absoluteX="0dp"
                tools:layout_editor_absoluteY="0dp">

                <TextView
                    android:id="@+id/we_can_tfind_you_text_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/blank_enable_location_activity_we_can_tfind_you_text_view_margin_start"
                    android:layout_marginTop="@dimen/blank_enable_location_activity_we_can_tfind_you_text_view_margin_top"
                    android:layout_marginEnd="@dimen/blank_enable_location_activity_we_can_tfind_you_text_view_margin_end"
                    android:fontFamily="@font/font_nunitosans_extrabold"
                    android:gravity="center"
                    android:lineSpacingMultiplier="0.97"
                    android:text="@string/blank_enable_location_activity_we_can_tfind_you_text_view_text"
                    android:textColor="@color/blank_enable_location_activity_we_can_tfind_you_text_view_text_color"
                    android:textSize="@dimen/blank_screen_title_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/turn_on_location_ser_text_view"
                    android:layout_width="@dimen/blank_enable_location_activity_turn_on_location_ser_text_view_width"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/blank_enable_location_activity_turn_on_location_ser_text_view_margin_top"
                    android:fontFamily="@font/font_nunitosans_regular"
                    android:gravity="center"
                    android:lineSpacingMultiplier="1.09"
                    android:text="@string/blank_enable_location_activity_turn_on_location_ser_text_view_text"
                    android:textColor="@color/blank_enable_location_activity_turn_on_location_ser_text_view_text_color"
                    android:textSize="@dimen/blank_screen_desc_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/we_can_tfind_you_text_view" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/icon_empty_magnifying_glass_image_view"
            android:layout_width="100dp"
            android:layout_height="115dp"
            android:layout_marginTop="@dimen/blank_enable_location_activity_icon_empty_magnifying_glass_image_view_margin_top"
            android:src="@drawable/ic_location"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/button_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="@dimen/blank_enable_location_activity_button_constraint_layout_height"
        android:layout_marginStart="@dimen/blank_enable_location_activity_button_constraint_layout_margin_start"
        android:layout_marginEnd="@dimen/blank_enable_location_activity_button_constraint_layout_margin_end"
        android:layout_marginBottom="75dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <android.widget.Button
            android:id="@+id/button_open_settings"
            style="?android:attr/borderlessButtonStyle"
            android:theme="@style/BottomCTAButton"
            android:layout_width="0dp"
            android:layout_height="@dimen/blank_out_of_people_activity_button_large_active_button_height"
            android:background="@drawable/blank_out_of_people_activity_button_large_active_button_selector"
            android:text="Open Settings"
            android:textColor="@color/blank_out_of_people_activity_button_large_active_button_text_color"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>