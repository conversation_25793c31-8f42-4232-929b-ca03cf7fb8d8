<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="250dp"
        android:layout_height="wrap_content"
        android:background="@android:color/white"
        app:tabIndicatorColor="@color/color_primary"
        app:tabSelectedTextColor="@color/grey1"
        app:tabTextColor="@color/grey3"
        app:tabMode="fixed"
        app:tabInlineLabel="true"
        app:tabTextAppearance="@style/TabTextAppearance.Unselected"
        app:tabSelectedTextAppearance="@style/TabTextAppearance.Selected"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/view_pager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/tab_layout"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
