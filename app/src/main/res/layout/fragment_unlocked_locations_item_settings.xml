<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/other_location_item_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/city_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:src="@drawable/ic_loc_pin"
        android:layout_marginBottom="16dp"/>

    <TextView
        android:id="@+id/city_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/grey2"
        android:textSize="16sp"
        android:fontFamily="@font/font_nunitosans_regular"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="12dp"
        android:layout_gravity="center"
        android:layout_toEndOf="@+id/city_image"
        tools:text="San Francisco, CA"/>

    <ImageView
        android:id="@+id/ticker_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:scaleType="center"
        android:visibility="gone"
        android:layout_marginTop="14dp"
        android:layout_alignParentEnd="true"
        android:src="@drawable/ic_selection_tick" />

    <View
        android:id="@+id/view_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="14dp"
        android:background="#F2F2F2"
        android:layout_below="@+id/city_name"/>
</RelativeLayout>