<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blank_enable_location_activity_blank_enable_location_constraint_layout_background_color">

    <ImageView
        android:id="@+id/icon_empty_magnifying_glass_image_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="150dp"
        android:src="@drawable/ic_camera_for_permissions"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/we_can_tfind_you_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/blank_enable_location_activity_we_can_tfind_you_text_view_margin_start"
        android:layout_marginTop="36dp"
        android:layout_marginEnd="@dimen/blank_enable_location_activity_we_can_tfind_you_text_view_margin_end"
        android:fontFamily="@font/font_nunitosans_extrabold"
        android:gravity="center"
        android:lineSpacingMultiplier="0.97"
        android:text="Enable camera access"
        android:textColor="@color/blank_enable_location_activity_we_can_tfind_you_text_view_text_color"
        android:textSize="@dimen/blank_screen_title_text_size"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/icon_empty_magnifying_glass_image_view" />

    <TextView
        android:id="@+id/turn_on_location_ser_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/blank_enable_location_activity_turn_on_location_ser_text_view_margin_top"
        android:fontFamily="@font/font_nunitosans_regular"
        android:gravity="center"
        android:lineSpacingMultiplier="1.09"
        android:text="Please enable camera access for DateUp in your phone settings in order to proceed with height verification."
        android:textColor="@color/blank_enable_location_activity_turn_on_location_ser_text_view_text_color"
        android:textSize="@dimen/blank_screen_desc_text_size"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/we_can_tfind_you_text_view" />

    <android.widget.Button
        android:id="@+id/button_open_settings"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="0dp"
        android:layout_height="@dimen/blank_out_of_people_activity_button_large_active_button_height"
        android:background="@drawable/blank_out_of_people_activity_button_large_active_button_selector"
        android:text="Open Settings"
        android:layout_marginTop="36dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:textColor="@color/blank_out_of_people_activity_button_large_active_button_text_color"
        android:theme="@style/BottomCTAButton"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/turn_on_location_ser_text_view"/>

</androidx.constraintlayout.widget.ConstraintLayout>