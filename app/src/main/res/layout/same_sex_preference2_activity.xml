<androidx.constraintlayout.widget.ConstraintLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/same_sex_preference2_activity_same_sex_preference2_constraint_layout_background_color">

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group5_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="@dimen/same_sex_preference2_activity_group5_constraint_layout_height"
		android:layout_marginLeft="@dimen/same_sex_preference2_activity_group5_constraint_layout_margin_start"
		android:layout_marginTop="28dp"
		android:layout_marginRight="@dimen/same_sex_preference2_activity_group5_constraint_layout_margin_end"
		app:layout_constraintHorizontal_bias="1.0"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toTopOf="parent">

		<android.widget.Button
			android:id="@+id/button_taller"
			android:layout_width="@dimen/same_sex_preference2_activity_radio_button_unselected_copy5_constraint_layout_width"
			android:layout_height="@dimen/same_sex_preference2_activity_radio_button_unselected_copy5_constraint_layout_height"
			android:layout_marginTop="@dimen/same_sex_preference2_activity_radio_button_unselected_copy5_constraint_layout_margin_top"
			android:background="@drawable/same_sex_preference2_activity_radio_button_unselected_copy4_button_selector"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/group4_constraint_layout"
			android:text="@string/same_sex_preference2_activity_value_text_view_text"
			android:textColor="@color/same_sex_preference2_activity_value_text_view_text_color"
			android:textSize="@dimen/same_sex_preference2_activity_value_text_view_text_size"
			android:gravity="center"
			android:textAllCaps="false"
			android:fontFamily="@font/font_nunitosans_regular"
			android:lineSpacingMultiplier="1.1"
			tools:layout_editor_absoluteX="82dp"
			tools:layout_editor_absoluteY="214dp">
		</android.widget.Button>

		<android.widget.Button
			android:id="@+id/button_shorter"
			android:layout_width="@dimen/same_sex_preference2_activity_radio_button_unselected_copy4_button_width"
			android:layout_height="@dimen/same_sex_preference2_activity_radio_button_unselected_copy4_button_height"
			android:layout_marginBottom="@dimen/same_sex_preference2_activity_radio_button_unselected_copy4_button_margin_bottom"
			android:background="@drawable/same_sex_preference2_activity_radio_button_unselected_copy4_button_selector"
			android:fontFamily="@font/font_nunitosans_regular"
			android:gravity="center"
			android:lineSpacingMultiplier="1"
			android:text="@string/same_sex_preference2_activity_radio_button_unselected_copy4_button_text"
			android:textColor="@color/same_sex_preference2_activity_radio_button_unselected_copy4_button_text_color"
			android:textSize="@dimen/same_sex_preference2_activity_radio_button_unselected_copy4_button_text_size"
			android:textAllCaps="false"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			tools:layout_editor_absoluteX="82dp"
			tools:layout_editor_absoluteY="266dp" />

		<androidx.constraintlayout.widget.ConstraintLayout
			android:id="@+id/group4_constraint_layout"
			android:layout_width="0dp"
			android:layout_height="@dimen/same_sex_preference2_activity_group4_constraint_layout_height"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="0dp"
			tools:layout_editor_absoluteY="0dp">

			<TextView
				android:id="@+id/should_the_women_be_text_view"
				android:layout_width="@dimen/same_sex_preference2_activity_should_the_women_be_text_view_width"
				android:layout_height="wrap_content"
				android:layout_marginLeft="@dimen/same_sex_preference2_activity_should_the_women_be_text_view_margin_start"
				android:layout_marginRight="@dimen/same_sex_preference2_activity_should_the_women_be_text_view_margin_end"
				android:fontFamily="@font/font_nunitosans_extrabold"
				android:gravity="left"
				android:lineSpacingMultiplier="1"
				android:text="@string/same_sex_preference2_activity_should_the_women_be_text_view_text"
				android:textColor="@color/same_sex_preference2_activity_should_the_women_be_text_view_text_color"
				android:textSize="@dimen/same_sex_preference2_activity_should_the_women_be_text_view_text_size"
				app:layout_constraintLeft_toLeftOf="parent"
				app:layout_constraintRight_toRightOf="parent"
				app:layout_constraintTop_toTopOf="parent"
				tools:layout_editor_absoluteX="2dp"
				tools:layout_editor_absoluteY="0dp" />

			<TextView
				android:id="@+id/you_can_specify_sho_text_view"
				android:layout_width="@dimen/same_sex_preference2_activity_you_can_specify_sho_text_view_width"
				android:layout_height="wrap_content"
				android:fontFamily="@font/font_nunitosans_regular"
				android:gravity="left"
				android:lineSpacingMultiplier="0.96"
				android:text="@string/same_sex_preference2_activity_you_can_specify_sho_text_view_text"
				android:textColor="@color/same_sex_preference2_activity_you_can_specify_sho_text_view_text_color"
				android:textSize="@dimen/same_sex_preference2_activity_you_can_specify_sho_text_view_text_size"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintLeft_toLeftOf="parent"
				app:layout_constraintRight_toRightOf="parent"
				tools:layout_editor_absoluteX="0dp"
				tools:layout_editor_absoluteY="103dp" />
		</androidx.constraintlayout.widget.ConstraintLayout>

	</androidx.constraintlayout.widget.ConstraintLayout>

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group3_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="232dp"
		android:layout_marginLeft="@dimen/sex_preference_activity_group3_constraint_layout_margin_start"
		android:layout_marginTop="65dp"
		android:layout_marginRight="@dimen/sex_preference_activity_group3_constraint_layout_margin_end"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintHorizontal_bias="0.501"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/group5_constraint_layout">

		<android.widget.Button
			android:id="@+id/button_enable_location"
			style="?android:attr/borderlessButtonStyle"
			android:theme="@style/BottomCTAButton"
			android:layout_width="0dp"
			android:layout_height="@dimen/sex_preference_activity_button_large_active_button_height"
			android:layout_marginStart="@dimen/intro1_activity_button_constraint_layout_margin_start"
			android:layout_marginTop="10dp"
			android:layout_marginEnd="@dimen/intro1_activity_button_constraint_layout_margin_end"
			android:background="@drawable/sex_preference_activity_button_large_active_button_selector"
			android:text="@string/sex_preference_activity_button_large_active_button_text"
			android:textColor="@color/sex_preference_activity_button_large_active_button_text_color"
			app:layout_constraintHorizontal_bias="0.0"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			app:layout_constraintVertical_bias="0.0" />

	</androidx.constraintlayout.widget.ConstraintLayout>

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group6_constraint_layout"
		android:layout_width="@dimen/lottie_imageview_width"
		android:layout_height="@dimen/lottie_imageview_height"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent">

		<com.airbnb.lottie.LottieAnimationView
            android:layout_width="match_parent"
			android:layout_height="match_parent"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintEnd_toEndOf="parent"
			app:layout_constraintStart_toStartOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			app:lottie_autoPlay="true"
			app:lottie_loop="true"
			app:lottie_rawRes="@raw/clouds" />
	</androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>