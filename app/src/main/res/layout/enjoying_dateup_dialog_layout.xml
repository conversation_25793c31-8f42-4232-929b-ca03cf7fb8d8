<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tip_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="32dp"
        android:layout_marginEnd="24dp"
        android:fontFamily="@font/font_nunitosans_extrabold"
        android:gravity="center"
        android:lineSpacingMultiplier="1.29"
        android:text="@string/review_enjoying_dateup_title"
        android:textColor="@color/height_celebration3_activity_is_agreat_heig_text_view_text_color"
        android:textSize="20sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tip_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="@dimen/height_celebration3_activity_let_sproceed_with_stext_view_margin_top"
        android:layout_marginEnd="24dp"
        android:fontFamily="@font/font_nunitosans_regular"
        android:gravity="center"
        android:lineSpacingMultiplier="1.09"
        android:text="@string/review_enjoying_dateup_desc"
        android:textColor="@color/grey2"
        android:textSize="16sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tip_title" />

    <LinearLayout
        android:id="@+id/needs_work_container"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginStart="36dp"
        android:layout_marginTop="36dp"
        android:layout_marginEnd="24dp"
        android:layout_marginBottom="36dp"
        android:background="@drawable/sex_preference_activity_radio_button_unselected_button_selector"
        android:orientation="vertical"
        app:flow_verticalBias="0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/love_it_container"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tip_desc"
        app:layout_constraintVertical_bias="0.0">

        <ImageView
            android:id="@+id/needs_work_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="14dp"
            android:foregroundGravity="center"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_sad_face" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="12dp"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="center"
            android:lineSpacingMultiplier="1.09"
            android:text="Needs work"
            android:textColor="@color/grey2"
            android:textSize="16sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/love_it_container"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginTop="36dp"
        android:layout_marginEnd="36dp"
        android:layout_marginBottom="36dp"
        android:background="@drawable/sex_preference_activity_radio_button_unselected_button_selector"
        android:orientation="vertical"
        app:flow_verticalBias="0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/needs_work_container"
        app:layout_constraintTop_toBottomOf="@+id/tip_desc"
        app:layout_constraintVertical_bias="0.0">

        <ImageView
            android:id="@+id/love_it_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="14dp"
            android:foregroundGravity="center"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_heart_face" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="12dp"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="center"
            android:lineSpacingMultiplier="1.09"
            android:text="Love it"
            android:textColor="@color/grey2"
            android:textSize="16sp" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>