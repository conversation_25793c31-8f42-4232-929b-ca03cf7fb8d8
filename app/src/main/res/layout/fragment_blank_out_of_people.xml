<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blank_out_of_people_activity_blank_out_of_people_constraint_layout_background_color">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/blank_out_of_people_activity_group_constraint_layout_margin_start"
        android:layout_marginTop="90dp"
        android:layout_marginEnd="@dimen/blank_out_of_people_activity_group_constraint_layout_margin_end"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/icon_empty_magnifying_glass_image_view"
            android:layout_width="150dp"
            android:layout_height="150dp"
            android:src="@drawable/ic_magnifying_glass"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_editor_absoluteX="95dp"
            tools:layout_editor_absoluteY="0dp" />

        <TextView
            android:id="@+id/you_re_out_of_people_text_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/blank_out_of_people_activity_you_re_out_of_people_text_view_margin_start"
            android:layout_marginTop="40dp"
            android:layout_marginEnd="@dimen/blank_out_of_people_activity_you_re_out_of_people_text_view_margin_end"
            android:fontFamily="@font/font_nunitosans_extrabold"
            android:gravity="center"
            android:lineSpacingMultiplier="0.97"
            android:text="@string/blank_out_of_people_activity_you_re_out_of_people_text_view_text"
            android:textColor="@color/blank_out_of_people_activity_you_re_out_of_people_text_view_text_color"
            android:textSize="@dimen/blank_screen_title_text_size"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/icon_empty_magnifying_glass_image_view" />

        <TextView
            android:id="@+id/try_expanding_your_ptext_view"
            android:layout_width="@dimen/blank_out_of_people_activity_try_expanding_your_ptext_view_width"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="@dimen/blank_out_of_people_activity_try_expanding_your_ptext_view_margin_bottom"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="center"
            android:lineSpacingMultiplier="1.09"
            android:text="@string/blank_out_of_people_activity_try_expanding_your_ptext_view_text"
            android:textColor="@color/blank_out_of_people_activity_try_expanding_your_ptext_view_text_color"
            android:textSize="@dimen/blank_screen_desc_text_size"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/you_re_out_of_people_text_view" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/button_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/blank_out_of_people_activity_button_constraint_layout_margin_start"
        android:layout_marginTop="85dp"
        android:layout_marginEnd="@dimen/blank_out_of_people_activity_button_constraint_layout_margin_end"
        android:layout_marginBottom="98dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/group_constraint_layout">

        <android.widget.Button
            android:id="@+id/button_open_preferences"
            style="?android:attr/borderlessButtonStyle"
            android:theme="@style/BottomCTAButton"
            android:layout_width="0dp"
            android:layout_height="@dimen/blank_out_of_people_activity_button_large_active_button_height"
            android:background="@drawable/blank_out_of_people_activity_button_large_active_button_selector"
            android:text="@string/blank_out_of_people_activity_button_large_active_button_text"
            android:textColor="@color/blank_out_of_people_activity_button_large_active_button_text_color"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_editor_absoluteX="0dp"
            tools:layout_editor_absoluteY="0dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>