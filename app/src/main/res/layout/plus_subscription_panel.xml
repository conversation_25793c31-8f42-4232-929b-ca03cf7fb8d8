<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_margin="16dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- DateUp Plus Banner -->
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:src="@drawable/plus_logo" />

        <!-- Header Row with Columns -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingBottom="16dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/font_nunitosans_bold"
                android:text="What's Included"
                android:textColor="@color/grey1"
                android:textSize="16sp" />

            <TextView
                android:layout_width="60dp"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_nunitosans_extrabolditalic"
                android:gravity="center"
                android:text="Select"
                android:textColor="#ED9F40"
                android:textSize="16sp" />

            <TextView
                android:layout_width="60dp"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_nunitosans_extrabolditalic"
                android:gravity="center"
                android:text="Plus"
                android:textColor="#5DA8F5"
                android:textSize="16sp" />
        </LinearLayout>

        <!-- Direct Messages -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_nunitosans_bold"
                        android:text="3 Direct Messages per Day"
                        android:textColor="@color/grey1"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_nunitosans_regular"
                        android:text="Message anyone, anytime — no match needed."
                        android:textColor="@color/grey3"
                        android:textSize="14sp" />
                </LinearLayout>

                <FrameLayout
                    android:layout_width="60dp"
                    android:layout_height="match_parent">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_check_grey" />
                </FrameLayout>

                <FrameLayout
                    android:layout_width="60dp"
                    android:layout_height="match_parent">
                    <!-- Empty for PLUS column -->
                </FrameLayout>
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:background="#E0E0E0" />
        </LinearLayout>

        <!-- View Passed Profiles -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_nunitosans_bold"
                        android:text="View Passed Profiles"
                        android:textColor="@color/grey1"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_nunitosans_regular"
                        android:text="Explore any profiles you've passed on already."
                        android:textColor="@color/grey3"
                        android:textSize="14sp" />
                </LinearLayout>

                <FrameLayout
                    android:layout_width="60dp"
                    android:layout_height="match_parent">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_check_grey" />
                </FrameLayout>

                <FrameLayout
                    android:layout_width="60dp"
                    android:layout_height="match_parent">
                    <!-- Empty for PLUS column -->
                </FrameLayout>
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:background="#E0E0E0" />
        </LinearLayout>

        <!-- See Who Likes You -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_nunitosans_bold"
                        android:text="See Who Likes You"
                        android:textColor="@color/grey1"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_nunitosans_regular"
                        android:text="Save time by seeing everyone that's liked you."
                        android:textColor="@color/grey3"
                        android:textSize="14sp" />
                </LinearLayout>

                <FrameLayout
                    android:layout_width="60dp"
                    android:layout_height="match_parent">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_check_grey" />
                </FrameLayout>

                <FrameLayout
                    android:layout_width="60dp"
                    android:layout_height="match_parent">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_check_blue" />
                </FrameLayout>
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:background="#E0E0E0" />
        </LinearLayout>

        <!-- Teleport Mode -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_nunitosans_bold"
                        android:text="Teleport Mode"
                        android:textColor="@color/grey1"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_nunitosans_regular"
                        android:text="Easily teleport to any city where DateUp is unlocked."
                        android:textColor="@color/grey3"
                        android:textSize="14sp" />
                </LinearLayout>

                <FrameLayout
                    android:layout_width="60dp"
                    android:layout_height="match_parent">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_check_grey" />
                </FrameLayout>

                <FrameLayout
                    android:layout_width="60dp"
                    android:layout_height="match_parent">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_check_blue" />
                </FrameLayout>
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:background="#E0E0E0" />
        </LinearLayout>

        <!-- Unlimited Likes -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_nunitosans_bold"
                        android:text="Unlimited Likes"
                        android:textColor="@color/grey1"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_nunitosans_regular"
                        android:text="Browse without limits until your heart is content."
                        android:textColor="@color/grey3"
                        android:textSize="14sp" />
                </LinearLayout>

                <FrameLayout
                    android:layout_width="60dp"
                    android:layout_height="match_parent">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_check_grey" />
                </FrameLayout>

                <FrameLayout
                    android:layout_width="60dp"
                    android:layout_height="match_parent">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_check_blue" />
                </FrameLayout>
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:background="#E0E0E0" />
        </LinearLayout>

        <!-- Read Receipts -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_nunitosans_bold"
                        android:text="Read Receipts"
                        android:textColor="@color/grey1"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_nunitosans_regular"
                        android:text="Always know when your message has been read."
                        android:textColor="@color/grey3"
                        android:textSize="14sp" />
                </LinearLayout>

                <FrameLayout
                    android:layout_width="60dp"
                    android:layout_height="match_parent">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_check_grey" />
                </FrameLayout>

                <FrameLayout
                    android:layout_width="60dp"
                    android:layout_height="match_parent">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_check_blue" />
                </FrameLayout>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
