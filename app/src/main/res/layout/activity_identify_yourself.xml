<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/sex_preference_activity_sex_preference_constraint_layout_background_color">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header_normal_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="@dimen/settings_settings_supernova_activity_header_normal_constraint_layout_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="0dp"
        tools:layout_editor_absoluteY="0dp">

        <ImageButton
            android:id="@+id/notifications_header_left_image_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/preferences_men_secopy2supernova_activity_tail_left_image_view_margin_start"
            android:layout_marginTop="4dp"
            android:background="@null"
            android:elevation="2dp"
            android:padding="10dp"
            android:scaleType="center"
            android:src="@drawable/ic_icon_arrow_left"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group4_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@+id/button_next"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/header_normal_constraint_layout"
        app:layout_constraintVertical_bias="0.76"
        app:layout_constraintVertical_chainStyle="spread_inside">

        <TextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="48dp"
            android:layout_marginTop="36dp"
            android:layout_marginEnd="48dp"
            android:fontFamily="@font/font_nunitosans_extrabold"
            android:gravity="left"
            android:lineSpacingMultiplier="0.97"
            android:text="How do you identify?"
            android:textColor="@color/sex_preference_activity_you_are_atext_view_text_color"
            android:textSize="20sp"
            app:layout_constraintHorizontal_bias="0.53"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/desc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="48dp"
            android:layout_marginEnd="48dp"
            android:fontFamily="@font/font_nunitosans_regular"
            android:lineSpacingMultiplier="0.96"
            android:text="Everyone’s welcome on DateUp."
            android:textColor="@color/same_sex_preference2_activity_you_can_specify_sho_text_view_text_color"
            android:textSize="18sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title"
            tools:layout_editor_absoluteX="0dp"
            tools:layout_editor_absoluteY="103dp" />

        <android.widget.Button
            android:id="@+id/button_preference_man"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="48dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="48dp"
            android:background="@drawable/sex_preference_activity_radio_button_unselected_button_selector"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="center"
            android:lineSpacingMultiplier="1"
            android:text="Man"
            android:textAllCaps="false"
            android:textColor="@color/sex_preference_activity_radio_button_unselected_button_text_color"
            android:textSize="@dimen/sex_preference_activity_radio_button_unselected_button_text_size"
            app:layout_constraintHorizontal_bias="0.468"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/desc" />

        <android.widget.Button
            android:id="@+id/button_preference_woman"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="48dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="48dp"
            android:background="@drawable/sex_preference_activity_radio_button_unselected_button_selector"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="center"
            android:lineSpacingMultiplier="1"
            android:text="Woman"
            android:textAllCaps="false"
            android:textColor="@color/sex_preference_activity_radio_button_unselected_button_text_color"
            android:textSize="@dimen/sex_preference_activity_radio_button_unselected_button_text_size"
            app:layout_constraintHorizontal_bias="0.468"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/button_preference_man"
            tools:text="@string/sex_preference_activity_radio_button_unselected_copy4_button_text" />

        <android.widget.Button
            android:id="@+id/button_preference_non_binary"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="48dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="48dp"
            android:background="@drawable/sex_preference_activity_radio_button_unselected_button_selector"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="center"
            android:lineSpacingMultiplier="1"
            android:text="Non-binary"
            android:textAllCaps="false"
            android:textColor="@color/sex_preference_activity_radio_button_unselected_button_text_color"
            android:textSize="@dimen/sex_preference_activity_radio_button_unselected_button_text_size"
            app:layout_constraintHorizontal_bias="0.468"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/button_preference_woman" />

        <TextView
            android:id="@+id/button_more_options"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="24dp"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="center"
            android:lineSpacingMultiplier="1.18"
            android:padding="16dp"
            android:text="@string/more_options"
            android:textColor="@color/images3_activity_please_upload_at_lea_text_view_text_color"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/button_preference_non_binary" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <android.widget.Button
        android:id="@+id/button_next"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="0dp"
        android:layout_height="@dimen/first_name_activity_button_large_active_button_height"
        android:layout_marginStart="48dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="48dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/bottom_button_disabled_state"
        android:enabled="false"
        android:lineSpacingMultiplier="1"
        android:text="Next"
        android:textColor="@color/first_name_activity_button_large_active_button_text_color"
        android:theme="@style/BottomCTAButton"
        app:layout_constraintBottom_toTopOf="@+id/group3_constraint_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/group4_constraint_layout"
        app:layout_constraintVertical_bias="1.0" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group3_constraint_layout"
        android:layout_width="@dimen/lottie_imageview_width"
        android:layout_height="@dimen/lottie_imageview_height"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/button_next">

        <com.airbnb.lottie.LottieAnimationView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/clouds" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>