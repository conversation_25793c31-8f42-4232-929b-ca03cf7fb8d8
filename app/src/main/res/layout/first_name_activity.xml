<androidx.constraintlayout.widget.ConstraintLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/first_name_activity_first_name_constraint_layout_background_color">

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/button_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="@dimen/first_name_activity_button_constraint_layout_height"
		android:layout_marginStart="@dimen/first_name_activity_button_constraint_layout_margin_start"
		android:layout_marginTop="52dp"
		android:layout_marginEnd="@dimen/first_name_activity_button_constraint_layout_margin_end"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/group_constraint_layout">

		<android.widget.Button
			android:id="@+id/button_enable_location"
			style="?android:attr/borderlessButtonStyle"
			android:theme="@style/BottomCTAButton"
			android:layout_width="0dp"
			android:layout_height="@dimen/first_name_activity_button_large_active_button_height"
			android:background="@drawable/bottom_button_disabled_state"
			android:enabled="false"
			android:lineSpacingMultiplier="1"
			android:text="@string/first_name_activity_button_large_active_button_text"
			android:textColor="@color/first_name_activity_button_large_active_button_text_color"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="0dp"
			tools:layout_editor_absoluteY="0dp" />
	</androidx.constraintlayout.widget.ConstraintLayout>

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="wrap_content"
		android:layout_marginLeft="@dimen/first_name_activity_group_constraint_layout_margin_start"
		android:layout_marginTop="64dp"
		android:layout_marginRight="@dimen/first_name_activity_group_constraint_layout_margin_end"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toTopOf="parent">

		<TextView
			android:id="@+id/what_syour_first_na_text_view"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginRight="@dimen/first_name_activity_what_syour_first_na_text_view_margin_end"
			android:fontFamily="@font/font_nunitosans_extrabold"
			android:gravity="left"
			android:lineSpacingMultiplier="1"
			android:singleLine="true"
			android:text="@string/first_name_activity_what_syour_first_na_text_view_text"
			android:textColor="@color/first_name_activity_what_syour_first_na_text_view_text_color"
			android:textSize="@dimen/first_name_activity_what_syour_first_na_text_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="0dp"
			tools:layout_editor_absoluteY="0dp" />

		<androidx.constraintlayout.widget.ConstraintLayout
			android:id="@+id/field_short_placeholder_constraint_layout"
			android:layout_width="@dimen/first_name_activity_field_short_placeholder_constraint_layout_width"
			android:layout_height="@dimen/first_name_activity_field_short_placeholder_constraint_layout_height"
			android:layout_marginTop="14dp"
			android:layout_marginBottom="14dp"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/this_will_be_display_text_view">

			<EditText
				android:id="@+id/first_name_edit_text"
				android:layout_width="0dp"
				android:layout_height="wrap_content"
				android:background="@color/first_name_activity_value_edit_text_background_color"
				android:fontFamily="@font/font_nunitosans_regular"
				android:gravity="left"
				android:hint="@string/first_name_activity_value_edit_text_hint"
				android:inputType="textNoSuggestions|textCapWords"
				android:lineSpacingMultiplier="1"
				android:textColor="@color/first_name_activity_value_edit_text_text_color"
				android:textColorHint="@color/default_hint"
				android:textSize="@dimen/first_name_activity_value_edit_text_text_size"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintLeft_toLeftOf="parent"
				app:layout_constraintRight_toRightOf="parent"
				app:layout_constraintTop_toTopOf="parent"
				android:importantForAutofill="no" />

			<androidx.constraintlayout.widget.ConstraintLayout
				android:id="@+id/rectangle_constraint_layout"
				android:layout_width="0dp"
				android:layout_height="@dimen/first_name_activity_rectangle_constraint_layout_height"
				android:layout_marginTop="2dp"
				android:background="@color/first_name_activity_rectangle_constraint_layout_background_color"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintLeft_toLeftOf="parent"
				app:layout_constraintRight_toRightOf="parent"
				app:layout_constraintTop_toBottomOf="@+id/first_name_edit_text" />
		</androidx.constraintlayout.widget.ConstraintLayout>

		<TextView
			android:id="@+id/this_will_be_display_text_view"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginTop="@dimen/first_name_activity_this_will_be_display_text_view_margin_top"
			android:layout_marginRight="@dimen/first_name_activity_this_will_be_display_text_view_margin_end"
			android:fontFamily="@font/font_nunitosans_regular"
			android:gravity="left"
			android:lineSpacingMultiplier="1.09"
			android:text="@string/first_name_activity_this_will_be_display_text_view_text"
			android:textColor="@color/first_name_activity_this_will_be_display_text_view_text_color"
			android:textSize="@dimen/first_name_activity_this_will_be_display_text_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/what_syour_first_na_text_view"
			tools:layout_editor_absoluteX="0dp"
			tools:layout_editor_absoluteY="39dp" />
	</androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>