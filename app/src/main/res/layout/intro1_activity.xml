<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/intro1_activity_intro1_constraint_layout_background_color">

    <ImageView
        android:id="@+id/logo"
        android:layout_width="82dp"
        android:layout_height="36dp"
        android:layout_marginTop="56dp"
        android:src="@drawable/ic_logo_expanded"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/view_pager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp"
        app:layout_constraintBottom_toTopOf="@+id/tab_layout"
        app:layout_constraintTop_toBottomOf="@+id/logo"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:foregroundGravity="center"
        app:layout_constraintBottom_toTopOf="@id/button_constraint_layout"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:tabBackground="@drawable/tab_selector"
        app:tabGravity="center"
        app:tabIndicatorGravity="center"
        app:tabIndicatorHeight="0dp"
        app:tabPaddingEnd="7dp"
        app:tabPaddingStart="7dp" />

    <TextView
        android:id="@+id/by_proceeding_you_ag_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/intro1_activity_by_proceeding_you_ag_text_view_margin_start"
        android:layout_marginEnd="@dimen/intro1_activity_by_proceeding_you_ag_text_view_margin_start"
        android:fontFamily="@font/font_nunitosans_regular"
        android:gravity="center"
        android:lineSpacingMultiplier="1.29"
        android:text="@string/intro1_activity_by_proceeding_you_ag_text_view_text"
        android:textColor="@color/intro1_activity_by_proceeding_you_ag_text_view_text_color"
        android:textColorLink="@color/intro1_activity_by_proceeding_you_ag_text_view_text_color"
        android:textSize="@dimen/intro1_activity_by_proceeding_you_ag_text_view_text_size"
        android:layout_marginTop="12dp"
        app:layout_constraintTop_toBottomOf="@+id/google_sign_in_button"
        app:layout_constraintBottom_toTopOf="@+id/group3_constraint_layout"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <com.facebook.login.widget.LoginButton
        android:id="@+id/facebook_sign_in_button"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="24dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/button_rounded_facebook"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:textSize="18sp"
        app:circularflow_radiusInDP="15dp"
        app:layout_constraintTop_toBottomOf="@+id/button_constraint_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent" />

    <android.widget.Button
        android:id="@+id/google_sign_in_button"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="24dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/button_rounded_gmail"
        android:drawableStart="@drawable/google_icon_logo"
        android:drawablePadding="-32dp"
        android:paddingStart="10dp"
        android:text="@string/continue_gmail_text"
        android:visibility="gone"
        android:textColor="@color/grey2"
        android:layout_marginBottom="6dp"
        app:layout_constraintTop_toBottomOf="@+id/facebook_sign_in_button"
        app:layout_constraintBottom_toTopOf="@+id/by_proceeding_you_ag_text_view"
        android:theme="@style/BottomCTAButton"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/button_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="@dimen/intro1_activity_button_constraint_layout_height"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="24dp"
        app:layout_constraintBottom_toTopOf="@+id/facebook_sign_in_button"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <android.widget.Button
            android:id="@+id/button_enable_location"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="@dimen/intro1_activity_button_large_active_button_height"
            android:background="@drawable/intro1_activity_button_large_active_button_selector"
            android:text="@string/intro1_activity_button_large_active_button_text"
            android:textColor="@color/intro1_activity_button_large_active_button_text_color"
            android:theme="@style/BottomCTAButton"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group3_constraint_layout"
        android:layout_width="@dimen/lottie_imageview_width"
        android:layout_height="@dimen/lottie_imageview_height"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <com.airbnb.lottie.LottieAnimationView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/clouds" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="62dp"
        android:layout_height="62dp"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>