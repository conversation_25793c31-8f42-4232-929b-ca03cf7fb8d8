<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/scroll_dialog"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fillViewport="true"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/dialogContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_gravity="center"
        android:layout_marginHorizontal="16dp"
        android:background="@drawable/dialog_background"
        android:padding="16dp">

        <!-- Close button -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/close_button"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_close"
                android:contentDescription="Close"
                android:layout_gravity="end" />
        </FrameLayout>

        <!-- Profile Image -->
        <ImageView
            android:id="@+id/userProfileImage"
            android:layout_width="72dp"
            android:layout_height="72dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="16dp"
            android:scaleType="centerCrop" />

        <!-- Title -->
        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Send a Message"
            android:textSize="18sp"
            android:textStyle="bold"
            android:fontFamily="@font/font_nunitosans_bold"
            android:textColor="@color/grey1"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="8dp" />

        <!-- Subtitle -->
        <TextView
            android:id="@+id/subtitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Make a great first impression — personalized messages get more replies!"
            android:textSize="14sp"
            android:fontFamily="@font/font_nunitosans_regular"
            android:textColor="@color/grey3"
            android:gravity="center"
            android:layout_marginBottom="16dp" />

        <!-- Message Input -->
        <EditText
            android:id="@+id/messageInput"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:hint="Type message..."
            android:background="@drawable/dialog_input_background"
            android:gravity="top|start"
            android:padding="12dp"
            android:textSize="16sp"
            android:maxLength="180"
            android:fontFamily="@font/font_nunitosans_regular"
            android:textColor="@color/grey1"
            android:layout_marginBottom="8dp" />

        <!-- Character Count -->
        <TextView
            android:id="@+id/charCount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="0/180"
            android:textSize="12sp"
            android:textColor="@color/grey4"
            android:gravity="end"
            android:layout_marginBottom="16dp" />

        <!-- Send Button -->
        <Button
            android:id="@+id/sendButton"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="Send Message"
            android:textSize="16sp"
            android:background="@drawable/intro1_activity_button_large_active_button_selector"
            android:textColor="@color/intro1_activity_button_large_active_button_text_color"
            android:textAllCaps="false"
            android:theme="@style/BottomCTAButton" />

    </LinearLayout>
</ScrollView>
