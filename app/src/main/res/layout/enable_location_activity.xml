<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/enable_location_activity_enable_location_constraint_layout_background_color">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/enable_location_activity_group_constraint_layout_margin_start"
        android:layout_marginTop="54dp"
        android:layout_marginEnd="@dimen/enable_location_activity_group_constraint_layout_margin_end"
        android:layout_marginBottom="24dp"
        app:layout_constraintBottom_toTopOf="@+id/button_constraint_layout"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="@dimen/enable_location_activity_container_constraint_layout_height"
            android:layout_marginStart="@dimen/enable_location_activity_container_constraint_layout_margin_start"
            android:layout_marginEnd="@dimen/enable_location_activity_container_constraint_layout_margin_end"
            android:alpha="0"
            android:background="@color/enable_location_activity_container_constraint_layout_background_color"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_editor_absoluteX="2dp"
            tools:layout_editor_absoluteY="0dp" />

        <TextView
            android:id="@+id/where_are_you_locate_text_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/enable_location_activity_where_are_you_locate_text_view_margin_start"
            android:layout_marginTop="@dimen/enable_location_activity_where_are_you_locate_text_view_margin_top"
            android:layout_marginEnd="@dimen/enable_location_activity_where_are_you_locate_text_view_margin_end"
            android:fontFamily="@font/font_nunitosans_extrabold"
            android:gravity="center"
            android:lineSpacingMultiplier="0.97"
            android:text="@string/enable_location_activity_where_are_you_locate_text_view_text"
            android:textColor="@color/enable_location_activity_where_are_you_locate_text_view_text_color"
            android:textSize="@dimen/enable_location_activity_where_are_you_locate_text_view_text_size"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/icon_empty_magnifying_glass_image_view"
            tools:layout_editor_absoluteX="3dp"
            tools:layout_editor_absoluteY="158dp" />

        <TextView
            android:id="@+id/enable_your_location_text_view"
            android:layout_width="@dimen/enable_location_activity_enable_your_location_text_view_width"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/enable_location_activity_enable_your_location_text_view_margin_top"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="center"
            android:lineSpacingMultiplier="1.09"
            android:text="@string/enable_location_activity_enable_your_location_text_view_text"
            android:textColor="@color/enable_location_activity_enable_your_location_text_view_text_color"
            android:textSize="@dimen/enable_location_activity_enable_your_location_text_view_text_size"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/where_are_you_locate_text_view"
            tools:layout_editor_absoluteX="0dp"
            tools:layout_editor_absoluteY="201dp" />

        <ImageView
            android:id="@+id/icon_empty_magnifying_glass_image_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="36dp"
            android:layout_marginBottom="29dp"
            android:src="@drawable/ic_illustration_location"
            app:layout_constraintBottom_toTopOf="@+id/where_are_you_locate_text_view"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/button_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="@dimen/enable_location_activity_button_constraint_layout_height"
        android:layout_marginStart="@dimen/enable_location_activity_button_constraint_layout_margin_start"
        android:layout_marginEnd="@dimen/enable_location_activity_button_constraint_layout_margin_end"
        android:layout_marginBottom="@dimen/enable_location_activity_button_constraint_layout_margin_bottom"
        app:layout_constraintBottom_toTopOf="@+id/group3_constraint_layout"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:layout_editor_absoluteX="24dp"
        tools:layout_editor_absoluteY="444dp">

        <android.widget.Button
            android:id="@+id/button_enable_location"
            style="?android:attr/borderlessButtonStyle"
            android:theme="@style/BottomCTAButton"
            android:layout_width="0dp"
            android:layout_height="@dimen/enable_location_activity_button_large_active_button_height"
            android:background="@drawable/enable_location_activity_button_large_active_button_selector"
            android:text="@string/enable_location_activity_button_large_active_button_text"
            android:textColor="@color/enable_location_activity_button_large_active_button_text_color"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1.0" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group3_constraint_layout"
        android:layout_width="@dimen/lottie_imageview_width"
        android:layout_height="@dimen/lottie_imageview_height"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:layout_editor_absoluteX="-184dp"
        tools:layout_editor_absoluteY="514dp">

        <com.airbnb.lottie.LottieAnimationView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/clouds" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>