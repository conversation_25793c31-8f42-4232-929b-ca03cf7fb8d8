<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group4_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/tip_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="48dp"
            android:scaleType="centerCrop"
            android:layout_marginStart="36dp"
            android:layout_marginEnd="36dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tip_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="24dp"
            android:layout_marginTop="32dp"
            android:fontFamily="@font/font_nunitosans_extrabold"
            android:gravity="center"
            android:lineSpacingMultiplier="1.29"
            android:textColor="@color/height_celebration3_activity_is_agreat_heig_text_view_text_color"
            android:textSize="20sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tip_image" />

        <TextView
            android:id="@+id/tip_desc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/height_celebration3_activity_let_sproceed_with_stext_view_margin_top"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="24dp"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="start"
            android:lineSpacingMultiplier="1.09"
            android:textColor="@color/grey2"
            android:textSize="16sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tip_title" />

        <TextView
            android:id="@+id/quick_tips_count_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:layout_marginStart="24dp"
            android:fontFamily="@font/font_nunitosans_extrabold"
            android:gravity="center"
            android:lineSpacingMultiplier="1.18"
            android:text="@string/quick_tips_count"
            android:textColorLink="@color/grey3"
            android:textColor="@color/grey3"
            android:textSize="14sp"
            android:layout_marginTop="36dp"
            android:layout_marginBottom="58dp"
            app:layout_constraintVertical_bias="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tip_desc" />

        <android.widget.Button
            android:id="@+id/action_button"
            style="?android:attr/borderlessButtonStyle"
            android:theme="@style/BottomCTAButton"
            android:layout_width="0dp"
            android:layout_height="@dimen/age_restriction_activity_button_large_active_button_height"
            android:layout_marginEnd="24dp"
            android:background="@drawable/height_celebration3_activity_button_large_active_button_background"
            android:text="Next"
            android:textColor="@color/height_celebration3_activity_button_large_active_button_text_color"
            android:layout_marginTop="36dp"
            android:layout_marginBottom="48dp"
            app:layout_constraintVertical_bias="1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tip_desc"
            app:layout_constraintEnd_toEndOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>