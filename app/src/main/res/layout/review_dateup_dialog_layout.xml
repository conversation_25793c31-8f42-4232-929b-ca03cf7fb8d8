<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageButton
        android:id="@+id/close_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="18dp"
        android:background="@null"
        android:elevation="2dp"
        android:padding="10dp"
        android:scaleType="center"
        android:src="@drawable/ic_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/grey3" />

    <ImageView
        android:id="@+id/love_it_image_review"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="36dp"
        android:layout_marginEnd="36dp"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_heart_face"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/close_button" />

    <ImageView
        android:id="@+id/stars_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="36dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="36dp"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_stars_review"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/love_it_image_review" />

    <TextView
        android:id="@+id/review_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:fontFamily="@font/font_nunitosans_extrabold"
        android:gravity="center"
        android:lineSpacingMultiplier="1.29"
        android:text="@string/review_title"
        android:textColor="@color/grey1"
        android:textSize="20sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/stars_image" />

    <TextView
        android:id="@+id/review_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:fontFamily="@font/font_nunitosans_regular"
        android:gravity="center"
        android:lineSpacingMultiplier="1.09"
        android:text="@string/review_desc"
        android:textColor="@color/grey2"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/review_title" />

    <android.widget.Button
        android:id="@+id/write_review_button"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="0dp"
        android:layout_height="@dimen/age_restriction_activity_button_large_active_button_height"
        android:layout_marginStart="72dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="72dp"
        android:layout_marginBottom="32dp"
        android:background="@drawable/height_celebration3_activity_button_large_active_button_background"
        android:text="Write a review"
        android:textColor="@color/height_celebration3_activity_button_large_active_button_text_color"
        android:theme="@style/BottomCTAButton"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/review_desc"
        app:layout_constraintVertical_bias="0.0" />

</androidx.constraintlayout.widget.ConstraintLayout>