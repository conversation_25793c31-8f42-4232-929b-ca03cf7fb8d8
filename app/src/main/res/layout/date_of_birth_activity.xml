<androidx.constraintlayout.widget.ConstraintLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/date_of_birth_activity_date_of_birth_constraint_layout_background_color">

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/button_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="wrap_content"
		android:layout_marginStart="@dimen/date_of_birth_activity_button_constraint_layout_margin_start"
		android:layout_marginTop="52dp"
		android:layout_marginEnd="@dimen/date_of_birth_activity_button_constraint_layout_margin_end"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/group_constraint_layout">

		<TextView
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginStart="24dp"
			android:layout_marginEnd="24dp"
			android:layout_marginBottom="12dp"
			android:fontFamily="@font/font_nunitosans_regular"
			android:gravity="center"
			android:lineSpacingMultiplier="1"
			android:text="(This cannot be changed after account creation)"
			android:textColor="@color/grey2"
			android:textSize="12sp"
			app:layout_constraintStart_toStartOf="parent"
			app:layout_constraintEnd_toEndOf="parent"
			app:layout_constraintBottom_toTopOf="@+id/button_enable_location"/>

		<android.widget.Button
			android:id="@+id/button_enable_location"
			style="?android:attr/borderlessButtonStyle"
			android:theme="@style/BottomCTAButton"
			android:layout_width="0dp"
			android:layout_height="@dimen/date_of_birth_activity_button_large_active_button_height"
			android:background="@drawable/bottom_button_disabled_state"
			android:enabled="false"
			android:text="@string/date_of_birth_activity_button_large_active_button_text"
			android:textColor="@color/date_of_birth_activity_button_large_active_button_text_color"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="0dp"
			tools:layout_editor_absoluteY="0dp" />
	</androidx.constraintlayout.widget.ConstraintLayout>

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="@dimen/date_of_birth_activity_group_constraint_layout_height"
		android:layout_marginStart="@dimen/date_of_birth_activity_group_constraint_layout_margin_start"
		android:layout_marginTop="64dp"
		android:layout_marginEnd="@dimen/date_of_birth_activity_group_constraint_layout_margin_end"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toTopOf="parent">

		<TextView
            android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginEnd="@dimen/date_of_birth_activity_when_were_you_born_text_view_margin_end"
			android:fontFamily="@font/font_nunitosans_extrabold"
			android:gravity="left"
			android:lineSpacingMultiplier="1"
			android:text="@string/date_of_birth_activity_when_were_you_born_text_view_text"
			android:textColor="@color/date_of_birth_activity_when_were_you_born_text_view_text_color"
			android:textSize="@dimen/date_of_birth_activity_when_were_you_born_text_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="0dp"
			tools:layout_editor_absoluteY="0dp" />

		<androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="@dimen/date_of_birth_activity_view_constraint_layout_width"
			android:layout_height="@dimen/date_of_birth_activity_view_constraint_layout_height"
			android:layout_marginStart="@dimen/date_of_birth_activity_view_constraint_layout_margin_start"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			tools:layout_editor_absoluteX="4dp"
			tools:layout_editor_absoluteY="55dp">

			<EditText
				android:id="@+id/mm_edit_text"
				android:layout_width="wrap_content"
				android:layout_height="wrap_content"
				android:layout_marginStart="@dimen/date_of_birth_activity_mm_edit_text_margin_start"
				android:layout_marginBottom="@dimen/date_of_birth_activity_mm_edit_text_margin_bottom"
				android:background="@color/date_of_birth_activity_mm_edit_text_background_color"
				android:fontFamily="@font/font_nunitosans_regular"
				android:gravity="center"
				android:hint="@string/date_of_birth_activity_mm_edit_text_hint"
				android:inputType="number"
				android:lineSpacingMultiplier="1"
				android:maxLength="2"
				android:textColor="@color/date_of_birth_activity_mm_edit_text_text_color"
				android:textColorHint="@color/default_hint"
				android:textSize="@dimen/date_of_birth_activity_mm_edit_text_text_size"
				app:layout_constraintBottom_toTopOf="@+id/rectangle_constraint_layout"
				app:layout_constraintLeft_toLeftOf="parent" />

			<androidx.constraintlayout.widget.ConstraintLayout
				android:id="@+id/rectangle_constraint_layout"
				android:layout_width="@dimen/date_of_birth_activity_rectangle_constraint_layout_width"
				android:layout_height="@dimen/date_of_birth_activity_rectangle_constraint_layout_height"
				android:background="@color/date_of_birth_activity_rectangle_constraint_layout_background_color"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintLeft_toLeftOf="parent"
				tools:layout_editor_absoluteX="0dp"
				tools:layout_editor_absoluteY="26dp" />

			<EditText
				android:id="@+id/dd_edit_text"
				android:layout_width="wrap_content"
				android:layout_height="wrap_content"
				android:layout_marginStart="@dimen/date_of_birth_activity_dd_edit_text_margin_start"
				android:layout_marginBottom="@dimen/date_of_birth_activity_dd_edit_text_margin_bottom"
				android:background="@color/date_of_birth_activity_dd_edit_text_background_color"
				android:fontFamily="@font/font_nunitosans_regular"
				android:gravity="center"
				android:hint="@string/date_of_birth_activity_dd_edit_text_hint"
				android:inputType="number"
				android:lineSpacingMultiplier="1"
				android:maxLength="2"
				android:textColor="@color/date_of_birth_activity_dd_edit_text_text_color"
				android:textColorHint="@color/default_hint"
				android:textSize="@dimen/date_of_birth_activity_dd_edit_text_text_size"
				app:layout_constraintBottom_toTopOf="@+id/user_second_ice_breaker_photo_image_view"
				app:layout_constraintLeft_toRightOf="@+id/line2_image_view" />

			<androidx.constraintlayout.widget.ConstraintLayout
				android:id="@+id/user_second_ice_breaker_photo_image_view"
				android:layout_width="@dimen/date_of_birth_activity_rectangle_copy_constraint_layout_width"
				android:layout_height="@dimen/date_of_birth_activity_rectangle_copy_constraint_layout_height"
				android:layout_marginStart="@dimen/date_of_birth_activity_rectangle_copy_constraint_layout_margin_start"
				android:background="@color/date_of_birth_activity_rectangle_copy_constraint_layout_background_color"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintLeft_toRightOf="@+id/line2_image_view"
				tools:layout_editor_absoluteX="65dp"
				tools:layout_editor_absoluteY="26dp" />

			<ImageView
				android:id="@+id/line2_image_view"
				android:layout_width="wrap_content"
				android:layout_height="wrap_content"
				android:layout_marginStart="@dimen/date_of_birth_activity_line2_image_view_margin_start"
				app:srcCompat="@drawable/ic_dobslash"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintLeft_toRightOf="@+id/rectangle_constraint_layout"
				tools:layout_editor_absoluteX="42dp"
				tools:layout_editor_absoluteY="2dp" />

			<EditText
				android:id="@+id/yyyy_edit_text"
				android:layout_width="wrap_content"
				android:layout_height="wrap_content"
				android:layout_marginEnd="@dimen/date_of_birth_activity_yyyy_edit_text_margin_end"
				android:layout_marginBottom="@dimen/date_of_birth_activity_yyyy_edit_text_margin_bottom"
				android:background="@color/date_of_birth_activity_yyyy_edit_text_background_color"
				android:fontFamily="@font/font_nunitosans_regular"
				android:gravity="center"
				android:hint="@string/date_of_birth_activity_yyyy_edit_text_hint"
				android:inputType="number"
				android:lineSpacingMultiplier="1"
				android:maxLength="4"
				android:textColor="@color/date_of_birth_activity_yyyy_edit_text_text_color"
				android:textColorHint="@color/default_hint"
				android:textSize="@dimen/date_of_birth_activity_yyyy_edit_text_text_size"
				app:layout_constraintBottom_toTopOf="@+id/rectangle_copy2_constraint_layout"
				app:layout_constraintRight_toRightOf="parent" />

			<androidx.constraintlayout.widget.ConstraintLayout
				android:id="@+id/rectangle_copy2_constraint_layout"
				android:layout_width="@dimen/date_of_birth_activity_rectangle_copy2_constraint_layout_width"
				android:layout_height="@dimen/date_of_birth_activity_rectangle_copy2_constraint_layout_height"
				android:background="@color/date_of_birth_activity_rectangle_copy2_constraint_layout_background_color"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintRight_toRightOf="parent"
				tools:layout_editor_absoluteX="131dp"
				tools:layout_editor_absoluteY="26dp" />

			<ImageView
                android:layout_width="wrap_content"
				android:layout_height="wrap_content"
				android:layout_marginStart="@dimen/date_of_birth_activity_line2_copy_image_view_margin_start"
				app:srcCompat="@drawable/ic_dobslash"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintLeft_toRightOf="@+id/user_second_ice_breaker_photo_image_view"
				tools:layout_editor_absoluteX="110dp"
				tools:layout_editor_absoluteY="2dp" />
		</androidx.constraintlayout.widget.ConstraintLayout>
	</androidx.constraintlayout.widget.ConstraintLayout>

	<androidx.appcompat.widget.Toolbar
		android:id="@+id/toolbar"
		android:layout_width="0dp"
		android:layout_height="wrap_content"
		android:background="@color/date_of_birth_activity_toolbar_background_color"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>