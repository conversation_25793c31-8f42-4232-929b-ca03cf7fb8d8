<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.GuestHeightPreferenceActivity"
    android:background="@color/height_preference_men_activity_height_preference_men_constraint_layout_background_color">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group5_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="@dimen/height_preference_men_activity_group5_constraint_layout_height"
        android:layout_marginLeft="@dimen/height_preference_men_activity_group5_constraint_layout_margin_start"
        android:layout_marginRight="@dimen/height_preference_men_activity_group5_constraint_layout_margin_end"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="24dp"
        tools:layout_editor_absoluteY="88dp">

        <TextView
            android:id="@+id/guest_height_pref_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="35dp"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="center"
            android:lineSpacingMultiplier="1.44"
            android:text="@string/height_preference_men_activity_to64_text_view_text"
            android:textColor="@color/height_preference_men_activity_to64_text_view_text_color"
            android:textSize="@dimen/height_preference_men_activity_to64_text_view_text_size"
            app:layout_constraintBottom_toTopOf="@+id/guest_height_pref_seekbar"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/group4_constraint_layout" />

        <io.apptik.widget.MultiSlider
            android:id="@+id/guest_height_pref_seekbar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="145dp"
            android:thumb="@drawable/custom_thumb"
            android:thumbTint="@color/preferences_men_secopy2supernova_activity_group_four_seek_bar_thumb_tint"
            app:rangeColor="@color/preferences_men_secopy2supernova_activity_group_four_seek_bar_progress_tint"
            app:thumbColor="@color/preferences_men_secopy2supernova_activity_group_four_seek_bar_progress_tint"
            app:drawThumbsApart="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/group4_constraint_layout"
            android:layout_width="0dp"
            android:layout_height="@dimen/height_preference_men_activity_group4_constraint_layout_height"
            android:layout_marginTop="@dimen/height_preference_men_activity_group4_constraint_layout_margin_top"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_editor_absoluteX="0dp"
            tools:layout_editor_absoluteY="16dp">

            <TextView
                android:layout_width="@dimen/height_preference_men_activity_how_tall_should_your_text_view_width"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_nunitosans_extrabold"
                android:gravity="center"
                android:lineSpacingMultiplier="1"
                android:text="@string/height_preference_men_activity_how_tall_should_your_text_view_text"
                android:textColor="@color/height_preference_men_activity_how_tall_should_your_text_view_text_color"
                android:textSize="@dimen/height_preference_men_activity_how_tall_should_your_text_view_text_size"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <android.widget.Button
            android:id="@+id/button_enable_location"
            style="?android:attr/borderlessButtonStyle"
            android:theme="@style/BottomCTAButton"
            android:layout_width="0dp"
            android:layout_height="@dimen/height_preference_men_activity_button_large_active_button_height"
            android:background="@drawable/height_preference_men_activity_button_large_active_button_selector"
            android:text="@string/height_preference_men_activity_button_large_active_button_text"
            android:textColor="@color/height_preference_men_activity_button_large_active_button_text_color"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/guest_height_pref_seekbar" />

    </androidx.constraintlayout.widget.ConstraintLayout>



    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group6_constraint_layout"
        android:layout_width="@dimen/lottie_imageview_width"
        android:layout_height="@dimen/lottie_imageview_height"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <com.airbnb.lottie.LottieAnimationView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/clouds" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>