<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <nl.dionsegijn.konfetti.KonfettiView
        android:id="@+id/viewKonfetti"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:elevation="5dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group4_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="72dp"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintBottom_toTopOf="@+id/button_large_active_button"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/height_celebration_members_image_view">

        <TextView
            android:id="@+id/congrats_you_re_amtext_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="12dp"
            android:fontFamily="@font/font_nunitosans_extrabold"
            android:gravity="center"
            android:lineSpacingMultiplier="1.29"
            android:text="@string/height_celebration1_activity_congrats_you_re_amtext_view_text"
            android:textColor="@color/height_celebration1_activity_congrats_you_re_amtext_view_text_color"
            android:textSize="22sp"
            app:layout_constraintBottom_toTopOf="@+id/men_over61_and_wo_text_view"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/men_over61_and_wo_text_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="24dp"
            android:layout_marginBottom="72dp"
            android:layout_marginTop="6dp"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="center"
            android:lineSpacingMultiplier="1.09"
            app:layout_constraintVertical_bias="0"
            android:textColor="@color/height_celebration1_activity_men_over61_and_wo_text_view_text_color"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/congrats_you_re_amtext_view"
            tools:text="@string/height_celebration1_activity_men_over61_and_wo_text_view_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/height_celebration_members_image_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="72dp"
        android:layout_marginBottom="12dp"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_tall_man_with_friends"
        app:layout_constraintVertical_bias="1"
        app:layout_constraintBottom_toTopOf="@+id/group4_constraint_layout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <android.widget.Button
        android:id="@+id/button_large_active_button"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="0dp"
        android:layout_height="@dimen/height_celebration1_activity_button_large_active_button_height"
        android:layout_marginStart="@dimen/height_celebration1_activity_button_large_active_button_margin_start"
        android:layout_marginEnd="@dimen/height_celebration1_activity_button_large_active_button_margin_start"
        android:layout_marginBottom="24dp"
        android:background="@drawable/height_celebration1_activity_button_large_active_button_ripple"
        android:text="@string/height_celebration1_activity_button_large_active_button_text"
        android:textColor="@color/height_celebration1_activity_button_large_active_button_text_color"
        android:theme="@style/BottomCTAButton"
        app:layout_constraintTop_toBottomOf="@+id/group4_constraint_layout"
        app:layout_constraintBottom_toTopOf="@+id/group7_constraint_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintVertical_bias="1" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group7_constraint_layout"
        android:layout_width="@dimen/lottie_imageview_width"
        android:layout_height="@dimen/lottie_imageview_height"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <com.airbnb.lottie.LottieAnimationView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/clouds" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>