<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@id/dialogRootLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@id/dialogContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground">

        <ImageView
            android:id="@id/dialogAvatar"
            android:layout_width="@dimen/dialog_avatar_width"
            android:layout_height="@dimen/dialog_avatar_height"
            android:layout_margin="16dp"/>

        <TextView
            android:id="@id/dialogName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="27dp"
            android:layout_toEndOf="@id/dialogAvatar"
            android:layout_toStartOf="@id/dialogDate"
            android:ellipsize="end"
            android:textAllCaps="false"
            android:textSize="16sp"
            android:textColor="@color/grey1"
            android:fontFamily="@font/font_nunitosans_bold"
            android:includeFontPadding="false"
            android:maxLines="1"/>

        <TextView
            android:id="@id/dialogDate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_marginEnd="16dp"
            android:layout_marginRight="16dp"
            android:fontFamily="@font/font_nunitosans_regular"
            android:textSize="12sp"
            android:layout_marginTop="27dp"
            android:ellipsize="end"
            android:maxLines="1"/>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/dialogName"
            android:layout_marginEnd="16dp"
            android:layout_marginRight="16dp"
            android:layout_marginTop="7dp"
            android:layout_toEndOf="@id/dialogAvatar"
            android:layout_toStartOf="@+id/dialogUnreadBubble">

            <com.stfalcon.chatkit.utils.ShapeImageView
                android:id="@id/dialogLastMessageUserAvatar"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="7dp"
                android:layout_marginRight="7dp"/>


            <TextView
                android:id="@id/dialogLastMessage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginEnd="36dp"
                android:layout_toEndOf="@id/dialogLastMessageUserAvatar"
                android:layout_toRightOf="@id/dialogLastMessageUserAvatar"
                android:fontFamily="@font/font_nunitosans_regular"
                android:ellipsize="end"
                android:gravity="top"
                android:maxLines="1"/>

        </RelativeLayout>

        <TextView
            android:id="@id/dialogUnreadBubble"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_below="@id/dialogDate"
            android:layout_marginEnd="24dp"
            android:layout_marginRight="24dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/bubble_circle"
            android:ellipsize="end"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="center"
            android:lines="1"/>

        <ImageView
            android:id="@+id/matchStatus"
            android:layout_width="32dp"
            android:layout_height="24dp"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_below="@id/dialogDate"
            android:layout_marginEnd="12dp"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:src="@drawable/ic_match_status"/>

        <!--<FrameLayout-->
            <!--android:id="@id/dialogDividerContainer"-->
            <!--android:layout_width="wrap_content"-->
            <!--android:layout_height="wrap_content"-->
            <!--android:layout_alignParentBottom="true"-->
            <!--android:layout_marginTop="16dp">-->

            <!--<View-->
                <!--android:id="@id/dialogDivider"-->
                <!--android:layout_width="match_parent"-->
                <!--android:layout_height="1dp"-->
                <!--android:background="@color/dialog_divider"/>-->

        <!--</FrameLayout>-->

    </RelativeLayout>

</FrameLayout>
