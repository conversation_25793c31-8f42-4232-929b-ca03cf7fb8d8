<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
	android:layout_height="match_parent">

	<nl.dionsegijn.konfetti.KonfettiView
		android:id="@+id/viewKonfetti"
		android:elevation="5dp"
		android:layout_width="0dp"
		android:layout_height="0dp"
		app:layout_constraintTop_toTopOf="parent"
		app:layout_constraintStart_toStartOf="parent"
		app:layout_constraintEnd_toEndOf="parent"
		app:layout_constraintBottom_toBottomOf="parent"/>

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group4_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="wrap_content"
		android:layout_marginTop="12dp"
		android:layout_marginBottom="72dp"
		app:layout_constraintVertical_bias="0"
		app:layout_constraintBottom_toTopOf="@+id/button_large_active_button"
		app:layout_constraintStart_toStartOf="parent"
		app:layout_constraintEnd_toEndOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/illustration5_hand_image_view">

		<TextView
			android:id="@+id/is_agreat_heig_text_view"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginStart="12dp"
			android:layout_marginTop="@dimen/height_celebration3_activity_is_agreat_heig_text_view_margin_top"
			android:layout_marginEnd="12dp"
			android:fontFamily="@font/font_nunitosans_extrabold"
			android:gravity="center"
			android:lineSpacingMultiplier="1.29"
			android:text="@string/height_celebration3_activity_is_agreat_heig_text_view_text"
			android:textColor="@color/height_celebration3_activity_is_agreat_heig_text_view_text_color"
			android:textSize="22sp"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent" />

		<TextView
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginTop="@dimen/height_celebration3_activity_let_sproceed_with_stext_view_margin_top"
			android:layout_marginBottom="10dp"
			android:layout_marginStart="24dp"
			android:layout_marginEnd="24dp"
			android:fontFamily="@font/font_nunitosans_regular"
			android:gravity="center"
			android:lineSpacingMultiplier="1.09"
			android:text="@string/height_celebration3_activity_let_sproceed_with_stext_view_text"
			android:textColor="@color/height_celebration3_activity_let_sproceed_with_stext_view_text_color"
			android:textSize="16sp"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/is_agreat_heig_text_view" />
	</androidx.constraintlayout.widget.ConstraintLayout>

	<ImageView
		android:id="@+id/illustration5_hand_image_view"
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:layout_marginTop="72dp"
		android:layout_marginBottom="12dp"
		android:scaleType="centerCrop"
		android:src="@drawable/ic_illustration_hand"
		app:layout_constraintVertical_bias="1"
		app:layout_constraintBottom_toTopOf="@+id/group4_constraint_layout"
		app:layout_constraintStart_toStartOf="parent"
		app:layout_constraintEnd_toEndOf="parent"
		app:layout_constraintTop_toTopOf="parent" />

	<android.widget.Button
		android:id="@+id/button_large_active_button"
		style="?android:attr/borderlessButtonStyle"
		android:theme="@style/BottomCTAButton"
		android:layout_width="0dp"
		android:layout_height="@dimen/age_restriction_activity_button_large_active_button_height"
		android:layout_marginStart="@dimen/age_restriction_activity_button_large_active_button_margin_start"
		android:layout_marginEnd="@dimen/age_restriction_activity_button_large_active_button_margin_end"
		android:layout_marginBottom="32dp"
		android:background="@drawable/height_celebration3_activity_button_large_active_button_background"
		android:text="@string/height_celebration3_activity_button_large_active_button_text"
		android:textColor="@color/height_celebration3_activity_button_large_active_button_text_color"
		app:layout_constraintBottom_toTopOf="@+id/group7_constraint_layout"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent" />

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group7_constraint_layout"
		android:layout_width="@dimen/lottie_imageview_width"
		android:layout_height="@dimen/lottie_imageview_height"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent">

		<com.airbnb.lottie.LottieAnimationView
            android:layout_width="match_parent"
			android:layout_height="match_parent"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintEnd_toEndOf="parent"
			app:layout_constraintStart_toStartOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			app:lottie_autoPlay="true"
			app:lottie_loop="true"
			app:lottie_rawRes="@raw/clouds" />
	</androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>