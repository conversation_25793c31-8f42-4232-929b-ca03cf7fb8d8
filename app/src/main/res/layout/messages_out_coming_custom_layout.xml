<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:layout_marginLeft="16dp"
    android:layout_marginRight="16dp"
    android:layout_marginTop="8dp">

    <LinearLayout
        android:id="@id/bubble"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_marginLeft="40dp"
        android:layout_marginStart="40dp"
        android:orientation="vertical">

        <TextView
            android:id="@id/messageText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

    </LinearLayout>

    <TextView
        android:id="@id/messageTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:textColor="@color/grey3"
        android:fontFamily="@font/font_nunitosans_regular"
        android:layout_alignEnd="@id/bubble"
        android:layout_below="@id/bubble" />

</RelativeLayout>