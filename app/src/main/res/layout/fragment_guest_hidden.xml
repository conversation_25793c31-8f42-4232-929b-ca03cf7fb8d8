<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/guest_matchmaking2_activity_guest_matchmaking2_constraint_layout_background_color">

    <TextView
        android:id="@+id/should_we_show_you_gtext_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="48dp"
        android:layout_marginRight="48dp"
        android:layout_marginBottom="48dp"
        android:fontFamily="@font/font_nunitosans_regular"
        android:gravity="center"
        android:lineSpacingMultiplier="0.96"
        android:text="Guests are shorter individuals interested in dating-up."
        android:textColor="@color/guest_matchmaking2_activity_should_we_show_you_gtext_view_text_color"
        android:textSize="18sp"
        app:layout_constraintBottom_toTopOf="@+id/enable_guest_matching_button"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <android.widget.Button
        android:id="@+id/enable_guest_matching_button"
        style="?android:attr/borderlessButtonStyle"
        android:theme="@style/BottomCTAButton"
        android:layout_width="0dp"
        android:layout_height="@dimen/guest_matchmaking2_activity_button_large_active_button_height"
        android:layout_marginLeft="32dp"
        android:layout_marginRight="32dp"
        android:layout_marginBottom="60dp"
        android:background="@drawable/guest_matchmaking2_activity_button_large_active_button_ripple"
        android:text="Enable Guest Matchmaking"
        android:textColor="@color/guest_matchmaking2_activity_button_large_active_button_text_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <ImageView
        android:id="@+id/guest_hidden_image_view"
        android:layout_width="200dp"
        android:layout_height="200dp"
        android:layout_marginBottom="24dp"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toTopOf="@+id/like_text_view"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/like_text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="48dp"
        android:layout_marginEnd="48dp"
        android:layout_marginBottom="20dp"
        android:text="Guests Hidden"
        android:textColor="#25333D"
        android:textSize="@dimen/guest_matchmaking2_activity_button_large_active_button_text_size"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/should_we_show_you_gtext_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>