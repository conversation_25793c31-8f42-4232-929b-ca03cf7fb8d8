<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/scroll"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/subscription_title_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="24dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="24dp"
            android:layout_marginBottom="24dp"
            android:fontFamily="@font/nunitosans_bold"
            android:gravity="center"
            android:lineSpacingMultiplier="1.18"
            android:text="@string/subscription_upgrade_title"
            android:textColor="@color/black"
            android:textSize="18sp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <ImageButton
            android:id="@+id/close_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="18dp"
            android:background="@null"
            android:elevation="2dp"
            android:padding="10dp"
            android:scaleType="center"
            android:src="@drawable/ic_close"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="#111111" />

        <View
            android:id="@+id/view_divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            app:layout_constraintTop_toBottomOf="@+id/subscription_title_text"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="12dp"
            android:background="#F2F2F2" />

        <TextView
            android:id="@+id/offer_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="24dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="24dp"
            android:fontFamily="@font/nunitosans_bold"
            android:gravity="center"
            android:lineSpacingMultiplier="1.18"
            android:text="@string/limited_offer"
            android:textColor="@color/darkGold"
            android:textSize="14sp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@+id/view_divider"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_bottom_sheet"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="18dp"
            android:nestedScrollingEnabled="false"
            android:overScrollMode="never"
            app:fastScrollEnabled="false"
            app:layout_constraintTop_toBottomOf="@+id/view_divider"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <TextView
            android:id="@+id/restore_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="24dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="24dp"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="center"
            android:lineSpacingMultiplier="1.18"
            android:text="@string/subscription_restore_title"
            android:textColor="@color/images3_activity_please_upload_at_lea_text_view_text_color"
            android:textSize="14sp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@+id/recycler_view_bottom_sheet"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <TextView
            android:id="@+id/subscription_terms_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="24dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="24dp"
            android:layout_marginBottom="8dp"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="center"
            android:lineSpacingMultiplier="1.18"
            android:text="@string/subscription_terms"
            android:textColorLink="@color/grey3"
            android:textColor="@color/grey3"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/restore_text_view"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>