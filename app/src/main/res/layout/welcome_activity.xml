<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/welcome_activity_welcome_constraint_layout_background_color">

        <android.widget.Button
            android:id="@+id/button_large_active_button"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="@dimen/welcome_activity_button_large_active_button_height"
            android:layout_marginStart="@dimen/welcome_activity_button_large_active_button_margin_start"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="27dp"
            android:layout_marginBottom="24dp"
            android:background="@drawable/welcome_activity_button_large_active_button_ripple"
            android:text="@string/got_it"
            android:textColor="@color/welcome_activity_button_large_active_button_text_color"
            android:theme="@style/BottomCTAButton"
            app:layout_constraintBottom_toTopOf="@+id/group7_constraint_layout"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/how_it_work_3_desc"
            app:layout_constraintVertical_bias="1" />

        <TextView
            android:id="@+id/welcome_to_dateup_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="56dp"
            android:fontFamily="@font/font_nunitosans_extrabold"
            android:gravity="start"
            android:text="@string/how_it_works"
            android:textColor="@color/welcome_activity_welcome_to_dateup_text_view_text_color"
            android:textSize="32sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/dateup_is_acommunit_text_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="24dp"
            android:fontFamily="@font/font_nunitosans_bold"
            android:gravity="start"
            android:text="@string/how_it_works_point_1"
            android:textColor="@color/grey1"
            android:textSize="18sp"
            android:layout_marginTop="32dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintTop_toBottomOf="@+id/welcome_to_dateup_text_view"
            app:layout_constraintStart_toEndOf="@+id/group8_image_view" />

        <TextView
            android:id="@+id/how_it_work_1_desc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="24dp"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="start"
            android:text="@string/how_it_works_point_2_desc"
            android:textColor="@color/grey2"
            android:textSize="18sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@+id/group8_image_view"
            app:layout_constraintTop_toBottomOf="@+id/dateup_is_acommunit_text_view" />

        <TextView
            android:id="@+id/tall_people_on_dateu_text_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="@dimen/welcome_activity_tall_people_on_dateu_text_view_margin_top"
            android:layout_marginEnd="24dp"
            android:fontFamily="@font/font_nunitosans_bold"
            android:gravity="start"
            android:text="@string/how_it_works_point_2"
            android:textColor="@color/grey1"
            android:textSize="18sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@+id/group7_image_view"
            app:layout_constraintTop_toBottomOf="@+id/how_it_work_1_desc" />

        <TextView
            android:id="@+id/how_it_work_2_desc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="24dp"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="start"
            android:text="@string/how_it_works_point_2_desc"
            android:textColor="@color/grey2"
            android:textSize="18sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@+id/group7_image_view"
            app:layout_constraintTop_toBottomOf="@+id/tall_people_on_dateu_text_view" />

        <ImageView
            android:id="@+id/group8_image_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="32dp"
            android:layout_marginTop="4dp"
            android:scaleType="center"
            android:src="@drawable/ic_icon_number_1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@+id/dateup_is_acommunit_text_view" />

        <ImageView
            android:id="@+id/group7_image_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="32dp"
            android:layout_marginTop="4dp"
            android:scaleType="center"
            android:src="@drawable/ic_icon_number_2"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tall_people_on_dateu_text_view" />

        <ImageView
            android:id="@+id/number3_image_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="32dp"
            android:layout_marginTop="4dp"
            android:scaleType="center"
            android:src="@drawable/ic_icon_number3"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@+id/third_point" />

        <TextView
            android:id="@+id/third_point"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="@dimen/welcome_activity_tall_people_on_dateu_text_view_margin_top"
            android:layout_marginEnd="24dp"
            android:fontFamily="@font/font_nunitosans_bold"
            android:gravity="start"
            android:text="@string/how_it_works_point_3"
            android:textColor="@color/grey1"
            android:textSize="18sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@+id/group8_image_view"
            app:layout_constraintTop_toBottomOf="@+id/how_it_work_2_desc" />

        <TextView
            android:id="@+id/how_it_work_3_desc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="24dp"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="start"
            android:text="@string/how_it_works_point_3_desc"
            android:textColor="@color/grey2"
            android:textSize="18sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@+id/group8_image_view"
            app:layout_constraintTop_toBottomOf="@+id/third_point" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/group7_constraint_layout"
            android:layout_width="@dimen/lottie_imageview_width"
            android:layout_height="@dimen/lottie_imageview_height"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent">

            <com.airbnb.lottie.LottieAnimationView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:lottie_autoPlay="true"
                app:lottie_loop="true"
                app:lottie_rawRes="@raw/clouds" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>