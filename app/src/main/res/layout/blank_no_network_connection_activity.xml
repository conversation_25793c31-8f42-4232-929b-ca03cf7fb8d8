<androidx.constraintlayout.widget.ConstraintLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/blank_no_network_connection_activity_blank_no_network_connection_constraint_layout_background_color">

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group5_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="@dimen/blank_no_network_connection_activity_group5_constraint_layout_height"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toTopOf="parent"
		tools:layout_editor_absoluteX="0dp"
		tools:layout_editor_absoluteY="68dp">
	
		<androidx.constraintlayout.widget.ConstraintLayout
			android:id="@+id/group4_constraint_layout"
			android:layout_width="0dp"
			android:layout_height="@dimen/blank_no_network_connection_activity_group4_constraint_layout_height"
			android:layout_marginLeft="@dimen/blank_no_network_connection_activity_group4_constraint_layout_margin_start"
			android:layout_marginRight="@dimen/blank_no_network_connection_activity_group4_constraint_layout_margin_end"
			android:layout_marginTop="@dimen/blank_no_network_connection_activity_group4_constraint_layout_margin_top"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="24dp"
			tools:layout_editor_absoluteY="26dp">

			<ImageView
				android:id="@+id/icon_empty_magnifying_glass_image_view"
				android:layout_width="100dp"
				android:layout_height="120dp"
				android:layout_marginTop="10dp"
				android:src="@drawable/ic_blank_no_connection"
				app:layout_constraintLeft_toLeftOf="parent"
				app:layout_constraintRight_toRightOf="parent"
				app:layout_constraintTop_toTopOf="parent" />
		
			<androidx.constraintlayout.widget.ConstraintLayout
				android:id="@+id/group_constraint_layout"
				android:layout_width="0dp"
				android:layout_height="@dimen/blank_no_network_connection_activity_group_constraint_layout_height"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintLeft_toLeftOf="parent"
				app:layout_constraintRight_toRightOf="parent"
				tools:layout_editor_absoluteX="0dp"
				tools:layout_editor_absoluteY="140dp">
			
				<TextView
					android:id="@+id/no_network_connectio_text_view"
					android:layout_width="0dp"
					android:layout_height="wrap_content"
					android:layout_marginTop="@dimen/blank_no_network_connection_activity_no_network_connectio_text_view_margin_top"
					android:fontFamily="@font/font_nunitosans_extrabold"
					android:gravity="center"
					android:lineSpacingMultiplier="0.97"
					android:text="@string/blank_no_network_connection_activity_no_network_connectio_text_view_text"
					android:textColor="@color/blank_no_network_connection_activity_no_network_connectio_text_view_text_color"
					android:textSize="@dimen/blank_screen_title_text_size"
					app:layout_constraintLeft_toLeftOf="parent"
					app:layout_constraintRight_toRightOf="parent"
					app:layout_constraintTop_toTopOf="parent"
					tools:layout_editor_absoluteX="0dp"
					tools:layout_editor_absoluteY="-1dp"/>
			
				<TextView
					android:id="@+id/please_make_sure_you_text_view"
					android:layout_width="@dimen/blank_no_network_connection_activity_please_make_sure_you_text_view_width"
					android:layout_height="wrap_content"
					android:layout_marginTop="@dimen/blank_no_network_connection_activity_please_make_sure_you_text_view_margin_top"
					android:fontFamily="@font/font_nunitosans_regular"
					android:gravity="center"
					android:lineSpacingMultiplier="1.09"
					android:text="@string/blank_no_network_connection_activity_please_make_sure_you_text_view_text"
					android:textColor="@color/blank_no_network_connection_activity_please_make_sure_you_text_view_text_color"
					android:textSize="@dimen/blank_screen_desc_text_size"
					app:layout_constraintLeft_toLeftOf="parent"
					app:layout_constraintRight_toRightOf="parent"
					app:layout_constraintTop_toBottomOf="@+id/no_network_connectio_text_view"
					tools:layout_editor_absoluteX="0dp"
					tools:layout_editor_absoluteY="39dp"/>
			</androidx.constraintlayout.widget.ConstraintLayout>
		</androidx.constraintlayout.widget.ConstraintLayout>
	
		<androidx.constraintlayout.widget.ConstraintLayout
			android:id="@+id/button_constraint_layout"
			android:layout_width="@dimen/blank_no_network_connection_activity_button_constraint_layout_width"
			android:layout_height="@dimen/blank_no_network_connection_activity_button_constraint_layout_height"
			android:layout_marginBottom="@dimen/blank_no_network_connection_activity_button_constraint_layout_margin_bottom"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			tools:layout_editor_absoluteX="24dp"
			tools:layout_editor_absoluteY="314dp">

			<android.widget.Button
				android:id="@+id/button_enable_location"
				style="?android:attr/borderlessButtonStyle"
				android:theme="@style/BottomCTAButton"
				android:layout_width="0dp"
				android:layout_height="@dimen/blank_out_of_people_activity_button_large_active_button_height"
				android:background="@drawable/blank_out_of_people_activity_button_large_active_button_selector"
				android:text="@string/blank_no_network_connection_activity_button_large_active_button_text"
				android:textColor="@color/blank_out_of_people_activity_button_large_active_button_text_color"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintLeft_toLeftOf="parent"
				app:layout_constraintRight_toRightOf="parent"
				app:layout_constraintTop_toTopOf="parent"
				tools:layout_editor_absoluteX="0dp"
				tools:layout_editor_absoluteY="0dp"/>

		</androidx.constraintlayout.widget.ConstraintLayout>
	</androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>