<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/layout_item_bottom_sheet"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <android.widget.Button
        android:id="@+id/button_large_active_button"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="0dp"
        android:layout_height="@dimen/guest_matchmaking2_activity_button_large_active_button_height"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:layout_marginBottom="12dp"
        android:background="@drawable/guest_matchmaking2_activity_button_large_active_button_ripple"
        android:textColor="@color/guest_matchmaking2_activity_button_large_active_button_text_color"
        android:textSize="16sp"
        android:theme="@style/BottomCTAButton"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
