<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/guest_matchmaking2_activity_guest_matchmaking2_constraint_layout_background_color">

    <TextView
        android:id="@+id/should_we_show_you_gtext_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="24dp"
        android:layout_marginTop="12dp"
        android:layout_marginRight="24dp"
        android:fontFamily="@font/font_nunitosans_regular"
        android:gravity="center"
        android:lineSpacingMultiplier="0.96"
        android:text="Help get this convo off the ground by sending the first message."
        android:textColor="@color/guest_matchmaking2_activity_should_we_show_you_gtext_view_text_color"
        android:textSize="18sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/like_text_view" />

    <android.widget.Button
        android:id="@+id/keep_browsing_button"
        style="?android:attr/borderlessButtonStyle"
        android:theme="@style/BottomCTAButton"
        android:layout_width="0dp"
        android:layout_height="@dimen/guest_matchmaking2_activity_button_large_active_button_height"
        android:layout_marginStart="@dimen/guest_matchmaking2_activity_button_large_active_button_margin_start"
        android:layout_marginEnd="@dimen/guest_matchmaking2_activity_button_large_active_button_margin_end"
        android:layout_marginBottom="16dp"
        android:background="@drawable/guest_matchmaking2_activity_button_large_active_button_ripple"
        android:text="Keep Browsing"
        android:textColor="@color/guest_matchmaking2_activity_button_large_active_button_text_color"
        app:layout_constraintBottom_toTopOf="@+id/group7_constraint_layout"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <android.widget.Button
        android:id="@+id/send_message_button"
        style="?android:attr/borderlessButtonStyle"
        android:theme="@style/BottomCTAButton"
        android:layout_width="0dp"
        android:layout_height="@dimen/guest_matchmaking2_activity_button_large_active_two_button_height"
        android:layout_marginBottom="@dimen/guest_matchmaking2_activity_button_large_active_two_button_margin_bottom"
        android:layout_marginStart="@dimen/guest_matchmaking2_activity_button_large_active_two_button_margin_start"
        android:layout_marginEnd="@dimen/guest_matchmaking2_activity_button_large_active_two_button_margin_end"
        android:background="@drawable/guest_matchmaking2_activity_button_large_active_two_button_ripple"
        android:text="Send Message"
        android:textColor="@color/guest_matchmaking2_activity_button_large_active_two_button_text_color"
        app:layout_constraintBottom_toTopOf="@+id/keep_browsing_button"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:layout_editor_absoluteX="24dp"
        tools:layout_editor_absoluteY="372dp"/>

    <ImageView
        android:id="@+id/user_profile_image_view"
        android:layout_width="155dp"
        android:layout_height="155dp"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="12dp"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toTopOf="@+id/should_we_show_you_gtext_view"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group7_constraint_layout"
        android:layout_width="@dimen/lottie_imageview_width"
        android:layout_height="@dimen/lottie_imageview_height"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <com.airbnb.lottie.LottieAnimationView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/clouds" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/like_text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="48dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="48dp"
        android:textColor="#25333D"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/user_profile_image_view" />
</androidx.constraintlayout.widget.ConstraintLayout>