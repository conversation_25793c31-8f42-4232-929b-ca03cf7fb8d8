<androidx.constraintlayout.widget.ConstraintLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/same_sex_shorter_activity_same_sex_shorter_constraint_layout_background_color">

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group5_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="@dimen/same_sex_shorter_activity_group5_constraint_layout_height"
		android:layout_marginLeft="@dimen/same_sex_shorter_activity_group5_constraint_layout_margin_start"
		android:layout_marginRight="@dimen/same_sex_shorter_activity_group5_constraint_layout_margin_end"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toTopOf="parent"
		tools:layout_editor_absoluteX="24dp"
		tools:layout_editor_absoluteY="104dp">

		<androidx.constraintlayout.widget.ConstraintLayout
			android:id="@+id/bg_constraint_layout"
			android:layout_width="0dp"
			android:layout_height="@dimen/same_sex_shorter_activity_bg_constraint_layout_height"
			android:alpha="0"
			android:background="@color/same_sex_shorter_activity_bg_constraint_layout_background_color"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="0dp"
			tools:layout_editor_absoluteY="3dp"/>

		<SeekBar
			android:id="@+id/slider_single_copy_seek_bar"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginBottom="200dp"
			android:theme="@style/SameSexShorterActivitySliderSingleCopySeekBarTheme"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/bg_constraint_layout" />

		<TextView
			android:id="@+id/how_much_shorter_sho_text_view"
			android:layout_width="@dimen/same_sex_shorter_activity_how_much_shorter_sho_text_view_width"
			android:layout_height="wrap_content"
			android:fontFamily="@font/font_nunitosans_extrabold"
			android:gravity="left"
			android:lineSpacingMultiplier="1"
			android:text="@string/same_sex_taller_activity_how_much_taller_shou_text_view_text"
			android:textColor="@color/same_sex_shorter_activity_how_much_shorter_sho_text_view_text_color"
			android:textSize="@dimen/same_sex_shorter_activity_how_much_shorter_sho_text_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="0dp"
			tools:layout_editor_absoluteY="0dp"/>

		<TextView
			android:id="@+id/and_above_text_view"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginTop="@dimen/same_sex_shorter_activity_and_under_text_view_margin_top"
			android:layout_marginBottom="10dp"
			android:fontFamily="@font/font_nunitosans_regular"
			android:gravity="center"
			android:lineSpacingMultiplier="1.44"
			android:text="@string/same_sex_taller_activity_and_up_text_view_text"
			android:textColor="@color/same_sex_shorter_activity_and_under_text_view_text_color"
			android:textSize="@dimen/same_sex_shorter_activity_and_under_text_view_text_size"
			app:layout_constraintBottom_toTopOf="@+id/slider_single_copy_seek_bar"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/how_much_shorter_sho_text_view" />
	</androidx.constraintlayout.widget.ConstraintLayout>

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group3_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="297dp"
		android:layout_marginLeft="@dimen/sex_preference_activity_group3_constraint_layout_margin_start"
		android:layout_marginTop="65dp"
		android:layout_marginRight="@dimen/sex_preference_activity_group3_constraint_layout_margin_end"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintHorizontal_bias="0.501"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/group5_constraint_layout">

		<android.widget.Button
			android:id="@+id/button_enable_location"
			style="?android:attr/borderlessButtonStyle"
			android:theme="@style/BottomCTAButton"
			android:layout_width="0dp"
			android:layout_height="@dimen/sex_preference_activity_button_large_active_button_height"
			android:layout_marginStart="@dimen/intro1_activity_button_constraint_layout_margin_start"
			android:layout_marginTop="10dp"
			android:textAllCaps="false"
			android:layout_marginEnd="@dimen/intro1_activity_button_constraint_layout_margin_end"
			android:background="@drawable/sex_preference_activity_button_large_active_button_selector"
			android:text="@string/sex_preference_activity_button_large_active_button_text"
			android:textColor="@color/sex_preference_activity_button_large_active_button_text_color"
			app:layout_constraintBottom_toTopOf="@+id/group2_image_view"
			app:layout_constraintHorizontal_bias="0.0"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			app:layout_constraintVertical_bias="0.0" />

		<ImageView
			android:id="@+id/group2_image_view"
			android:layout_width="0dp"
			android:layout_height="318dp"
			android:layout_marginBottom="30dp"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintHorizontal_bias="0.0"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			app:layout_constraintVertical_bias="1.0" />

		<ImageView
            android:layout_width="wrap_content"
			android:layout_height="78dp"
			android:layout_marginTop="100dp"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintTop_toTopOf="parent" />
	</androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>