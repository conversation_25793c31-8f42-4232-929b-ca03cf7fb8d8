package com.dateup.android.fragment

import android.os.Bundle
import android.text.SpannableString
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.widget.SwitchCompat
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentTransaction
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.cloudFunctions.UserPreferencesUpdatedFunc
import com.dateup.android.cloudFunctions.UserPreferencesUpdatedFunc.makeUserPreferencesUpdatedAPICall
import com.dateup.android.extensions.getDoubleValue
import com.dateup.android.extensions.getIntValue
import com.dateup.android.extensions.launchModeWithSingleTop
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.firebase.FirebaseRetrieveSingleUserListenerInterfaceFirestore
import com.dateup.android.models.GenderType
import com.dateup.android.models.InterestedInGender
import com.dateup.android.models.User
import com.dateup.android.models.UserObject
import com.dateup.android.paidVersion.changeLocation.TeleportFragment
import com.dateup.android.ui.browseProfiles.BrowseProfilesActivity
import com.dateup.android.utils.Constants
import com.dateup.android.utils.Constants.PREFERENCES_FRAGMENT_ARG1
import com.dateup.android.utils.Constants.noLimit
import com.dateup.android.utils.Constants.sliderMaxAge
import com.dateup.android.utils.Constants.sliderMaxHeightForMen
import com.dateup.android.utils.Constants.sliderMaxHeightForWomen
import com.dateup.android.utils.Utils
import io.apptik.widget.MultiSlider
import timber.log.Timber

class PreferencesFragment : Fragment() {

    private var argumentValue: String? = null

    private var minMatchHeight: Double? = Constants.defaultMinHeightInInchForPrefsInterestedInMen
    private var maxMatchHeight: Double? = Constants.defaultMaxHeightInInchForPrefsInterestedInMen

    private var minGuestHeight: Double? = Constants.defaultMinGuestsHeightInInchForPrefsInterestedInMen
    private var maxGuestHeight: Double? = Constants.defaultMaxGuestsHeightInInchForPrefsInterestedInMen

    var firebaseDatabaseReference = activity?.let { FirebaseDatabaseUtil(it) }

    private lateinit var preferenceFragmentLayout: View

    private var mainUserId = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        arguments?.let {
            argumentValue = it.getString(PREFERENCES_FRAGMENT_ARG1)
        }

        settingsSettingsFragment = SettingsSettingsFragment()
        settingsSexPreferencesFragment = SettingsSexPreferenceFragment()
        teleportFragment = TeleportFragment()

        activity?.let {
            mainUserId = AccountPreferences.getInstance(it).getStringValue(Constants.firebaseUserId, "")
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        preferenceFragmentLayout = inflater.inflate(R.layout.fragment_preferences, container, false)

        return preferenceFragmentLayout
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        init(view)
    }

    private lateinit var ageRangeSeekBar: MultiSlider
    private lateinit var titleTextView: TextView
    private lateinit var valueTextView: TextView
    private lateinit var distanceSeekBar: MultiSlider
    private lateinit var sexTextView: TextView
    private lateinit var valueTwoTextView: TextView
    private lateinit var guestHeightSeekBar: MultiSlider
    private lateinit var titleTwoTextView: TextView
    private lateinit var valueThreeTextView: TextView
    private lateinit var sexPreferenceTextView: TextView
    private lateinit var sexPreferenceTwoTextView: TextView
    private lateinit var sexPreferenceThreeTextView: TextView
    private lateinit var sexPreferenceFourTextView: TextView
    private lateinit var iMinterestedInTextView: TextView
    private lateinit var menTextView: TextView
    private lateinit var guestMatchmakingTextView: TextView
    private lateinit var showGuestsSwitch: SwitchCompat
    private lateinit var guestsAreMenBelowTextView: TextView
    private lateinit var sexTwoTextView: TextView
    private lateinit var valueFourTextView: TextView
    private lateinit var titleThreeTextView: TextView
    private lateinit var rightTextView: TextView
    private lateinit var memberHeightSeekbar: MultiSlider

    private lateinit var settingsSettingsFragment: SettingsSettingsFragment
    private lateinit var headerLeftBackImageView: ImageView

    private lateinit var interestedInConstraintLayout: ConstraintLayout
    private lateinit var guestDividerConstraintLayout: ConstraintLayout
    private lateinit var teleportConstraintLayout: ConstraintLayout

    private lateinit var settingsSexPreferencesFragment: SettingsSexPreferenceFragment

    private lateinit var teleportFragment: TeleportFragment
    private lateinit var teleportedToTextView: TextView
    private lateinit var locationDescTextView: TextView

    private lateinit var filtersDisabledTextView: TextView
    private lateinit var heightPreferencesLayoutHeader: ConstraintLayout
    private lateinit var heightFilterLayout: ConstraintLayout

    private lateinit var interestedIn: String

    private fun init(layout: View) {

        firebaseDatabaseReference = activity?.let { FirebaseDatabaseUtil(it) }

        // Configure Group component
        ageRangeSeekBar = layout.findViewById(R.id.age_rage_group_seek_bar)

        // Configure Title component
        titleTextView = layout.findViewById(R.id.title_text_view)

        // Configure Value component
        valueTextView = layout.findViewById(R.id.value_text_view)
        val valueTextViewText = SpannableString(context?.getString(R.string.preferences_men_secopy2supernova_activity_value_text_view_text))
        valueTextView.text = valueTextViewText

        // Configure Group component
        distanceSeekBar = layout.findViewById(R.id.distance_group_two_seek_bar)

        // Configure Sex component
        sexTextView = layout.findViewById(R.id.sex_text_view)

        // Configure Value component
        valueTwoTextView = layout.findViewById(R.id.value_two_text_view)
        val valueTwoTextViewText = SpannableString(context?.getString(R.string.preferences_men_secopy2supernova_activity_value_two_text_view_text, ""))
        valueTwoTextView.text = valueTwoTextViewText

        // Configure Group component
        guestHeightSeekBar = layout.findViewById(R.id.guest_heightgroup_three_seek_bar)

        memberHeightSeekbar = layout.findViewById(R.id.member_height_seek_bar)

        teleportedToTextView = layout.findViewById(R.id.teleported_textview)
        locationDescTextView = layout.findViewById(R.id.location_desc)

        // Configure Title component
        titleTwoTextView = layout.findViewById(R.id.title_two_text_view)

        // Configure Value component
        valueThreeTextView = layout.findViewById(R.id.value_three_text_view)
        val valueThreeTextViewText = SpannableString(context?.getString(R.string.preferences_men_secopy2supernova_activity_value_three_text_view_text))
        valueThreeTextView.text = valueThreeTextViewText

        // Configure SEX PREFerence component
        sexPreferenceTextView = layout.findViewById(R.id.sex_preference_text_view)
        val sexPreferenceTextViewText = SpannableString(context?.getString(R.string.preferences_men_secopy2supernova_activity_sex_preference_text_view_text))
        sexPreferenceTextView.text = sexPreferenceTextViewText

        // Configure SEX PREFerence component
        sexPreferenceTwoTextView = layout.findViewById(R.id.sex_preference_two_text_view)
        val sexPreferenceTwoTextViewText = SpannableString(context?.getString(R.string.preferences_men_secopy2supernova_activity_sex_preference_two_text_view_text))
        sexPreferenceTwoTextView.text = sexPreferenceTwoTextViewText

        // Configure SEX PREFerence component
        sexPreferenceThreeTextView = layout.findViewById(R.id.sex_preference_three_text_view)
        val sexPreferenceThreeTextViewText = SpannableString(context?.getString(R.string.preferences_men_secopy2supernova_activity_sex_preference_three_text_view_text))
        sexPreferenceThreeTextView.text = sexPreferenceThreeTextViewText

        // Configure SEX PREFerence component
        sexPreferenceFourTextView = layout.findViewById(R.id.sex_preference_four_text_view)

        // Configure I'm interested in component
        iMinterestedInTextView = layout.findViewById(R.id.i_minterested_in_text_view)

        // Configure Men component
        menTextView = layout.findViewById(R.id.men_text_view)

        // Configure Guest matchmaking component
        guestMatchmakingTextView = layout.findViewById(R.id.guest_matchmaking_text_view)

        interestedInConstraintLayout = layout.findViewById(R.id.row_with_value_constraint_layout)
        teleportConstraintLayout = layout.findViewById(R.id.teleport_current_location_container)

        // Configure switch/on copy 2 component
        showGuestsSwitch = layout.findViewById(R.id.show_guests_switch)

        interestedInConstraintLayout.setOnClickListener {
            fragmentManager?.beginTransaction()?.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN)?.replace(R.id.main_container, settingsSexPreferencesFragment)?.commit()
        }

        teleportConstraintLayout.setOnClickListener {
            fragmentManager?.beginTransaction()?.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN)?.replace(R.id.main_container, teleportFragment)?.commit()
        }

        guestDividerConstraintLayout = layout.findViewById(R.id.divider_with_text_copy3_constraint_layout)

        // Configure Guests are men below component
        guestsAreMenBelowTextView = layout.findViewById(R.id.guests_are_men_below_text_view)

        // Configure Sex component
        sexTwoTextView = layout.findViewById(R.id.sex_two_text_view)

        // Configure Value component
        valueFourTextView = layout.findViewById(R.id.value_four_text_view)
        val valueFourTextViewText = SpannableString(context?.getString(R.string.preferences_men_secopy2supernova_activity_value_four_text_view_text))
        valueFourTextView.text = valueFourTextViewText

        // Configure Title component
        titleThreeTextView = layout.findViewById(R.id.title_three_text_view)

        headerLeftBackImageView = layout.findViewById(R.id.preferences_header_left_image_view)

        filtersDisabledTextView = layout.findViewById(R.id.filters_disabled_textView)

        heightPreferencesLayoutHeader = layout.findViewById(R.id.divider_with_text_copy_constraint_layout)
        heightFilterLayout = layout.findViewById(R.id.slider_distance_two_constraint_layout)

        headerLeftBackImageView.setOnClickListener {
            if (Constants.PREFERENCES_FRAGMENT_SETTINGS_SETTINGS_FRAGMENT == argumentValue) {
                fragmentManager?.beginTransaction()?.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_CLOSE)?.replace(R.id.main_container, settingsSettingsFragment)?.commit()
            } else {

                startActivity(activity?.let { it1 -> BrowseProfilesActivity.newIntent(it1).launchModeWithSingleTop() })
            }
        }

        if (!AccountPreferences.getInstance(context).getBooleanValue(Constants.isAMember, true)) {
            guestMatchmakingTextView.visibility = View.GONE
            showGuestsSwitch.visibility = View.GONE
            guestsAreMenBelowTextView.visibility = View.GONE
            titleTwoTextView.visibility = View.GONE
            valueThreeTextView.visibility = View.GONE
            guestHeightSeekBar.visibility = View.GONE
            guestDividerConstraintLayout.visibility = View.GONE
        }

        interestedIn = AccountPreferences.getInstance(context).getStringValue(Constants.interestedIn, "")

        setDefaultHeights()

        setInitialData()

        setLocationTextView()
    }

    private fun setLocationTextView() {
        if (!UserObject.selectedUnlockedLocation?.city.isNullOrEmpty()) {
            teleportedToTextView.visibility = View.VISIBLE
            locationDescTextView.text = UserObject.selectedUnlockedLocation?.city
        }
    }

    private fun setInitialData() {
        if (UserObject.shouldFetchUserFromServer()) {
            firebaseDatabaseReference?.readMainUserInfoFromFirebaseFirestore(object :
                FirebaseRetrieveSingleUserListenerInterfaceFirestore {
                override fun onSuccess(user: User) {
                    setInitialPreferencesData(user)
                    UserObject.setUserDataStore(user, null)
                }
                override fun onFailure() {
                    Timber.e("retrieving single user data in preferences fragment failed")
                }
            })
        }else {
            setInitialPreferencesData(UserObject.user)
        }
    }

    private fun setInitialPreferencesData(user: User?) {
        val userDistance: Int = user?.distance.toString().getIntValue() ?: Constants.defaultDistance

        setDistanceText(userDistance)

        interestedIn = user?.interestedIn.toString()
        when (interestedIn) {
            InterestedInGender.man.toString() -> {
                menTextView.text = "Men"
            }
            InterestedInGender.woman.toString() -> {
                menTextView.text = "Women"
            }
            InterestedInGender.everyone.toString() -> {
                menTextView.text = "Everyone"
            }
            else -> {
                menTextView.text = interestedIn
            }
        }

        val minUserMatchPrefHeight = Utils.heightInFeetFromInchesWithQuotes(user?.minMatchHeight.toString().getDoubleValue())
        val maxUserMatchPrefHeight = Utils.heightInFeetFromInchesWithQuotes(user?.maxMatchHeight.toString().getDoubleValue())

        val minUserGuestPrefHeight = Utils.heightInFeetFromInchesWithQuotes(user?.minGuestHeight.toString().getDoubleValue())
        val maxUserGuestPrefHeight = Utils.heightInFeetFromInchesWithQuotes(user?.maxGuestHeight.toString().getDoubleValue())

        minMatchHeight = user?.minMatchHeight.toString().getDoubleValue()
        maxMatchHeight = user?.maxMatchHeight.toString().getDoubleValue()

        minGuestHeight = user?.minGuestHeight.toString().getDoubleValue()
        maxGuestHeight = user?.maxGuestHeight.toString().getDoubleValue()

        var minMatchAge = user?.minMatchAge.toString().toInt()
        if (minMatchAge <= 18) {
            minMatchAge = 18
        }
        setAgeTextViewText(minMatchAge, user?.maxMatchAge.toString().toInt())

        showGuestsSwitch.isChecked = user?.showGuests ?: false

        ageRangeSeekBar.getThumb(0).value = (user?.minMatchAge.toString().toFloat().minus(Constants.startingAge).div(Constants.ageMutiplier).toInt())
        ageRangeSeekBar.getThumb(1).value = user?.maxMatchAge.toString().toFloat().minus(Constants.startingAge).div(Constants.ageMutiplier).toInt()

        distanceSeekBar.getThumb(0).value = userDistance.toString().toInt()

        setThumbValues(user)

        setHeightTextViewText(minUserMatchPrefHeight, maxUserMatchPrefHeight)
        setGuestHeightTextViewText(minUserGuestPrefHeight, maxUserGuestPrefHeight)

        setGuestsDescTextView()

        if (AccountPreferences.getInstance(context).getBooleanValue(Constants.isAMember, true) &&
                (user?.showGuests == false)) {
            titleTwoTextView.visibility = View.GONE
            valueThreeTextView.visibility = View.GONE
            guestHeightSeekBar.visibility = View.GONE
        }

        if (user?.interestedIn == InterestedInGender.everyone.toString()) {
            filtersDisabledTextView.visibility = View.VISIBLE
            hideHeightFilters()
        } else {
            filtersDisabledTextView.visibility = View.GONE
            showHeightFilters()
        }

        setListeners()
    }

    private fun setListeners() {

        memberHeightSeekbar.setOnThumbValueChangeListener(object : MultiSlider.SimpleChangeListener() {
            override fun onValueChanged(multiSlider: MultiSlider?, thumb: MultiSlider.Thumb?, thumbIndex: Int, value: Int) {

                var minHeightInches: Double = AccountPreferences.getInstance(context).getDoubleValue(Constants.minMatchHeight, minMatchHeight)
                var maxHeightInches: Double = AccountPreferences.getInstance(context).getDoubleValue(Constants.maxMatchHeight, maxMatchHeight)

                if (thumbIndex == 0) {

                    minHeightInches = when (interestedIn) {
                        InterestedInGender.man.toString() -> {
                            value.times(Constants.preferencesMatchHeightSliderMultiplierInterestedInMen).plus(Constants.preferencesMatchStartingHeightInInchInterestedInMen)
                        }
                        InterestedInGender.woman.toString() -> {
                            value.times(Constants.preferencesMatchHeightSliderMultiplierInterestedInWomen).plus(Constants.preferencesMatchStartingHeightInInchInterestedInWomen)
                        }
                        else -> {
                            value.times(Constants.preferencesMatchHeightSliderMultiplierInterestedInMen).plus(Constants.preferencesMatchStartingHeightInInchInterestedInMen)
                        }
                    }

                } else {
                    maxHeightInches = when (interestedIn) {
                        InterestedInGender.man.toString() -> {
                            value.times(Constants.preferencesMatchHeightSliderMultiplierInterestedInMen).plus(Constants.preferencesMatchStartingHeightInInchInterestedInMen)
                        }
                        InterestedInGender.woman.toString() -> {
                            value.times(Constants.preferencesMatchHeightSliderMultiplierInterestedInWomen).plus(Constants.preferencesMatchStartingHeightInInchInterestedInWomen)
                        }
                        else -> {
                            value.times(Constants.preferencesMatchHeightSliderMultiplierInterestedInMen).plus(Constants.preferencesMatchStartingHeightInInchInterestedInMen)
                        }
                    }
                }

                minMatchHeight = minHeightInches
                maxMatchHeight = maxHeightInches

                val minHeightString = Utils.heightInFeetFromInchesWithQuotes(minHeightInches)
                val maxHeightString = Utils.heightInFeetFromInchesWithQuotes(maxHeightInches)

                setHeightTextViewText(minHeightString, maxHeightString)
                saveMatchHeightToFirebase()
            }
        })

        guestHeightSeekBar.setOnThumbValueChangeListener(object : MultiSlider.SimpleChangeListener() {
            override fun onValueChanged(multiSlider: MultiSlider?, thumb: MultiSlider.Thumb?, thumbIndex: Int, value: Int) {

                var minGuestHeightInches: Double = AccountPreferences.getInstance(context).getDoubleValue(Constants.minGuestHeight, minGuestHeight).toDouble()
                var maxGuestHeightInches: Double = AccountPreferences.getInstance(context).getDoubleValue(Constants.maxGuestHeight, maxGuestHeight).toDouble()

                if (thumbIndex == 0) {

                    minGuestHeightInches = when (interestedIn) {
                        InterestedInGender.man.toString() -> {
                            value.times(Constants.preferencesGuestHeightSliderMultiplierInterestedInMen).plus(Constants.preferencesGuestStartingHeightInInchInterestedInMen)
                        }
                        InterestedInGender.woman.toString() -> {
                            value.times(Constants.preferencesGuestHeightSliderMultiplierInterestedInWomen).plus(Constants.preferencesGuestStartingHeightInInchInterestedInWomen)
                        }
                        else -> {
                            value.times(Constants.preferencesGuestHeightSliderMultiplierInterestedInMen).plus(Constants.preferencesGuestStartingHeightInInchInterestedInMen)
                        }
                    }

                } else {
                    maxGuestHeightInches = when (interestedIn) {
                        InterestedInGender.man.toString() -> {
                            value.times(Constants.preferencesGuestHeightSliderMultiplierInterestedInMen).plus(Constants.preferencesGuestStartingHeightInInchInterestedInMen)
                        }
                        InterestedInGender.woman.toString() -> {
                            value.times(Constants.preferencesGuestHeightSliderMultiplierInterestedInWomen).plus(Constants.preferencesGuestStartingHeightInInchInterestedInWomen)
                        }
                        else -> {
                            value.times(Constants.preferencesGuestHeightSliderMultiplierInterestedInMen).plus(Constants.preferencesGuestStartingHeightInInchInterestedInMen)
                        }
                    }
                }

                minGuestHeight = minGuestHeightInches.toString().getDoubleValue()
                maxGuestHeight = maxGuestHeightInches.toString().getDoubleValue()

                val minGuestHeightString = Utils.heightInFeetFromInchesWithQuotes(minGuestHeightInches)
                val maxGuestHeightString = Utils.heightInFeetFromInchesWithQuotes(maxGuestHeightInches)

                setGuestHeightTextViewText(minGuestHeightString, maxGuestHeightString)
                saveGuestHeightToFirebase()
            }
        })

        distanceSeekBar.setOnThumbValueChangeListener(object : MultiSlider.SimpleChangeListener() {
            override fun onValueChanged(multiSlider: MultiSlider?, thumb: MultiSlider.Thumb?, thumbIndex: Int, value: Int) {

                val distance: Int = value.toString().getIntValue() ?: Constants.defaultDistance

                setDistanceText(distance)
                saveDistanceToFirebase(distance)
            }
        })

        ageRangeSeekBar.setOnThumbValueChangeListener(object : MultiSlider.SimpleChangeListener() {
            override fun onValueChanged(multiSlider: MultiSlider?, thumb: MultiSlider.Thumb?, thumbIndex: Int, value: Int) {

                var minAge: Int = AccountPreferences.getInstance(context).getIntValue(Constants.minMatchAge, 18)
                var maxAge: Int = AccountPreferences.getInstance(context).getIntValue(Constants.maxMatchAge, 90)

                if (thumbIndex == 0) {
                    minAge = value.times(Constants.ageMutiplier).plus(Constants.startingAge).toInt()
                } else {
                    maxAge = value.times(Constants.ageMutiplier).plus(Constants.startingAge).toInt()
                }

                setAgeTextViewText(minAge, maxAge)
                saveAgeToFirebase(minAge, maxAge)
            }
        })

        showGuestsSwitch.setOnClickListener {
            val showGuestsStatus = showGuestsSwitch.isChecked

            saveShowGuestsToFirebase(showGuestsStatus)

            if (AccountPreferences.getInstance(context).getBooleanValue(Constants.isAMember, true) && showGuestsStatus) {
                titleTwoTextView.visibility = View.VISIBLE
                valueThreeTextView.visibility = View.VISIBLE
                guestHeightSeekBar.visibility = View.VISIBLE
            } else {
                titleTwoTextView.visibility = View.GONE
                valueThreeTextView.visibility = View.GONE
                guestHeightSeekBar.visibility = View.GONE
            }
        }
    }

    private fun setGuestHeightTextViewText(minGuestHeightString: String, maxGuestHeightString: String) {

        val guestHeightText = SpannableString(context?.getString(R.string.preferences_men_secopy2supernova_activity_value_three_text_view_text, minGuestHeightString, maxGuestHeightString))
        valueThreeTextView.text = guestHeightText
    }

    private fun setAgeTextViewText(minAge: Int, maxAge: Int) {

        var ageText = SpannableString(context?.getString(R.string.preferences_men_secopy2supernova_activity_value_text_view_text, minAge.toString(), maxAge.toString()))

        if (maxAge >= sliderMaxAge) {

            ageText = SpannableString(context?.getString(R.string.preferences_men_secopy2supernova_activity_value_text_view_text, minAge.toString(), "$sliderMaxAge+"))
        }

        if(minAge >= sliderMaxAge && maxAge >= sliderMaxAge) {

            ageText = SpannableString(context?.getString(R.string.preferences_men_secopy2supernova_activity_value_text_view_text, sliderMaxAge.toString(), "$sliderMaxAge+"))
        }

        valueTextView.text = ageText
    }

    private fun setHeightTextViewText(minHeightString: String, maxHeightString: String) {

        var heightText = SpannableString(context?.getString(R.string.preferences_men_secopy2supernova_activity_value_four_text_view_text, minHeightString, maxHeightString))

        if (interestedIn == InterestedInGender.man.toString()) {

            if (maxMatchHeight == sliderMaxHeightForMen) {

                heightText = SpannableString(context?.getString(R.string.preferences_men_secopy2supernova_activity_value_four_text_view_text, minHeightString, "$maxHeightString+"))
            }
        } else if (interestedIn == InterestedInGender.woman.toString()) {

            if (maxMatchHeight == sliderMaxHeightForWomen) {

                heightText = SpannableString(context?.getString(R.string.preferences_men_secopy2supernova_activity_value_four_text_view_text, minHeightString, "$maxHeightString+"))
            }
        }


        valueFourTextView.text = heightText

        preferenceFragmentLayout.invalidate()
    }

    private fun setDistanceText(distance: Int) {
        val distanceText = SpannableString(context?.getString(R.string.preferences_men_secopy2supernova_activity_value_two_text_view_text, distance.toString()))
        if (distance >= Constants.defaultDistance) {
            valueTwoTextView.text = noLimit
        } else {
            valueTwoTextView.text = distanceText
        }
    }

    private fun saveShowGuestsToFirebase(showGuestsStatus: Boolean) {
        AccountPreferences.getInstance(context).setValue(Constants.showGuests, showGuestsStatus)
        firebaseDatabaseReference?.saveUserInfoToFirebase(Constants.showGuests, showGuestsStatus)
        UserObject.userPreferencesChangedLiveData.postValue(true)
        makeUserPreferencesUpdatedAPICall(mainUserId)
    }

    private fun saveDistanceToFirebase(distance: Int) {
        firebaseDatabaseReference?.saveUserInfoToFirebase(Constants.distance, distance)
        AccountPreferences.getInstance(context).setValue(Constants.distance, distance)
        UserObject.userPreferencesChangedLiveData.postValue(true)
        makeUserPreferencesUpdatedAPICall(mainUserId)
    }

    private fun saveAgeToFirebase(minAge: Int, maxAge: Int) {
        AccountPreferences.getInstance(context).setValue(Constants.minMatchAge, minAge)
        AccountPreferences.getInstance(context).setValue(Constants.maxMatchAge, maxAge)

        firebaseDatabaseReference?.saveUserInfoToFirebase(Constants.minMatchAge, minAge)
        firebaseDatabaseReference?.saveUserInfoToFirebase(Constants.maxMatchAge, maxAge)
        UserObject.userPreferencesChangedLiveData.postValue(true)
        makeUserPreferencesUpdatedAPICall(mainUserId)
    }

    private fun saveMatchHeightToFirebase() {
        AccountPreferences.getInstance(context).setValue(Constants.minMatchHeight, minMatchHeight)
        AccountPreferences.getInstance(context).setValue(Constants.maxMatchHeight, maxMatchHeight)

        firebaseDatabaseReference?.saveUserInfoToFirebase(Constants.minMatchHeight, minMatchHeight)
        firebaseDatabaseReference?.saveUserInfoToFirebase(Constants.maxMatchHeight, maxMatchHeight)
        UserObject.userPreferencesChangedLiveData.postValue(true)
        makeUserPreferencesUpdatedAPICall(mainUserId)
    }

    private fun saveGuestHeightToFirebase() {
        AccountPreferences.getInstance(context).setValue(Constants.minGuestHeight, minGuestHeight)
        AccountPreferences.getInstance(context).setValue(Constants.maxGuestHeight, maxGuestHeight)

        firebaseDatabaseReference?.saveUserInfoToFirebase(Constants.minGuestHeight, minGuestHeight)
        firebaseDatabaseReference?.saveUserInfoToFirebase(Constants.maxGuestHeight, maxGuestHeight)
        UserObject.userPreferencesChangedLiveData.postValue(true)
        makeUserPreferencesUpdatedAPICall(mainUserId)
    }

    private fun setGuestsDescTextView() {

        var userGender = ""

        userGender = AccountPreferences.getInstance(context).getStringValue(Constants.gender, "")

        if (userGender == GenderType.man.toString() && interestedIn == InterestedInGender.man.toString()) {
            guestsAreMenBelowTextView.text = context?.getString(R.string.preferences_men_secopy2supernova_activity_guests_are_men_below_text_view_text, "men")
        } else if (userGender == GenderType.man.toString() && interestedIn == InterestedInGender.woman.toString()) {
            guestsAreMenBelowTextView.text = context?.getString(R.string.preferences_men_secopy2supernova_activity_guests_are_women_below_text_view_text, "men")
        } else if (userGender == GenderType.woman.toString() && interestedIn == InterestedInGender.man.toString()) {
            guestsAreMenBelowTextView.text = context?.getString(R.string.preferences_men_secopy2supernova_activity_guests_are_men_below_text_view_text, "women")
        } else if (userGender == GenderType.woman.toString() && interestedIn == InterestedInGender.woman.toString()) {
            guestsAreMenBelowTextView.text = context?.getString(R.string.preferences_men_secopy2supernova_activity_guests_are_women_below_text_view_text, "women")
        }
    }

    private fun setDefaultHeights() {

        activity?.let {

            when (AccountPreferences.getInstance(it).getStringValue(Constants.interestedIn, "")) {
                InterestedInGender.man.toString() -> {

                    minMatchHeight = Constants.defaultMinHeightInInchForPrefsInterestedInMen
                    maxMatchHeight = Constants.defaultMaxHeightInInchForPrefsInterestedInMen

                    minGuestHeight = Constants.defaultMinGuestsHeightInInchForPrefsInterestedInMen
                    maxGuestHeight = Constants.defaultMaxGuestsHeightInInchForPrefsInterestedInMen

                }
                InterestedInGender.woman.toString() -> {

                    minMatchHeight = Constants.defaultMinHeightInInchForPrefsInterestedInWomen
                    maxMatchHeight = Constants.defaultMaxHeightInInchForPrefsInterestedInWomen

                    minGuestHeight = Constants.defaultMinGuestsHeightInInchForPrefsInterestedInWomen
                    maxGuestHeight = Constants.defaultMaxGuestsHeightInInchForPrefsInterestedInWomen
                }
                else -> {

                    minMatchHeight = Constants.defaultMinHeightInInchForPrefsInterestedInMen
                    maxMatchHeight = Constants.defaultMaxHeightInInchForPrefsInterestedInMen

                    minGuestHeight = Constants.defaultMinGuestsHeightInInchForPrefsInterestedInMen
                    maxGuestHeight = Constants.defaultMaxGuestsHeightInInchForPrefsInterestedInMen
                }
            }
        }
    }

    private fun setThumbValues(user: User?) {

        try {

            if (user?.minGuestHeight != null &&
                    user.maxGuestHeight != null &&
                    user.minMatchHeight != null &&
                    user.maxMatchHeight != null) {

                when (interestedIn) {
                    InterestedInGender.man.toString() -> {

                        guestHeightSeekBar.getThumb(0).value = (user.minGuestHeight.toString().toFloat().minus(Constants.preferencesGuestStartingHeightInInchInterestedInMen).div(Constants.preferencesGuestHeightSliderMultiplierInterestedInMen).toInt())
                        guestHeightSeekBar.getThumb(1).value = user.maxGuestHeight.toString().toFloat().minus(Constants.preferencesGuestStartingHeightInInchInterestedInMen).div(Constants.preferencesGuestHeightSliderMultiplierInterestedInMen).toInt()

                        memberHeightSeekbar.getThumb(0).value = (user.minMatchHeight.toString().toFloat().minus(Constants.preferencesMatchStartingHeightInInchInterestedInMen).div(Constants.preferencesMatchHeightSliderMultiplierInterestedInMen).toInt())
                        memberHeightSeekbar.getThumb(1).value = (user.maxMatchHeight.toString().toFloat().minus(Constants.preferencesMatchStartingHeightInInchInterestedInMen).div(Constants.preferencesMatchHeightSliderMultiplierInterestedInMen).toInt())

                    }
                    InterestedInGender.woman.toString() -> {

                        guestHeightSeekBar.getThumb(0).value = (user.minGuestHeight.toString().toFloat().minus(Constants.preferencesGuestStartingHeightInInchInterestedInWomen).div(Constants.preferencesGuestHeightSliderMultiplierInterestedInWomen).toInt())
                        guestHeightSeekBar.getThumb(1).value = user.maxGuestHeight.toString().toFloat().minus(Constants.preferencesGuestStartingHeightInInchInterestedInWomen).div(Constants.preferencesGuestHeightSliderMultiplierInterestedInWomen).toInt()

                        memberHeightSeekbar.getThumb(0).value = (user.minMatchHeight.toString().toFloat().minus(Constants.preferencesMatchStartingHeightInInchInterestedInWomen).div(Constants.preferencesMatchHeightSliderMultiplierInterestedInWomen).toInt())
                        memberHeightSeekbar.getThumb(1).value = (user.maxMatchHeight.toString().toFloat().minus(Constants.preferencesMatchStartingHeightInInchInterestedInWomen).div(Constants.preferencesMatchHeightSliderMultiplierInterestedInWomen).toInt())

                    }
                    else -> {

                        guestHeightSeekBar.getThumb(0).value = (user.minGuestHeight.toString().toFloat().minus(Constants.preferencesGuestStartingHeightInInchInterestedInMen).div(Constants.preferencesGuestHeightSliderMultiplierInterestedInMen).toInt())
                        guestHeightSeekBar.getThumb(1).value = user.maxGuestHeight.toString().toFloat().minus(Constants.preferencesGuestStartingHeightInInchInterestedInMen).div(Constants.preferencesGuestHeightSliderMultiplierInterestedInMen).toInt()

                        memberHeightSeekbar.getThumb(0).value = (user.minMatchHeight.toString().toFloat().minus(Constants.preferencesMatchStartingHeightInInchInterestedInMen).div(Constants.preferencesMatchHeightSliderMultiplierInterestedInMen).toInt())
                        memberHeightSeekbar.getThumb(1).value = (user.maxMatchHeight.toString().toFloat().minus(Constants.preferencesMatchStartingHeightInInchInterestedInMen).div(Constants.preferencesMatchHeightSliderMultiplierInterestedInMen).toInt())
                    }
                }
            } else {

                Timber.d("Null values in setting thumb values")
            }
        } catch (ex: Exception) {

            Timber.e("exception in setting thumb values: $ex")
        }
    }

    private fun hideHeightFilters() {
        heightPreferencesLayoutHeader.visibility = View.GONE
        heightFilterLayout.visibility = View.GONE
    }

    private fun showHeightFilters() {
        heightPreferencesLayoutHeader.visibility = View.VISIBLE
        heightFilterLayout.visibility = View.VISIBLE
    }
}
