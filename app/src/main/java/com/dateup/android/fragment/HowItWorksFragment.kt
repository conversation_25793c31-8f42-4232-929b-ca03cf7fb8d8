package com.dateup.android.fragment

import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.BackgroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentTransaction
import com.dateup.android.R


class HowItWorksFragment : Fragment() {

    private lateinit var point2TextView: TextView
    private lateinit var point3TextView: TextView
    private lateinit var headerLeftBackImageView: ImageView
    private lateinit var settingsFragment: SettingsFragment

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {

        return inflater.inflate(R.layout.fragment_how_it_works, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        settingsFragment = SettingsFragment()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        this.init(view)
    }

    private fun init(layout: View) {

        headerLeftBackImageView = layout.findViewById(R.id.account_header_left_image_view)
        point2TextView = layout.findViewById(R.id.tall_people_on_dateu_text_view)
        point3TextView = layout.findViewById(R.id.third_point)

        headerLeftBackImageView.setOnClickListener {
            parentFragmentManager.beginTransaction().setTransition(FragmentTransaction.TRANSIT_FRAGMENT_CLOSE).replace(R.id.main_container, settingsFragment).commit()
        }

        val spannableString = SpannableString(getString(R.string.how_it_works_point_2))
        val backgroundSpan = BackgroundColorSpan(resources.getColor(R.color.members_color, null))
        spannableString.setSpan(backgroundSpan, 20, spannableString.trim().length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        point2TextView.text = spannableString

        val spannableString2 = SpannableString(getString(R.string.how_it_works_point_3))
        val backgroundSpan2 = BackgroundColorSpan(resources.getColor(R.color.guests_color, null))
        spannableString2.setSpan(backgroundSpan2, 23, spannableString2.trim().length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        point3TextView.text = spannableString2
    }
}
