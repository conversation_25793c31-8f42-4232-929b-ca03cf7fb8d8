package com.dateup.android.fragment

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentTransaction
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.activity.Icebreaker1Activity
import com.dateup.android.adapter.SettingsHomeSupernovaActivityViewRecyclerViewAdapter
import com.dateup.android.deepLinking.AppInvites
import com.dateup.android.extensions.launchModeWithSingleTop
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.firebase.FirebaseRetrieveSingleUserListenerInterfaceFirestore
import com.dateup.android.firebase.RemoteConfigurationService
import com.dateup.android.glide.ImageLoaderModule
import com.dateup.android.heightVerification.ui.HeightVerificationScan
import com.dateup.android.models.User
import com.dateup.android.models.UserObject
import com.dateup.android.subscriptions.views.SubscriptionActivity
import com.dateup.android.utils.AlertDialogView
import com.dateup.android.utils.Constants
import com.dateup.android.utils.Utils
import com.dateup.android.viewModels.SpacesImagesViewModel
import timber.log.Timber

class SettingsFragment : Fragment() {

    private lateinit var viewRecyclerView: RecyclerView
    private val homeSettingsData: ArrayList<String> = ArrayList()

    private lateinit var settingsSettingsFragment: SettingsSettingsFragment
    private lateinit var howItWorksFragment: HowItWorksFragment
    private lateinit var editProfileFragment: EditProfileFragment
    private lateinit var userNameTextView: TextView
    private lateinit var upgradeTextView: TextView
    private lateinit var plusStatusTextView: TextView
    private lateinit var plusStatusImageView: ImageView

    private lateinit var userProfileImageView: ImageView

    private lateinit var mainUserId: String

    private lateinit var spacesImagesViewModel: SpacesImagesViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        spacesImagesViewModel = ViewModelProvider(this).get(SpacesImagesViewModel::class.java)
        settingsSettingsFragment = SettingsSettingsFragment()
        editProfileFragment = EditProfileFragment()
        howItWorksFragment = HowItWorksFragment()
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment

        return inflater.inflate(R.layout.fragment_settings_main, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        init(view)
    }

    private fun init(layout: View) {
        userNameTextView = layout.findViewById(R.id.user_name_text_view)
        userProfileImageView = layout.findViewById(R.id.user_profile_image_view)
        viewRecyclerView = layout.findViewById(R.id.view_recycler_view)
        upgradeTextView = layout.findViewById(R.id.upgrade_text_view)
        plusStatusTextView = layout.findViewById(R.id.plus_status)
        plusStatusImageView = layout.findViewById(R.id.plus_image)

        viewRecyclerView.layoutManager = LinearLayoutManager(activity, LinearLayoutManager.VERTICAL, false)
        viewRecyclerView.adapter = SettingsHomeSupernovaActivityViewRecyclerViewAdapter(activity, homeSettingsData, {
            settingsItem: String -> settingsItemClicked(settingsItem) }, 1)

        mainUserId = AccountPreferences.getInstance(context).getStringValue(Constants.firebaseUserId, "")

        addHomeSettingsData()
        readUserInfoFromFirebase()
        userProfileImageView.setOnClickListener {
            checkForProfileCompletion()
        }

        if (UserObject.hasPlusOrSelect) {
            plusStatusTextView.visibility = View.VISIBLE
            plusStatusImageView.visibility = View.VISIBLE
            upgradeTextView.visibility = View.GONE
            if (UserObject.isDateUpSelectUser == true) {
                plusStatusTextView.text = resources.getString(R.string.dateup_select_member)
            } else {
                plusStatusTextView.text = resources.getString(R.string.dateup_plus_member)
            }
        }else {
            plusStatusTextView.visibility = View.GONE
            plusStatusImageView.visibility = View.GONE
            upgradeTextView.visibility = View.VISIBLE
            upgradeTextView.setOnClickListener {
                activity?.let {
                    startActivity(SubscriptionActivity.newIntent(it))
                }
            }
        }
    }

    private fun addHomeSettingsData() {
        if (homeSettingsData.isEmpty()) {
            context?.let { context ->
                homeSettingsData.add(context.getString(R.string.settings_main_item_1))
                homeSettingsData.add(context.getString(R.string.settings_main_item_2))
                homeSettingsData.add(context.getString(R.string.settings_main_item_3))
                homeSettingsData.add(context.getString(R.string.settings_main_item_4))
                RemoteConfigurationService.fetchConfig(RemoteConfigurationService.BETA_INVITE_MESSAGE) {
                    homeSettingsData.add(context.getString(R.string.settings_main_item_6))
                    viewRecyclerView.adapter?.notifyDataSetChanged()
                }
            }
        }
    }

    private fun readUserInfoFromFirebase() {

        activity?.let {
            ImageLoaderModule.loadImageIntoImageViewWithSpacesPathCircleTransformAndLoading(it, spacesImagesViewModel, "${mainUserId}/${Constants.photoFileName1}", userProfileImageView)
        }

        if (UserObject.shouldFetchUserFromServer()) {
            val firebaseDatabaseReference = activity?.let { FirebaseDatabaseUtil(it) }
            firebaseDatabaseReference?.readMainUserInfoFromFirebaseFirestore(object :
                FirebaseRetrieveSingleUserListenerInterfaceFirestore {
                override fun onSuccess(user: User) {
                    UserObject.setUserDataStore(user, mainUserId)
                    setInitialUserData(user)
                }
                override fun onFailure() {
                    Timber.e("retrieving single user data in settings fragment failed")
                }
            })
        }else {
            setInitialUserData(UserObject.user)
        }
    }

    private fun setInitialUserData(user: User?) {
        userNameTextView.text = user?.name.toString()

        if (user?.isHeightVerified == false) {
            context?.let { context ->
                homeSettingsData.add(context.getString(R.string.settings_main_item_5))
            }
        }
        viewRecyclerView.adapter?.notifyDataSetChanged()
    }

    private fun settingsItemClicked(settingsItem: String) {

        context?.let { context ->
            when (settingsItem) {
                context.getString(R.string.settings_main_item_1) -> {

                    if (activity == null) {

                        parentFragmentManager.beginTransaction().setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN).replace(R.id.main_container, editProfileFragment).commit()
                    } else {

                        checkForProfileCompletion()
                    }
                }
                context.getString(R.string.settings_main_item_2) ->
                    parentFragmentManager.beginTransaction().setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN).replace(R.id.main_container, settingsSettingsFragment).commit()
                context.getString(R.string.settings_main_item_3) ->
                    parentFragmentManager.beginTransaction().setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN).replace(R.id.main_container, howItWorksFragment).commit()
                context.getString(R.string.settings_main_item_4) ->
                    activity?.let { Utils.openGmailApp(it, "DateUp Feedback") }
                context.getString(R.string.settings_main_item_5) -> {
                    activity?.let {
                        val intent = HeightVerificationScan.newIntent(it).launchModeWithSingleTop()
                        startActivity(intent)
                    }
                }
                context.getString(R.string.settings_main_item_6) ->
                    RemoteConfigurationService.fetchConfig(RemoteConfigurationService.BETA_INVITE_MESSAGE) { msg ->
                        activity?.let {
                            if (msg != null) {
                                AppInvites.shareLink(it, msg)
                            }
                        }
                    }
                else -> null
            }
        }
    }

    private fun checkForProfileCompletion() {

        activity?.let {

            val iceBreakerQuestion1 = AccountPreferences.getInstance(it).getStringValue(Constants.iceBreaker1, "")
            val iceBreakerQuestion2 = AccountPreferences.getInstance(it).getStringValue(Constants.iceBreaker2, "")
            if (iceBreakerQuestion1.isNullOrEmpty() && iceBreakerQuestion2.isNullOrEmpty()) {

                AlertDialogView.showAlertDialog(context = it,
                        title = it.getString(R.string.complete_profile),
                        message = it.getString(R.string.complete_profile_desc),
                        buttonPositiveText = it.getString(R.string.continue_text),
                        buttonNegativeText = it.getString(R.string.cancel)) { dialog, which ->

                    if (which == DialogInterface.BUTTON_POSITIVE) {

                        startActivity(Icebreaker1Activity.newIntent(it))
                    } else {

                        dialog.cancel()
                    }
                }
            } else {

                parentFragmentManager.beginTransaction().setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN).replace(R.id.main_container, editProfileFragment).commit()
            }
        }
    }
}
