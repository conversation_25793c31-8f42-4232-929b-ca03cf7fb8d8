package com.dateup.android.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentTransaction
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.adapter.SettingsHomeSupernovaActivityViewRecyclerViewAdapter
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.models.UserNotificationPreferences
import com.dateup.android.utils.Constants
import com.dateup.android.utils.ManagePermissions
import com.dateup.android.utils.Utils

class SettingsNotificationFragment : Fragment() {

    private lateinit var viewRecyclerView: RecyclerView
    private val notificationsSettingsData: ArrayList<UserNotificationPreferences> = ArrayList()

    private lateinit var headerLeftBackImageView: ImageView
    private lateinit var settingsSettingsFragment: Fragment


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        settingsSettingsFragment = SettingsSettingsFragment()
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_settings_notifications, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        init(view)
    }

    private fun init(layout: View) {

        // Configure View component
        viewRecyclerView = layout.findViewById(R.id.notifications_view_recycler_view)
        headerLeftBackImageView = layout.findViewById(R.id.notifications_header_left_image_view)

        if (notificationsSettingsData.isEmpty()) {
            addHomeSettingsData()
        }

        viewRecyclerView.layoutManager = LinearLayoutManager(activity, LinearLayoutManager.VERTICAL, false)
        viewRecyclerView.adapter = SettingsHomeSupernovaActivityViewRecyclerViewAdapter(context, notificationsSettingsData, { settingsNotificationPreferenceItem: UserNotificationPreferences -> settingsItemClicked(settingsNotificationPreferenceItem) }, 2)

        headerLeftBackImageView.setOnClickListener {
            fragmentManager?.beginTransaction()?.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_CLOSE)?.replace(R.id.main_container, settingsSettingsFragment)?.commit()
        }
    }

    private fun settingsItemClicked(settingsItem: UserNotificationPreferences) {

        val firebaseDatabaseReference = activity?.let { FirebaseDatabaseUtil(it) }

        if (context != null && context is Context && !ManagePermissions.areNotificationsEnabled(context as Context)) {
            activity?.let { it ->
                Utils.showPermissionAlert("To continue, enable notifications for DateUp in settings", it)
            }
        }else {
            when (settingsItem.notificationItemTitle) {
                resources.getString(R.string.settings_notifications_item_1) -> {
                    AccountPreferences.getInstance(activity).setValue(Constants.showNewMatches, settingsItem.notificationItemCheckedStatus)
                    firebaseDatabaseReference?.saveUserInfoToFirebase(Constants.showNewMatches, settingsItem.notificationItemCheckedStatus)
                }
                resources.getString(R.string.settings_notifications_item_2) -> {
                    AccountPreferences.getInstance(activity).setValue(Constants.showMessages, settingsItem.notificationItemCheckedStatus)
                    firebaseDatabaseReference?.saveUserInfoToFirebase(Constants.showMessages, settingsItem.notificationItemCheckedStatus)
                }
            }
        }
    }

    private fun addHomeSettingsData() {

        var userNotificationPreferences1 = UserNotificationPreferences(resources.getString(R.string.settings_notifications_item_1), AccountPreferences.getInstance(activity).getBooleanValue(Constants.showNewMatches, true))
        var userNotificationPreferences2 = UserNotificationPreferences(resources.getString(R.string.settings_notifications_item_2), AccountPreferences.getInstance(activity).getBooleanValue(Constants.showMessages, true))

        context?.let {
            if (!ManagePermissions.areNotificationsEnabled(it)) {
                userNotificationPreferences1 = UserNotificationPreferences(resources.getString(R.string.settings_notifications_item_1), false)
                userNotificationPreferences2 = UserNotificationPreferences(resources.getString(R.string.settings_notifications_item_2), false)
            }
        }
        notificationsSettingsData.add(userNotificationPreferences1)
        notificationsSettingsData.add(userNotificationPreferences2)
    }
}
