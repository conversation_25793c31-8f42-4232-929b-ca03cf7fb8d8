package com.dateup.android.fragment

import android.app.Activity
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentTransaction
import androidx.lifecycle.ViewModelProvider
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.activity.EditProfileModalActivity
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.firebase.FirebaseRetrieveSingleUserListenerInterfaceFirestore
import com.dateup.android.glide.ImageLoaderModule
import com.dateup.android.models.User
import com.dateup.android.models.UserObject
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Constants
import com.dateup.android.utils.Utils
import com.dateup.android.utils.Utils.Companion.isValidUser
import com.dateup.android.viewModels.SpacesImagesViewModel
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.*

class EditProfileFragment : Fragment() {

    private lateinit var professionTextView: TextView
    private lateinit var ageTextView: TextView
    private lateinit var heightTextView: TextView
    private lateinit var educationTextView: TextView
    private lateinit var hometownTextView: TextView
    private lateinit var iceBreakerQuestion1TextView: TextView
    private lateinit var iceBreakerAnswer1TextView: TextView
    private lateinit var iceBreakerQuestion2TextView: TextView
    private lateinit var iceBreakerAnswer2TextView: TextView
    private lateinit var userNameTextView: TextView
    private lateinit var buttonGhostButton: Button
    private lateinit var heightVerifiedBadge: ImageView
    private lateinit var heightVerifiedTextView: TextView

    private lateinit var firstPhotoImageView: ImageView
    private lateinit var secondPhotoImageView: ImageView
    private lateinit var thirdPhotoImageView: ImageView
    private lateinit var fourthPhotoImageView: ImageView
    private lateinit var fifthPhotoImageView: ImageView
    private lateinit var sixthPhotoImageView: ImageView

    private lateinit var firstIcebreakerBackgroundConstraintLayout: ConstraintLayout
    private lateinit var secondIcebreakerBackgroundConstraintLayout: ConstraintLayout
    private lateinit var firstIcebreakerBackgroundImageView: ImageView
    private lateinit var secondIcebreakerBackgroundImageView: ImageView

    private lateinit var backImageButton: ImageView
    private lateinit var editButton: TextView

    private lateinit var mainUserId: String

    private lateinit var settingsFragment: SettingsFragment

    private lateinit var editProfileFragmentLayout: View

    var firstPhotoRef = ""
    var secondPhotoRef = ""
    var thirdPhotoRef = ""
    var fourthPhotoRef = ""
    var fifthPhotoRef = ""
    var sixthPhotoRef = ""

    private lateinit var spacesImagesViewModel: SpacesImagesViewModel

    private lateinit var professionLabel: TextView
    private lateinit var educationLabel: TextView
    private lateinit var homeTownLabel: TextView

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        editProfileFragmentLayout = inflater.inflate(R.layout.fragment_edit_profile, container, false)
        return editProfileFragmentLayout
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        spacesImagesViewModel = ViewModelProvider(this).get(SpacesImagesViewModel::class.java)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        init(view)
    }

    private fun init(view: View) {

        settingsFragment = SettingsFragment()

        // Configure Product Designer component
        professionLabel = view.findViewById(R.id.profession_text_view)
        professionTextView = view.findViewById(R.id.user_profession_text_view)

        // Configure Age component
        ageTextView = view.findViewById(R.id.user_age_text_view)

        heightVerifiedBadge = view.findViewById(R.id.height_verified_badge)
        heightVerifiedTextView = view.findViewById(R.id.height_verified_text_view)

        // Configure Education component
        educationLabel = view.findViewById(R.id.education_text_view)
        educationTextView = view.findViewById(R.id.user_education_text_view)

        // Configure Hometown component
        homeTownLabel = view.findViewById(R.id.hometown_text_view)
        hometownTextView = view.findViewById(R.id.user_home_town_text_view)

        backImageButton = view.findViewById(R.id.edit_profile_header_left_image_view)

        // Configure How do you describe component
        iceBreakerQuestion1TextView = view.findViewById(R.id.ice_breaker_1_question_text_view)

        // Configure I’m definitely fun a component
        iceBreakerAnswer1TextView = view.findViewById(R.id.ice_breaker_1_answer_text_view)

        // Configure How do you describe component
        iceBreakerQuestion2TextView = view.findViewById(R.id.ice_breaker_2_question_text_view)

        // Configure I’m definitely fun a component
        iceBreakerAnswer2TextView = view.findViewById(R.id.ice_breaker_2_answer_text_view)

        // Configure Jon component
        userNameTextView = view.findViewById(R.id.user_name_text_view)

        heightTextView = view.findViewById(R.id.user_height_text_view)

        firstPhotoImageView = view.findViewById(R.id.user_first_photo_image_view)

        secondPhotoImageView = view.findViewById(R.id.user_second_photo_image_view)

        thirdPhotoImageView = view.findViewById(R.id.user_third_photo_image_view)

        firstIcebreakerBackgroundConstraintLayout = view.findViewById(R.id.profilecard_blank_photo_copy_constraint_layout)
        secondIcebreakerBackgroundConstraintLayout = view.findViewById(R.id.profilecard_blank_photo_copy3_constraint_layout)

        firstIcebreakerBackgroundImageView = view.findViewById(R.id.rectangle_image_view)
        secondIcebreakerBackgroundImageView = view.findViewById(R.id.rectangle_two_image_view)

        fourthPhotoImageView = view.findViewById(R.id.user_fourth_photo_image_view)
        fifthPhotoImageView = view.findViewById(R.id.user_fifth_photo_image_view)
        sixthPhotoImageView = view.findViewById(R.id.user_sixth_photo_image_view)

        setImageViewHeightBasedOnDeviceWidth()

        // Configure button/ghost component
        buttonGhostButton = view.findViewById(R.id.report_user_button)

        editButton = view.findViewById(R.id.edit_profile_text_view)

        editButton.setOnClickListener {
            this.startActivity(EditProfileModalActivity.newIntent(context))
        }

        backImageButton.setOnClickListener {

            parentFragmentManager.beginTransaction().setTransition(FragmentTransaction.TRANSIT_FRAGMENT_CLOSE).replace(R.id.main_container, settingsFragment).commit()
        }

        mainUserId = AccountPreferences.getInstance(context).getStringValue(Constants.firebaseUserId, "")

        readUserInfoFromFirebase()
    }

    private fun setImageViewHeightBasedOnDeviceWidth() {
        val height = AppUtils.getHeightForImages()

        val firstImageLayoutParams = firstPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        firstImageLayoutParams?.height = height
        firstImageLayoutParams?.let { params ->
            firstPhotoImageView.layoutParams = params
        }

        val firstIceBackgroundImage = firstIcebreakerBackgroundConstraintLayout.layoutParams as? ConstraintLayout.LayoutParams
        firstIceBackgroundImage?.height = height
        firstIceBackgroundImage?.let { params ->
            firstIcebreakerBackgroundConstraintLayout.layoutParams = params
        }

        val secondImageLayoutParams = secondPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        secondImageLayoutParams?.height = height
        secondImageLayoutParams?.let { params ->
            secondPhotoImageView.layoutParams = params
        }

        val secondIceBackgroundImage = secondIcebreakerBackgroundConstraintLayout.layoutParams as? ConstraintLayout.LayoutParams
        secondIceBackgroundImage?.height = height
        secondIceBackgroundImage?.let { params ->
            secondIcebreakerBackgroundConstraintLayout.layoutParams = params
        }

        val thirdImageLayoutParams = thirdPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        thirdImageLayoutParams?.height = height
        thirdImageLayoutParams?.let { params ->
            thirdPhotoImageView.layoutParams = params
        }

        val fourthImageLayoutParams = fourthPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        fourthImageLayoutParams?.height = height
        fourthImageLayoutParams?.let { params ->
            fourthPhotoImageView.layoutParams = params
        }

        val fifthImageLayoutParams = fifthPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        fifthImageLayoutParams?.height = height
        fifthImageLayoutParams?.let { params ->
            fifthPhotoImageView.layoutParams = params
        }

        val sixthImageLayoutParams = sixthPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        sixthImageLayoutParams?.height = height
        sixthImageLayoutParams?.let { params ->
            sixthPhotoImageView.layoutParams = params
        }
    }

    private fun readUserInfoFromFirebase() {
        if (UserObject.shouldFetchUserFromServer()) {
            val firebaseDatabaseReference = activity?.let { FirebaseDatabaseUtil(it) }
            firebaseDatabaseReference?.readMainUserInfoFromFirebaseFirestore(object :
                FirebaseRetrieveSingleUserListenerInterfaceFirestore {
                override fun onSuccess(user: User) {
                    UserObject.setUserDataStore(user, mainUserId)
                    if (isValidUser(user)) {
                        showProfile(user)
                    } else {
                        Timber.e("is not a valid user but reached edit profile screen")
                    }
                }
                override fun onFailure() {
                    Timber.e("retrieving single user data in edit profile fragment failed")
                }
            })
        }else {
            if (isValidUser(UserObject.user)) {
                showProfile(UserObject.user)
            } else {
                Timber.e("is not a valid user but reached edit profile screen")
            }
        }
    }

    fun showProfile(user: User?) {

        firstPhotoRef = "$mainUserId/${Constants.photoFileName1}"
        secondPhotoRef = "$mainUserId/${Constants.photoFileName2}"
        thirdPhotoRef = "$mainUserId/${Constants.photoFileName3}"
        fourthPhotoRef = "$mainUserId/${Constants.photoFileName4}"
        fifthPhotoRef = "$mainUserId/${Constants.photoFileName5}"
        sixthPhotoRef = "$mainUserId/${Constants.photoFileName6}"

        try {

            activity?.let {
                setPhotoIntoImageViewWithIceBreaker(it, firstPhotoRef, firstPhotoImageView, firstIcebreakerBackgroundImageView)

                setPhotoIntoImageViewWithIceBreaker(it, secondPhotoRef, secondPhotoImageView, secondIcebreakerBackgroundImageView)

                setPhotoIntoImageViewWithIceBreaker(it, thirdPhotoRef, thirdPhotoImageView, null)

                setPhotoIntoImageViewWithIceBreaker(it, fourthPhotoRef, fourthPhotoImageView, null)

                setPhotoIntoImageViewWithIceBreaker(it, fifthPhotoRef, fifthPhotoImageView, null)

                setPhotoIntoImageViewWithIceBreaker(it, sixthPhotoRef, sixthPhotoImageView, null)
            }

            val sdf = SimpleDateFormat("MM/dd/yyyy", Locale.US)
            val date = sdf.parse(user?.dob.toString())

            val iceBreaker1Questions = user?.iceBreaker1 as? HashMap<*, *>
            val iceBreaker2Questions = user?.iceBreaker2 as? HashMap<*, *>
            var iceBreaker1Question = ""
            var iceBreaker1Answer = ""
            var iceBreaker2Question = ""
            var iceBreaker2Answer = ""

            if (iceBreaker1Questions != null) {

                for (key in iceBreaker1Questions.keys) {

                    iceBreaker1Question = key.toString()
                }

                iceBreaker1Answer = iceBreaker1Questions[iceBreaker1Question].toString()

            }

            if (iceBreaker2Questions != null) {

                for (key2 in iceBreaker2Questions.keys) {

                    iceBreaker2Question = key2.toString()
                }

                iceBreaker2Answer = iceBreaker2Questions[iceBreaker2Question].toString()
            }

            userNameTextView.text = user?.name.toString()
            heightTextView.text = Utils.heightInFeetFromInchesWithQuotes(user?.height.toString().toDouble())
            ageTextView.text = Utils.getAge(date).toString() + " yrs"

            var school = ""
            school = user?.school.toString()

            if (!TextUtils.isEmpty(user?.school2.toString())) {
                school = school + "\n" + user?.school2.toString()
            }

            if (TextUtils.isEmpty(user?.profession.toString())) {
                hideProfession()
            } else {
                showProfession()
                professionTextView.text = user?.profession.toString()
            }

            if (TextUtils.isEmpty(school)) {
                hideSchool()
            } else {
                showSchool()
                educationTextView.text = school
            }

            if (TextUtils.isEmpty(user?.homeTown.toString())) {
                hideHomeTown()
            } else {
                showHomeTown()
                hometownTextView.text = user?.homeTown.toString()
            }

            if (user?.isHeightVerified == true) {

                showHeightVerifiedBadge()
            }else {

                hideHeightVerifiedBadge()
            }

            iceBreakerQuestion1TextView.text = iceBreaker1Question
            iceBreakerAnswer1TextView.text = iceBreaker1Answer
            iceBreakerQuestion2TextView.text = iceBreaker2Question
            iceBreakerAnswer2TextView.text = iceBreaker2Answer

        } catch (e: Exception) {

            Timber.e("exception in showing user profile: $e")
        }

    }

    private fun hideProfession() {
        professionLabel.visibility = View.GONE
        professionTextView.visibility = View.GONE

        invalidateLayout()
    }

    private fun showProfession() {
        professionLabel.visibility = View.VISIBLE
        professionTextView.visibility = View.VISIBLE

        invalidateLayout()
    }

    private fun hideSchool() {
        educationTextView.visibility = View.GONE
        educationLabel.visibility = View.GONE

        invalidateLayout()
    }

    private fun showSchool() {
        educationLabel.visibility = View.VISIBLE
        educationTextView.visibility = View.VISIBLE

        invalidateLayout()
    }

    private fun hideHomeTown() {
        homeTownLabel.visibility = View.GONE
        hometownTextView.visibility = View.GONE

        invalidateLayout()
    }

    private fun showHomeTown() {
        homeTownLabel.visibility = View.VISIBLE
        hometownTextView.visibility = View.VISIBLE

        invalidateLayout()
    }

    private fun hidePhotoImageView(photoView: ImageView) {

        photoView.visibility = View.GONE

        invalidateLayout()
    }

    private fun showPhotoImageView(photoView: ImageView) {

        photoView.visibility = View.VISIBLE

        invalidateLayout()
    }

    private fun invalidateLayout() {

        editProfileFragmentLayout.invalidate()
    }

    private fun showHeightVerifiedBadge() {

        heightVerifiedBadge.visibility = View.VISIBLE
        heightVerifiedTextView.visibility = View.VISIBLE
    }

    private fun hideHeightVerifiedBadge() {

        heightVerifiedBadge.visibility = View.GONE
        heightVerifiedTextView.visibility = View.GONE
    }

    private fun setPhotoIntoImageViewWithIceBreaker(activity: Activity,
                                                    spacesPath: String,
                                                    actualImageView: ImageView,
                                                    icebreakerImageView: ImageView?) {

        ImageLoaderModule.loadImageIntoImageViewWithLoadingAndCallback(activity.applicationContext, activity, spacesImagesViewModel, spacesPath, actualImageView, shouldCacheImage = true, onSuccess = {
            showPhotoImageView(actualImageView)
        }, onFailure = {
            hidePhotoImageView(actualImageView)
        })

        if (icebreakerImageView != null) {
            ImageLoaderModule.loadImageIntoImageViewWithLoadingAndCallback(activity.applicationContext, activity, spacesImagesViewModel, spacesPath, icebreakerImageView, shouldCacheImage = true, onSuccess = {
                showPhotoImageView(icebreakerImageView)
            }, onFailure = {
                hidePhotoImageView(icebreakerImageView)
            })
        }
    }
}
