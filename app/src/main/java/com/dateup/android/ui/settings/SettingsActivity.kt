package com.dateup.android.ui.settings

import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.activity.ProfessionActivity
import com.dateup.android.fragment.EditProfileFragment
import com.dateup.android.fragment.PreferencesFragment
import com.dateup.android.fragment.SettingsAccountFragment
import com.dateup.android.fragment.SettingsFragment
import com.dateup.android.heightVerification.ui.HeightVerificationScanResult.Companion.VERIFY_HEIGHT_STATUS
import com.dateup.android.models.HeightVerificationStatus
import com.dateup.android.models.UserObject
import com.dateup.android.paidVersion.changeLocation.TeleportFragment
import com.dateup.android.ui.BottomNavigationBarActivity
import com.dateup.android.utils.AlertDialogView
import com.dateup.android.utils.Constants
import com.dateup.android.utils.Constants.PREFERENCES_FRAGMENT_HOME_PROFILE_ACTIVITY
import com.dateup.android.utils.Constants.PREFERENCES_FRAGMENT_TELEPORT_FRAGMENT

class SettingsActivity : BottomNavigationBarActivity() {

    private lateinit var mLayoutContainer: FrameLayout

    override fun getBottomNavigationBarItem(): Int {

        return R.id.navigation_settings
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        initLayout()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun initLayout() {
        mLayoutContainer = findViewById(R.id.layout_container)

        val settingsLayout = View.inflate(this, R.layout.activity_settings, null)
        mLayoutContainer.addView(settingsLayout)

        when {
            intent.getBooleanExtra(PREFERENCES_FRAGMENT_HOME_PROFILE_ACTIVITY, false) -> {

                val preferencesFragment = PreferencesFragment()
                val arguments = Bundle()
                arguments.putString(Constants.PREFERENCES_FRAGMENT_ARG1, PREFERENCES_FRAGMENT_HOME_PROFILE_ACTIVITY)
                preferencesFragment.arguments = arguments
                supportFragmentManager.beginTransaction().add(R.id.main_container, preferencesFragment).commit()

            }
            intent.getBooleanExtra("EditProfileModalActivity", false) -> {

                val editProfileFragment = EditProfileFragment()

                supportFragmentManager
                        .beginTransaction()
                        .add(R.id.main_container, editProfileFragment)
                        .commit()
            }
            intent.getBooleanExtra("AccountSettingsUpdateEmailOrPhoneActivity", false) -> {

                val settingsAccountFragment = SettingsAccountFragment()

                supportFragmentManager
                        .beginTransaction()
                        .add(R.id.main_container, settingsAccountFragment)
                        .commit()
            }
            intent.getBooleanExtra(PREFERENCES_FRAGMENT_TELEPORT_FRAGMENT, false) -> {

                val teleportFragment = TeleportFragment()
                supportFragmentManager
                    .beginTransaction()
                    .add(R.id.main_container, teleportFragment)
                    .commit()
            }
            else -> {

                val settingsFragment = SettingsFragment()
                supportFragmentManager.beginTransaction().replace(R.id.main_container, settingsFragment).commit()
            }
        }

        intent.getStringExtra(VERIFY_HEIGHT_STATUS)?.let { showHeightVerificationAlert(it, this, false) }
    }

    companion object {

        fun newIntent(context: Context): Intent {
            return Intent(context, SettingsActivity::class.java)
        }

        fun showHeightVerificationAlert(status: String, activity: Activity, fromOnboarding: Boolean) {

            var alertTitle = ""
            var alertDesc = ""
            var alertButtonText = ""

            when (status) {

                HeightVerificationStatus.Success.name -> {

                    alertTitle = activity.getString(R.string.height_scan_success_alert_title)
                    alertDesc = activity.getString(R.string.height_scan_success_alert_desc)
                    alertButtonText = activity.getString(R.string.got_it)
                    UserObject.isHeightVerified = true
                    UserObject.user?.isHeightVerified = true
                }
                HeightVerificationStatus.ParsingError.name -> {

                    alertDesc = activity.getString(R.string.height_scan_failed_alert)
                    alertButtonText = activity.getString(R.string.okay)
                }
                HeightVerificationStatus.Error.name -> {

                    alertDesc = activity.getString(R.string.server_error_generic)
                    alertButtonText =activity.getString(R.string.okay)
                }
            }

            if (alertTitle.isNotEmpty() || alertDesc.isNotEmpty()) {

                AlertDialogView.showAlertDialog(context = activity,
                        title = alertTitle,
                        message = alertDesc,
                        buttonPositiveText = alertButtonText,
                        buttonNegativeText = null) { dialog, which ->

                    if (which == DialogInterface.BUTTON_POSITIVE) {

                        if (fromOnboarding) {
                            activity.startActivity(ProfessionActivity.newIntent(activity))
                        }else {
                            dialog.cancel()
                        }
                    }
                }
            }
        }
    }
}
