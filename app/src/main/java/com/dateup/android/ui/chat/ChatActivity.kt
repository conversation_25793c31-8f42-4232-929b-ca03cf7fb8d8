package com.dateup.android.ui.chat

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.chat.dialog.ChatDialogListFragment
import com.dateup.android.chat.message.MessageListActivity.Companion.UN_MATCHED_USER_ID
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.ui.BottomNavigationBarActivity

class ChatActivity : BottomNavigationBarActivity() {

    private lateinit var mLayoutContainer: FrameLayout

    override fun getBottomNavigationBarItem(): Int {

        return R.id.navigation_chat
    }

    companion object {

        fun newIntent(context: Context): Intent {
            return Intent(context, ChatActivity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        initLayout()

        isChatScreenActive = true

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun initLayout() {

        mLayoutContainer = findViewById(R.id.layout_container)

        val browseProfilesLayout = View.inflate(this, R.layout.activity_chat, null)
        mLayoutContainer.addView(browseProfilesLayout)

        val chatFragment = ChatDialogListFragment()

        val bundle = Bundle()

        when {
            intent.getStringExtra(UN_MATCHED_USER_ID) != null -> {
                val id = intent.getStringExtra(UN_MATCHED_USER_ID)
                val firebaseDatabaseUtil = FirebaseDatabaseUtil(this)
                firebaseDatabaseUtil.deleteUnmatchedUserFromMainUserNode(id) {
                    supportFragmentManager.beginTransaction().replace(R.id.main_container, chatFragment).commit()
                }
            }
            else -> {
                chatFragment.arguments = bundle
                supportFragmentManager.beginTransaction().replace(R.id.main_container, chatFragment).commit()
            }
        }
    }
}
