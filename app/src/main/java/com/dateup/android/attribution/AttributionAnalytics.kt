package com.dateup.android.attribution

import android.content.Context
import android.os.Bundle
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.SkuDetails
import com.dateup.android.analytics.AD_SOURCE
import com.dateup.android.analytics.AnalyticsTrackingService
import com.dateup.android.analytics.APP_INSTALL
import com.dateup.android.analytics.FAILURE_REASON
import com.dateup.android.analytics.ONBOARDING_COMPLETE
import com.dateup.android.analytics.PRICE
import com.dateup.android.analytics.RESPONSE_CODE
import com.dateup.android.analytics.SKU
import com.dateup.android.analytics.SUBSCRIPTION_FAILED
import com.dateup.android.analytics.SUBSCRIPTION_SUCCESS
import timber.log.Timber

/**
 * Utility class for attribution-enhanced analytics tracking
 */
object AttributionAnalytics {

    private const val TAG = "AttributionAnalytics"

    /**
     * Log app install with attribution source - called when attribution is first detected
     */
    fun logAppInstall(context: Context, attributionSource: String) {
        try {
            val bundle = Bundle()
            bundle.putString(AD_SOURCE, attributionSource)

            AnalyticsTrackingService.logEvent(context, APP_INSTALL, bundle)
            Timber.tag(TAG).d("Logged app install with attribution: $attributionSource")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Error logging app install")
        }
    }

    /**
     * Log onboarding completion with attribution source
     */
    fun logOnboardingComplete(context: Context) {
        try {
            val bundle = Bundle()
            val attributionSource = AttributionManager.getInstance().getAttributionSource(context)
            bundle.putString(AD_SOURCE, attributionSource)
            
            AnalyticsTrackingService.logEvent(context, ONBOARDING_COMPLETE, bundle)
            Timber.tag(TAG).d("Logged onboarding complete with attribution: $attributionSource")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Error logging onboarding complete")
        }
    }

    /**
     * Log subscription success with attribution and purchase details
     */
    fun logSubscriptionSuccess(
        context: Context, 
        purchase: Purchase, 
        skuDetails: SkuDetails? = null
    ) {
        try {
            val bundle = Bundle()
            
            // Add attribution source
            val attributionSource = AttributionManager.getInstance().getAttributionSource(context)
            bundle.putString(AD_SOURCE, attributionSource)
            
            // Add SKU information
            if (purchase.skus.isNotEmpty()) {
                bundle.putString(SKU, purchase.skus[0])
            }
            
            // Add price if available from SkuDetails
            skuDetails?.let { details ->
                bundle.putString(PRICE, details.price)
            }
            
            AnalyticsTrackingService.logEvent(context, SUBSCRIPTION_SUCCESS, bundle)
            Timber.tag(TAG).d("Logged subscription success with attribution: $attributionSource, SKU: ${purchase.skus.firstOrNull()}")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Error logging subscription success")
        }
    }

    /**
     * Log subscription failure with attribution and failure details
     */
    fun logSubscriptionFailure(
        context: Context,
        responseCode: Int,
        debugMessage: String?,
        sku: String? = null
    ) {
        try {
            val bundle = Bundle()

            // Add attribution source
            val attributionSource = AttributionManager.getInstance().getAttributionSource(context)
            bundle.putString(AD_SOURCE, attributionSource)

            // Add failure details
            bundle.putInt(RESPONSE_CODE, responseCode)
            debugMessage?.let { bundle.putString(FAILURE_REASON, it) }
            sku?.let { bundle.putString(SKU, it) }

            AnalyticsTrackingService.logEvent(context, SUBSCRIPTION_FAILED, bundle)
            Timber.tag(TAG).d("Logged subscription failure with attribution: $attributionSource, code: $responseCode")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Error logging subscription failure")
        }
    }
}
