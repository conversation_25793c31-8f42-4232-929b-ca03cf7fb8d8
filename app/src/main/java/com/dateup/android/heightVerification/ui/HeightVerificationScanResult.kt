package com.dateup.android.heightVerification.ui

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.res.ResourcesCompat
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.activity.Intro1Activity
import com.dateup.android.activity.ProfessionActivity
import com.dateup.android.databinding.ActivityHeightVerificationScanResultBinding
import com.dateup.android.databinding.CodeVerificationActivityBinding
import com.dateup.android.extensions.launchModeWithNoBackStack
import com.dateup.android.extensions.launchModeWithSingleTop
import com.dateup.android.models.HeightVerificationStatus
import com.dateup.android.models.VerifyHeightConfirmationResult
import com.dateup.android.models.VerifyHeightResult
import com.dateup.android.ui.browseProfiles.BrowseProfilesActivity
import com.dateup.android.ui.settings.SettingsActivity
import com.dateup.android.utils.Constants
import com.dateup.android.utils.FontSpan
import com.dateup.android.utils.Utils
import com.google.firebase.functions.ktx.functions
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
import timber.log.Timber


class HeightVerificationScanResult : AppCompatActivity() {

    private lateinit var binding: ActivityHeightVerificationScanResultBinding

    companion object {

        const val VERIFY_HEIGHT_RESULT = "VERIFY_HEIGHT_RESULT"
        const val LIC_RAW_DATA = "LIC_RAW_DATA"
        const val VERIFY_HEIGHT_STATUS = "VERIFY_HEIGHT_STATUS"

        fun newIntent(context: Context): Intent {

            return Intent(context, HeightVerificationScanResult::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityHeightVerificationScanResultBinding.inflate(layoutInflater)
        setContentView(binding.root)

        val verificationResult = intent?.getSerializableExtra(VERIFY_HEIGHT_RESULT) as? VerifyHeightResult

        initLayout(verificationResult)
    }

    private fun initLayout(verificationResult: VerifyHeightResult?) {

        var statusText = ""
        var statusDescription = SpannableString("")
        var actionButtonText = ""

        when (verificationResult?.status) {

            HeightVerificationStatus.Success.name -> {

                val height = verificationResult.height?.let { Utils.heightInFeetFromInchesWithQuotes(it) }

                statusText = getString(R.string.scan_success)
                statusDescription = SpannableString(this.getString(R.string.scan_result_desc_success, height, verificationResult.age.toString()))
                statusDescription.setSpan(FontSpan(ResourcesCompat.getFont(this, R.font.font_nunitosans_bold)), 32, 36, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                statusDescription.setSpan(FontSpan(ResourcesCompat.getFont(this, R.font.font_nunitosans_bold)), 53, 56, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                statusDescription.setSpan(ForegroundColorSpan(Color.parseColor("#000000")), 32, 36, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                statusDescription.setSpan(ForegroundColorSpan(Color.parseColor("#000000")), 53, 56, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)

                actionButtonText = getString(R.string.accept_verification)

                binding.cancelVerifyTextView.visibility = View.VISIBLE
                binding.iconEmptyMagnifyingGlassImageView.setImageResource(R.drawable.ic_success_check)
                binding.iconEmptyMagnifyingGlassImageView.visibility = View.VISIBLE

                binding.buttonAcceptVerify.setOnClickListener {

                    if (verificationResult.height != null && verificationResult.dob != null) {

                        verifyHeightConfirmation(verificationResult.height, verificationResult.dob, verificationResult.status)
                    }
                }
            }
            HeightVerificationStatus.DobMismatch.name -> {

                statusText = getString(R.string.height_failed_dob_mismatch_title)
                statusDescription = SpannableString(getString(R.string.height_failed_dob_mismatch_desc))
                actionButtonText = getString(R.string.okay)

                binding.buttonAcceptVerify.setOnClickListener {

                    startBrowseProfileActivity()
                }
            }
            HeightVerificationStatus.UserAgeRestricted.name -> {

                verifyHeightConfirmation(verificationResult.height, verificationResult.dob, verificationResult.status)
            }
            else -> {

                statusText = getString(R.string.id_not_verified)
                statusDescription = SpannableString(getString(R.string.sca_result_desc_failed))
                actionButtonText = getString(R.string.submit_licence)
                binding.cancelVerifyTextView.text = getString(R.string.cancel)

                binding.cancelVerifyTextView.visibility = View.VISIBLE
                binding.iconEmptyMagnifyingGlassImageView.setImageResource(R.drawable.ic_license_not_scanned)
                binding.iconEmptyMagnifyingGlassImageView.visibility = View.VISIBLE

                binding.buttonAcceptVerify.setOnClickListener {

                    val raw: String? = intent?.getStringExtra(LIC_RAW_DATA)
                    raw?.let {
                        licRaw -> saveUnaparsedLicData(licRaw)
                    }
                }
            }
        }

        binding.scanStatusTextView.text = statusText
        binding.scanStatusDescTextView.text = statusDescription
        binding.buttonAcceptVerify.text = actionButtonText

        binding.cancelVerifyTextView.setOnClickListener {

            startBrowseProfileActivity()
        }
    }

    private fun saveUnaparsedLicData(raw: String) {

        binding.progress.progressBar.visibility = View.VISIBLE
        binding.progress.progressTextView.visibility = View.VISIBLE

        val functions = Firebase.functions

        val firebaseUID = AccountPreferences.getInstance(this).getStringValue(Constants.firebaseUserId, "")

        val data = hashMapOf(
                "rawText" to raw,
                "uid" to firebaseUID
        )

        functions
                .getHttpsCallable("saveUnparsedLicData")
                .call(data)
                .addOnCompleteListener { task ->

                    binding.progress.progressBar.visibility = View.GONE
                    binding.progress.progressTextView.visibility = View.GONE

                    if (!task.isSuccessful) {

                        Timber.e("exception in saving unparsed height data ${task.exception}")
                        startSettingsActivity(HeightVerificationStatus.ParsingError.name)
                    } else {
                        startBrowseProfileActivity()
                    }
                }
    }

    private fun verifyHeightConfirmation(height: Double?, dob: String?, status: String) {

        if (status == HeightVerificationStatus.UserAgeRestricted.name) {

            val intent = Intro1Activity.newIntent(this).launchModeWithNoBackStack()
            intent.putExtra(Intro1Activity.IS_USER_AGE_RESTRICTED, true)
            startActivity(intent)
        } else if (status == HeightVerificationStatus.Error.name || status == HeightVerificationStatus.ParsingError.name) {

            startSettingsActivity(status)
        } else {

            binding.progress.progressBar.visibility = View.VISIBLE
            binding.progress.progressTextView.visibility = View.VISIBLE

            val functions = Firebase.functions

            val firebaseUID = AccountPreferences.getInstance(this).getStringValue(Constants.firebaseUserId, "")
            val gender = AccountPreferences.getInstance(this).getStringValue(Constants.gender, "")

            val data = hashMapOf(
                    "dob" to dob,
                    "height" to height,
                    "uid" to firebaseUID,
                    "gender" to gender
            )

            functions
                    .getHttpsCallable("verifyHeightConfirmation")
                    .call(data)
                    .addOnCompleteListener { task ->
                        binding.progress.progressBar.visibility = View.GONE
                        binding.progress.progressTextView.visibility = View.GONE
                        if (!task.isSuccessful) {
                            Timber.e("exception in confirm height ${task.exception}")
                            startSettingsActivity(HeightVerificationStatus.Error.name)
                        } else {
                            val result = Gson().fromJson(task.result?.getData().toString(), VerifyHeightConfirmationResult::class.java)
                            startSettingsActivity(result.status.toString())
                        }
                    }
        }
    }

    private fun startSettingsActivity(status: String) {

        if (intent.getStringExtra(HeightVerificationScan.FROM_ONBOARDING) != null) {

            SettingsActivity.showHeightVerificationAlert(status, this, true)
        }else {
            val intent = SettingsActivity.newIntent(this).launchModeWithSingleTop()
            intent.putExtra(VERIFY_HEIGHT_STATUS, status)
            startActivity(intent)
        }
    }

    private fun startBrowseProfileActivity() {

        if (intent.getStringExtra(HeightVerificationScan.FROM_ONBOARDING) != null) {

            startProfessionActivity()
        }else {

            val intent = BrowseProfilesActivity.newIntent(this).launchModeWithNoBackStack()
            this.startActivity(intent)
        }
    }

    private fun startProfessionActivity() {

        startActivity(ProfessionActivity.newIntent(this))
    }
}