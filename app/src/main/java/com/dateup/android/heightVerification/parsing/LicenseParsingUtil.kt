package com.dateup.android.heightVerification.parsing

class LicenseParsingUtil {

    /*
     * https://developers.google.com/android/reference/com/google/android/gms/vision/barcode/Barcode.DriverLicense#documentType
     */

    companion object {

        const val CITY = "DAI"
        const val STATE = "DAJ"
        const val ZIP = "DAK"
        const val BIRTH_DATE = "DBB"
        const val FIRST_NAME = "DAC"
        const val GENDER = "DBC"
        const val LAST_NAME = "DCS"
        const val LICENSE_NUMBER = "DAQ"
        const val HEIGHT = "DAU"

        /*
     * https://www.aamva.org/DL-ID-Card-Design-Standard/
     */

        const val DAV = "DAV"
        const val DBL = "DBL"
        const val DBO = "DBO"
        const val DBP = "DBP"
    }

    val driversLicenseInfoMap: HashMap<String, String?> = object : HashMap<String, String?>() {
        init {
            //put(LAST_NAME, "Last Name:")
            put(FIRST_NAME, "First Name:")
            //put(BIRTH_DATE, "Date of Birth:")
            //put(GENDER, "Physical Description – Sex:")
            put(CITY, "Address – City:")
            put(STATE, "Address – Jurisdiction Code:")
            put(ZIP, "Address – Zip Code:")
            //put(LICENSE_NUMBER, "Customer ID Number:")
            put(HEIGHT, "Height in Inches:")

            // Old standard
            put(DAV, "Height in CM:")
            put(DBL, "Date Of Birth:")
            //put(DBO, "Last Name:")
            put(DBP, "First Name:")
        }
    }

    fun readUSDriverLicense(resultText: String): HashMap<String, String> {

        val resultMap: HashMap<String, String> = HashMap()

        for ((key) in driversLicenseInfoMap) {
            try {
                val startIndex = resultText.indexOf("""$key""".trimIndent())

                if (startIndex != -1) {

                    val endIndex = resultText.indexOf("\n", startIndex + key.length + 1)
                    val value = resultText.substring(startIndex + key.length, endIndex)

                    resultMap[key] = value
                }
            } catch (ex: Exception) {

                ex.printStackTrace()
            }
        }
        return resultMap
    }

//    fun readUSDDL(resultText: String): HashMap<String, String> {
//
//        var resultText = resultText
//        val resultMap: HashMap<String, String> = HashMap()
//        resultText = resultText.substring(resultText.indexOf("\n") + 1)
//
//        val end = resultText.indexOf("\n")
//        val firstLine = resultText.substring(0, end + 1)
//
//        var findFirstLine = false
//        for ((key, mapValue) in driversLicenseInfoMap) {
//            try {
//                val startIndex = resultText.indexOf("""$key""".trimIndent())
//
//                if (startIndex != -1) {
//
//                    val endIndex = resultText.indexOf("\n", startIndex + key.length + 1)
//                    val value = resultText.substring(startIndex + key.length, endIndex)
//
//                    resultMap[key] = value
//                    //Timber.d("Testing license value: $key - $mapValue and value: $value")
//                } else if (!findFirstLine) {
//
//                    val index = firstLine.indexOf(key)
//
//                    if (index != -1) {
//
//                        val endIndex = firstLine.indexOf("\n", key.length + 1)
//                        val value = firstLine.substring(index + key.length, endIndex)
//                        resultMap[key] = value
//                        findFirstLine = true
//                    }
//                }
//            } catch (ex: Exception) {
//
//                ex.printStackTrace()
//            }
//        }
//        return resultMap
//    }
//
//    fun ifDriverLicense(barcodeText: String?): Boolean {
//
//        if (barcodeText == null || barcodeText.length < 21) {
//            return false
//        }
//
//        val str = barcodeText.trim { it <= ' ' }.replace("\r", "\n")
//        val strArray = str.split("\n".toRegex()).toTypedArray()
//        val strList = ArrayList<String>()
//
//        for (i in strArray.indices) {
//            if (strArray[i].isNotEmpty()) {
//
//                strList.add(strArray[i])
//            }
//        }
//        if (strList[0] == "@") {
//
//            val data = strList[2]
//            if ((data[0] == 'A' && data[1] == 'N' && data[2] == 'S' && data[3] == 'I'
//                            && data[4] == ' ' || data[0] == 'A' && data[1] == 'A'
//                            && data[2] == 'M' && data[3] == 'V' && data[4] == 'A')
//                    && data[5] >= '0' && data[5] <= '9' && data[6] >= '0' && data[6] <= '9' && data[7] >= '0' && data[7] <= '9'
//                    && data[8] >= '0' && data[8] <= '9' && data[9] >= '0' && data[9] <= '9' && data[10] >= '0' && data[10] <= '9'
//                    && data[11] >= '0' && data[11] <= '9' && data[12] >= '0' && data[12] <= '9'
//                    && data[13] >= '0' && data[13] <= '9' && data[14] >= '0' && data[14] <= '9') {
//                return true
//            }
//        }
//        return false
//    }
}
