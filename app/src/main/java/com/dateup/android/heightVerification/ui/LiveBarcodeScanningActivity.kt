/*
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dateup.android.heightVerification.ui

import android.content.Context
import android.content.Intent
import android.hardware.Camera
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.activity.HowHeightVerificationWorks
import com.dateup.android.analytics.*
import com.dateup.android.databinding.ActivityLiveBarcodeBinding
import com.dateup.android.extensions.launchModeWithSingleTop
import com.dateup.android.heightVerification.barcodedetection.BarcodeProcessor
import com.dateup.android.heightVerification.barcodedetection.WorkflowModel
import com.dateup.android.heightVerification.camera.CameraSource
import com.dateup.android.heightVerification.camera.CameraSourcePreview
import com.dateup.android.heightVerification.camera.GraphicOverlay
import com.dateup.android.heightVerification.models.BarcodeField
import com.dateup.android.heightVerification.ui.HeightVerificationScanResult.Companion.LIC_RAW_DATA
import com.dateup.android.heightVerification.ui.HeightVerificationScanResult.Companion.VERIFY_HEIGHT_RESULT
import com.dateup.android.models.HeightVerificationStatus
import com.dateup.android.models.VerifyHeightResult
import com.dateup.android.ui.settings.SettingsActivity
import com.dateup.android.ui.settings.SettingsActivity.Companion.showHeightVerificationAlert
import com.dateup.android.utils.Constants
import com.google.android.gms.common.internal.Objects
import com.google.firebase.functions.FirebaseFunctionsException
import com.google.firebase.functions.ktx.functions
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
import timber.log.Timber
import java.io.IOException
import java.util.*

/** Demonstrates the barcode scanning workflow using camera preview.  */
class LiveBarcodeScanningActivity : AppCompatActivity(), OnClickListener {

    private lateinit var binding: ActivityLiveBarcodeBinding

    private var cameraSource: CameraSource? = null
    private var preview: CameraSourcePreview? = null
    private var graphicOverlay: GraphicOverlay? = null
    private var settingsButton: View? = null
    private var flashButton: View? = null

    private var workflowModel: WorkflowModel? = null
    private var currentWorkflowState: WorkflowModel.WorkflowState? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityLiveBarcodeBinding.inflate(layoutInflater)
        this.setContentView(binding.root)
        preview = findViewById(R.id.camera_preview)
        graphicOverlay = findViewById<GraphicOverlay>(R.id.camera_preview_graphic_overlay).apply {
            setOnClickListener(this@LiveBarcodeScanningActivity)
            cameraSource = CameraSource(this)
        }

        findViewById<View>(R.id.close_button).setOnClickListener(this)
        flashButton = findViewById<View>(R.id.flash_button).apply {
            setOnClickListener(this@LiveBarcodeScanningActivity)
        }

        setUpWorkflowModel()

        binding.helpTextView.setOnClickListener {

            startHelpActivity()
        }
    }

    override fun onResume() {
        super.onResume()

        workflowModel?.markCameraFrozen()
        settingsButton?.isEnabled = true
        currentWorkflowState = WorkflowModel.WorkflowState.NOT_STARTED
        cameraSource?.setFrameProcessor(BarcodeProcessor(graphicOverlay!!, workflowModel!!))
        workflowModel?.setWorkflowState(WorkflowModel.WorkflowState.DETECTING)
    }

    override fun onPause() {
        super.onPause()
        currentWorkflowState = WorkflowModel.WorkflowState.NOT_STARTED
        stopCameraPreview()
    }

    override fun onDestroy() {
        super.onDestroy()
        cameraSource?.release()
        cameraSource = null
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.close_button -> onBackPressed()
            R.id.flash_button -> {
                flashButton?.let {
                    if (it.isSelected) {
                        it.isSelected = false
                        cameraSource?.updateFlashMode(Camera.Parameters.FLASH_MODE_OFF)
                    } else {
                        it.isSelected = true
                        cameraSource!!.updateFlashMode(Camera.Parameters.FLASH_MODE_TORCH)
                    }
                }
            }
        }
    }

    private fun startCameraPreview() {
        val workflowModel = this.workflowModel ?: return
        val cameraSource = this.cameraSource ?: return
        if (!workflowModel.isCameraLive) {
            try {
                workflowModel.markCameraLive()
                preview?.start(cameraSource)
            } catch (e: IOException) {
                Timber.d("Failed to start camera preview! $e")
                cameraSource.release()
                this.cameraSource = null
            }
        }
    }

    private fun stopCameraPreview() {
        val workflowModel = this.workflowModel ?: return
        if (workflowModel.isCameraLive) {
            workflowModel.markCameraFrozen()
            flashButton?.isSelected = false
            preview?.stop()
        }
    }

    private fun setUpWorkflowModel() {
        workflowModel = ViewModelProvider(this).get(WorkflowModel::class.java)

        // Observes the workflow state changes, if happens, update the overlay view indicators and
        // camera preview state.
        workflowModel!!.workflowState.observe(this, Observer { workflowState ->
            if (workflowState == null || Objects.equal(currentWorkflowState, workflowState)) {
                return@Observer
            }

            currentWorkflowState = workflowState

            when (workflowState) {
                WorkflowModel.WorkflowState.DETECTING -> {
                    startCameraPreview()
                }
                WorkflowModel.WorkflowState.CONFIRMING -> {
                    startCameraPreview()
                }
                WorkflowModel.WorkflowState.SEARCHING -> {
                    stopCameraPreview()
                }
                WorkflowModel.WorkflowState.DETECTED, WorkflowModel.WorkflowState.SEARCHED -> {
                    stopCameraPreview()
                }

                else -> {}
            }
        })

        workflowModel?.detectedBarcode?.observe(this) { barcode ->
            val barcodeFieldList = ArrayList<BarcodeField>()
            barcodeFieldList.add(BarcodeField("Raw Value", barcode.rawValue ?: ""))
            verifyHeight(barcode.rawValue ?: "")
        }
    }

    private fun verifyHeight(rawText: String) {

        binding.progress.progressBar.visibility = View.VISIBLE
        binding.progress.progressTextView.visibility = View.VISIBLE

        val functions = Firebase.functions

        val firebaseUID = AccountPreferences.getInstance(this).getStringValue(Constants.firebaseUserId, "")

        val data = hashMapOf(
                "rawText" to rawText,
                "uid" to firebaseUID
        )

        functions
                .getHttpsCallable("verifyHeight")
                .call(data)
                .addOnCompleteListener { task ->

                    binding.progress.progressBar.visibility = View.GONE
                    binding.progress.progressTextView.visibility = View.GONE

                    val bundle = Bundle()

                    if (!task.isSuccessful) {
                        bundle.putString(STATUS, ANALYTICS_FAILURE)
                        task.exception?.let {
                            bundle.putString(ERROR_REASON, it.message)
                            if (it is FirebaseFunctionsException) {
                                bundle.putString(ERROR_CODE, it.code.name)
                            }
                        }
                        Timber.e("height verification error : ${task.exception}")

                        startSettingsActivity(HeightVerificationStatus.Error.name)
                    } else {

                        val result = Gson().fromJson(task.result?.getData().toString(), VerifyHeightResult::class.java)

                        bundle.putString(STATUS, result?.status.toString())

                        startHeightVerificationResult(result, rawText)
                    }

                    AnalyticsTrackingService.logEvent(this, HEIGHT_VERIFICATION, bundle)
                }
    }

    private fun startSettingsActivity(status: String) {

        if (intent.getStringExtra(HeightVerificationScan.FROM_ONBOARDING) != null) {

            showHeightVerificationAlert(status, this, true)
        } else {

            val intent = SettingsActivity.newIntent(this).launchModeWithSingleTop()
            intent.putExtra(HeightVerificationScanResult.VERIFY_HEIGHT_STATUS, status)
            startActivity(intent)
        }
    }

    private fun startHeightVerificationResult(verifyHeightResult: VerifyHeightResult?, rawText: String) {

        val intent = HeightVerificationScanResult.newIntent(this).launchModeWithSingleTop()
        intent.putExtra(VERIFY_HEIGHT_RESULT, verifyHeightResult)

        if (<EMAIL>(HeightVerificationScan.FROM_ONBOARDING) != null) {
            intent.putExtra(HeightVerificationScan.FROM_ONBOARDING, "true")
        }

        intent.putExtra(LIC_RAW_DATA, rawText)
        startActivity(intent)
    }

    private fun startHelpActivity() {

        val intent = HowHeightVerificationWorks.newIntent(this)
        startActivity(intent)
    }

    companion object {
        private const val TAG = "LiveBarcodeActivity"

        fun newIntent(context: Context): Intent {
            return Intent(context, LiveBarcodeScanningActivity::class.java)
        }
    }
}
