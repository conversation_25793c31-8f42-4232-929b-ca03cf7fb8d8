package com.dateup.android.heightVerification.ui

import android.Manifest
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ReportFragment.Companion.reportFragment
import com.dateup.android.R
import com.dateup.android.activity.ProfessionActivity
import com.dateup.android.databinding.ActivityHeightVerificationScanBinding
import com.dateup.android.databinding.CodeVerificationActivityBinding
import com.dateup.android.extensions.launchModeWithSingleTop
import com.dateup.android.utils.ManagePermissions

class HeightVerificationScan : AppCompatActivity() {

    private lateinit var binding: ActivityHeightVerificationScanBinding

    companion object {

        const val FROM_ONBOARDING = "from_onboarding"

        fun newIntent(context: Context): Intent {

            return Intent(context, HeightVerificationScan::class.java)
        }
    }

    private lateinit var managePhotoPermission: ManagePermissions
    private val photoPermissionRequestCode = 600
    private val photoPermissionList = listOf(
            Manifest.permission.CAMERA
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityHeightVerificationScanBinding.inflate(layoutInflater)
        setContentView(binding.root)

        managePhotoPermission = ManagePermissions(this, photoPermissionList, photoPermissionRequestCode)

        if (intent.getStringExtra(FROM_ONBOARDING) != null) {

            binding.cancelTextView.text = getString(R.string.profession_activity_edit_menu_item_text)
            binding.cancelTextView.setOnClickListener {

                startProfessionActivity()
            }
        }else {

            binding.cancelTextView.setOnClickListener {

                onBackPressed()
            }
        }

        binding.buttonScan.setOnClickListener {

            askPhotoPermission()
        }
    }

    private fun startProfessionActivity() {

        startActivity(ProfessionActivity.newIntent(this))
    }

    private fun askPhotoPermission() {

        if (managePhotoPermission.isPermissionGranted()) {

            startHeightVerification()
        } else {
            managePhotoPermission.checkPermissions()
        }
    }

    private fun startHeightVerification() {

        val intent = Intent(this, LiveBarcodeScanningActivity::class.java).launchModeWithSingleTop()

        if (<EMAIL>(FROM_ONBOARDING) != null) {

            intent.putExtra(FROM_ONBOARDING, "true")
        }
        startActivity(intent)
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>,
                                            grantResults: IntArray) {
        when (requestCode) {
            photoPermissionRequestCode -> {
                val isPermissionsGranted = managePhotoPermission
                        .processPermissionsResult(requestCode, permissions, grantResults)

                if (isPermissionsGranted) {

                    startHeightVerification()
                } else {
                    showEnableCameraLayout()
                }
                return
            }
        }
    }

    private fun showEnableCameraLayout() {
        binding.layoutEnableCamera.root.visibility = View.VISIBLE
        binding.layoutEnableCamera.buttonOpenSettings.setOnClickListener {
            startActivityForResult(Intent().apply {
                action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                data = Uri.fromParts("package", packageName, null)
            }, photoPermissionRequestCode)
        }
    }
}