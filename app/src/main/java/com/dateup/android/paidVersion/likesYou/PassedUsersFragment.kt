package com.dateup.android.paidVersion.likesYou

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.cloudFunctions.GetPassedUsers
import com.dateup.android.extensions.launchModeWithNoBackStack
import com.dateup.android.extensions.launchModeWithSingleTop
import com.dateup.android.models.LikesTabUser
import com.dateup.android.models.UserObject
import com.dateup.android.subscriptions.views.SubscriptionActivity
import com.dateup.android.utils.Constants
import com.dateup.android.viewModels.SpacesImagesViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber

class PassedUsersFragment : Fragment() {

    private lateinit var recyclerView: RecyclerView
    private lateinit var progressBar: ProgressBar
    private lateinit var emptyTextView: TextView
    private lateinit var passedUsersAdapter: PassedUsersAdapter
    private lateinit var spacesImagesViewModel: SpacesImagesViewModel

    private var currentPage = Constants.INITIAL_PAGE
    private var isLoading = false
    private var isLastPage = false
    private var pageSize = Constants.PASSED_USERS_PAGE_SIZE

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_passed_users, container, false)
        recyclerView = view.findViewById(R.id.passed_users_recycler_view)
        progressBar = view.findViewById(R.id.progressBar)
        emptyTextView = view.findViewById(R.id.emptyTextView)

        setupRecyclerView()
        setupScrollListener()

        activity?.let {
            if (UserObject.isDateUpSelectUser == false || UserObject.isDateUpSelectUser == null) {
                startActivity(SubscriptionActivity.newIntent(it).launchModeWithNoBackStack())
            } else {
                fetchUsers(refresh = true)
            }
        }

        return view
    }

    private fun setupRecyclerView() {
        recyclerView.layoutManager = GridLayoutManager(requireContext(), 2)
        spacesImagesViewModel = ViewModelProvider(this)[SpacesImagesViewModel::class.java]

        recyclerView.layoutManager = GridLayoutManager(requireContext(), 2)
        val spacing = resources.getDimensionPixelSize(R.dimen.likes_you_recycler_item_spacing)
        recyclerView.addItemDecoration(LikesYouItemDecoration(spacing))


        passedUsersAdapter = PassedUsersAdapter(requireActivity(), spacesImagesViewModel)
        recyclerView.adapter = passedUsersAdapter
    }

    private fun setupScrollListener() {
        recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                val layoutManager = recyclerView.layoutManager as GridLayoutManager
                val visibleItemCount = layoutManager.childCount
                val totalItemCount = layoutManager.itemCount
                val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()

                if (!isLoading && !isLastPage) {
                    // Start loading when user is Constants.VISIBLE_THRESHOLD items away from the end
                    if ((visibleItemCount + firstVisibleItemPosition + Constants.VISIBLE_THRESHOLD) >= totalItemCount
                        && firstVisibleItemPosition >= 0
                        && totalItemCount >= pageSize
                    ) {
                        loadNextPage()
                    }
                }
            }
        })
    }

    private fun loadNextPage() {
        currentPage++
        fetchUsers(refresh = false)
    }

    private fun updateViewsVisibility(users: List<LikesTabUser>, isFirstPage: Boolean) {
        progressBar.visibility = View.GONE
        if (isFirstPage && users.isEmpty()) {
            // Show empty state only on first page when no users
            recyclerView.visibility = View.GONE
            emptyTextView.visibility = View.VISIBLE
        } else {
            recyclerView.visibility = View.VISIBLE
            emptyTextView.visibility = View.GONE
        }
    }

    private fun fetchUsers(refresh: Boolean = false) {
        if (isLoading) return
        
        if (refresh) {
            currentPage = Constants.INITIAL_PAGE
            isLastPage = false
        }

        isLoading = true
        progressBar.visibility = View.VISIBLE
        if (!refresh) {
            // Hide empty state when loading more pages
            emptyTextView.visibility = View.GONE
        }
        val uid = AccountPreferences.getInstance(requireContext()).getStringValue(Constants.firebaseUserId, "")
        if (uid.isEmpty()) {
            handleError()
            return
        }

        getPassedUsers(
            uid = uid,
            page = currentPage,
            limit = pageSize,
            onSuccess = { users ->
                isLoading = false
                
                if (users.isEmpty()) {
                    isLastPage = true
                }

                if (refresh) {
                    passedUsersAdapter.setUsers(users)
                } else {
                    passedUsersAdapter.appendUsers(users)
                }

                updateViewsVisibility(users, refresh)
            },
            onError = {
                handleError()
            }
        )
    }

    private fun handleError() {
        progressBar.visibility = View.GONE
        isLoading = false
        if (passedUsersAdapter.itemCount == 0) {
            // Show empty state on error if no items are displayed
            recyclerView.visibility = View.GONE
            emptyTextView.visibility = View.VISIBLE
            emptyTextView.text = "Error loading passed users. Please try again later"
        }
        Timber.e("Error loading passed users")
    }

    private fun getPassedUsers(
        uid: String,
        page: Int,
        limit: Int,
        onSuccess: (List<LikesTabUser>) -> Unit,
        onError: () -> Unit
    ) {
        if (uid.isEmpty()) {
            onError()
            return
        }

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val response = GetPassedUsers.getPassedUsers(uid, page, limit)
                withContext(Dispatchers.Main) {
                    when (response.second) {
                        GetPassedUsers.GetUsersResponse.SUCCESS -> {
                            response.first?.let { users ->
                                onSuccess(users)
                            } ?: onError()
                        }
                        GetPassedUsers.GetUsersResponse.FAILED -> {
                            onError()
                        }
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "Error fetching passed users")
                withContext(Dispatchers.Main) {
                    onError()
                }
            }
        }
    }
}
