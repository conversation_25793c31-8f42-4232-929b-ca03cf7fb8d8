package com.dateup.android.paidVersion.changeLocation

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.dateup.android.R
import com.dateup.android.models.UserObject

class UnlockedLocationsFragment : Fragment() {

    private lateinit var unlockedLocationsRecyclerList: RecyclerView
    private lateinit var unlockedLocationsRecyclerAdapter: UnlockedLocationsRecyclerAdapter
    private lateinit var progressBar: ProgressBar
    private var teleportOrigin: String? = null
    private var fromSettings = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        arguments?.let {
            teleportOrigin = it.getString(TELEPORT_ORIGIN)
        }

        if (teleportOrigin == TELEPORT_FROM_PREFERENCES) {
            fromSettings = true
        }else if (teleportOrigin == TELEPORT_FROM_BROWSE_PROFILES) {
            fromSettings = false
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.fragment_unlocked_locations_list, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        unlockedLocationsRecyclerList = view.findViewById(R.id.unlocked_locations_list)
        progressBar = view.findViewById(R.id.progressBar)

        unlockedLocationsRecyclerList.layoutManager = LinearLayoutManager(activity, LinearLayoutManager.VERTICAL, false)
        unlockedLocationsRecyclerAdapter = UnlockedLocationsRecyclerAdapter(context, listOf(), UserObject.selectedUnlockedLocation?.city?: "", fromSettings)
        unlockedLocationsRecyclerList.adapter = unlockedLocationsRecyclerAdapter

        progressBar.visibility = View.VISIBLE

        UnlockedLocationsApi.userTeleportLocationPreference.observe(viewLifecycleOwner) { preference ->
            if (preference == UnlockedLocationsApi.TeleportPreference.CurrentLocation) {
                getAndSetUnlockedLocations(UserObject.selectedUnlockedLocation?.city?: "")
            }
        }

        getAndSetUnlockedLocations(selectedCity = UserObject.selectedUnlockedLocation?.city?: "")
    }

    private fun getAndSetUnlockedLocations(selectedCity: String) {
        activity?.let {
            UnlockedLocationsApi.getUnlockedLocations(it) { unlockedLocationsList ->
                progressBar.visibility = View.GONE
                unlockedLocationsList?.let { list ->
                    unlockedLocationsRecyclerAdapter = UnlockedLocationsRecyclerAdapter(context, list, selectedCity, fromSettings)
                    unlockedLocationsRecyclerList.adapter = unlockedLocationsRecyclerAdapter
                }
            }
        }
    }

    companion object {
        const val TAG = "UnlockedLocationsFragment"
        const val TELEPORT_ORIGIN = "TELEPORT_ORIGIN"
        const val TELEPORT_FROM_PREFERENCES = "TELEPORT_FROM_PREFERENCES"
        const val TELEPORT_FROM_BROWSE_PROFILES = "TELEPORT_FROM_BROWSE_PROFILES"
    }
}