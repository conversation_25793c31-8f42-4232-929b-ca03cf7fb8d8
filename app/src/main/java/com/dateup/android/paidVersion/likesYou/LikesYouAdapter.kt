package com.dateup.android.paidVersion.likesYou

import android.app.Activity
import android.content.Intent
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.dateup.android.activity.OtherUserProfileModal
import com.dateup.android.databinding.ItemLikesYouBinding
import com.dateup.android.glide.ImageLoaderModule
import com.dateup.android.models.LikesTabUser
import com.dateup.android.paidVersion.likesYou.LikesYouAdapter.Companion.EXTRA_LIKES_YOU_USER
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Constants
import com.dateup.android.utils.Utils
import com.dateup.android.viewModels.SpacesImagesViewModel

class LikesYouAdapter(private val context: Activity, private val spacesImagesViewModel: SpacesImagesViewModel) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {

        const val EXTRA_LIKES_YOU_USER = "EXTRA_LIKES_YOU_USER"
        const val EXTRA_PASSED_USER = "EXTRA_PASSED_USER"
    }

    private var likesYourUsersList = arrayListOf<LikesTabUser>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {

        val itemBinding = ItemLikesYouBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        itemBinding.userProfilePhoto.clipToOutline = true
        return LikesYouUserViewHolder(itemBinding)
    }

    override fun getItemCount(): Int = likesYourUsersList.size

    override fun onBindViewHolder(viewHolder: RecyclerView.ViewHolder, position: Int) {

        val likesYouViewHolder = viewHolder as LikesYouUserViewHolder

        likesYouViewHolder.bindView(likesYourUsersList[position], context, spacesImagesViewModel)
    }

    fun setLikesYouUsersList(likesTabUsersMap: ArrayList<LikesTabUser>) {

        this.likesYourUsersList = likesTabUsersMap
        notifyDataSetChanged()
    }
}

class LikesYouUserViewHolder(private val itemBinding: ItemLikesYouBinding) : RecyclerView.ViewHolder(itemBinding.root) {

    fun bindView(likesTabUser: LikesTabUser, context: Activity, spacesImagesViewModel: SpacesImagesViewModel) {

        val userHeight = Utils.heightInFeetFromInchesWithQuotes(likesTabUser.height.toString().toDouble())

        itemBinding.userName.text = likesTabUser.name.toString()
        itemBinding.userHeight.text = userHeight

        val width = (AppUtils.getDpForImages(context, 156f))
        val height = AppUtils.getDpForImages(context, 156f)
        ImageLoaderModule.loadImageIntoImageViewNoCropWithLoadingAndCallback(context,spacesImagesViewModel,
                "${likesTabUser.likedYouUserFirebaseId}/${Constants.photoFileName1}",itemBinding.userProfilePhoto,
                shouldCacheImage = true, width = width, height = height)

        itemBinding.userProfilePhoto.setOnClickListener {

            val intent = Intent(context, OtherUserProfileModal::class.java)
            intent.putExtra(EXTRA_LIKES_YOU_USER, likesTabUser)
            context.startActivity(intent)
        }
    }
}