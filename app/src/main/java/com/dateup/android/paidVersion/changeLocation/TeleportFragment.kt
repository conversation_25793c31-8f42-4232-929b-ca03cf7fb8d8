package com.dateup.android.paidVersion.changeLocation

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentTransaction
import com.dateup.android.R
import com.dateup.android.autoComplete.AddNewCityActivity
import com.dateup.android.extensions.launchModeWithSingleTop
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.fragment.PreferencesFragment
import com.dateup.android.models.UserObject
import com.dateup.android.subscriptions.views.SubscriptionActivity

class TeleportFragment : Fragment() {

    private lateinit var preferenceTopBackButton: ImageButton
    private lateinit var preferencesFragment: PreferencesFragment
    private lateinit var teleportContainer: ConstraintLayout
    private lateinit var currentLocationTicker: ImageView
    private lateinit var unlockedLocationsFragment: UnlockedLocationsFragment
    private lateinit var addAnotherTextView: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        preferencesFragment = PreferencesFragment()
        unlockedLocationsFragment = UnlockedLocationsFragment()

        if (UserObject.hasPlusOrSelect && !FirebaseDatabaseUtil.areApiKeysValid()) {
            activity?.let {
                val firebaseDatabaseUtil = FirebaseDatabaseUtil(it)
                firebaseDatabaseUtil.getApiKeys()
            }
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_teleport, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        init(view)

        if (UserObject.selectedUnlockedLocation == null) {
            currentLocationTicker.visibility = View.VISIBLE
        }else {
            currentLocationTicker.visibility = View.GONE
        }

        UnlockedLocationsApi.userTeleportLocationPreference.observe(viewLifecycleOwner) { preference ->
            if (preference == UnlockedLocationsApi.TeleportPreference.CurrentLocation) {
                currentLocationTicker.visibility = View.VISIBLE
            }else {
                currentLocationTicker.visibility = View.GONE
            }
        }
    }

    private fun init(layout: View) {
        preferenceTopBackButton = layout.findViewById(R.id.settings_header_left_image_view)
        teleportContainer = layout.findViewById(R.id.teleport_current_location_container)
        currentLocationTicker = layout.findViewById(R.id.location_chevron)
        addAnotherTextView = layout.findViewById(R.id.add_another_text_view)

        teleportContainer.setOnClickListener {
            UserObject.selectedUnlockedLocation = null
            UnlockedLocationsApi.userTeleportLocationPreference.postValue(UnlockedLocationsApi.TeleportPreference.CurrentLocation)
            UserObject.userPreferencesChangedLiveData.postValue(true)
        }

        preferenceTopBackButton.setOnClickListener {
            fragmentManager?.beginTransaction()?.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_CLOSE)?.replace(R.id.main_container, preferencesFragment)?.commit()
        }

        addAnotherTextView.setOnClickListener {
            if(!UserObject.hasPlusOrSelect) { // check if user is free user
                // Should show subscription screen
                    activity?.let {
                        startActivity(SubscriptionActivity.newIntent(it).launchModeWithSingleTop())
                    }
            }else {
                activity?.let {
                    startActivity(AddNewCityActivity.newIntent(it))
                }
            }
        }

        if (UserObject.hasPlusOrSelect) {
            fragmentManager?.beginTransaction()?.replace(R.id.locations_container, unlockedLocationsFragment, UnlockedLocationsFragment.TAG)?.commit()
        }
    }
}
