package com.dateup.android.firebase

import com.google.firebase.database.ChildEventListener
import com.google.firebase.database.DataSnapshot
import com.google.firebase.database.DatabaseReference

interface FirebaseRetrieveChildListener {

    fun onChildChanged(dataSnapshot: DataSnapshot, p1: String?, reference: DatabaseReference, listener: ChildEventListener)
    fun onChildAdded(dataSnapshot: DataSnapshot, p1: String?, reference: DatabaseReference, listener: ChildEventListener)
    fun onChildRemoved(dataSnapshot: DataSnapshot, reference: DatabaseReference, listener: ChildEventListener)
    fun onChildMoved(dataSnapshot: DataSnapshot, p1: String?,reference: DatabaseReference, listener: ChildEventListener)
    fun onFailure()
}


interface FirebaseRetrieveChildListenerGeneric {
    fun onSuccess(dataSnapshot: DataSnapshot)
    fun onFailure()
}
