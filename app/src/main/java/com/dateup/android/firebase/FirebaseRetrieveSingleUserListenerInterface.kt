package com.dateup.android.firebase

import com.dateup.android.models.User
import com.google.firebase.database.DataSnapshot
import com.google.firebase.firestore.DocumentSnapshot

interface FirebaseRetrieveSingleUserListenerInterface {

    fun onSuccess(dataSnapshot: DataSnapshot)
    fun onFailure()
}

interface FirebaseRetrieveSingleUserListenerInterfaceFirestore {

    fun onSuccess(user: User)
    fun onFailure()
}

interface FirebaseRetrieveSingleUserRawDataListenerInterfaceFirestore {

    fun onSuccess(userDocumentSnapShot: DocumentSnapshot)
    fun onFailure()
}