package com.dateup.android.bottomSheet

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import androidx.recyclerview.widget.RecyclerView
import com.dateup.android.R
import kotlinx.android.extensions.LayoutContainer

class BottomSheetListAdapter(private val mContext: Context,
                             private val mOptionsList: List<DateUpBottomSheet.Item>,
                             private val mDateUpBottomSheet: DateUpBottomSheet) : RecyclerView.Adapter<BottomSheetListAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup,
                                    viewType: Int): ViewHolder {
        return ViewHolder(LayoutInflater.from(mContext)
                .inflate(R.layout.item_bottom_sheet_adapter, parent, false))
    }

    override fun getItemCount(): Int {
        return mOptionsList.size
    }

    override fun onBindViewHolder(holder: ViewHolder,
                                  position: Int) {
        val item = mOptionsList[position]
        holder.bindItems(item.title, item.action)
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView),
                                             LayoutContainer {

        override val containerView: View
            get() = itemView

        fun bindItems(title: String?,
                      action: ((Boolean) -> Unit)?) {

            val actionButton = containerView.findViewById<Button>(R.id.button_large_active_button)
            actionButton.text = title
            action?.let { actionCallBack ->
                actionButton.setOnClickListener {
                    actionCallBack(true)
                    mDateUpBottomSheet.dismiss()
                }
            }
        }
    }
}
