package com.dateup.android.viewModels

import android.location.Location
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.dateup.android.extensions.getDoubleValue
import com.dateup.android.extensions.getIntValue
import com.dateup.android.firebase.FirebaseRetrieveNearbyUsersListenerInterface
import com.dateup.android.geofire.NearByUsers
import com.dateup.android.models.NearByUser
import com.dateup.android.models.User
import com.dateup.android.utils.Constants
import com.dateup.android.utils.Utils
import com.firebase.geofire.GeoFire
import com.firebase.geofire.GeoLocation
import com.firebase.geofire.GeoQuery
import com.firebase.geofire.GeoQueryEventListener
import com.google.android.gms.tasks.Task
import com.google.android.gms.tasks.Tasks
import com.google.firebase.database.*
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.ArrayList
import kotlin.collections.HashMap
import kotlin.collections.LinkedHashMap
import androidx.test.espresso.idling.CountingIdlingResource
import com.dateup.android.geofire.GetUsersResponse
import com.dateup.android.models.GenderType
import com.dateup.android.models.GetNearByUsersResult
import com.google.firebase.functions.ktx.functions
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit


class BrowseProfilesViewModel : ViewModel() {

    fun readNearByUsersFromFirebaseForTesting(searchLat: Double,
                                    searchLong: Double,
                                    distance: Int,
                                    isPaginating: Boolean = false,
                                              mainUserId: String,
                                    callback:(usersMembersMap: LinkedHashMap<String, User?>?,
                                              usersGuestsMap: LinkedHashMap<String, User?>?) -> Unit) {

        val database: DatabaseReference = FirebaseDatabase.getInstance().reference
        var mainUserInfo: User?

        val singleUserDatabaseReference = database.child(Constants.users).child(mainUserId)
        singleUserDatabaseReference.addListenerForSingleValueEvent(object : ValueEventListener {
            override fun onCancelled(p0: DatabaseError) {
                Timber.d("getting user data failed")
            }

            override fun onDataChange(dataSnapshot: DataSnapshot) {
                mainUserInfo = dataSnapshot.getValue(User::class.java)

                val geoFire = GeoFire(database.child("geofire"))

                val usersMembersMap: LinkedHashMap<String, User?> = LinkedHashMap()
                val usersGuestsMap: LinkedHashMap<String, User?> = LinkedHashMap()

                val distanceInKm = Utils.getKmsFromMiles(distance.toDouble())
                val geoQuery = geoFire.queryAtLocation(GeoLocation(searchLat, searchLong), distanceInKm)
                geoQuery?.let {
                    readNearbyUsersInfoFromFirebaseGeofire(it, isPaginating, searchLat, searchLong,  object : FirebaseRetrieveNearbyUsersListenerInterface {
                        override fun onSuccess(nearByUsersHashMap: LinkedHashMap<String, User?>, usersDistanceMap: LinkedHashMap<String, Int>) {
                            println("getting firebase geo data success: ${nearByUsersHashMap.size}")

                            for ((key, value) in nearByUsersHashMap) {

                                if (value?.isAMember == true) {
                                    if (!usersMembersMap.containsKey(key) && Utils.isValidUser(value)) {
                                        if (isUserMatchedWithMainUserPreferences(key, value,
                                                mainUserInfo, mainUserInfo?.gender.toString(), mainUserId)) {
                                            usersMembersMap[key] = value
                                        }
                                    } else if (usersMembersMap.containsKey(key)) {
                                        Timber.d("Not a valid user: Key already exists")
                                    } else {
                                        Timber.d("Not a valid user: $key")
                                    }
                                } else {
                                    if (!usersGuestsMap.containsKey(key) && Utils.isValidUser(value)) {
                                        if (isUserMatchedWithMainUserPreferences(key, value,
                                                mainUserInfo,mainUserInfo?.gender.toString(), mainUserId)) {
                                            usersGuestsMap[key] = value
                                        }
                                    } else if (usersGuestsMap.containsKey(key)) {
                                        Timber.d("Not a valid user: Key already exists")
                                    } else {
                                        Timber.d("Not a valid user: $key")
                                    }
                                }
                            }


                            callback(usersMembersMap, usersGuestsMap)
                        }
                        override fun onFailure() {
                            println("getting firebase geo data failed")
                            callback(null, null)
                        }
                    })
                }

            }
        })
    }

    fun getNearByUsersForTesting(searchLat: Double,
                                 searchLong: Double,
                                 distance: Int,
                                 mainUserId: String,
                                 callback: (List<NearByUser>?) -> Unit) {
        NearByUsers.getNearByUsers(mainUserId, searchLat, searchLong, distance) { usersList, _ ->
            callback(usersList)
        }
    }

    // Function Only for testing
    private fun readNearbyUsersInfoFromFirebaseGeofire(
        geoQuery: GeoQuery,
        isPaginating: Boolean,
        mainUserLatitude: Double,
        mainUserLongitude: Double,
        retrieveNearbyUsersListener: FirebaseRetrieveNearbyUsersListenerInterface
    ) {
        val nearByUsersKeysLinkedHashMap: LinkedHashMap<String, Location> = LinkedHashMap()
        val nearByUsersHashMap: LinkedHashMap<String, User?> = LinkedHashMap()
        val nearByUsersFetchStatus = mutableSetOf("")
        val mainUserLocation = Location("mainUserLocation")
        val database: DatabaseReference = FirebaseDatabase.getInstance().reference

        geoQuery.addGeoQueryEventListener(object : GeoQueryEventListener {
            override fun onGeoQueryReady() {
                //https://github.com/firebase/geofire-js/issues/59

                val usersDistanceMap: LinkedHashMap<String, Int> = LinkedHashMap()
                var usersSortedByDistanceLinkedHashMap: LinkedHashMap<String, User?> = LinkedHashMap()
                val taskList = mutableListOf<Task<DataSnapshot>>()

                mainUserLocation.latitude = mainUserLatitude
                mainUserLocation.longitude = mainUserLongitude

                if (nearByUsersKeysLinkedHashMap.isNotEmpty()) {

                    for ((key, value ) in nearByUsersKeysLinkedHashMap) {
                        if (nearByUsersFetchStatus.contains(key)) {
                            continue
                        }
                        val distanceBetweenUsers =
                            Utils.getMilesFromMeters(mainUserLocation.distanceTo(value).toInt())
                        usersDistanceMap[key] = distanceBetweenUsers

                        nearByUsersFetchStatus.add(key)

                        val databaseReferenceTask: Task<DataSnapshot> = database.child(Constants.users).child(key).get()
                        taskList.add(databaseReferenceTask)
                    }

                    val resultTask = Tasks.whenAll(taskList)
                    resultTask.addOnCompleteListener {
                        for (task in taskList) {
                            val snapshotKey: String? = task.result.key
                            val snapShotValue = task.result
                            if (snapshotKey != null && snapShotValue != null) {
                                if (snapShotValue.getValue(User::class.java) != null && snapshotKey != "mFirebaseUserId") {
                                    nearByUsersHashMap[snapshotKey] = snapShotValue.getValue(User::class.java)
                                }
                            }
                        }

                        usersSortedByDistanceLinkedHashMap = sortUsersByDistance(nearByUsersHashMap, usersDistanceMap)
                        retrieveNearbyUsersListener.onSuccess(usersSortedByDistanceLinkedHashMap, usersDistanceMap)
                    }

                    if (!isPaginating) {
                        geoQuery.removeAllListeners()
                    }
                } else {
                    retrieveNearbyUsersListener.onFailure()
                }
            }

            override fun onKeyEntered(key: String?, location: GeoLocation?) {
                if (key != null) {

                    val otherUserLocation = Location("geofire-location")
                    if (location != null) {
                        otherUserLocation.latitude = location.latitude
                        otherUserLocation.longitude = location.longitude
                    }

                    nearByUsersKeysLinkedHashMap[key] = otherUserLocation
                }
            }

            override fun onKeyMoved(key: String?, location: GeoLocation?) {
            }

            override fun onKeyExited(key: String?) {
            }

            override fun onGeoQueryError(error: DatabaseError?) {
                retrieveNearbyUsersListener.onFailure()
                geoQuery.removeAllListeners()
            }
        })
    }

    // Function Only for testing
    private fun sortUsersByDistance(
        originalUsersHashMap: LinkedHashMap<String, User?>,
        originalDistanceHashMap: LinkedHashMap<String, Int>
    ): LinkedHashMap<String, User?> {
        val sortedDistanceLinkedHashMap: LinkedHashMap<String, Int> =
            (originalDistanceHashMap.entries.sortedBy { it.value }
                .associate { it.toPair() } as? LinkedHashMap<String, Int>) ?: LinkedHashMap()
        val sortedUsersByDistanceLinkedHashMap: LinkedHashMap<String, User?> = LinkedHashMap()
        for ((key, _) in sortedDistanceLinkedHashMap) {
            val user: User? = originalUsersHashMap[key]
            sortedUsersByDistanceLinkedHashMap[key] = user
        }
        return sortedUsersByDistanceLinkedHashMap
    }

    private fun isUserMatchedWithMainUserPreferences(otherUserId: String,
        otherUser: User?,
                                                     mainUser: User?,
                                                     mainUserGender: String,
                                                     mainUserId: String): Boolean {

        try {
            if (mainUser != null && otherUser != null) {

                val sdf = SimpleDateFormat("MM/dd/yyyy", Locale.US)

                val mainUserInterestedIn = mainUser.interestedIn
                val mainUserMinHeightInInches: Int? = mainUser.minMatchHeight.toString().getDoubleValue()?.getIntValue()
                val mainUserMaxHeightInInches: Int? = mainUser.maxMatchHeight.toString().getDoubleValue()?.getIntValue()
                val mainUserMinGuestHeightInInches: Int? = mainUser.minGuestHeight.toString().getDoubleValue()?.getIntValue()
                val mainUserMaxGuestHeightInInches: Int? = mainUser.maxGuestHeight.toString().getDoubleValue()?.getIntValue()
                val mainUserHeightInInches: Int? = mainUser.height.toString().getDoubleValue()?.getIntValue()
                val mainUserMinAge: Int? = mainUser.minMatchAge.toString().getIntValue()
                val mainUserMaxAge: Int? = mainUser.maxMatchAge.toString().getIntValue()
                val mainUserDob = mainUser.dob.toString()
                val mainUserDate = sdf.parse(mainUserDob)
                val mainUserAge = Utils.getAge(mainUserDate)
                val mainUserShowGuests = mainUser.showGuests
                val isMainUserMember = mainUser.isAMember

                val otherUserGender = otherUser.gender
                val otherUserHeightInInches: Int? = otherUser.height.toString().getDoubleValue()?.getIntValue()
                val otherUserDob = otherUser.dob.toString()
                val otherUserDate = sdf.parse(otherUserDob)
                val otherUserAge = Utils.getAge(otherUserDate)
                val isOtherUserMember = otherUser.isAMember
                val otherUserInterestedIn = otherUser.interestedIn
                val otherUserMinAge: Int? = otherUser.minMatchAge.toString().getIntValue()
                val otherUserMaxAge: Int? = otherUser.maxMatchAge.toString().getIntValue()
                val otherUserMinHeightInInches: Int? = otherUser.minMatchHeight.toString().getDoubleValue()?.getIntValue()
                val otherUserMaxHeightInInches: Int? = otherUser.maxMatchHeight.toString().getDoubleValue()?.getIntValue()
                val otherUserMinGuestHeightInInches: Int? = otherUser.minGuestHeight.toString().getDoubleValue()?.getIntValue()
                val otherUserMaxGuestHeightInInches: Int? = otherUser.maxGuestHeight.toString().getDoubleValue()?.getIntValue()
                val otherUserShowGuests = otherUser.showGuests

                val isOtherUserLocked = otherUser.isUserLocked

                if (otherUserId == "-McqUtAafsozaE_4tZ3G") {
                    println(otherUser.name)
                }

                if (isOtherUserLocked == true) {

                    return false
                }

                if (otherUser.isUserReported == true) {

                    return false
                }

                if (isMainUserMember == true) {

                    if (mainUserShowGuests == false) {

                        if (isOtherUserMember == false) {

                            return false
                        }
                    }
                }

                if (isOtherUserMember == true) {

                    if (otherUserShowGuests == false) {

                        if (isMainUserMember == false) {

                            return false
                        }
                    }
                }

                if (otherUserInterestedIn != null) {

                    if (otherUserInterestedIn != mainUserGender) {

                        return false
                    }
                } else {

                    return false
                }

                mainUserInterestedIn?.let {

                    if (it != otherUserGender) {

                        return false
                    }
                }

                if (mainUserMinAge != null) {

                    if (mainUserMinAge > otherUserAge) {

                        return false
                    }
                }

                if (mainUserMaxAge != Constants.sliderMaxAge) {

                    if (mainUserMaxAge != null) {

                        if (mainUserMaxAge < otherUserAge) {

                            return false
                        }
                    }
                }

                if (otherUserMinAge != null) {

                    if (otherUserMinAge > mainUserAge) {

                        return false
                    }
                }

                if (otherUserMaxAge != Constants.sliderMaxAge) {

                    if (otherUserMaxAge != null) {

                        if (otherUserMaxAge < mainUserAge) {

                            return false
                        }
                    }
                }

                if (isMainUserMember == true && mainUserShowGuests == true && isOtherUserMember == false) {

                    if (mainUserMinGuestHeightInInches != null && otherUserHeightInInches != null) {

                        if (mainUserMinGuestHeightInInches > otherUserHeightInInches) {

                            return false
                        }
                    }

                    if (mainUserInterestedIn?.equals(GenderType.man.toString()) == true) {

                        if (mainUserMaxGuestHeightInInches != Constants.sliderMaxHeightForMen.toInt()) {

                            if (mainUserMaxGuestHeightInInches != null && otherUserHeightInInches != null) {

                                if (mainUserMaxGuestHeightInInches < otherUserHeightInInches) {

                                    return false
                                }
                            }
                        }
                    } else if (mainUserInterestedIn?.equals(GenderType.woman.toString()) == true) {

                        if (mainUserMaxGuestHeightInInches != Constants.sliderMaxHeightForWomen.toInt()) {

                            if (mainUserMaxGuestHeightInInches != null && otherUserHeightInInches != null) {

                                if (mainUserMaxGuestHeightInInches < otherUserHeightInInches) {

                                    return false
                                }
                            }
                        }
                    }

                    if (otherUserMinHeightInInches != null && mainUserHeightInInches != null) {

                        if (otherUserMinHeightInInches > mainUserHeightInInches) {

                            return false
                        }
                    }

                    if (otherUserInterestedIn == GenderType.man.toString()) {

                        if (otherUserMaxHeightInInches != Constants.sliderMaxHeightForMen.toInt()) {

                            if (otherUserMaxHeightInInches != null && mainUserHeightInInches != null) {

                                if (otherUserMaxHeightInInches < mainUserHeightInInches) {

                                    return false
                                }
                            }
                        }
                    } else if (otherUserInterestedIn == GenderType.woman.toString()) {

                        if (otherUserMaxHeightInInches != Constants.sliderMaxHeightForWomen.toInt()) {

                            if (otherUserMaxHeightInInches != null && mainUserHeightInInches != null) {

                                if (otherUserMaxHeightInInches < mainUserHeightInInches) {

                                    return false
                                }
                            }
                        }
                    }
                } else if (isMainUserMember == false && isOtherUserMember == true) {

                    if (mainUserMinHeightInInches != null && otherUserHeightInInches != null) {

                        if (mainUserMinHeightInInches > otherUserHeightInInches) {

                            return false
                        }
                    }

                    if (mainUserInterestedIn?.equals(GenderType.man.toString()) == true) {

                        if (mainUserMaxHeightInInches != Constants.sliderMaxHeightForMen.toInt()) {

                            if (mainUserMaxHeightInInches != null && otherUserHeightInInches != null) {

                                if (mainUserMaxHeightInInches < otherUserHeightInInches) {

                                    return false
                                }
                            }
                        }
                    } else if (mainUserInterestedIn?.equals(GenderType.woman.toString()) == true) {

                        if (mainUserMaxHeightInInches != Constants.sliderMaxHeightForWomen.toInt()) {

                            if (mainUserMaxHeightInInches != null && otherUserHeightInInches != null) {

                                if (mainUserMaxHeightInInches < otherUserHeightInInches) {

                                    return false
                                }
                            }
                        }
                    }

                    if (otherUserMinGuestHeightInInches != null && mainUserHeightInInches != null) {

                        if (otherUserMinGuestHeightInInches > mainUserHeightInInches) {

                            return false
                        }
                    }

                    if (otherUserInterestedIn == GenderType.man.toString()) {

                        if (otherUserMaxGuestHeightInInches != Constants.sliderMaxHeightForMen.toInt()) {

                            if (otherUserMaxGuestHeightInInches != null && mainUserHeightInInches != null) {

                                if (otherUserMaxGuestHeightInInches < mainUserHeightInInches) {

                                    return false
                                }
                            }
                        }
                    } else if (otherUserInterestedIn == GenderType.woman.toString()) {

                        if (otherUserMaxGuestHeightInInches != Constants.sliderMaxHeightForWomen.toInt()) {

                            if (otherUserMaxGuestHeightInInches != null && mainUserHeightInInches != null) {

                                if (otherUserMaxGuestHeightInInches < mainUserHeightInInches) {

                                    return false
                                }
                            }
                        }
                    }
                } else {

                    if (mainUserMinHeightInInches != null && otherUserHeightInInches != null) {

                        if (mainUserMinHeightInInches > otherUserHeightInInches) {

                            return false
                        }
                    }

                    if (mainUserInterestedIn?.equals(GenderType.man.toString()) == true) {

                        if (mainUserMaxHeightInInches != Constants.sliderMaxHeightForMen.toInt()) {

                            if (mainUserMaxHeightInInches != null && otherUserHeightInInches != null) {

                                if (mainUserMaxHeightInInches < otherUserHeightInInches) {

                                    return false
                                }
                            }
                        }

                    } else if (mainUserInterestedIn?.equals(GenderType.woman.toString()) == true) {

                        if (mainUserMaxHeightInInches != Constants.sliderMaxHeightForWomen.toInt()) {

                            if (mainUserMaxHeightInInches != null && otherUserHeightInInches != null) {

                                if (mainUserMaxHeightInInches < otherUserHeightInInches) {

                                    return false
                                }
                            }
                        }
                    }

                    if (otherUserMinHeightInInches != null && mainUserHeightInInches != null) {

                        if (otherUserMinHeightInInches > mainUserHeightInInches) {

                            return false
                        }
                    }

                    if (otherUserInterestedIn == GenderType.man.toString()) {

                        if (otherUserMaxHeightInInches != Constants.sliderMaxHeightForMen.toInt()) {

                            if (mainUserHeightInInches != null && otherUserMaxHeightInInches != null) {

                                if (otherUserMaxHeightInInches < mainUserHeightInInches) {

                                    return false
                                }
                            }
                        }
                    } else if (otherUserInterestedIn == GenderType.woman.toString()) {

                        if (otherUserMaxHeightInInches != Constants.sliderMaxHeightForWomen.toInt()) {

                            if (mainUserHeightInInches != null && otherUserMaxHeightInInches != null) {

                                if (otherUserMaxHeightInInches < mainUserHeightInInches) {

                                    return false
                                }
                            }
                        }
                    }
                }

                val otherUserConnectionsMap = otherUser.connections

                if (otherUserConnectionsMap?.likedBy != null) {

                    if (otherUserConnectionsMap.likedBy is HashMap<*, *>) {
                        val otherUsersLikedBy = otherUserConnectionsMap.likedBy as HashMap<*, *>

                        if (otherUsersLikedBy.containsKey(mainUserId)) {
                            return false
                        }
                    }
                }

                if (otherUserConnectionsMap?.dislikedBy != null) {

                    if (otherUserConnectionsMap.dislikedBy is HashMap<*, *>) {

                        val otherUsersDislikedBy = otherUserConnectionsMap.dislikedBy as HashMap<*, *>

                        if (otherUsersDislikedBy.containsKey(mainUserId)) {

                            return false
                        }
                    }
                }
            }
        } catch (exception: Exception) {
            Timber.e("exception in isUserMatchedWithMainUserPreferences: $exception")
        }

        return true
    }
}