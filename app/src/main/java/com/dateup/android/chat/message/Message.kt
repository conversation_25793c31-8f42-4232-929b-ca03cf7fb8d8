package com.dateup.android.chat.message

import com.dateup.android.chat.dialog.ChatUser
import com.stfalcon.chatkit.commons.models.IMessage
import java.util.*

class Message @JvmOverloads constructor(private val id: String,
                                        private val user: ChatUser,
                                        private var text: String?,
                                        private var createdAt: Date?,
                                        var messageStatus: String = "") : IMessage {

    var voice: Voice? = null

    override fun getId(): String {
        return id
    }

    override fun getText(): String? {

        return text
    }

    override fun getCreatedAt(): Date? {

        return createdAt
    }

    override fun getUser(): ChatUser {
        return user
    }

    fun setText(text: String) {
        this.text = text
    }

    fun setCreatedAt(createdAt: Date) {
        this.createdAt = createdAt
    }

    fun setImage(image: Image?) {}
    class Image(url: String?)
    class Voice(val url: String, val duration: Int)
}