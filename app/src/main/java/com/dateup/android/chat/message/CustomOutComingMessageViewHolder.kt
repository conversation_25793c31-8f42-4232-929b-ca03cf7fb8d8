package com.dateup.android.chat.message

import android.view.View
import com.dateup.android.models.UserObject
import com.dateup.android.models.UserObject.hasPlusOrSelect
import com.stfalcon.chatkit.messages.MessageHolders

class CustomOutComingMessageViewHolder(itemView: View?, payload: Any?) : MessageHolders.OutcomingTextMessageViewHolder<Message?>(itemView, payload) {

    override fun onBind(message: Message?) {
        super.onBind(message)
        if (hasPlusOrSelect) {
            time.text = message?.messageStatus?: ""
        } else {
            time.visibility = View.GONE
        }
    }
}