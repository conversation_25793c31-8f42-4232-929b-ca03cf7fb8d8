package com.dateup.android.services

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.location.Address
import android.location.Geocoder
import android.location.Location
import android.text.TextUtils
import androidx.core.app.ActivityCompat
import androidx.core.app.JobIntentService
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.dateup.android.AccountPreferences
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.models.UserObject
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Constants
import com.dateup.android.utils.ManagePermissions
import com.google.android.gms.location.LocationServices
import timber.log.Timber
import java.util.*

class BackgroundLocationIntentService : JobIntentService() {

    companion object {

        const val LOCATION_FETCH_STATUS_ACTION = "LOCATION_FETCH_STATUS_ACTION"
        const val LOCATION_FETCH_RESULT = "LOCATION_FETCH_RESULT"

        const val locationPermissionRequestCode = 551

        val locationPermissionsList = listOf(
                Manifest.permission.ACCESS_COARSE_LOCATION,
                Manifest.permission.ACCESS_FINE_LOCATION
        )

        fun enqueueWork(context: Context, intent: Intent) {

            enqueueWork(context, BackgroundLocationIntentService::class.java, 35, intent)
        }
    }

    override fun onHandleWork(intent: Intent) {

        onHandleIntent(intent)
    }

    private fun onHandleIntent(intent: Intent) {

        val sysTimeSaved: Long = AccountPreferences.getInstance(this).getFloatValue(Constants.systemTimeSaved, 0L).toLong()
        val lat = AccountPreferences.getInstance(this).getDoubleValue(Constants.latitude, 0.0)
        val long = AccountPreferences.getInstance(this).getDoubleValue(Constants.longitude, 0.0)

        if (lat == 0.0 || long == 0.0 || AppUtils.hourDifference(sysTimeSaved, System.currentTimeMillis()) > 1) {

            AccountPreferences.getInstance(this).setValue(Constants.systemTimeSaved, System.currentTimeMillis())

            getAndSaveDeviceLocationDetails(this)
        }
    }

    private fun getAndSaveDeviceLocationDetails(context: Context) {

        try {

            val fusedLocationClient = LocationServices.getFusedLocationProviderClient(context)

            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION)
                    != PackageManager.PERMISSION_GRANTED &&
                    ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_COARSE_LOCATION)
                    != PackageManager.PERMISSION_GRANTED) {

                return
            }

            fusedLocationClient.lastLocation
                    .addOnSuccessListener { location: Location? ->

                        try {

                            if (location != null) {

                                val latitude: Double = location.latitude
                                val longitude: Double = location.longitude

                                val gecoder = Geocoder(context, Locale.getDefault())
                                var zipCode = ""
                                var city = ""
                                var state = ""
                                var country = ""
                                var address: Address? = null

                                val firebaseDatabaseReference = FirebaseDatabaseUtil(context)

                                firebaseDatabaseReference.saveLocationToGeofire(latitude, longitude)

                                val accountPreferences = AccountPreferences.getInstance(context)
                                accountPreferences.setValue(Constants.latitude, latitude)
                                accountPreferences.setValue(Constants.longitude, longitude)

                                val addresses = gecoder.getFromLocation(latitude, longitude, 1)

                                if (addresses != null && addresses.size > 0) {

                                    for (i in addresses.indices) {

                                        address = addresses[i]
                                        if (address != null) {
                                            zipCode = address.postalCode ?: ""
                                            city = address.locality ?: ""
                                            state = address.adminArea ?: ""
                                            country = address.countryName ?: ""
                                        }
                                    }

                                    if (!TextUtils.isEmpty(city)) {

                                        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.city, city)
                                    }

                                    if (!TextUtils.isEmpty(state)) {

                                        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.state, state)
                                    }
                                    if (!TextUtils.isEmpty(zipCode)) {

                                        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.zip, zipCode)
                                    }
                                    if (!TextUtils.isEmpty(country)) {

                                        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.country, country)
                                    }

                                    if (!UserObject.isServerOnboardingComplete) {

                                        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.distance, Constants.defaultDistance)
                                    }
                                }

                                sendBroadcast(true)
                            } else {

                                sendBroadcast(false)
                            }
                        } catch (ex: java.lang.Exception) {

                            sendBroadcast(false)
                            Timber.e("exception in getting location using fused location service $ex")
                        }
                    }
                    .addOnFailureListener { failure ->

                        sendBroadcast(false)
                        Timber.e("failure in getting location: $failure")
                    }
        } catch (exception: Exception) {

            sendBroadcast(false)
            Timber.e("exception in getting location: $exception")
        }
    }

    @SuppressLint("MissingPermission")
    fun forceGetLatAndLongValues(activity: Activity, callback: (Double?, Double?) -> Unit) {
        try {
            val fusedLocationClient = LocationServices.getFusedLocationProviderClient(activity)
            val managePermissions = ManagePermissions(activity, locationPermissionsList, locationPermissionRequestCode)
            if (managePermissions.isPermissionGranted()) {
                fusedLocationClient.lastLocation
                        .addOnSuccessListener { location: Location? ->
                            callback(location?.latitude, location?.longitude)
                        }.addOnFailureListener {
                            callback(null, null)
                        }
            }else {
                callback(null, null)
            }
        } catch (exception: Exception) {
            callback(null, null)
        }
    }

    private fun sendBroadcast(status: Boolean) {

        val intent = Intent(LOCATION_FETCH_STATUS_ACTION)
        intent.putExtra(LOCATION_FETCH_RESULT, status)
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent)
    }
}