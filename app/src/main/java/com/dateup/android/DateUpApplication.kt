package com.dateup.android

import android.app.Application
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.ProcessLifecycleOwner
import com.dateup.android.attribution.AttributionManager
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.firebase.RemoteConfigurationService
import com.dateup.android.paidVersion.NumLikesRestriction
import com.dateup.android.room.AppDatabase
import com.dateup.android.room.LocalDataSource
import com.dateup.android.spaces.SpacesRepository
import com.dateup.android.subscriptions.billing.BillingClientLifecycle
import com.dateup.android.subscriptions.network.ServerFunctionsImpl
import com.dateup.android.subscriptions.repo.DataRepository
import com.dateup.android.utils.TimberLogger
import com.google.firebase.Firebase
import com.google.firebase.appcheck.appCheck
import com.google.firebase.appcheck.playintegrity.PlayIntegrityAppCheckProviderFactory
import com.google.firebase.initialize
import io.branch.referral.Branch
import timber.log.Timber

class DateUpApplication : Application(), LifecycleObserver {

    private val database: AppDatabase
        get() = AppDatabase.getInstance(this)

    private val localDataSource: LocalDataSource
        get() = LocalDataSource.getInstance(database)

    private val serverFunctions: ServerFunctionsImpl
        get() {
            return ServerFunctionsImpl()
        }

    val billingClientLifecycle: BillingClientLifecycle
        get() = BillingClientLifecycle.getInstance(this)

    val repository: DataRepository
        get() = DataRepository.getInstance(serverFunctions, localDataSource)

    override fun onCreate() {
        super.onCreate()

        TimberLogger()

        Timber.d("Application Started")

        Firebase.initialize(context = this)
        Firebase.appCheck.installAppCheckProviderFactory(
            PlayIntegrityAppCheckProviderFactory.getInstance(),
        )

        // Initialize attribution tracking early
        AttributionManager.getInstance().initializeAttribution(this)

        RemoteConfigurationService.initialize()

        if (!SpacesRepository.isSpacesConfigValid() && AuthService.isUserAuthenticated()) {
            val firebaseDatabaseUtil = FirebaseDatabaseUtil(this)
            firebaseDatabaseUtil.getSpacesConfigs()
        }

        if (AuthService.isUserAuthenticated()) {
            NumLikesRestriction.setDeviceLikeCounterAndDate(context = applicationContext) {}
        }

        ProcessLifecycleOwner.get().lifecycle.addObserver(billingClientLifecycle)

        // Branch logging for debugging
        if (BuildConfig.DEBUG) {
            Branch.enableTestMode()
            Branch.enableLogging()
        }

        // Branch object initialization
        Branch.getAutoInstance(this)
    }
}