package com.dateup.android.deepLinking

import android.app.Activity
import android.content.Intent
import android.net.Uri
import com.dateup.android.R
import com.dateup.android.analytics.APP_SHARE
import com.dateup.android.analytics.AnalyticsTrackingService
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.dynamiclinks.DynamicLink
import com.google.firebase.dynamiclinks.FirebaseDynamicLinks
import timber.log.Timber

class AppInvites {

    companion object {

        private const val influencerInvites = "influencerInvites"

        fun handleDynamicLinks(intent: Intent, activity: Activity) {
            try {
                FirebaseDynamicLinks.getInstance()
                        .getDynamicLink(intent)
                        .addOnSuccessListener(activity) { pendingDynamicLinkData ->
                            if (pendingDynamicLinkData != null) {
                                val deepLinkUri = pendingDynamicLinkData.link
                                val promotion = deepLinkUri?.getQueryParameter("promotion").orEmpty()
                                if (promotion == influencerInvites) {
                                    val influencerId = deepLinkUri?.getQueryParameter("influencerId").orEmpty()
                                    val influencerName = deepLinkUri?.getQueryParameter("name").orEmpty()
                                    if (influencerId.isNotEmpty()) {
                                        PromotionsHandler.handleInfluencerSignups(activity, influencerId, influencerName)
                                    }
                                }
                            }
                        }
                        .addOnFailureListener(activity) { e ->
                            Timber.e("FirebaseDynamicLinks failure: $e")
                        }
            }catch (exception: Exception) {
                Timber.e("Exception in handling deeplinks: $exception")
            }
        }

        fun shareLink(activity: Activity, message: String) {
            val intent = Intent(Intent.ACTION_SEND)
            intent.type = "text/plain"
            intent.putExtra(Intent.EXTRA_TEXT, message)
            intent.putExtra(Intent.EXTRA_TITLE, activity.getString(R.string.settings_main_item_6))
            activity.startActivity(Intent.createChooser(intent, "Share Link"))

            AnalyticsTrackingService.logEvent(activity, APP_SHARE)
        }

        fun buildReferralLink() {
            val user = FirebaseAuth.getInstance().currentUser
            val uid = user?.uid

            if (!uid.isNullOrEmpty()) {
                val link = "https://www.dateup.co/?invitedby=$uid"
                FirebaseDynamicLinks.getInstance().createDynamicLink()
                        .setLink(Uri.parse(link))
                        .setDomainUriPrefix("")
                        .setAndroidParameters(
                                DynamicLink.AndroidParameters.Builder("com.android.dateup")
                                        .build())
                        .setIosParameters(
                                DynamicLink.IosParameters.Builder("com.ios.dateup")
                                        .setAppStoreId("")
                                        .build())
                        .buildShortDynamicLink()
                        .addOnSuccessListener { shortDynamicLink ->
                            val invitationUrl = shortDynamicLink.shortLink
                        }
                        .addOnFailureListener {
                            Timber.d("Invitation link for dateup failed: $it")
                        }
            }
        }

        fun sendInvitationLinkEmail(activity: Activity, invitationUrl: String) {

            val referrerName = FirebaseAuth.getInstance().currentUser?.displayName

            val subject = String.format("%s wants you to install dateUp!", referrerName)

            val msg = "Install dateup, Use my referrer link: $invitationUrl"

            //val msgHtml = String.format("<p>Let's play MyExampleGame together! Use my " +
            //      "<a href=\"%s\">referrer link</a>!</p>", invitationUrl)

            val intent = Intent(Intent.ACTION_SENDTO)
            intent.data = Uri.parse("mailto:") // only email apps should handle this
            intent.putExtra(Intent.EXTRA_SUBJECT, subject)
            intent.putExtra(Intent.EXTRA_TEXT, msg)

            if (intent.resolveActivity(activity.packageManager) != null) {

                activity.startActivity(intent)
            }
        }
    }
}