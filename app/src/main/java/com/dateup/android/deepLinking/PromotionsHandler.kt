package com.dateup.android.deepLinking

import android.content.Context
import com.dateup.android.AccountPreferences
import com.dateup.android.utils.Constants

object PromotionsHandler {

    fun handleInfluencerSignups(context: Context, influencerId: String, influencerName: String) {
        AccountPreferences.getInstance(context).setValue(Constants.influencerId, influencerId)
        AccountPreferences.getInstance(context).setValue(Constants.influencerName, influencerName)
    }

    fun resetInfluencerSignups(context: Context) {
        AccountPreferences.getInstance(context).setValue(Constants.influencerId, "")
        AccountPreferences.getInstance(context).setValue(Constants.influencerName, "")
    }

    fun handleBranchCampaignSignups(context: Context, source: String) {
        AccountPreferences.getInstance(context).setValue(Constants.installSource, source)
    }
}