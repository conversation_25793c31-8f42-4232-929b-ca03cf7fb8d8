package com.dateup.android.customViews

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import androidx.appcompat.app.AlertDialog
import com.dateup.android.R
import com.dateup.android.analytics.*
import com.dateup.android.databinding.EnjoyingDateupDialogLayoutBinding
import com.dateup.android.databinding.ReviewDateupDialogLayoutBinding
import com.dateup.android.subscriptions.SubscriptionConstants.PLAY_STORE_APP_URL
import com.dateup.android.utils.Utils

class AppReview {

    fun showEnjoyingDateUpDialog(activity: Activity) {
        val binding: EnjoyingDateupDialogLayoutBinding
        val inflater: LayoutInflater = activity.layoutInflater
        binding = EnjoyingDateupDialogLayoutBinding.inflate(inflater)
        val view = binding.root


        val builder = AlertDialog.Builder(activity).create()
        builder.setView(view)
        builder.setCancelable(false)

        binding.needsWorkContainer?.setOnClickListener {
            builder.dismiss()
            showReviewDateUpDialog(activity, ReviewStatus.LEAVE_FEEDBACK)
            AnalyticsTrackingService.logEvent(activity, APP_REVIEW_NEEDS_WORK)
        }

        binding.loveItContainer?.setOnClickListener {
            builder.dismiss()
            showReviewDateUpDialog(activity, ReviewStatus.WRITE_REVIEW)
            AnalyticsTrackingService.logEvent(activity, APP_REVIEW_LOVE_IT)
        }

        builder.show()
    }

    private fun showReviewDateUpDialog(activity: Activity, status: ReviewStatus) {
        val binding: ReviewDateupDialogLayoutBinding
        val inflater: LayoutInflater = activity.layoutInflater
        binding = ReviewDateupDialogLayoutBinding.inflate(inflater)
        val view = binding.root

        val builder = AlertDialog.Builder(activity).create()
        builder.setView(view)
        builder.setCancelable(false)

        if (status == ReviewStatus.WRITE_REVIEW) {
            binding.reviewTitle.text = activity.getString(R.string.review_title)
            binding.reviewDesc.text = activity.getString(R.string.review_desc)
            binding.writeReviewButton.text = activity.getString(R.string.review_button_write_review)
            binding.writeReviewButton?.setOnClickListener {
                openPlayStore(activity)
                AnalyticsTrackingService.logEvent(activity, APP_REVIEW_WRITE)
            }
        } else {
            binding.starsImage?.visibility = View.GONE
            binding.loveItImageReview?.setImageDrawable(activity.getDrawable(R.drawable.ic_sad_face))
            binding.reviewTitle.visibility = View.GONE
            binding.reviewDesc.text = activity.getString(R.string.review_give_feedback)
            binding.writeReviewButton.text = activity.getString(R.string.review_button_leave_feedback)
            binding.writeReviewButton?.setOnClickListener {
                Utils.openGmailApp(activity, "DateUp Feedback")
            }
        }

        binding.closeButton?.setOnClickListener {
            AnalyticsTrackingService.logEvent(activity, APP_REVIEW_CLOSED)
            builder.dismiss()
        }

        builder.show()
    }

    private fun openPlayStore(activity: Activity) {
        val intent = Intent(Intent.ACTION_VIEW)
        intent.data = Uri.parse(PLAY_STORE_APP_URL)
        activity.startActivity(intent)
    }

    enum class ReviewStatus {
        WRITE_REVIEW,
        LEAVE_FEEDBACK
    }
}