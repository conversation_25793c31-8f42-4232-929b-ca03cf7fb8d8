package com.dateup.android.customViews

import android.app.Activity
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import androidx.appcompat.app.AlertDialog
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.databinding.TutorialToolTipViewBinding
import com.dateup.android.utils.Constants

class ToolTipView {

    private var tutorialList = getTootTips(null)
    private var totalToolTips = tutorialList.size
    private var counter = 1
    private var isLastItem = false
    private var actionButtonText = "Next"

    fun showTour(activity: Activity) {
        tutorialList = getTootTips(activity)
        totalToolTips = tutorialList.size
        counter = 1
        isLastItem = false
        actionButtonText = "Next"

        showTutorial(activity)
    }

    private fun showTutorial(activity: Activity) {
        if (totalToolTips == counter) {
            isLastItem = true
            actionButtonText = "Done"
            showDialog(activity, tutorialList[0].title, tutorialList[0].desc, tutorialList[0].image, counter, actionButtonText, isLastItem) {}
            counter++
        } else if (tutorialList.isNotEmpty()) {
            showDialog(activity, tutorialList[0].title, tutorialList[0].desc, tutorialList[0].image,counter ,actionButtonText, isLastItem) {
                tutorialList.removeAt(0)
                showTutorial(activity)
            }
            counter++
        }
    }

    private fun showDialog(
        activity: Activity,
        title: String = "",
        desc: String,
        image: Int,
        currentToolTipNo: Int,
        actionButtonText: String?,
        isLastTip: Boolean,
        callback: () -> Unit
    ) {
        val inflater: LayoutInflater = activity.layoutInflater

        val binding = TutorialToolTipViewBinding.inflate(inflater)
        val view = binding.root
        val builder = AlertDialog.Builder(activity).create()
        builder.setView(view)
        builder.setCancelable(false)

        binding.tipTitle?.text = title
        binding.tipDesc?.text = desc
        binding.actionButton?.text = actionButtonText
        binding.quickTipsCountText?.text = activity.getString(R.string.quick_tips_count, currentToolTipNo.toString(), totalToolTips.toString())
        binding.tipImage?.setImageDrawable(activity.getDrawable(image))

        binding.actionButton?.setOnClickListener {
            if (isLastTip) {
                builder.dismiss()
            }else {
                builder.dismiss()
                callback()
            }
        }
        if (!activity.isFinishing) {
            builder.show()
        }
    }

    private fun getTootTips(context: Context?): MutableList<ToolTipData> {
        val toolTipDataList = arrayListOf<ToolTipData>()
        context?.let {
            val isMember = AccountPreferences.getInstance(it).getBooleanValue(Constants.isAMember, false)
            if (isMember) {
                toolTipDataList.add(ToolTipData("Only Members see this switch", "Members have the exclusive ability to switch between viewing members or guests (guests can only view members).", R.drawable.ic_tutorial_1))
            }
        }
        toolTipDataList.add(ToolTipData("Adjust preferences here", "Open your preferences to adjust search radius, age range, height preferences, and more.", R.drawable.ic_tutorial_2))
        return toolTipDataList
    }
}

data class ToolTipData(val title: String, val desc: String, val image:Int)