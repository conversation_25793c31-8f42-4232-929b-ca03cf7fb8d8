package com.dateup.android

import android.content.Context
import android.content.Intent
import com.dateup.android.activity.*
import com.dateup.android.activity.genderFlow.IdentifyYourselfActivity
import com.dateup.android.activity.genderFlow.InterestedInActivity
import com.dateup.android.activity.genderFlow.WhoShouldSeeActivity
import com.dateup.android.extensions.launchModeWithNoBackStack
import com.dateup.android.firebase.FirebaseDatabaseUtil

class ScreenRouter {

    companion object {

        private const val lastSeenScreen = "lastSeenScreen"

        const val HOME_ACTIVITY = "HOME_ACTIVITY"
        const val EMAIL_ACTIVITY = "EMAIL_ACTIVITY"
        const val WELCOME_ACTIVITY = "WELCOME_ACTIVITY"
        const val LOCATION_ACTIVITY = "LOCATION_ACTIVITY"
        const val FIRST_NAME_ACTIVITY = "FIRST_NAME_ACTIVITY"
        const val DATE_OF_BIRTH_ACTIVITY = "DATE_OF_BIRTH_ACTIVITY"
        const val AGE_RESTRICTION_ACTIVITY = "AGE_RESTRICTION_ACTIVITY"
        const val SEX_PREFERENCE_ACTIVITY = "SEX_PREFERENCE_ACTIVITY"
        const val IDENTIFY_YOURSELF_ACTIVITY = "IDENTIFY_YOURSELF_ACTIVITY"
        const val INTERESTED_IN_ACTIVITY = "INTERESTED_IN_ACTIVITY"
        const val WHO_SHOULD_SEE_ACTIVITY = "WHO_SHOULD_SEE_ACTIVITY"
        const val SEX_PREFERENCE_ACTIVITY_2 = "SEX_PREFERENCE_ACTIVITY_2"
        const val YOUR_HEIGHT_ACTIVITY = "YOUR_HEIGHT_ACTIVITY"
        const val HEIGHT_CELEBRATION_MEMBERS_ACTIVITY = "HEIGHT_CELEBRATION_MEMBERS_ACTIVITY"
        const val HEIGHT_CELEBRATION_GUESTS_ACTIVITY = "HEIGHT_CELEBRATION_GUESTS_ACTIVITY"
        const val GUEST_MATCH_MAKING_ACTIVITY = "GUEST_MATCH_MAKING_ACTIVITY"
        const val GUEST_HEIGHT_PREFERENCE_ACTIVITY = "GUEST_HEIGHT_PREFERENCE_ACTIVITY"
        const val IMAGES_ACTIVITY = "IMAGES_ACTIVITY"
        const val PLEDGE_ACTIVITY = "PLEDGE_ACTIVITY"

        fun setLastSeenScreen(screenTag: String, context: Context) {
            val sharedPreferences = AccountPreferences(context)
            sharedPreferences.setValue(lastSeenScreen, screenTag)
        }

        fun getLastScreenScreen(context: Context): String {
            val sharedPreferences = AccountPreferences(context)
            return sharedPreferences.getStringValue(lastSeenScreen, "")
        }

        fun navigate(screenTag: String, context: Context) {

            when (screenTag) {

                EMAIL_ACTIVITY -> {
                    navigateToScreen(context, EmailActivity::class.java)
                }
                WELCOME_ACTIVITY -> {
                    navigateToScreen(context, WelcomeActivity::class.java)
                }
                LOCATION_ACTIVITY -> {
                    navigateToScreen(context, EnableLocationActivity::class.java)
                }
                FIRST_NAME_ACTIVITY -> {
                    navigateToScreen(context, FirstNameActivity::class.java)
                }
                DATE_OF_BIRTH_ACTIVITY -> {
                    navigateToScreen(context, DateOfBirthActivity::class.java)
                }
                AGE_RESTRICTION_ACTIVITY -> {
                    navigateToScreen(context, AgerestrictionActivity::class.java)
                }
                SEX_PREFERENCE_ACTIVITY -> {
                    navigateToScreen(context, SexPreferenceActivity::class.java)
                }
                YOUR_HEIGHT_ACTIVITY -> {
                    navigateToScreen(context, YourHeightActivity::class.java)
                }
                HEIGHT_CELEBRATION_MEMBERS_ACTIVITY -> {
                    navigateToScreen(context, HeightCelebrationMembersActivity::class.java)
                }
                HEIGHT_CELEBRATION_GUESTS_ACTIVITY -> {
                    navigateToScreen(context, HeightCelebrationGuestsActivity::class.java)
                }
                GUEST_MATCH_MAKING_ACTIVITY -> {
                    navigateToScreen(context, GuestMatchMakingActivity::class.java)
                }
                GUEST_HEIGHT_PREFERENCE_ACTIVITY -> {
                    navigateToScreen(context, GuestHeightPreferenceActivity::class.java)
                }
                IMAGES_ACTIVITY -> {
                    navigateToScreen(context, Images3Activity::class.java)
                }
                PLEDGE_ACTIVITY -> {
                    navigateToScreen(context, DateUpPledgeActivity::class.java)
                }
                IDENTIFY_YOURSELF_ACTIVITY -> {
                    navigateToScreen(context, IdentifyYourselfActivity::class.java)
                }
                INTERESTED_IN_ACTIVITY -> {
                    navigateToScreen(context, InterestedInActivity::class.java)
                }
                WHO_SHOULD_SEE_ACTIVITY -> {
                    navigateToScreen(context, WhoShouldSeeActivity::class.java)
                }
            }
        }

        private fun navigateToScreen(context: Context, activityName: Class<*>) {

            val intent = Intent(context, activityName).launchModeWithNoBackStack()
            context.startActivity(intent)
        }

        fun navigateToBlankNoNetworkConnectionScreen(context: Context) {

            val intent = Intent(context, BlankNoNetworkConnectionActivity::class.java)
            context.startActivity(intent)
        }

        fun saveScreenInfoToFirebase(context: Context, screenName: String) {
            val firebaseDatabaseUtil = FirebaseDatabaseUtil(context)
            firebaseDatabaseUtil.saveLastScreen(screenName)
        }
    }
}