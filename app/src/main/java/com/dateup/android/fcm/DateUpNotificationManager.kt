package com.dateup.android.fcm

import android.app.NotificationManager
import android.content.Context
import com.dateup.android.AccountPreferences
import com.dateup.android.utils.Constants

class DateUpNotificationManager {

    companion object {

        fun clearAllNotifications(context: Context) {

            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.cancelAll()
        }

        fun setUnreadMessageStatus(context: Context, status: Boolean) {
            AccountPreferences.getInstance(context).setValue(Constants.isNewMessageReceived, status)
        }

        fun getUnreadMessageStatus(context: Context) {
            AccountPreferences.getInstance(context).getBooleanValue(Constants.isNewMessageReceived, false)
        }
    }
}