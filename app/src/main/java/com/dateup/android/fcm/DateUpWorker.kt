package com.dateup.android.fcm

import android.content.Context
import androidx.work.Worker
import androidx.work.WorkerParameters

class DateUpWorker(appContext: Context, workerParams: WorkerParameters) : Worker(appContext, workerParams) {

    override fun doWork(): Result {
        //  add long running task here.
        return Result.success()
    }

    companion object {
        private const val TAG = "MyWorker"
    }
}
