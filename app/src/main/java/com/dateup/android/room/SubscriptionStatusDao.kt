package com.dateup.android.room

import androidx.lifecycle.LiveData
import androidx.room.*
import com.dateup.android.subscriptions.model.SubscriptionStatus

@Dao
interface SubscriptionStatusDao {

    @Query("SELECT * FROM SubscriptionStatus")
    fun getAll(): LiveData<List<SubscriptionStatus>>

    @Query("SELECT * FROM SubscriptionStatus WHERE purchaseToken = :purchaseToken")
    fun getSubscription(purchaseToken: String): LiveData<SubscriptionStatus>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(subscriptionDataList: List<SubscriptionStatus>)

    @Query("UPDATE SubscriptionStatus SET is_purchase_acknowledged = :isAcknowledged WHERE purchaseToken = :purchaseToken")
    suspend fun updateSubscriptionAcknowledgementData(purchaseToken: String, isAcknowledged: Boolean)

    @Query("DELETE FROM SubscriptionStatus")
    suspend fun deleteAll()

    @Transaction
    suspend fun replaceAll(subscriptionDataList: List<SubscriptionStatus>) {
        deleteAll()
        insertAll((subscriptionDataList))
    }
}
