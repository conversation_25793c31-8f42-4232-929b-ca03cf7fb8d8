/*
 * Copyright 2018 Google LLC. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dateup.android.subscriptions

object SubscriptionConstants {

    const val MONTHLY_SKU_WITH_TRAIL_OFFER = "com.dateup.android.plus.monthly.trail"
    const val INFLUENCER_MONTHLY_SKU_WITH_TRAIL_OFFER = "com.dateup.android.plus.monthly.trail.influencer"

    const val MONTHLY_SKU = "com.dateup.android.plus.monthly"
    const val THREE_MONTHS_SKU = "com.dateup.android.plus.threemonths"
    const val HALF_YEARLY_SKU = "com.dateup.android.plus.halfyearly"

    const val SELECT_PRODUCT_ID = "com.dateup.android.select"
    const val SELECT_MONTHLY_OFFER_ID = "montly"
    const val SELECT_THREE_MONTHS_OFFER_ID = "threemonth"
    const val SELECT_SIX_MONTHS_OFFER_ID = "sixmonth"

    const val PLAY_STORE_SUBSCRIPTION_URL
            = "https://play.google.com/store/account/subscriptions"
    const val PLAY_STORE_SUBSCRIPTION_DEEPLINK_URL
            = "https://play.google.com/store/account/subscriptions?sku=%s&package=%s"

    const val PLAY_STORE_APP_URL = "https://play.google.com/store/apps/details?id=com.dateup.android"
}
