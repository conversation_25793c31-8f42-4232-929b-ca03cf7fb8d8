package com.dateup.android.subscriptions.views

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.dateup.android.R
import com.dateup.android.activity.GuestHeightPreferenceActivity
import com.dateup.android.activity.GuestMatchMakingActivity
import com.dateup.android.activity.Images3Activity
import com.dateup.android.databinding.ActivityDateUpPledgeBinding
import com.dateup.android.databinding.ActivityDateUpPlusCelebrationBinding
import com.dateup.android.databinding.AgeRestrictionActivityBinding
import com.dateup.android.extensions.launchModeWithNoBackStack
import com.dateup.android.models.GenderType
import com.dateup.android.models.InterestedInGender
import com.dateup.android.ui.browseProfiles.BrowseProfilesActivity
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Utils
import nl.dionsegijn.konfetti.models.Shape
import nl.dionsegijn.konfetti.models.Size

class DateUpPlusCelebrationActivity : AppCompatActivity() {

    private lateinit var binding: ActivityDateUpPlusCelebrationBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDateUpPlusCelebrationBinding.inflate(layoutInflater)
        this.setContentView(binding.root)

        binding.buttonLargeActiveButton.setOnClickListener {
            startProfileActivity()
        }

        startKonfetti()
    }

    private fun startKonfetti() {
        binding.viewKonfetti.build()
            .addColors(resources.getColor(R.color.grey4, null), resources.getColor(R.color.konfetti_color2, null))
            .setDirection(0.0, 359.0)
            .setSpeed(1f, 5f)
            .setFadeOutEnabled(true)
            .setTimeToLive(2000L)
            .addShapes(Shape.Square, Shape.Circle)
            .addSizes(Size(12))
            .setPosition(-50f, AppUtils.getScreenWidth(this) + 50f, -50f, -50f)
            .streamFor(300, 2000L)
    }

    private fun startProfileActivity() {
        val intent = BrowseProfilesActivity.newIntent(this).launchModeWithNoBackStack()
        this.startActivity(intent)
    }

    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, DateUpPlusCelebrationActivity::class.java)
        }
    }
}