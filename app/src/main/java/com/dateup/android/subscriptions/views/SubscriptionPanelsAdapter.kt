package com.dateup.android.subscriptions.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.recyclerview.widget.RecyclerView
import com.dateup.android.R

class SubscriptionPanelsAdapter(
    private val layoutInflater: LayoutInflater
) : RecyclerView.Adapter<SubscriptionPanelsAdapter.PanelViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PanelViewHolder {
        // Create a FrameLayout to wrap the actual content and ensure match_parent
        val frameLayout = FrameLayout(parent.context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
        }

        // Inflate the actual content
        val contentView = when (viewType) {
            0 -> layoutInflater.inflate(R.layout.select_subscription_panel, frameLayout, false)
            else -> layoutInflater.inflate(R.layout.plus_subscription_panel, frameLayout, false)
        }

        // Add content to frame
        frameLayout.addView(contentView)
        return PanelViewHolder(frameLayout)
    }

    override fun onBindViewHolder(holder: PanelViewHolder, position: Int) {
        // Binding is handled in the layout
    }

    override fun getItemCount(): Int = 2

    override fun getItemViewType(position: Int) = position

    class PanelViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)
}