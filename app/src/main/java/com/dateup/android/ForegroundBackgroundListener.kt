package com.dateup.android

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import timber.log.Timber

class ForegroundBackgroundListener(private val onAppGoesToBackground: () -> Unit = {},
                                   private val onAppEntersForeground: () -> Unit = {}) : LifecycleEventObserver {

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        when (event) {
            Lifecycle.Event.ON_START -> {
                Timber.d("DateUp app is in foreground")
                onAppEntersForeground()
            }
            Lifecycle.Event.ON_STOP -> {
                Timber.d("DateUp app is in background")
                onAppGoesToBackground()
            }
            else -> {
                Timber.d("DateUp app event: $event")
            }
        }
    }

    fun attach() {
        ProcessLifecycleOwner.get().lifecycle.addObserver(this)
    }

    fun detach() {
        ProcessLifecycleOwner.get().lifecycle.removeObserver(this)
    }
}