package com.dateup.android.autoComplete

import android.content.Context
import android.content.Intent
import android.location.Address
import android.location.Geocoder
import android.os.Bundle
import android.view.View
import android.widget.EditText
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatImageButton
import com.dateup.android.R
import com.dateup.android.databinding.ActivityAddNewCityBinding
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.firebase.FirebaseDatabaseUtil.Companion.GOOGLE_PLACES_API_KEY
import com.dateup.android.firebase.FirebaseDatabaseUtil.Companion.areApiKeysValid
import com.dateup.android.firebase.FirebaseGenericStatusListener
import com.dateup.android.models.UnlockedLocation
import com.dateup.android.paidVersion.changeLocation.UnlockedLocationsApi
import com.dateup.android.ui.settings.SettingsActivity
import com.dateup.android.utils.Constants
import com.google.android.gms.common.api.Status
import com.google.android.libraries.places.api.Places
import com.google.android.libraries.places.api.model.Place
import com.google.android.libraries.places.widget.AutocompleteSupportFragment
import com.google.android.libraries.places.widget.listener.PlaceSelectionListener
import timber.log.Timber

class AddNewCityActivity : AppCompatActivity() {

    private lateinit var binding: ActivityAddNewCityBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAddNewCityBinding.inflate(layoutInflater)
        this.setContentView(binding.root)

        if (!areApiKeysValid()) {
            val firebaseDatabaseUtil = FirebaseDatabaseUtil(this)
            firebaseDatabaseUtil.getApiKeys(object : FirebaseGenericStatusListener{
                override fun onSuccess() {
                    setupPlaces()
                }
                override fun onFailure() {
                    Timber.e("Failure in getting API keys")
                }
            })
        } else {
            setupPlaces()
        }

        binding.settingsHeaderLeftImageView.setOnClickListener {
            onBackPressed()
        }
    }

    private fun setupPlaces() {
        Places.initialize(applicationContext, GOOGLE_PLACES_API_KEY)

        val autocompleteFragment = supportFragmentManager.findFragmentById(R.id.autocomplete_fragment) as AutocompleteSupportFragment

        autocompleteFragment.setPlaceFields(listOf(Place.Field.ID, Place.Field.NAME, Place.Field.LAT_LNG, Place.Field.ADDRESS))
        autocompleteFragment.setHint("e.g. San Francisco")
        autocompleteFragment.view?.findViewById<AppCompatImageButton>(R.id.places_autocomplete_search_button)?.visibility = View.GONE
        autocompleteFragment.view?.findViewById<EditText>(R.id.places_autocomplete_search_input)?.textSize = 16.0f
        autocompleteFragment.view?.findViewById<EditText>(R.id.places_autocomplete_search_input)?.requestFocus()
        autocompleteFragment.setCountries("USA")

        autocompleteFragment.setOnPlaceSelectedListener(object : PlaceSelectionListener {
            override fun onPlaceSelected(place: Place) {
                val latLong = place.latLng
                val geocoder = Geocoder(this@AddNewCityActivity)
                try {
                    val placesLatitude = latLong?.latitude
                    val placesLongitude = latLong?.longitude
                    if (placesLatitude != null && placesLongitude != null) {
                        val addresses: List<Address>? = geocoder.getFromLocation(placesLatitude, placesLongitude,1)
                        val city = addresses?.get(0)?.locality
                        val zip = addresses?.get(0)?.postalCode
                        val state = addresses?.get(0)?.adminArea

                        val cityKey = "$city-$state".replace(" ", "")
                        val firebaseDatabaseUtil = FirebaseDatabaseUtil(this@AddNewCityActivity)
                        if (city != null && zip != null && state != null) {
                            firebaseDatabaseUtil.saveUserCityInfoToFirebase(cityKey, Constants.city, city)
                            firebaseDatabaseUtil.saveUserCityInfoToFirebase(cityKey, Constants.state, state)
                            firebaseDatabaseUtil.saveUserCityInfoToFirebase(cityKey, Constants.zip, zip)
                        }

                        firebaseDatabaseUtil.saveUserCityInfoToFirebase(cityKey, Constants.lat, placesLatitude)
                        firebaseDatabaseUtil.saveUserCityInfoToFirebase(cityKey, Constants.lng, placesLongitude)

                        if (UnlockedLocationsApi.unlockedLocationsList == null) {
                            UnlockedLocationsApi.unlockedLocationsList = mutableListOf()
                        }
                        val unlockedLocation = UnlockedLocation(placesLatitude, placesLongitude, city, state)
                        if (UnlockedLocationsApi.unlockedLocationsList != null && !UnlockedLocationsApi.unlockedLocationsList!!.contains(unlockedLocation)) {
                            UnlockedLocationsApi.unlockedLocationsList?.add(unlockedLocation)
                        }

                        val intent = SettingsActivity.newIntent(this@AddNewCityActivity)
                        intent.putExtra(Constants.PREFERENCES_FRAGMENT_TELEPORT_FRAGMENT, true)
                        startActivity(intent)
                    }
                } catch (e: Exception) {
                    Timber.e("Error in getting location details: ${e.printStackTrace()}")
                }
            }

            override fun onError(status: Status) {
                Timber.d("An error occurred: $status")
            }
        })
    }

    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, AddNewCityActivity::class.java)
        }
    }
}