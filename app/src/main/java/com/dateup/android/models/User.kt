package com.dateup.android.models

import com.google.firebase.database.IgnoreExtraProperties
import com.google.firebase.database.PropertyName

@IgnoreExtraProperties
data class User(var dob: Any?,
                var email: Any?,
                var gender: Any?,
                var height: Any?,
                var homeTown: Any?,
                var iceBreaker1: Any?,
                var iceBreaker2: Any?,
                var interestedIn: Any?,
                @field:JvmField var isAMember: Boolean?,
                var name: Any?,
                var phone: Any?,
                var profession: Any?,
                var distance: Any?,
                @set:PropertyName("showGuests")
                @get:PropertyName("showGuests")
                var showGuests: Boolean?,
                @field:JvmField var isOnboardingComplete: Boolean?,
                @field:JvmField var isUserLocked: Boolean?,
                var country: Any? = "",
                var city: Any?,
                var school: Any?,
                var school2: Any?,
                var zip: Any?,
                var minMatchAge: Any?,
                var maxMatchAge: Any?,
                var minGuestHeight: Any?,
                var maxGuestHeight: Any?,
                var minMatchHeight: Any?,
                var maxMatchHeight: Any?,
                var connections: Connections?,
                @field:JvmField var isUserReported: Boolean?,
                @field:JvmField var isHeightVerified: Boolean?,
                @set:PropertyName("showNewMatches")
                @get:PropertyName("showNewMatches")
                var showNewMatches: Boolean?,
                @set:PropertyName("showMessages")
                @get:PropertyName("showMessages")
                var showMessages: Boolean?,
                @set:PropertyName("showEverythingElse")
                @get:PropertyName("showEverythingElse")
                var showEverythingElse: Boolean?,
                var state: Any?,
                @set:PropertyName("freeTrailUsed")
                @get:PropertyName("freeTrailUsed")
                var freeTrailUsed: Boolean?,
                @set:PropertyName("rsDel")
                @get:PropertyName("rsDel")
                var rsDel: Boolean?,
                var rTitle: Any? = "",
                var rMsg: Any? = "",
                var externalId: Any? = "") {

    constructor() : this("", "", "", "", "", "", "", "",
            false, "", "", "", "", false,
            false, false, "", "", "", "", "", "", "", "", "",
            "", "", Connections(), false, false, true, true, true, "",
        false, false, "", "", "")
}

