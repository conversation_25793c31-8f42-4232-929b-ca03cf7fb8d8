package com.dateup.android.models

// Enum should be sent to server. They are all lower case letters and with no space
enum class GenderType(val displayText: String) {
    man("man"),
    woman("woman"),
}

// Enum should be sent to server. They are all lower case letters and with no space
enum class OtherGenderType(val displayText: String) {
    agender("Agender"),
    bigender("Bigender"),
    genderfluid("Genderfluid"),
    genderqueer("Genderqueer"),
    gendernonconforming("Gender nonconforming"),
    genderquestioning("Gender questioning"),
    gendervariant("Gender variant"),
    intersex("Intersex"),
    neutrois("Neutrois"),
    nonbinaryman("Nonbinary man"),
    nonBinarywoman("Nonbinary woman"),
    pangender("Pangender"),
    polygender("Polygender"),
    transgender("Transgender"),
    twospirit("Two-spirit"),
    nonbinary("Non-binary")
}

enum class InterestedInGender {
    man,
    woman,
    everyone
}