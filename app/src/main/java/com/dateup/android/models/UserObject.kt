package com.dateup.android.models

import android.content.Context
import androidx.lifecycle.MutableLiveData
import com.dateup.android.AccountPreferences
import com.dateup.android.utils.Constants
import timber.log.Timber
import java.util.*

object UserObject {

    var user: User? = null
    var selectedUnlockedLocation: UnlockedLocation? = null

    var isUserLocationUnlocked: Boolean? = null

    var userFirebaseId: String? = ""

    var isHeightVerified: Boolean? = false
    var isServerOnboardingComplete = false
    var isLocationSentToServer = false

    var isDateUpPlusUser: Boolean? = null
    var isDateUpSelectUser: Boolean? = null

    var userPreferencesChangedLiveData = MutableLiveData(false)

    var userPreferencesUpdatedAPICalled = false

    var isVersionSet = false

    var allLikesYouProfiles = arrayListOf<String>()

    val hasPlusOrSelect: Boolean
        get() {
            return isDateUpPlusUser == true || isDateUpSelectUser == true
        }

    fun setAccountPreferences(user: User, context: Context) {
        try {
            Thread {
                AccountPreferences.getInstance(context).setValue(Constants.dob, user.dob.toString())
                AccountPreferences.getInstance(context).setValue(Constants.email, user.email.toString())
                AccountPreferences.getInstance(context).setValue(Constants.gender, user.gender.toString())
                AccountPreferences.getInstance(context).setValue(Constants.homeTown, user.homeTown.toString())
                AccountPreferences.getInstance(context).setValue(Constants.gender, user.gender.toString())
                AccountPreferences.getInstance(context).setValue(Constants.interestedIn, user.interestedIn.toString())
                AccountPreferences.getInstance(context).setValue(Constants.isAMember, user.isAMember)
                AccountPreferences.getInstance(context).setValue(Constants.name, user.name.toString())
                AccountPreferences.getInstance(context).setValue(Constants.profession, user.profession.toString())
                AccountPreferences.getInstance(context).setValue(Constants.country, user.country.toString())
                AccountPreferences.getInstance(context).setValue(Constants.city, user.city.toString())
                AccountPreferences.getInstance(context).setValue(Constants.school, user.school.toString())
                AccountPreferences.getInstance(context).setValue(Constants.school2, user.school2.toString())
                AccountPreferences.getInstance(context).setValue(Constants.zip, user.zip.toString())
                AccountPreferences.getInstance(context).setValue(Constants.externalId, user.externalId.toString())
                if (user.freeTrailUsed != null) {
                    AccountPreferences.getInstance(context).setValue(Constants.freeTrailUsed, user.freeTrailUsed)
                }

                val iceBreaker1Questions = user.iceBreaker1 as? HashMap<*, *>
                val iceBreaker2Questions = user.iceBreaker2 as? HashMap<*, *>
                var iceBreaker1Question = ""
                var iceBreaker2Question = ""

                if (iceBreaker1Questions != null) {

                    for (key in iceBreaker1Questions.keys) {

                        iceBreaker1Question = key.toString()
                    }
                }

                if (iceBreaker2Questions != null) {

                    for (key2 in iceBreaker2Questions.keys) {

                        iceBreaker2Question = key2.toString()
                    }
                }

                AccountPreferences.getInstance(context).setValue(Constants.showGuests, user.showGuests)

                AccountPreferences.getInstance(context).setValue(Constants.iceBreaker1, iceBreaker1Question)
                AccountPreferences.getInstance(context).setValue(Constants.iceBreaker2, iceBreaker2Question)

                if (user.height.toString().isNotEmpty()) {
                    AccountPreferences.getInstance(context).setValue(Constants.height, user.height.toString().toDouble())
                }

                AccountPreferences.getInstance(context).setValue(Constants.minGuestHeight, user.minGuestHeight.toString().toDouble())
                AccountPreferences.getInstance(context).setValue(Constants.maxGuestHeight, user.maxGuestHeight.toString().toDouble())
                AccountPreferences.getInstance(context).setValue(Constants.minMatchHeight, user.minMatchHeight.toString().toDouble())
                AccountPreferences.getInstance(context).setValue(Constants.maxMatchHeight, user.maxMatchHeight.toString().toDouble())

                AccountPreferences.getInstance(context).setValue(Constants.minMatchAge, user.minMatchAge.toString().toInt())
                AccountPreferences.getInstance(context).setValue(Constants.maxMatchAge, user.maxMatchAge.toString().toInt())

                // Setting distance to default distance 100 even after sign in
                AccountPreferences.getInstance(context).setValue(Constants.distance, Constants.defaultDistance)
//                if (user.distance.toString().isNotEmpty()) {
//                    AccountPreferences.getInstance(context).setValue(Constants.distance, user.distance.toString().toInt())
//                }
            }.start()
        } catch (exception: Exception) {
            Timber.e("exception in setting preferences in user object: $exception")
        }
    }

    fun setUserDataStore(user: User, userFirebaseId: String?) {
        this.user = user
        if (userFirebaseId != null) {
            this.userFirebaseId = userFirebaseId
        }
    }

    fun shouldFetchUserFromServer(): Boolean {
        return this.user == null
    }

    fun cleanup() {
        isServerOnboardingComplete = false
        userFirebaseId = ""
        isLocationSentToServer = false
        isHeightVerified = false
        user = null
    }
}