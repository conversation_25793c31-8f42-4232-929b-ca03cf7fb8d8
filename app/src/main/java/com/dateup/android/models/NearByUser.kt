package com.dateup.android.models

import com.google.firebase.database.IgnoreExtraProperties

@IgnoreExtraProperties
data class GetNearByUsersResult(
    val status: String?,
    val users: List<NearByUser>?)

@IgnoreExtraProperties
data class NearByUser(
                val uid: String?,
                val dob: String?,
                var externalId: String?,
                val height: Any?,
                val gender: String?,
                val homeTown: String?,
                val iceBreaker1: Any?,
                val iceBreaker2: Any?,
                @field:JvmField val isAMember: Boolean?,
                @field:JvmField var isHeightVerified: Boolean?,
                val name: String?,
                val profession: String?,
                val school: String?,
                val school2: String?,
                val distance: Any?,
                val city: String?,
                val state: String?,
                val zip: String?,
                val country: String?)
