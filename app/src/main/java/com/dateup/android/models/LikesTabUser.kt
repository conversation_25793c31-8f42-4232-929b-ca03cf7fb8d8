package com.dateup.android.models

import com.google.firebase.database.IgnoreExtraProperties
import com.google.firebase.database.PropertyName
import java.io.Serializable

@IgnoreExtraProperties
data class LikesTabUser(var uid: Any?,
                        var dob: Any?,
                        var email: Any?,
                        var gender: Any?,
                        var height: Any?,
                        var homeTown: Any?,
                        var iceBreaker1: Any?,
                        var iceBreaker2: Any?,
                        var interestedIn: Any?,
                        @field:JvmField var isAMember: Boolean?,
                        var name: Any?,
                        var phone: Any?,
                        var profession: Any?,
                        var distance: Any?,
                        @set:PropertyName("showGuests")
                        @get:PropertyName("showGuests")
                        var showGuests: Boolean?,
                        @field:JvmField var isOnboardingComplete: Boolean?,
                        @field:JvmField var isUserLocked: Boolean?,
                        var country: Any? = "",
                        var city: Any?,
                        var school: Any?,
                        var school2: Any?,
                        var zip: Any?,
                        var minMatchAge: Any?,
                        var maxMatchAge: Any?,
                        var minGuestHeight: Any?,
                        var maxGuestHeight: Any?,
                        var minMatchHeight: Any?,
                        var maxMatchHeight: Any?,
                        @field:JvmField var isUserReported: Boolean?,
                        @field:JvmField var isHeightVerified: Boolean?,
                        var state: Any?) : Serializable {

    var likedYouUserFirebaseId: String = ""

    constructor() : this("","", "", "", "", "", "", "", "",
            false, "", "", "", "", false,
            false, false, "", "", "", "", "", "", "", "", "",
            "", "", false, false, "")
}