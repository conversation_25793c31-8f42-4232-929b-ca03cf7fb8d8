package com.dateup.android.cloudFunctions

import com.dateup.android.models.UserObject
import com.google.firebase.functions.ktx.functions
import com.google.firebase.ktx.Firebase
import timber.log.Timber

object UserPreferencesUpdatedFunc {

    fun makeUserPreferencesUpdatedAPICall(mainUserId: String) {
        if (mainUserId.isNotEmpty() && !UserObject.userPreferencesUpdatedAPICalled) {
            Timber.d("Calling UserPreferencesUpdatedFunc")
            UserObject.userPreferencesUpdatedAPICalled = true
            callUserPreferencesUpdatedCloudFunction(mainUserId)
        }
    }

    fun callUserPreferencesUpdatedCloudFunction(uid: String) {
        try {
            val functions = Firebase.functions
            val data = hashMapOf(
                "uid" to uid,
            )
            functions
                .getHttpsCallable("userPreferencesUpdated")
                .call(data)
        }catch (exception: Exception) {
            Timber.e("Exception in callUserPreferencesUpdatedCloudFunction API: $exception")
        }
    }
}