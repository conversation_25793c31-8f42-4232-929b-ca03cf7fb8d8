package com.dateup.android.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.dateup.android.R
import com.dateup.android.databinding.ActivityDateUpPledgeBinding
import com.dateup.android.databinding.RowQuestionViewHolderBinding

class Icebreaker1ActivityTableRecyclerViewAdapter(private val questions: ArrayList<String>, val context: Context) : RecyclerView.Adapter<Icebreaker1ActivityTableRecyclerViewAdapter.RowQuestionViewHolder>() {

    private lateinit var listener: OnItemClickListener

    override fun onBindViewHolder(holder: RowQuestionViewHolder, position: Int) {
        holder.questionTextView.text = questions[position]
        holder.itemView.setOnClickListener {
            listener.onClick(it, questions[position])
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RowQuestionViewHolder {
        val itemBinding = RowQuestionViewHolderBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        setOnItemClickListener(listener)
        return RowQuestionViewHolder(itemBinding)
    }

    override fun getItemCount(): Int {

        return questions.size
    }

    interface OnItemClickListener {
        fun onClick(view: View, data: String)
    }

    fun setOnItemClickListener(listener: OnItemClickListener) {
        this.listener = listener
    }

    class RowQuestionViewHolder(itemView: RowQuestionViewHolderBinding) : RecyclerView.ViewHolder(itemView.root) {

        val questionTextView: TextView = itemView.yourFavoriteHobbyTextView
    }
}
