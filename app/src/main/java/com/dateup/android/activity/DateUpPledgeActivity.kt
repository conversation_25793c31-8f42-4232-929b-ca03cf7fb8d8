package com.dateup.android.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.databinding.ActivityDateUpPledgeBinding
import com.dateup.android.extensions.launchModeWithNoBackStack
import com.dateup.android.ui.browseProfiles.BrowseProfilesActivity
import com.dateup.android.extensions.launchModeWithSingleTop
import com.dateup.android.subscriptions.views.SubscriptionActivity
import com.dateup.android.models.GenderType
import com.dateup.android.utils.Constants

class DateUpPledgeActivity : AppCompatActivity() {

    private lateinit var binding: ActivityDateUpPledgeBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDateUpPledgeBinding.inflate(layoutInflater)
        setContentView(binding.root)

        ScreenRouter.setLastSeenScreen(TAG, this)

        val isMember = AccountPreferences.getInstance(this).getBooleanValue(Constants.isAMember, false)
        val gender: String = AccountPreferences.getInstance(this).getStringValue(Constants.gender, "")

        if (gender == GenderType.woman.toString() && isMember) {
            binding.pledgeDesc.text = getString(R.string.pledge_desc_for_women)
        }

        binding.pledgeAgreeButton.setOnClickListener {
            startSubscriptionScreen()
        }

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun startSubscriptionScreen() {
        val subIntent = SubscriptionActivity.newIntent(this)
        val influencerId = AccountPreferences.getInstance(applicationContext).getStringValue(com.dateup.android.utils.Constants.influencerId, "")
        if (influencerId.isNotEmpty()) {
            subIntent.putExtra(SubscriptionActivity.INFLUENCER_FREE_TRAIL, true)
        } else {
            subIntent.putExtra(SubscriptionActivity.FREE_TRAIL, true)
        }
        startActivity(subIntent.launchModeWithSingleTop())
    }

    companion object {

        const val TAG = ScreenRouter.PLEDGE_ACTIVITY

        fun newIntent(context: Context): Intent {
            return Intent(context, DateUpPledgeActivity::class.java)
        }
    }
}