package com.dateup.android.activity

import android.Manifest
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.view.MenuItem
import android.view.View
import android.view.WindowManager
import android.widget.*
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.lifecycle.ViewModelProvider
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.ScreenRouter.Companion.setLastSeenScreen
import com.dateup.android.attribution.AttributionAnalytics
import com.dateup.android.cloudVision.VisionImageModeration
import com.dateup.android.extensions.launchModeWithNoBackStack
import com.dateup.android.extensions.launchModeWithSingleTop
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.geofire.NearByUsers
import com.dateup.android.glide.ImageLoaderModule
import com.dateup.android.models.GenderType
import com.dateup.android.spaces.SpacesRepository
import com.dateup.android.subscriptions.AppExecutors
import com.dateup.android.subscriptions.viewModels.BillingViewModel
import com.dateup.android.subscriptions.views.SubscriptionActivity
import com.dateup.android.ui.browseProfiles.BrowseProfilesActivity
import com.dateup.android.utils.*
import com.dateup.android.utils.Constants.photoFileName1
import com.dateup.android.utils.Constants.photoFileName2
import com.dateup.android.utils.Constants.photoFileName3
import com.dateup.android.utils.Constants.photoFileName4
import com.dateup.android.utils.Constants.photoFileName5
import com.dateup.android.utils.Constants.photoFileName6
import com.dateup.android.viewModels.SpacesImagesViewModel
import com.yalantis.ucrop.UCrop
import pl.aprilapps.easyphotopicker.DefaultCallback
import pl.aprilapps.easyphotopicker.EasyImage
import pl.aprilapps.easyphotopicker.MediaFile
import pl.aprilapps.easyphotopicker.MediaSource
import timber.log.Timber
import java.io.ByteArrayOutputStream
import java.io.File
import java.util.*


class Images3Activity : AppCompatActivity() {

    companion object {

        const val TAG = ScreenRouter.IMAGES_ACTIVITY

        fun newIntent(context: Context): Intent {
            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, Images3Activity::class.java)
        }
    }

    private lateinit var pleaseUploadAtLeaTextView: TextView
    private lateinit var buttonLargeActiveButton: Button

    private lateinit var photoImageView0: ImageView
    private lateinit var photoImageView1: ImageView
    private lateinit var photoImageView2: ImageView
    private lateinit var photoImageView3: ImageView
    private lateinit var photoImageView4: ImageView
    private lateinit var photoImageView5: ImageView

    private lateinit var iconAddDelImageView0: ImageView
    private lateinit var iconAddDelImageView1: ImageView
    private lateinit var iconAddDelImageView2: ImageView
    private lateinit var iconAddDelImageView3: ImageView
    private lateinit var iconAddDelImageView4: ImageView
    private lateinit var iconAddDelImageView5: ImageView

    private lateinit var resultPhotoImageView: ImageView
    private lateinit var resultAddDelPhotoImageView: ImageView

    private lateinit var imagesProgressBar: ProgressBar

    private lateinit var toolbar: Toolbar
    private lateinit var skipTextView: TextView

    private lateinit var managePhotoPermission: ManagePermissions
    private lateinit var manageStoragePermission: ManagePermissions

    private val photoPermissionRequestCode = 3
    private val storagePermissionRequestCode = 5

    private var photoFileName = ""

    private lateinit var mainUserId: String

    private lateinit var spacesImagesViewModel: SpacesImagesViewModel

    private lateinit var billingViewModel: BillingViewModel

    private val photoPermissionList = listOf(
            Manifest.permission.CAMERA
    )

    private val storagePermissionList = listOf(
            Manifest.permission.READ_EXTERNAL_STORAGE
    )

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private val storagePermissionListApi33 = listOf(
        Manifest.permission.READ_MEDIA_IMAGES
    )

    private lateinit var easyImage: EasyImage

    var originalImageFiles: Array<MediaFile>? = null

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.images3_activity)

        billingViewModel = ViewModelProvider(this).get(BillingViewModel::class.java)

        spacesImagesViewModel = ViewModelProvider(this).get(SpacesImagesViewModel::class.java)

        easyImage = EasyImage.Builder(this)
                .setCopyImagesToPublicGalleryFolder(false)
                .setFolderName("dateup-photos")
                .allowMultiple(false)
                .build()

        this.init()

        setLastSeenScreen(TAG, this)
        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)

        makeNearbyUsersAPICall()
    }

    private fun init() {

        setupToolbar()

        mainUserId = AccountPreferences.getInstance(this).getStringValue(Constants.firebaseUserId, "")

        // Configure Please upload at lea component
        pleaseUploadAtLeaTextView = this.findViewById(R.id.please_upload_at_lea_text_view)
        val pleaseUploadAtLeaTextViewText = SpannableString(this.getString(R.string.images3_activity_please_upload_at_lea_text_view_text))
        pleaseUploadAtLeaTextViewText.setSpan(FontSpan(ResourcesCompat.getFont(this, R.font.font_nunitosans_extrabolditalic)), 13, 26, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        pleaseUploadAtLeaTextViewText.setSpan(ForegroundColorSpan(Color.parseColor("#25333D")), 13, 26, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        pleaseUploadAtLeaTextView.text = pleaseUploadAtLeaTextViewText

        pleaseUploadAtLeaTextView.text = pleaseUploadAtLeaTextViewText

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)

        imagesProgressBar = this.findViewById(R.id.images_progress_bar)

        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }

        photoImageView0 = this.findViewById(R.id.icon_camera_image_view0)
        photoImageView1 = this.findViewById(R.id.icon_camera_image_view1)
        photoImageView2 = this.findViewById(R.id.icon_camera_image_view2)
        photoImageView3 = this.findViewById(R.id.icon_camera_image_view3)
        photoImageView4 = this.findViewById(R.id.icon_camera_image_view4)
        photoImageView5 = this.findViewById(R.id.icon_camera_image_view5)

        iconAddDelImageView0 = this.findViewById(R.id.icon_plus_addphoto_image_view0)
        iconAddDelImageView1 = this.findViewById(R.id.icon_plus_addphoto_image_view1)
        iconAddDelImageView2 = this.findViewById(R.id.icon_plus_addphoto_image_view2)
        iconAddDelImageView3 = this.findViewById(R.id.icon_plus_addphoto_image_view3)
        iconAddDelImageView4 = this.findViewById(R.id.icon_plus_addphoto_image_view4)
        iconAddDelImageView5 = this.findViewById(R.id.icon_plus_addphoto_image_view5)

        photoImageView0.setOnClickListener { view ->
            resultPhotoImageView = photoImageView0
            resultAddDelPhotoImageView = iconAddDelImageView0
            photoFileName = photoFileName1
            this.showPictureDialog()
        }
        photoImageView1.setOnClickListener { view ->
            resultPhotoImageView = photoImageView1
            resultAddDelPhotoImageView = iconAddDelImageView1
            photoFileName = photoFileName2
            this.showPictureDialog()
        }
        photoImageView2.setOnClickListener { view ->
            resultPhotoImageView = photoImageView2
            resultAddDelPhotoImageView = iconAddDelImageView2
            photoFileName = photoFileName3
            this.showPictureDialog()
        }
        photoImageView3.setOnClickListener { view ->
            resultPhotoImageView = photoImageView3
            resultAddDelPhotoImageView = iconAddDelImageView3
            photoFileName = photoFileName4
            this.showPictureDialog()
        }
        photoImageView4.setOnClickListener { view ->
            resultPhotoImageView = photoImageView4
            resultAddDelPhotoImageView = iconAddDelImageView4
            photoFileName = photoFileName5
            this.showPictureDialog()
        }
        photoImageView5.setOnClickListener { view ->
            resultPhotoImageView = photoImageView5
            resultAddDelPhotoImageView = iconAddDelImageView5
            photoFileName = photoFileName6
            this.showPictureDialog()
        }

        iconAddDelImageView0.setOnClickListener { view ->
            resultPhotoImageView = photoImageView0
            resultAddDelPhotoImageView = iconAddDelImageView0
            photoFileName = photoFileName1
            if (photoImageView0.drawable?.constantState?.equals(ContextCompat.getDrawable(this, R.drawable.icon_camera)?.constantState) == true) {
                this.showPictureDialog()
            } else {
                this.clearPhoto(iconAddDelImageView0, photoImageView0, "$mainUserId/$photoFileName1")
                disableButton()
            }
        }
        iconAddDelImageView1.setOnClickListener { view ->
            resultPhotoImageView = photoImageView1
            resultAddDelPhotoImageView = iconAddDelImageView1
            photoFileName = photoFileName2
            if (photoImageView1.drawable?.constantState?.equals(ContextCompat.getDrawable(this, R.drawable.icon_camera)?.constantState) == true) {
                this.showPictureDialog()
            } else {
                this.clearPhoto(iconAddDelImageView1, photoImageView1, "$mainUserId/$photoFileName2")
                disableButton()
            }
        }
        iconAddDelImageView2.setOnClickListener { view ->
            if (photoImageView2.drawable?.constantState?.equals(ContextCompat.getDrawable(this, R.drawable.icon_camera)?.constantState) == true) {
                resultPhotoImageView = photoImageView2
                resultAddDelPhotoImageView = iconAddDelImageView2
                photoFileName = photoFileName3
                this.showPictureDialog()
            } else {
                this.clearPhoto(iconAddDelImageView2, photoImageView2, "$mainUserId/$photoFileName3")
            }
        }
        iconAddDelImageView3.setOnClickListener { view ->
            if (photoImageView3.drawable?.constantState?.equals(ContextCompat.getDrawable(this, R.drawable.icon_camera)?.constantState) == true) {
                resultPhotoImageView = photoImageView3
                resultAddDelPhotoImageView = iconAddDelImageView3
                photoFileName = photoFileName4
                this.showPictureDialog()
            } else {
                this.clearPhoto(iconAddDelImageView3, photoImageView3, "$mainUserId/$photoFileName4")
            }
        }
        iconAddDelImageView4.setOnClickListener { view ->
            if (photoImageView4.drawable?.constantState?.equals(ContextCompat.getDrawable(this, R.drawable.icon_camera)?.constantState) == true) {
                resultPhotoImageView = photoImageView4
                resultAddDelPhotoImageView = iconAddDelImageView4
                photoFileName = photoFileName5
                this.showPictureDialog()
            } else {
                this.clearPhoto(iconAddDelImageView4, photoImageView4, "$mainUserId/$photoFileName5")
            }
        }
        iconAddDelImageView5.setOnClickListener { view ->
            if (photoImageView5.drawable?.constantState?.equals(ContextCompat.getDrawable(this, R.drawable.icon_camera)?.constantState) == true) {
                resultPhotoImageView = photoImageView5
                resultAddDelPhotoImageView = iconAddDelImageView5
                photoFileName = photoFileName6
                this.showPictureDialog()
            } else {
                this.clearPhoto(iconAddDelImageView5, photoImageView5, "$mainUserId/$photoFileName6")
            }
        }

        managePhotoPermission = ManagePermissions(this, photoPermissionList, photoPermissionRequestCode)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            manageStoragePermission = ManagePermissions(this, storagePermissionListApi33, storagePermissionRequestCode)
        } else {
            manageStoragePermission = ManagePermissions(this, storagePermissionList, storagePermissionRequestCode)
        }

        checkForUploadedPhotos()
    }

    fun setupToolbar() {

        toolbar = findViewById(R.id.toolbar)

        skipTextView = this.findViewById(R.id.skip_text_view)
        skipTextView.visibility = View.GONE

        setSupportActionBar(toolbar)

        val actionBar = supportActionBar
        val drawable: Drawable? = ContextCompat.getDrawable(this, R.drawable.ic_arrow_left)

        actionBar?.setHomeAsUpIndicator(drawable)
        actionBar?.setDisplayShowTitleEnabled(true)
        actionBar?.setDisplayHomeAsUpEnabled(true)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {

        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                return true
            }
        }

        return super.onOptionsItemSelected(item)
    }

    private fun resetImageViewThumbnail(addDeleteImageView: ImageView, photoImageView: ImageView) {
        if (::resultAddDelPhotoImageView.isInitialized && ::resultPhotoImageView.isInitialized) {
            photoImageView.setImageResource(R.drawable.icon_camera)
            addDeleteImageView.setImageResource(R.drawable.icon_plus_addphoto)
            addDeleteImageView.elevation = 2F
        }
    }

    private fun clearPhoto(addDeleteImageView: ImageView, photoImageView: ImageView, imagePath: String) {
        showProgressBar()
        spacesImagesViewModel.deleteImage(imagePath) { spacesResult ->
            runOnUiThread {
                hideprogressBar()
                if (spacesResult == SpacesRepository.SpacesStatus.FAILED) {
                    Utils.showAlert("Unable to delete the picture. Please try again", this)
                } else {
                    resetImageViewThumbnail(addDeleteImageView, photoImageView)
                }
            }
        }
    }

    private fun showPictureDialog() {
        val pictureDialog = AlertDialog.Builder(this)
        pictureDialog.setTitle("Select Action")
        val pictureDialogItems = arrayOf("Select photo from gallery", "Capture photo from camera")
        pictureDialog.setItems(pictureDialogItems
        ) { dialog, which ->
            when (which) {
                0 -> askStoragePermission()
                1 -> askPhotoPermission()
            }
        }
        pictureDialog.show()
    }

    private fun askStoragePermission() {

        if (manageStoragePermission.isPermissionGranted()) {
            easyImage.openGallery(this)
        } else {
            manageStoragePermission.checkPermissions()
        }
    }


    private fun askPhotoPermission() {

        if (managePhotoPermission.isPermissionGranted()) {
            easyImage.openCameraForImage(this)
        } else {
            managePhotoPermission.checkPermissions()
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>,
                                            grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {

            photoPermissionRequestCode -> {
                val isPermissionsGranted = managePhotoPermission.processPermissionsResult(requestCode, permissions, grantResults)

                if (isPermissionsGranted) {
                    easyImage.openCameraForImage(this)
                } else {
                    Utils.showPermissionAlert("To continue, give DateUp access to your photos", this)
                }
                return
            }

            storagePermissionRequestCode -> {
                val isPermissionsGranted = manageStoragePermission.processPermissionsResult(requestCode, permissions, grantResults)
                if (isPermissionsGranted) {
                    easyImage.openGallery(this)
                } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    Utils.showPermissionAlert("To continue, give DateUp access to your Photos and videos", this)
                } else {
                    Utils.showPermissionAlert("To continue, give DateUp access to your files and media", this)
                }
                return
            }
        }
    }

    public override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if(resultCode != RESULT_CANCELED) {
            if (resultCode == RESULT_OK && requestCode == UCrop.REQUEST_CROP && data != null) {
                val selectedImageCroppedUri = UCrop.getOutput(data)
                selectedImageCroppedUri?.path?.let {
                    selectedImageToUpload(File(it))
                }
            } else if (resultCode == UCrop.RESULT_ERROR && data != null) {
                uploadOriginalPhotoToFirebase(data)
            }

            easyImage.handleActivityResult(requestCode, resultCode, data, this, object : DefaultCallback() {
                override fun onMediaFilesPicked(imageFiles: Array<MediaFile>, source: MediaSource) {
                    startCropScreen(imageFiles)
                    originalImageFiles = imageFiles
                }

                override fun onImagePickerError(error: Throwable, source: MediaSource) {
                    Timber.e("Exception in picking image ${error.printStackTrace()}")
                }

                override fun onCanceled(source: MediaSource) {
                    //Not necessary to remove any files manually anymore
                }
            })
        }
    }

    private fun replaceAddDelImage() {
        if (::resultAddDelPhotoImageView.isInitialized) {
            resultAddDelPhotoImageView.setImageResource(R.drawable.icon_del_photo)
            resultAddDelPhotoImageView.elevation = 2F
        }
    }

    private fun uploadOriginalPhotoToFirebase(data: Intent) {
        if (originalImageFiles != null && originalImageFiles is Array<MediaFile>) {
            Toast.makeText(this, "Unable to crop, Using original picture", Toast.LENGTH_LONG).show()
            replaceAddDelImage()
            ImageLoaderModule.loadImageIntoImageViewWithFile(this@Images3Activity, (originalImageFiles as Array<MediaFile>)[0].file, resultPhotoImageView)
            uploadBitmapToFirebase(getBitmapFromFile((originalImageFiles as Array<MediaFile>)[0]))
        }else {
            Timber.e("Exception while cropping image ${UCrop.getError(data)}")
            Toast.makeText(this, "Unable to crop the picture", Toast.LENGTH_LONG).show()
        }
        originalImageFiles = null
    }

    private fun getBitmapFromFile(mediaFile: MediaFile): Bitmap? {
        return PhotoUtils.handleImageOrientation(this, mediaFile.file.path, null)
    }

    private fun uploadBitmapToFirebase(bitmap: Bitmap?) {

        showProgressBar()
        val firebaseUserId = AccountPreferences.getInstance(applicationContext).getStringValue(Constants.firebaseUserId, "")
        if (!TextUtils.isEmpty(firebaseUserId)) {
            val executor = AppExecutors()
            executor.networkIO.execute {
                val baos = ByteArrayOutputStream()
                bitmap?.compress(Bitmap.CompressFormat.JPEG, 100, baos)
                val data = baos.toByteArray()

                VisionImageModeration.checkImageForModerationNode(mainUserId, data) { isPhotoViolated ->
                    if (isPhotoViolated) {
                        resetImageViewThumbnail(resultAddDelPhotoImageView, resultPhotoImageView)
                        executor.mainThread.execute {

                            AlertDialogView.showAlertDialogWithLink(context = this,
                                    title = getString(R.string.image_moderation_title),
                                    message = "We detected that this photo may not meet our <a href=\"https://www.dateup.co/photo-guidelines\">photo guidelines</a>. You must be present in all photos and explicit content is not allowed, which are common reasons why photos get flagged.",
                                    buttonPositiveText = getString(R.string.image_moderation_close),
                                    buttonNegativeText = null) { dialog, _ ->
                                dialog.cancel()
                            }
                            hideprogressBar()
                        }
                    } else {
                        bitmap?.let {
                            spacesImagesViewModel.uploadBitmap(this, firebaseUserId, photoFileName, it) {
                                executor.mainThread.execute {
                                    hideprogressBar()
                                }
                            }
                        }
                    }
                }
            }
        } else {
            hideprogressBar()
        }
        checkForUploadedPhotos()
    }

    fun onButtonLargeActivePressed() {
        if (!AppUtils.isNetworkConnected(application)) {
            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            val isMember = AccountPreferences.getInstance(this).getBooleanValue(Constants.isAMember, false)
            val gender: String = AccountPreferences.getInstance(this).getStringValue(Constants.gender, "")

            setOnboardingCompleteFlag()

            if (gender == GenderType.woman.toString() && !isMember) {
                startSubscriptionScreen()
            } else {
                startPledgeActivity()
            }
        }
    }

    private fun checkForUploadedPhotos() {

        if (photoImageView0.drawable?.constantState?.equals(ContextCompat.getDrawable(this, R.drawable.icon_camera)?.constantState) == true) {

            return
        }

        if (photoImageView1.drawable?.constantState?.equals(ContextCompat.getDrawable(this, R.drawable.icon_camera)?.constantState) == true) {

            return
        }
        enableButton()
    }

    private fun startSubscriptionScreen() {
        val subIntent = SubscriptionActivity.newIntent(this)
        val influencerId = AccountPreferences.getInstance(applicationContext).getStringValue(com.dateup.android.utils.Constants.influencerId, "")
        if (influencerId.isNotEmpty()) {
            subIntent.putExtra(SubscriptionActivity.INFLUENCER_FREE_TRAIL, true)
        } else {
            subIntent.putExtra(SubscriptionActivity.FREE_TRAIL, true)
        }
        startActivity(subIntent.launchModeWithSingleTop())
    }

    private fun startPledgeActivity() {
        val intent = DateUpPledgeActivity.newIntent(this).launchModeWithNoBackStack()
        this.startActivity(intent)
    }

    private fun startCropScreen(imageFiles: Array<MediaFile>) {
        val imageUri = Uri.fromFile(File((imageFiles[0].file.path)))
        val destinationFileName: String = imageFiles[0].file.name

        val options = UCrop.Options()
        options.setHideBottomControls(true)

        val ucrop = UCrop.of(imageUri, Uri.fromFile(File(cacheDir, destinationFileName)))
            .withAspectRatio(1f, 1f)
            .withOptions(options)
        ucrop.start(this)
    }

    private fun selectedImageToUpload(imageFile: File) {
        replaceAddDelImage()
        ImageLoaderModule.loadImageIntoImageViewWithFile(this@Images3Activity, imageFile, resultPhotoImageView)
        uploadBitmapToFirebase(getBitmapFromFile(imageFile))
    }

    private fun getBitmapFromFile(file: File): Bitmap? {
        return PhotoUtils.handleImageOrientation(this, file.path, null)
    }

    private fun enableButton() {
        buttonLargeActiveButton.setBackgroundResource(R.drawable.phone_number_activity_button_large_active_button_selector)
        buttonLargeActiveButton.isEnabled = true
    }

    private fun disableButton() {
        buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_disabled_state)
        buttonLargeActiveButton.isEnabled = false
    }

    private fun showProgressBar() {
        imagesProgressBar.visibility = View.VISIBLE
        window.setFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
    }

    private fun hideprogressBar() {
        imagesProgressBar.visibility = View.GONE
        window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
    }

    private fun makeNearbyUsersAPICall() {
        Thread {
            val distanceFromPrefs = AccountPreferences.getInstance(this).getIntValue(Constants.distance, Constants.defaultDistance)
            val lat = AccountPreferences.getInstance(this).getDoubleValue(Constants.latitude, 0.0)
            val long = AccountPreferences.getInstance(this).getDoubleValue(Constants.longitude, 0.0)
            NearByUsers.getNearByUsers(mainUserId, lat, long, distanceFromPrefs) {_,_ ->}
        }.start()
    }

    private fun setOnboardingCompleteFlag() {
        AccountPreferences.getInstance(this).setValue(Constants.isOnboardingComplete, true)
        val firebaseDatabaseUtil = FirebaseDatabaseUtil(this)
        firebaseDatabaseUtil.saveUserInfoToFirebase(Constants.isOnboardingComplete, true)

        // Log onboarding complete with attribution source
        AttributionAnalytics.logOnboardingComplete(this)
    }
}
