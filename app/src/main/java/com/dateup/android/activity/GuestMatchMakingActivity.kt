package com.dateup.android.activity

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.res.ResourcesCompat
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.ScreenRouter.Companion.setLastSeenScreen
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.models.GenderType
import com.dateup.android.models.InterestedInGender
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Constants
import com.dateup.android.utils.FontSpan

class GuestMatchMakingActivity : AppCompatActivity() {

    companion object {

        const val TAG = ScreenRouter.GUEST_MATCH_MAKING_ACTIVITY

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, GuestMatchMakingActivity::class.java)
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.guest_match_making_activity)
        this.init()

        setLastSeenScreen(TAG, this)
        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private lateinit var guestsImageView: ImageView
    private lateinit var shouldWeShowYouGtextView: TextView
    private lateinit var showGuestsButton: Button
    private lateinit var notInterestedButton: Button

    private fun init() {
        shouldWeShowYouGtextView = this.findViewById(R.id.should_we_show_you_gtext_view)
        val shouldWeShowYouGtextViewText = SpannableString(this.getString(R.string.guest_matchmaking2_activity_should_we_show_you_gtext_view_text))
        shouldWeShowYouGtextViewText.setSpan(ForegroundColorSpan(Color.parseColor("#25333D")), 19, 25, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        shouldWeShowYouGtextViewText.setSpan(FontSpan(ResourcesCompat.getFont(this, R.font.font_nunitosans_extrabolditalic)), 19, 25, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        shouldWeShowYouGtextView.text = shouldWeShowYouGtextViewText

        guestsImageView = this.findViewById(R.id.guests_match_making_image_view)

        val mainUserGender: String = AccountPreferences.getInstance(this).getStringValue(Constants.gender, "")

        val interestedIn = AccountPreferences.getInstance(this).getStringValue(Constants.interestedIn, "")

        when {
            (mainUserGender == GenderType.man.toString()) and (interestedIn == InterestedInGender.woman.toString()) -> {
                guestsImageView.setImageResource(R.drawable.ic_tall_man_with_short_women)
            }
            (mainUserGender == GenderType.woman.toString()) and (interestedIn == InterestedInGender.man.toString()) -> {
                guestsImageView.setImageResource(R.drawable.ic_color_tall_woman_with_short_man)
            }
            (mainUserGender ==GenderType.woman.toString()) and (interestedIn == InterestedInGender.woman.toString()) -> {
                guestsImageView.setImageResource(R.drawable.tall_woman_short_woman)
            }
            (mainUserGender == GenderType.man.toString()) and (interestedIn == InterestedInGender.man.toString()) -> {
                guestsImageView.setImageResource(R.drawable.tall_man_short_man)
            }
        }

        showGuestsButton = this.findViewById(R.id.button_large_active_two_button)
        notInterestedButton = this.findViewById(R.id.button_large_active_button)

        showGuestsButton.setOnClickListener { view ->
            this.showGuestsButtonClicked()
        }

        notInterestedButton.setOnClickListener { view ->
            this.notInterestedButtonClicked()
        }
    }

    private fun showGuestsButtonClicked() {
        saveGuestPreferenceToFirebase(true)
        startImagesActivity()
    }

    private fun notInterestedButtonClicked() {
        saveGuestPreferenceToFirebase(false)
        startImagesActivity()
    }

    private fun startImagesActivity() {

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            this.startActivity(Images3Activity.newIntent(this))
        }
    }

    private fun saveGuestPreferenceToFirebase(preference: Boolean) {

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.showGuests, preference)

        AccountPreferences.getInstance(this).setValue(Constants.showGuests, preference)
    }
}
