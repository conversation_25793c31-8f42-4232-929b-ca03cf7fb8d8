package com.dateup.android.activity

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import com.dateup.android.R
import com.dateup.android.databinding.ActivityHowHeightVerificationWorksBinding
import com.dateup.android.ScreenRouter
import com.dateup.android.utils.Utils


class HowHeightVerificationWorks : AppCompatActivity() {

    private lateinit var binding: ActivityHowHeightVerificationWorksBinding

    companion object {

        fun newIntent(context: Context): Intent {
            return Intent(context, HowHeightVerificationWorks::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityHowHeightVerificationWorksBinding.inflate(layoutInflater)
        setContentView(binding.root)


        binding.accountHeaderLeftImageView.setOnClickListener {

            onBackPressed()
        }

        binding.buttonLargeActiveButton.setOnClickListener {

            onBackPressed()
        }

        val ss = SpannableString(getString(R.string.height_scan_help_point_3))
        val clickableSpan: ClickableSpan = object : ClickableSpan() {

            override fun onClick(textView: View) {

                Utils.openGmailApp(this@HowHeightVerificationWorks, "")
            }
        }
        ss.setSpan(clickableSpan, 0, 16, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

        binding.thirdPoint.text = ss
        binding.thirdPoint.movementMethod = LinkMovementMethod.getInstance()
        binding.thirdPoint.highlightColor = Color.TRANSPARENT

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }
}