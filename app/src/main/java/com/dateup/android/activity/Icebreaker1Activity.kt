package com.dateup.android.activity

import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.text.SpannableString
import android.view.View
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.adapter.Icebreaker1ActivityTableRecyclerViewAdapter
import com.dateup.android.extensions.launchModeWithNoBackStack
import com.dateup.android.ui.browseProfiles.BrowseProfilesActivity
import com.dateup.android.utils.AlertDialogView
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Utils

class Icebreaker1Activity : AppCompatActivity() {

    companion object {

        const val ICE_BREAKER_1_QUESTION = "ICE_BREAKER_1_QUESTION"
        const val ICE_BREAKER_1_ANSWER = "ICE_BREAKER_1_ANSWER"

        const val ICE_BREAKER_2_QUESTION = "ICE_BREAKER_2_QUESTION"

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, Icebreaker1Activity::class.java)
        }

        fun abandonIceBreakers(activity: Activity) {

            AlertDialogView.showAlertDialog(context = activity,
                    title = activity.getString(R.string.abandon_title),
                    message = activity.getString(R.string.abandon_desc),
                    buttonPositiveText = activity.getString(R.string.yes_abandon),
                    buttonNegativeText = activity.getString(R.string.no_keep_editing)) { dialog, which ->

                if (which == DialogInterface.BUTTON_POSITIVE) {

                    val intent = BrowseProfilesActivity.newIntent(activity).launchModeWithNoBackStack()
                    activity.startActivity(intent)
                } else {

                    dialog.cancel()
                }
            }
        }
    }

    private lateinit var chooseTwoProfileQtextView: TextView
    private lateinit var tableRecyclerView: RecyclerView
    private lateinit var skipTextView: TextView

    private val questions: ArrayList<String> = ArrayList()

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.icebreaker1_activity)
        this.init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        // Configure Choose two profile q component
        chooseTwoProfileQtextView = this.findViewById(R.id.choose_two_profile_qtext_view)
        val chooseTwoProfileQtextViewText = SpannableString(this.getString(R.string.icebreaker1_activity_choose_two_profile_qtext_view_text))
        chooseTwoProfileQtextView.text = chooseTwoProfileQtextViewText

        // Configure table component
        tableRecyclerView = this.findViewById(R.id.table_recycler_view)
        tableRecyclerView.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)

        addQuestions()

        val questionsAdapter = Icebreaker1ActivityTableRecyclerViewAdapter(questions, this)
        tableRecyclerView.adapter = questionsAdapter

        questionsAdapter.setOnItemClickListener(object : Icebreaker1ActivityTableRecyclerViewAdapter.OnItemClickListener {

            override fun onClick(view: View, data: String) {

                startIceBreaker2Activity(data)
            }
        })

        setupToolbar()
    }

    fun setupToolbar() {

        skipTextView = this.findViewById(R.id.skip_text_view)

        skipTextView.setOnClickListener {

            abandonIceBreakers(this)
        }
    }

    private fun onSkipButtonPressed() {

        val intent = BrowseProfilesActivity.newIntent(this).launchModeWithNoBackStack()
        this.startActivity(intent)
    }

    private fun startIceBreaker2Activity(question: String) {

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            val intent = Intent(this@Icebreaker1Activity, Icebreaker2Activity::class.java)
            intent.putExtra(ICE_BREAKER_1_QUESTION, question)
            startActivity(intent)
        }
    }

    private fun addQuestions() {

        val questionsMap = Utils.getIceBreakerQuestions()

        for (key in questionsMap.keys) {

            questions.add(key)
        }
    }
}
