package com.dateup.android.activity.genderFlow

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.analytics.AnalyticsTrackingService
import com.dateup.android.analytics.USER_ORIENTATION_PROPERTY
import com.dateup.android.databinding.ActivityGenderListBinding
import com.dateup.android.databinding.ActivityHeightVerificationScanBinding
import com.dateup.android.databinding.ActivityHeightVerificationScanResultBinding
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.models.OtherGenderType
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Constants
class GenderListActivity : AppCompatActivity() {

    private lateinit var binding: ActivityGenderListBinding

    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, GenderListActivity::class.java)
        }
    }

    private val genders: ArrayList<OtherGenderType> = ArrayList()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityGenderListBinding.inflate(layoutInflater)
        setContentView(binding.root)

        init()

        ScreenRouter.saveScreenInfoToFirebase(this, GenderListActivity::class.java.simpleName)
    }

    private fun init() {

        binding.skipTextView.setOnClickListener {
            onCancelButtonPressed()
        }

        // Configure table component
        binding.tableRecyclerView.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)

        addGenders()

        val genderAdapter = GenderTypeListRecyclerAdapter(genders, this)

        binding.tableRecyclerView.adapter = genderAdapter

        genderAdapter.setOnItemClickListener(object : GenderTypeListRecyclerAdapter.OnItemClickListener {
            override fun onClick(view: View, data: OtherGenderType) {
                saveGenderOrientation(data)
                startWhoShouldSeeProfileActivity()
            }
        })
    }

    private fun onCancelButtonPressed() {
        val intent = IdentifyYourselfActivity.newIntent(this)
        this.startActivity(intent)
    }

    private fun startWhoShouldSeeProfileActivity() {
        if (!AppUtils.isNetworkConnected(application)) {
            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            val intent = WhoShouldSeeActivity.newIntent(this)
            this.startActivity(intent)
        }
    }

    private fun addGenders() {
        OtherGenderType.values().forEach {
            genders.add(it)
        }
    }

    private fun saveGenderOrientation(genderType: OtherGenderType) {
        AccountPreferences.getInstance(this).setValue(Constants.genderOrientation, genderType.toString())

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.genderOrientation, genderType.toString())

        AnalyticsTrackingService.setUserProperty(this, USER_ORIENTATION_PROPERTY, genderType.toString())
    }
}