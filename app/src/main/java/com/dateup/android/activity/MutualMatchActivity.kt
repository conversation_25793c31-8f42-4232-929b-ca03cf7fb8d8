package com.dateup.android.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.chat.dialog.ChatDialogListFragment
import com.dateup.android.chat.dialog.ChatDialogListFragment.Companion.CHAT_ID
import com.dateup.android.chat.dialog.ChatDialogListFragment.Companion.MAKE_A_MOVE
import com.dateup.android.chat.message.MessageListActivity
import com.dateup.android.databinding.ActivityHowHeightVerificationWorksBinding
import com.dateup.android.databinding.ActivityMutualMatchBinding
import com.dateup.android.extensions.launchModeWithSingleClearTop
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.firebase.FirebaseRetrieveSingleUserListenerInterfaceFirestore
import com.dateup.android.glide.ImageLoaderModule
import com.dateup.android.models.GenderType
import com.dateup.android.models.User
import com.dateup.android.paidVersion.likesYou.LikesYouActivity
import com.dateup.android.paidVersion.likesYou.LikesYouAdapter
import com.dateup.android.ui.browseProfiles.BrowseProfilesActivity
import com.dateup.android.utils.Constants
import com.dateup.android.utils.Constants.photoFileName1
import com.dateup.android.viewModels.SpacesImagesViewModel
import com.google.firebase.database.DataSnapshot
import timber.log.Timber
import java.io.Serializable
import java.util.*

class MutualMatchActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMutualMatchBinding

    private lateinit var mContext: Context
    private lateinit var otherUserProfilePhotoUrl: String

    private var otherUserfirebaseId: String? = null
    private var chatId: String? = CHAT_ID

    private lateinit var spacesImagesViewModel: SpacesImagesViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMutualMatchBinding.inflate(layoutInflater)
        setContentView(binding.root)

        spacesImagesViewModel = ViewModelProvider(this).get(SpacesImagesViewModel::class.java)

        if (intent.getStringExtra(Constants.firebaseUserId) != null) {

            otherUserfirebaseId = intent.getStringExtra(Constants.firebaseUserId)
        }

        if (intent.getStringExtra(Constants.conversationId) != null) {

            chatId = intent.getStringExtra(Constants.conversationId)
        }

        mContext = this

        getPhotoUrl()
    }

    fun init() {

        val fromOtherUserProfile = intent.getBooleanExtra(OtherUserProfileModal.EXTRA_MESSAGE_OTHER_PROFILE_MODAL, false)

        if (fromOtherUserProfile) {

            var likesYouUser: Serializable? = null

            if (intent.getSerializableExtra(LikesYouAdapter.EXTRA_LIKES_YOU_USER) != null) {

                likesYouUser = intent.getSerializableExtra(LikesYouAdapter.EXTRA_LIKES_YOU_USER)
            }

            binding.keepBrowsingButton.text = "Back to Likes"

            binding.keepBrowsingButton.setOnClickListener {

                val intent = Intent(mContext, LikesYouActivity::class.java).launchModeWithSingleClearTop()

                if (likesYouUser != null) {

                    intent.putExtra(OtherUserProfileModal.EXTRA_USER_FROM_OTHER_PROFILE_MODAL, likesYouUser)
                }

                startActivity(intent)
                finish()
            }
        } else {

            binding.keepBrowsingButton.setOnClickListener {

                val intent = Intent(mContext, BrowseProfilesActivity::class.java)
                startActivity(intent)
                finish()
            }
        }

        ImageLoaderModule.loadImageIntoImageViewWithSpacesPathCircleTransformAndLoading(this, spacesImagesViewModel, "${otherUserfirebaseId}/${photoFileName1}",binding.userProfileImageView)

        val firebaseDatabaseUtil = FirebaseDatabaseUtil(this)

        otherUserfirebaseId?.let {

            firebaseDatabaseUtil.readSingleUserInfoFromFirebaseFirestore(object :
                FirebaseRetrieveSingleUserListenerInterfaceFirestore {
                override fun onSuccess(user: User) {

                    when {
                        fromOtherUserProfile -> {

                            binding.likeTextView.text = "It's a match!"
                        }
                        user.gender == GenderType.man.toString() -> {

                            binding.likeTextView.text = "He likes you too!"
                        }
                        user.gender == GenderType.woman.toString() -> {

                            binding.likeTextView.text = "She likes you too!"
                        }
                        else -> {

                            binding.likeTextView.text = "It's a match!"
                        }
                    }

                    val user0Map = HashMap<String, String>()
                    val user1Map = HashMap<String, String>()

                    user0Map[Constants.name] = AccountPreferences.getInstance(mContext).getStringValue(Constants.name, "")
                    user0Map[Constants.avatar] = AccountPreferences.getInstance(mContext).getStringValue(Constants.userProfilePhotoUrl, "")
                    user0Map[Constants.firebaseUserId] = AccountPreferences.getInstance(mContext).getStringValue(Constants.firebaseUserId, "")

                    user1Map[Constants.name] = user.name.toString()
                    user1Map[Constants.avatar] = otherUserProfilePhotoUrl
                    user1Map[Constants.firebaseUserId] = it

                    binding.sendMessageButton.setOnClickListener {
                        val intent = Intent(mContext, MessageListActivity::class.java)
                        intent.putExtra(Constants.user0, user0Map)
                        intent.putExtra(Constants.user1, user1Map)
                        intent.putExtra(Constants.conversationId, chatId)
                        intent.putExtra(ChatDialogListFragment.EXTRA_LAST_MESSAGE, MAKE_A_MOVE)
                        startActivity(intent)
                        finish()
                    }
                }

                override fun onFailure() {
                    Timber.d("retrieving single user data in mutual match failed")
                }
            }, it)
        }

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun getPhotoUrl() {
        spacesImagesViewModel.getImageUrl( "$otherUserfirebaseId/${photoFileName1}") { url, _ ->
            runOnUiThread {
                otherUserProfilePhotoUrl = url?: ""
                init()
            }
        }
    }
}
