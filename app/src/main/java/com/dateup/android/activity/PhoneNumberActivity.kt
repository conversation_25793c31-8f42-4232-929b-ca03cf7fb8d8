package com.dateup.android.activity

import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.SpannableString
import android.view.MenuItem
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Constants
import com.dateup.android.utils.Utils.Companion.validate

class PhoneNumberActivity : AppCompatActivity() {

    companion object {

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, PhoneNumberActivity::class.java)
        }
    }

    private lateinit var weLlTextYouAcodTextView: TextView
    private lateinit var textViewTextView: TextView
    private lateinit var buttonLargeActiveButton: Button
    private lateinit var phoneNumberEditText: EditText

    private lateinit var toolbar: Toolbar
    private lateinit var skipTextView: TextView

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.phone_number_activity)
        this.init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        setupToolbar()

        // Configure We'll text you a cod component
        weLlTextYouAcodTextView = this.findViewById(R.id.we_ll_text_you_acod_text_view)
        val weLlTextYouAcodTextViewText = SpannableString(this.getString(R.string.phone_number_activity_we_ll_text_you_acod_text_view_text))
        weLlTextYouAcodTextView.text = weLlTextYouAcodTextViewText

        // Configure +1 component
        textViewTextView = this.findViewById(R.id.height_text_view)
        val textViewTextViewText = SpannableString(this.getString(R.string.phone_number_activity_text_view_text_view_text))
        textViewTextView.text = textViewTextViewText

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.isEnabled = false

        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }

        phoneNumberEditText = this.findViewById(R.id.phone_number_edit_text)
        phoneNumberEditText.requestFocus()

        phoneNumberEditText.validate("Please enter a valid 10 digit phone number.") { s ->
            isValidPhone(s)
        }
    }

    fun setupToolbar() {

        toolbar = findViewById(R.id.toolbar)

        skipTextView = this.findViewById(R.id.skip_text_view)
        skipTextView.visibility = View.GONE

        setSupportActionBar(toolbar)

        val actionBar = supportActionBar
        val drawable: Drawable? = ContextCompat.getDrawable(this, R.drawable.ic_arrow_left)

        actionBar?.setHomeAsUpIndicator(drawable)
        actionBar?.setDisplayShowTitleEnabled(true)
        actionBar?.setDisplayHomeAsUpEnabled(true)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {

        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                return true
            }
        }

        return super.onOptionsItemSelected(item)
    }

    fun onButtonLargeActivePressed() {

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            val phoneNumber = phoneNumberEditText.text

            val removeSpecialChars = Regex("[^\\d]")
            val unMaskedPhone = removeSpecialChars.replace(phoneNumber, "")

            if (unMaskedPhone.length == 10) {
                buttonLargeActiveButton.setBackgroundResource(R.drawable.phone_number_activity_button_large_active_button_selector)
            }

            val accountPreferences = AccountPreferences.getInstance(applicationContext)
            accountPreferences.setValue(Constants.phoneNumber, unMaskedPhone)
            startCodeVerificationActivity()
        }
    }

    private fun startCodeVerificationActivity() {

        this.startActivity(CodeVerificationActivity.newIntent(this))
    }

    private fun isValidPhone(input: String): Boolean {
        val removeSpecialChars = Regex("[^\\d]")
        val unMaskedPhone = removeSpecialChars.replace(input, "")

        return if (unMaskedPhone.length == 10) {
            buttonLargeActiveButton.setBackgroundResource(R.drawable.phone_number_activity_button_large_active_button_selector)
            buttonLargeActiveButton.isEnabled = true
            true
        } else {
            buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_disabled_state)
            buttonLargeActiveButton.isEnabled = false
            false
        }
    }
}
