package com.dateup.android.activity

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.View
import android.view.WindowManager
import android.widget.*
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.cloudVision.VisionImageModeration
import com.dateup.android.databinding.ActivityDateUpPledgeBinding
import com.dateup.android.databinding.ActivityEditProfileModalBinding
import com.dateup.android.databinding.AgeRestrictionActivityBinding
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.firebase.FirebaseRetrieveSingleUserListenerInterfaceFirestore
import com.dateup.android.glide.ImageLoaderModule
import com.dateup.android.models.User
import com.dateup.android.models.UserObject
import com.dateup.android.spaces.SpacesRepository
import com.dateup.android.subscriptions.AppExecutors
import com.dateup.android.ui.settings.SettingsActivity
import com.dateup.android.utils.*
import com.dateup.android.utils.Constants.photoFileName1
import com.dateup.android.utils.Constants.photoFileName2
import com.dateup.android.utils.Constants.photoFileName3
import com.dateup.android.utils.Constants.photoFileName4
import com.dateup.android.utils.Constants.photoFileName5
import com.dateup.android.utils.Constants.photoFileName6
import com.dateup.android.utils.Utils.Companion.getIceBreakerQuestions
import com.dateup.android.utils.Utils.Companion.showAlert
import com.dateup.android.viewModels.SpacesImagesViewModel
import com.yalantis.ucrop.UCrop
import pl.aprilapps.easyphotopicker.DefaultCallback
import pl.aprilapps.easyphotopicker.EasyImage
import pl.aprilapps.easyphotopicker.MediaFile
import pl.aprilapps.easyphotopicker.MediaSource
import timber.log.Timber
import java.io.ByteArrayOutputStream
import java.io.File
import java.util.*


class EditProfileModalActivity : AppCompatActivity() {

    private lateinit var binding: ActivityEditProfileModalBinding

    companion object {

        fun newIntent(context: Context?): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, EditProfileModalActivity::class.java)
        }
    }

    private lateinit var addAnotherTextView: TextView
    private lateinit var saveTextView: TextView
    private lateinit var cancelTextView: TextView
    private lateinit var hometownEditText: EditText
    private lateinit var professionEditText: EditText
    private lateinit var school2EditText: EditText
    private lateinit var school1EditText: EditText

    private lateinit var photoImageView0: ImageView
    private lateinit var photoImageView1: ImageView
    private lateinit var photoImageView2: ImageView
    private lateinit var photoImageView3: ImageView
    private lateinit var photoImageView4: ImageView
    private lateinit var photoImageView5: ImageView

    private lateinit var iconAddDelImageView0: ImageView
    private lateinit var iconAddDelImageView1: ImageView
    private lateinit var iconAddDelImageView2: ImageView
    private lateinit var iconAddDelImageView3: ImageView
    private lateinit var iconAddDelImageView4: ImageView
    private lateinit var iconAddDelImageView5: ImageView

    private lateinit var resultPhotoImageView: ImageView
    private lateinit var resultAddDelPhotoImageView: ImageView

    private lateinit var managePhotoPermission: ManagePermissions
    private lateinit var manageStoragePermission: ManagePermissions

    private val photoPermissionRequestCode = 3
    private val storagePermissionRequestCode = 5

    private var photoFileName = ""

    private val photoPermissionList = listOf(
            Manifest.permission.CAMERA
    )

    private val storagePermissionList = listOf(
            Manifest.permission.READ_EXTERNAL_STORAGE
    )

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private val storagePermissionListApi33 = listOf(
        Manifest.permission.READ_MEDIA_IMAGES
    )

    private lateinit var iceBreaker1TextView: TextView
    private lateinit var iceBreaker2TextView: TextView
    private lateinit var school2LineView: View
    private lateinit var homeTownConstraintLayout: ConstraintLayout
    private lateinit var editProfileProgressBar: ProgressBar

    private lateinit var mContext: Context
    private lateinit var mActivity: Activity
    private lateinit var mainUserId: String

    var iceBreaker1Question = ""
    var iceBreaker1Answer = ""
    var iceBreaker2Question = ""
    var iceBreaker2Answer = ""

    var firstPhotoRef = ""
    var secondPhotoRef = ""
    var thirdPhotoRef = ""
    var fourthPhotoRef = ""
    var fifthPhotoRef = ""
    var sixthPhotoRef = ""

    private lateinit var spacesImagesViewModel: SpacesImagesViewModel
    private lateinit var easyImage: EasyImage

    var originalImageFiles: Array<MediaFile>? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityEditProfileModalBinding.inflate(layoutInflater)
        setContentView(binding.root)

        spacesImagesViewModel = ViewModelProvider(this).get(SpacesImagesViewModel::class.java)

        mActivity = this

        easyImage = EasyImage.Builder(this)
                .setCopyImagesToPublicGalleryFolder(false)
                .setFolderName("dateup-photos")
                .allowMultiple(false)
                .build()

        init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    fun init() {

        mContext = this

        addAnotherTextView = this.findViewById(R.id.add_another_text_view)
        school2EditText = this.findViewById(R.id.school2_edit_text)
        school2LineView = this.findViewById(R.id.rectangle_two_constraint_layout_school2)
        homeTownConstraintLayout = this.findViewById(R.id.field_hometown_constraint_layout)
        saveTextView = this.findViewById(R.id.save_text_view)
        cancelTextView = this.findViewById(R.id.cancel_text_view)
        iceBreaker1TextView = this.findViewById(R.id.ice_breaker_1)
        iceBreaker2TextView = this.findViewById(R.id.ice_breaker_2)
        professionEditText = this.findViewById(R.id.profession_edit_text)
        hometownEditText = this.findViewById(R.id.hometown_edit_text)
        school1EditText = this.findViewById(R.id.education_edit_text)

        photoImageView0 = this.findViewById(R.id.icon_camera_image_view0)
        photoImageView1 = this.findViewById(R.id.icon_camera_image_view1)
        photoImageView2 = this.findViewById(R.id.icon_camera_image_view2)
        photoImageView3 = this.findViewById(R.id.icon_camera_image_view3)
        photoImageView4 = this.findViewById(R.id.icon_camera_image_view4)
        photoImageView5 = this.findViewById(R.id.icon_camera_image_view5)

        iconAddDelImageView0 = this.findViewById(R.id.icon_plus_addphoto_image_view0)
        iconAddDelImageView1 = this.findViewById(R.id.icon_plus_addphoto_image_view1)
        iconAddDelImageView2 = this.findViewById(R.id.icon_plus_addphoto_image_view2)
        iconAddDelImageView3 = this.findViewById(R.id.icon_plus_addphoto_image_view3)
        iconAddDelImageView4 = this.findViewById(R.id.icon_plus_addphoto_image_view4)
        iconAddDelImageView5 = this.findViewById(R.id.icon_plus_addphoto_image_view5)

        editProfileProgressBar = this.findViewById(R.id.edit_profile_progress_bar)

        setInitialData()
    }

    private fun setListeners() {

        binding.rowQuestionCopy2ConstraintLayout.setOnClickListener {
            val intent = Intent(this, SettingsIceBreakerTitleActivity::class.java)
            intent.putExtra("answer", iceBreaker1Answer)
            intent.putExtra("question", iceBreaker1Question)
            intent.putExtra("icebreaker", Constants.iceBreaker1)
            startActivity(intent)
        }

        binding.rowQuestionCopy3ConstraintLayout.setOnClickListener {
            val intent = Intent(this, SettingsIceBreakerTitleActivity::class.java)
            intent.putExtra("answer", iceBreaker2Answer)
            intent.putExtra("question", iceBreaker2Question)
            intent.putExtra("icebreaker", Constants.iceBreaker2)
            startActivity(intent)
        }

        addAnotherTextView.setOnClickListener {
            hideAddAnotherTextView()
        }

        cancelTextView.setOnClickListener {
            val intent = Intent(this, SettingsActivity::class.java)
            intent.putExtra("EditProfileModalActivity", true)
            this.startActivity(intent)
        }

        saveTextView.setOnClickListener {

            savePreferences()
        }

        photoImageView0.setOnClickListener { view ->
            resultPhotoImageView = photoImageView0
            resultAddDelPhotoImageView = iconAddDelImageView0
            photoFileName = photoFileName1
            this.showPictureDialog()
        }
        photoImageView1.setOnClickListener { view ->
            resultPhotoImageView = photoImageView1
            resultAddDelPhotoImageView = iconAddDelImageView1
            photoFileName = photoFileName2
            this.showPictureDialog()
        }
        photoImageView2.setOnClickListener { view ->
            resultPhotoImageView = photoImageView2
            resultAddDelPhotoImageView = iconAddDelImageView2
            photoFileName = photoFileName3
            this.showPictureDialog()
        }
        photoImageView3.setOnClickListener { view ->
            resultPhotoImageView = photoImageView3
            resultAddDelPhotoImageView = iconAddDelImageView3
            photoFileName = photoFileName4
            this.showPictureDialog()
        }
        photoImageView4.setOnClickListener { view ->
            resultPhotoImageView = photoImageView4
            resultAddDelPhotoImageView = iconAddDelImageView4
            photoFileName = photoFileName5
            this.showPictureDialog()
        }
        photoImageView5.setOnClickListener { view ->
            resultPhotoImageView = photoImageView5
            resultAddDelPhotoImageView = iconAddDelImageView5
            photoFileName = photoFileName6
            this.showPictureDialog()
        }

        iconAddDelImageView0.setOnClickListener { view ->
            resultPhotoImageView = photoImageView0
            resultAddDelPhotoImageView = iconAddDelImageView0
            photoFileName = photoFileName1
            this.showPictureDialog()
        }
        iconAddDelImageView1.setOnClickListener { view ->
            resultPhotoImageView = photoImageView1
            resultAddDelPhotoImageView = iconAddDelImageView1
            photoFileName = photoFileName2
            this.showPictureDialog()
        }
        iconAddDelImageView2.setOnClickListener { view ->
            if (photoImageView2.drawable?.constantState?.equals(ContextCompat.getDrawable(this, R.drawable.icon_camera)?.constantState) == true) {
                resultPhotoImageView = photoImageView2
                resultAddDelPhotoImageView = iconAddDelImageView2
                photoFileName = photoFileName3
                this.showPictureDialog()
            } else {
                this.clearPhoto(iconAddDelImageView2, photoImageView2, thirdPhotoRef)
            }
        }
        iconAddDelImageView3.setOnClickListener { view ->
            if (photoImageView3.drawable?.constantState?.equals(ContextCompat.getDrawable(this, R.drawable.icon_camera)?.constantState) == true) {
                resultPhotoImageView = photoImageView3
                resultAddDelPhotoImageView = iconAddDelImageView3
                photoFileName = photoFileName4
                this.showPictureDialog()
            } else {
                this.clearPhoto(iconAddDelImageView3, photoImageView3, fourthPhotoRef)
            }
        }
        iconAddDelImageView4.setOnClickListener { view ->
            if (photoImageView4.drawable?.constantState?.equals(ContextCompat.getDrawable(this, R.drawable.icon_camera)?.constantState) == true) {
                resultPhotoImageView = photoImageView4
                resultAddDelPhotoImageView = iconAddDelImageView4
                photoFileName = photoFileName5
                this.showPictureDialog()
            } else {
                this.clearPhoto(iconAddDelImageView4, photoImageView4, fifthPhotoRef)
            }
        }
        iconAddDelImageView5.setOnClickListener { view ->
            if (photoImageView5.drawable?.constantState?.equals(ContextCompat.getDrawable(this, R.drawable.icon_camera)?.constantState) == true) {
                resultPhotoImageView = photoImageView5
                resultAddDelPhotoImageView = iconAddDelImageView5
                photoFileName = photoFileName6
                this.showPictureDialog()
            } else {
                this.clearPhoto(iconAddDelImageView5, photoImageView5, sixthPhotoRef)
            }
        }

        managePhotoPermission = ManagePermissions(this, photoPermissionList, photoPermissionRequestCode)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            manageStoragePermission = ManagePermissions(this, storagePermissionListApi33, storagePermissionRequestCode)
        } else {
            manageStoragePermission = ManagePermissions(this, storagePermissionList, storagePermissionRequestCode)
        }
    }

    fun hideAddAnotherTextView() {
        addAnotherTextView.visibility = View.GONE
        school2EditText.visibility = View.VISIBLE
        school2LineView.visibility = View.VISIBLE

        val cl: ConstraintLayout.LayoutParams? = homeTownConstraintLayout.layoutParams as? ConstraintLayout.LayoutParams
        cl?.topMargin = 185
        homeTownConstraintLayout.layoutParams = cl
    }

    private fun setInitialData() {
        mainUserId = AccountPreferences.getInstance(mContext).getStringValue(Constants.firebaseUserId, "")
        if (UserObject.shouldFetchUserFromServer()) {
            val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
            firebaseDatabaseReference.readMainUserInfoFromFirebaseFirestore(object :
                FirebaseRetrieveSingleUserListenerInterfaceFirestore {
                override fun onSuccess(user: User) {
                    setInitialUserData(user)
                    UserObject.setUserDataStore(user, mainUserId)
                }

                override fun onFailure() {
                    Timber.d("retrieving user data in edit profile modal activity failed")
                }
            })
        }else {
            setInitialUserData(UserObject.user)
        }
    }

    private fun setInitialUserData(user: User?) {
        firstPhotoRef = "$mainUserId/$photoFileName1"
        secondPhotoRef = "$mainUserId/$photoFileName2"
        thirdPhotoRef = "$mainUserId/$photoFileName3"
        fourthPhotoRef = "$mainUserId/$photoFileName4"
        fifthPhotoRef = "$mainUserId/$photoFileName5"
        sixthPhotoRef = "$mainUserId/$photoFileName6"

        AccountPreferences.getInstance(applicationContext).setValue(Constants.name, user?.name.toString())

        ImageLoaderModule.loadImageIntoImageViewWithCallback(mActivity, spacesImagesViewModel, firstPhotoRef, photoImageView0, R.drawable.icon_camera,
                shouldCacheImage = true, onSuccess = {}, onFailure = {})

        ImageLoaderModule.loadImageIntoImageViewWithCallback(mActivity, spacesImagesViewModel, secondPhotoRef, photoImageView1, R.drawable.icon_camera,
                shouldCacheImage = true, onSuccess = {}, onFailure = {})


        ImageLoaderModule.loadImageIntoImageViewWithCallback(mActivity, spacesImagesViewModel, thirdPhotoRef, photoImageView2, R.drawable.icon_camera,
                shouldCacheImage = true, onSuccess = {
            replaceDelImage(iconAddDelImageView2)
        }, onFailure = {
            replaceAddImage(iconAddDelImageView2)
        })

        ImageLoaderModule.loadImageIntoImageViewWithCallback(mActivity, spacesImagesViewModel, fourthPhotoRef, photoImageView3, R.drawable.icon_camera,
                shouldCacheImage = true, onSuccess = {
            replaceDelImage(iconAddDelImageView3)
        }, onFailure = {
            replaceAddImage(iconAddDelImageView3)
        })

        ImageLoaderModule.loadImageIntoImageViewWithCallback(mActivity, spacesImagesViewModel, fifthPhotoRef, photoImageView4, R.drawable.icon_camera,
                shouldCacheImage = true, onSuccess = {
            replaceDelImage(iconAddDelImageView4)
        }, onFailure = {
            replaceAddImage(iconAddDelImageView4)
        })

        ImageLoaderModule.loadImageIntoImageViewWithCallback(mActivity, spacesImagesViewModel, sixthPhotoRef, photoImageView5, R.drawable.icon_camera,
                shouldCacheImage = true, onSuccess = {
            replaceDelImage(iconAddDelImageView5)
        }, onFailure = {
            replaceAddImage(iconAddDelImageView5)
        })

        professionEditText.setText(user?.profession.toString())
        school1EditText.setText(user?.school.toString())

        if (!TextUtils.isEmpty(user?.school2.toString())) {
            hideAddAnotherTextView()
            school2EditText.setText(user?.school2.toString())
        }

        hometownEditText.setText(user?.homeTown.toString())

        val iceBreaker1Questions = user?.iceBreaker1 as? HashMap<*, *>
        val iceBreaker2Questions = user?.iceBreaker2 as? HashMap<*, *>

        if (iceBreaker1Questions != null) {

            for (key in iceBreaker1Questions.keys) {

                iceBreaker1Question = key.toString()
            }

            iceBreaker1Answer = iceBreaker1Questions[iceBreaker1Question].toString()
        }

        if (iceBreaker2Questions != null) {

            for (key2 in iceBreaker2Questions.keys) {

                iceBreaker2Question = key2.toString()
            }

            iceBreaker2Answer = iceBreaker2Questions[iceBreaker2Question].toString()
        }

        if (iceBreaker1Question.isEmpty()) {

            iceBreaker1Question = getIceBreakerQuestions().keys.first()
        }

        if (iceBreaker2Question.isEmpty()) {

            iceBreaker2Question = getIceBreakerQuestions().keys.last()
        }

        iceBreaker1TextView.text = iceBreaker1Question
        iceBreaker2TextView.text = iceBreaker2Question

        setListeners()

        replaceDefaultAddDelImage()
    }

    private fun savePreferences() {

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.profession, professionEditText.text.toString())
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.school, school1EditText.text.toString())
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.school2, school2EditText.text.toString())
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.homeTown, hometownEditText.text.toString())

        AccountPreferences.getInstance(applicationContext).setValue(Constants.profession, professionEditText.text.toString())
        AccountPreferences.getInstance(applicationContext).setValue(Constants.school, school1EditText.text.toString())
        AccountPreferences.getInstance(applicationContext).setValue(Constants.school2, school2EditText.text.toString())
        AccountPreferences.getInstance(applicationContext).setValue(Constants.homeTown, hometownEditText.text.toString())

        firebaseDatabaseReference.saveTimeStamp(Constants.modifiedOn)

        showProgressBar()
        Handler(Looper.getMainLooper()).postDelayed({
            hideprogressBar()
            val intent = Intent(this, SettingsActivity::class.java)
            intent.putExtra("EditProfileModalActivity", true)
            this.startActivity(intent)
        }, 1000)
    }

    private fun resetImageViewThumbnail(addDeleteImageView: ImageView, photoImageView: ImageView) {
        if (::resultAddDelPhotoImageView.isInitialized && ::resultPhotoImageView.isInitialized) {
            photoImageView.setImageResource(R.drawable.icon_camera)
            addDeleteImageView.setImageResource(R.drawable.icon_plus_addphoto)
            addDeleteImageView.elevation = 2F
        }
    }

    private fun clearPhoto(addDeleteImageView: ImageView, photoImageView: ImageView, imagePath: String) {
        showProgressBar()
        spacesImagesViewModel.deleteImage(imagePath) { spacesResult ->
            runOnUiThread {
                hideprogressBar()
                if (spacesResult == SpacesRepository.SpacesStatus.FAILED) {
                    showAlert("Unable to delete the picture. Please try again", this)
                } else {
                    resetImageViewThumbnail(addDeleteImageView, photoImageView)
                }
            }
        }
    }

    private fun showPictureDialog() {
        val pictureDialog = AlertDialog.Builder(this)
        pictureDialog.setTitle("Select Action")
        val pictureDialogItems = arrayOf("Select photo from gallery", "Capture photo from camera")
        pictureDialog.setItems(pictureDialogItems
        ) { dialog, which ->
            when (which) {
                0 -> askStoragePermission()
                1 -> askPhotoPermission()
            }
        }
        pictureDialog.show()
    }

    private fun askStoragePermission() {
        if (manageStoragePermission.isPermissionGranted()) {
            easyImage.openGallery(this)
        } else {
            manageStoragePermission.checkPermissions()
        }
    }

    private fun askPhotoPermission() {
        if (managePhotoPermission.isPermissionGranted()) {
            easyImage.openCameraForImage(this)
        } else {
            managePhotoPermission.checkPermissions()
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>,
                                            grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            photoPermissionRequestCode -> {
                val isPermissionsGranted = managePhotoPermission.processPermissionsResult(requestCode, permissions, grantResults)
                if (isPermissionsGranted) {
                    easyImage.openCameraForImage(this)
                } else {
                    Utils.showPermissionAlert("To continue, give DateUp access to your photos", this)
                }
                return
            }

            storagePermissionRequestCode -> {
                val isPermissionsGranted = manageStoragePermission.processPermissionsResult(requestCode, permissions, grantResults)

                if (isPermissionsGranted) {
                    easyImage.openGallery(this)
                } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    Utils.showPermissionAlert("To continue, give DateUp access to your Photos and videos", this)
                } else {
                    Utils.showPermissionAlert("To continue, give DateUp access to your files and media", this)
                }
                return
            }
        }
    }

    public override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if(resultCode != RESULT_CANCELED){
            if (resultCode == RESULT_OK && requestCode == UCrop.REQUEST_CROP && data != null) {
                val selectedImageCroppedUri = UCrop.getOutput(data)
                selectedImageCroppedUri?.path?.let {
                    selectedImageToUpload(File(it))
                }
            } else if (resultCode == UCrop.RESULT_ERROR && data != null) {
                uploadOriginalPhotoToFirebase(data)
            }

            easyImage.handleActivityResult(requestCode, resultCode, data, this, object : DefaultCallback() {
                override fun onMediaFilesPicked(imageFiles: Array<MediaFile>, source: MediaSource) {
                    startCropScreen(imageFiles)
                    originalImageFiles = imageFiles
                }

                override fun onImagePickerError(error: Throwable, source: MediaSource) {
                    Timber.e("Exception in picking image ${error.printStackTrace()}")
                }

                override fun onCanceled(source: MediaSource) {
                    //Not necessary to remove any files manually anymore
                }
            })
        }
    }

    private fun uploadOriginalPhotoToFirebase(data: Intent) {
        if (originalImageFiles != null && originalImageFiles is Array<MediaFile>) {
            Toast.makeText(this, "Unable to crop, Using original picture", Toast.LENGTH_LONG).show()
            replaceAddDelImage()
            ImageLoaderModule.loadImageIntoImageViewWithFile(this@EditProfileModalActivity, (originalImageFiles as Array<MediaFile>)[0].file, resultPhotoImageView)
            uploadBitmapToFirebase(getBitmapFromFile((originalImageFiles as Array<MediaFile>)[0]))
        }else {
            Timber.e("Exception while cropping image ${UCrop.getError(data)}")
            Toast.makeText(this, "Unable to crop the picture", Toast.LENGTH_LONG).show()
        }
        originalImageFiles = null
    }

    private fun replaceAddDelImage() {
        if (::resultAddDelPhotoImageView.isInitialized) {
            if (resultAddDelPhotoImageView == iconAddDelImageView0 || resultAddDelPhotoImageView == iconAddDelImageView1) {
                resultAddDelPhotoImageView.setImageResource(R.drawable.icon_plus_addphoto)
                resultAddDelPhotoImageView.elevation = 2F
            } else {
                resultAddDelPhotoImageView.setImageResource(R.drawable.icon_del_photo)
                resultAddDelPhotoImageView.elevation = 2F
            }
        }
    }

    fun replaceAddImage(imageView: ImageView) {
        imageView.setImageResource(R.drawable.icon_plus_addphoto)
        imageView.elevation = 2F
    }

    fun replaceDelImage(imageView: ImageView) {
        imageView.setImageResource(R.drawable.icon_del_photo)
        imageView.elevation = 2F
    }

    fun replaceDefaultAddDelImage() {
        iconAddDelImageView0.setImageResource(R.drawable.icon_del_photo)
        iconAddDelImageView0.elevation = 2F

        iconAddDelImageView1.setImageResource(R.drawable.icon_del_photo)
        iconAddDelImageView1.elevation = 2F
    }

    private fun getBitmapFromFile(mediaFile: MediaFile): Bitmap? {
        return PhotoUtils.handleImageOrientation(this, mediaFile.file.path, null)
    }

    private fun uploadBitmapToFirebase(bitmap: Bitmap?) {

        showProgressBar()

        val firebaseUserId = AccountPreferences.getInstance(applicationContext).getStringValue(Constants.firebaseUserId, "")
        if (!TextUtils.isEmpty(firebaseUserId)) {
            val executor = AppExecutors()
            executor.networkIO.execute {
                val baos = ByteArrayOutputStream()
                bitmap?.compress(Bitmap.CompressFormat.JPEG, 100, baos)
                val data = baos.toByteArray()

                VisionImageModeration.checkImageForModerationNode(mainUserId, data) { isPhotoViolated ->
                    if (isPhotoViolated) {
                        resetImageViewThumbnail(resultAddDelPhotoImageView, resultPhotoImageView)
                        executor.mainThread.execute {
                            AlertDialogView.showAlertDialogWithLink(context = this,
                                title = getString(R.string.image_moderation_title),
                                message = "We detected that this photo may not meet our <a href=\"https://www.dateup.co/photo-guidelines\">photo guidelines</a>. You must be present in all photos and explicit content is not allowed, which are common reasons why photos get flagged.",
                                buttonPositiveText = getString(R.string.image_moderation_close),
                                buttonNegativeText = null) { dialog, _ ->
                                dialog.cancel()
                            }
                            hideprogressBar()
                        }
                    } else {
                        bitmap?.let {
                            spacesImagesViewModel.uploadBitmap(this, firebaseUserId, photoFileName, it) {
                                executor.mainThread.execute {
                                    hideprogressBar()
                                }
                            }
                        }
                    }
                }
            }
        } else {
            hideprogressBar()
        }
    }

    private fun startCropScreen(imageFiles: Array<MediaFile>) {
        val imageUri = Uri.fromFile(File((imageFiles[0].file.path)))
        val destinationFileName: String = imageFiles[0].file.name

        val options = UCrop.Options()
        options.setHideBottomControls(true)

        val ucrop = UCrop.of(imageUri, Uri.fromFile(File(cacheDir, destinationFileName)))
            .withAspectRatio(1f, 1f)
            .withOptions(options)
        ucrop.start(this)
    }

    private fun selectedImageToUpload(imageFile: File) {
        replaceAddDelImage()
        ImageLoaderModule.loadImageIntoImageViewWithFile(this@EditProfileModalActivity, imageFile, resultPhotoImageView)
        uploadBitmapToFirebase(getBitmapFromFile(imageFile))
    }

    private fun getBitmapFromFile(file: File): Bitmap? {
        return PhotoUtils.handleImageOrientation(this, file.path, null)
    }

    private fun showProgressBar() {
        editProfileProgressBar.visibility = View.VISIBLE
        window.setFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
    }

    private fun hideprogressBar() {
        editProfileProgressBar.visibility = View.GONE
        window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
    }
}
