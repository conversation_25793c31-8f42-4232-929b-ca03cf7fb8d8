package com.dateup.android.activity

import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.view.MenuItem
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import com.dateup.android.AccountPreferences
import com.dateup.android.BuildConfig
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.activity.Intro1Activity.Companion.IS_USER_AGE_RESTRICTED
import com.dateup.android.activity.Intro1Activity.Companion.IS_USER_REPORTED
import com.dateup.android.activity.Intro1Activity.Companion.REPORTED_USER_MESSAGE
import com.dateup.android.activity.Intro1Activity.Companion.REPORTED_USER_TITLE
import com.dateup.android.activity.Intro1Activity.Companion.SHOULD_DELETE_USER
import com.dateup.android.analytics.*
import com.dateup.android.cloudFunctions.AppToCloudLogger
import com.dateup.android.databinding.CodeVerificationActivityBinding
import com.dateup.android.extensions.launchModeWithNoBackStack
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.firebase.FirebaseRetrieveSingleUserRawDataListenerInterfaceFirestore
import com.dateup.android.models.User
import com.dateup.android.models.UserObject
import com.dateup.android.models.UserObject.setAccountPreferences
import com.dateup.android.services.BackgroundLocationIntentService
import com.dateup.android.ui.browseProfiles.BrowseProfilesActivity
import com.dateup.android.utils.*
import com.dateup.android.utils.Utils.Companion.afterTextChanged
import com.dateup.android.utils.Utils.Companion.isValidUser
import com.google.firebase.FirebaseException
import com.google.firebase.FirebaseTooManyRequestsException
import com.google.firebase.auth.*
import com.google.firebase.database.DatabaseReference
import com.google.firebase.database.FirebaseDatabase
import com.google.firebase.firestore.DocumentSnapshot
import timber.log.Timber
import java.util.*
import java.util.concurrent.TimeUnit

class CodeVerificationActivity : AppCompatActivity() {

    private lateinit var binding: CodeVerificationActivityBinding

    companion object {

        const val DELETE_ACTION = "DELETE_ACTION"

        const val OTP_INFO = "[OTP_INFO]"
        const val OTP_ERROR = "[OTP_ERROR]"

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, CodeVerificationActivity::class.java)
        }

        fun newIntentWithDeleteAction(context: Context): Intent {

            val intent = Intent(context, CodeVerificationActivity::class.java)
            intent.putExtra(DELETE_ACTION, true)
            return intent
        }
    }

    private lateinit var iDidnTgetAcodeTextView: TextView
    private lateinit var enter6DigitCodeTextView: TextView
    private lateinit var sentToTextView: TextView
    private lateinit var buttonLargeActiveButton: Button

    private lateinit var otpEditText1: EditText

    private lateinit var mCallbacks: PhoneAuthProvider.OnVerificationStateChangedCallbacks
    private val keyVerifyInProgress: String = "key_verify_in_progress"
    private var mVerificationInProgress: Boolean = false
    var resendToken: PhoneAuthProvider.ForceResendingToken? = null
    private lateinit var firebaseUserId: String
    private lateinit var phoneNumber: String
    private var otpVerificationCode: String? = null
    private var otpCode: String = ""

    private lateinit var toolbar: Toolbar
    private lateinit var skipTextView: TextView

    private var isUserReAuthenticatingForDeletion: Boolean = false

    private lateinit var database: DatabaseReference

    private lateinit var context: Context

    private var externalId = ""

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        binding = CodeVerificationActivityBinding.inflate(layoutInflater)
        setContentView(binding.root)

        context = this

        this.init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        phoneNumber = "+1" + AccountPreferences.getInstance(applicationContext).getStringValue(Constants.phoneNumber, "").toString()

        initAuthCallback()

        getOtp(phoneNumber = phoneNumber)

        iDidnTgetAcodeTextView = this.findViewById(R.id.i_didn_tget_acode_text_view)
        enter6DigitCodeTextView = this.findViewById(R.id.enter6_digit_code_text_view)
        sentToTextView = this.findViewById(R.id.sent_to_text_view)

        // Configure Sent to: component
        val sentToTextViewText = SpannableString(this.getString(R.string.code_verification_activity_sent_to_text_view_text) + phoneNumber)
        sentToTextViewText.setSpan(FontSpan(ResourcesCompat.getFont(this, R.font.font_nunitosans_bold)), 0, 8, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        sentToTextView.text = sentToTextViewText

        // Configure I didn’t get a code component
        val iDidnTgetAcodeTextViewText = SpannableString(this.getString(R.string.code_verification_activity_i_didn_tget_acode_text_view_text))
        iDidnTgetAcodeTextView.text = iDidnTgetAcodeTextViewText

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.isEnabled = false

        buttonLargeActiveButton.setOnClickListener { view ->

            this.onButtonLargeActivePressed()
        }

        //configure otp edit texts
        otpEditText1 = this.findViewById(R.id.edit_text_edit_text)
        otpEditText1.requestFocus()


        //Configure firebase
        database = FirebaseDatabase.getInstance().reference

        iDidnTgetAcodeTextView.setOnClickListener { view ->

            logOTPFlow(AppToCloudLogger.LogLevel.LEVEL_WARN,
                "$OTP_INFO Clicked on I didn't receive code",phoneNumber, null)

            val bundle = Bundle()
            bundle.putString(STATUS, ANALYTICS_FAILURE)
            AnalyticsTrackingService.logEvent(context, OTP_DIDNT_RECEIVE_CODE, bundle)

            resendToken?.let {
                this.resendVerificationCode(phoneNumber, it)
            }
        }

        isUserReAuthenticatingForDeletion = intent.getBooleanExtra(DELETE_ACTION, false)

        otpEditText1.afterTextChanged {

            makeCodeVerificationCall()
        }

        setupToolbar()

        if (isUserReAuthenticatingForDeletion) {

            // Configure Enter 6-Digit Code component
            val enter6DigitCodeTextViewText = SpannableString(this.getString(R.string.delete_action_desc))
            val typeFace = ResourcesCompat.getFont(this, R.font.font_nunitosans_regular)
            enter6DigitCodeTextView.typeface = typeFace
            enter6DigitCodeTextView.textSize = 16f
            enter6DigitCodeTextView.setTextColor(getColor(R.color.grey1))
            enter6DigitCodeTextView.text = enter6DigitCodeTextViewText
            buttonLargeActiveButton.text = getString(R.string.delete_action_button_text)
        } else {

            // Configure Enter 6-Digit Code component
            val enter6DigitCodeTextViewText = SpannableString(this.getString(R.string.code_verification_activity_enter6_digit_code_text_view_text))
            enter6DigitCodeTextView.text = enter6DigitCodeTextViewText
            buttonLargeActiveButton.text = getString(R.string.code_verification_activity_button_large_active_button_text)
        }
    }

    private fun isOtpCodeValid(): Boolean {

        if (otpCode.isNotEmpty() && otpCode.length == 6) {
            return true
        }
        return false
    }

    private fun makeCodeVerificationCall() {

        otpCode = otpEditText1.text.toString()

        if (otpVerificationCode != null && isOtpCodeValid()) {

            buttonLargeActiveButton.setBackgroundResource(R.drawable.phone_number_activity_button_large_active_button_selector)
            buttonLargeActiveButton.isEnabled = true

            val credential = PhoneAuthProvider.getCredential(otpVerificationCode.toString(), otpCode)

            if (!isUserReAuthenticatingForDeletion) {

                binding.otpProgressBar.visibility = View.VISIBLE

                signInWithPhoneAuthCredential(credential)
            }
        } else {
            buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_disabled_state)
            buttonLargeActiveButton.isEnabled = false
        }
    }

    fun setupToolbar() {

        toolbar = findViewById(R.id.toolbar)

        skipTextView = this.findViewById(R.id.skip_text_view)
        skipTextView.visibility = View.GONE

        setSupportActionBar(toolbar)

        val actionBar = supportActionBar
        val drawable: Drawable? = ContextCompat.getDrawable(this, R.drawable.ic_arrow_left)

        actionBar?.setHomeAsUpIndicator(drawable)
        actionBar?.setDisplayShowTitleEnabled(true)
        actionBar?.setDisplayHomeAsUpEnabled(true)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {

        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                return true
            }
        }

        return super.onOptionsItemSelected(item)
    }

    fun onButtonLargeActivePressed() {

        if (isUserReAuthenticatingForDeletion) {

            binding.otpProgressBar.visibility = View.VISIBLE
            val credential = PhoneAuthProvider.getCredential(otpVerificationCode.toString(), otpCode)
            reAuthenticateUserForDeletion(credential)
        } else {

            makeCodeVerificationCall()
        }
    }

    private fun startDobActivity() {

        this.startActivity(DateOfBirthActivity.newIntent(this))
    }

    private fun initAuthCallback() {

        mCallbacks = object : PhoneAuthProvider.OnVerificationStateChangedCallbacks() {
            override fun onVerificationCompleted(credential: PhoneAuthCredential) {

                // verification completed
                Timber.d("onVerificationCompleted:$credential")
                logOTPFlow(AppToCloudLogger.LogLevel.LEVEL_INFO,
                    "$OTP_INFO OTP verification success",null, null)

                if (isUserReAuthenticatingForDeletion) {

                    reAuthenticateUserForDeletion(credential)
                } else {

                    signInWithPhoneAuthCredential(credential)
                }


                val bundle = Bundle()
                bundle.putString(STATUS, ANALYTICS_SUCCESS)
                AnalyticsTrackingService.logEvent(context, OTP_VERIFICATION, bundle)
            }

            override fun onVerificationFailed(e: FirebaseException) {
                // This callback is invoked if an invalid request for verification is made,
                // for instance if the the phone number format is invalid.
                Timber.e("OTPCodeVerification on Code Verification Failed: $e")

                logOTPFlow(AppToCloudLogger.LogLevel.LEVEL_ERROR,
                    "$OTP_ERROR ${e.message}",phoneNumber, e.stackTraceToString())

                binding.otpProgressBar.visibility = View.GONE

                //Fire an event to developers that otp code request for this phone number failed. This block executes
                //when a user submits his phone number to get an otp code but failed to get that otp code
                //fire an event with the error message

                val bundle = Bundle()

                when (e) {
                    is FirebaseAuthInvalidCredentialsException -> {
                        // Invalid request
                        Timber.d("Invalid phone number: $e")
                        bundle.putString(ERROR_CODE, e.errorCode)
                        Utils.showAlert("The phone number you entered is invalid. Please check and try again", context)
                    }
                    is FirebaseTooManyRequestsException -> {
                        // The SMS quota for the project has been exceeded
                        Timber.e("Quota exceeded: $e")
                        Utils.showAlert("You have exceeded the number of sign in attempts, Please try again after some time", context)
                    }
                    else -> {
                        Timber.e("Unable to process the request: $e")
                        Utils.showAlert("Unable to process the request. Please try again", context)
                    }
                }

                bundle.putString(STATUS, ANALYTICS_FAILURE)
                e.let {
                    bundle.putString(ERROR_REASON, it.message)
                }
                AnalyticsTrackingService.logEvent(context, OTP_VERIFICATION, bundle)
            }

            override fun onCodeSent(verificationId: String, token: PhoneAuthProvider.ForceResendingToken) {
                // The SMS verification code has been sent to the provided phone number, we
                // now need to ask the user to enter the code and then construct a credential
                // by combining the code with a verification ID.
                // Save verification ID and resending token so we can use them later
                otpVerificationCode = verificationId
                resendToken = token

                binding.otpProgressBar.visibility = View.GONE
            }

            override fun onCodeAutoRetrievalTimeOut(verificationId: String) {
                // called when the timeout duration has passed without triggering onVerificationCompleted
                super.onCodeAutoRetrievalTimeOut(verificationId)
                //show the otp ui here
                Timber.d("onCodeAutoRetrievalTimeOut")
                logOTPFlow(AppToCloudLogger.LogLevel.LEVEL_ERROR, "$OTP_ERROR OTP Timed out, null)", phoneNumber)

                Utils.showAlert("Unable to process the request. Please try again", context)
                binding.otpProgressBar.visibility = View.GONE

                val bundle = Bundle()
                bundle.putString(STATUS, ANALYTICS_FAILURE)
                bundle.putString(ERROR_REASON, "code auto retrieval time out")
                AnalyticsTrackingService.logEvent(context, OTP_VERIFICATION, bundle)
            }
        }
    }

    private fun getOtp(phoneNumber: String) {
        val firebaseAuth = FirebaseAuth.getInstance()
        if (BuildConfig.DEBUG) {
            val firebaseAuthSettings = firebaseAuth.firebaseAuthSettings
            firebaseAuthSettings.setAppVerificationDisabledForTesting(true)
        }

        val phoneAuthOptions = PhoneAuthOptions.newBuilder(firebaseAuth)
                .setPhoneNumber(phoneNumber)       // Phone number to verify
                .setTimeout(60L, TimeUnit.SECONDS) // Timeout and unit
                .setActivity(this)                 // Activity (for callback binding)
                .setCallbacks(mCallbacks)          // OnVerificationStateChangedCallbacks
                .build()

        PhoneAuthProvider.verifyPhoneNumber(phoneAuthOptions)

        logOTPFlow(AppToCloudLogger.LogLevel.LEVEL_INFO,
            "$OTP_INFO OTP requested", null, null)
    }

    private fun signInWithPhoneAuthCredential(credential: PhoneAuthCredential) {
        FirebaseAuth.getInstance().signInWithCredential(credential)
                .addOnCompleteListener(this) { task ->

                    binding.otpProgressBar.visibility = View.GONE

                    if (task.isSuccessful) {
                        // Sign in success, update UI with the signed-in user's information
                        Timber.d("signInWithCredential:success")

                        firebaseUserId = task.result?.user?.uid ?: ""

                        AnalyticsTrackingService.setUserId(this, firebaseUserId)

                        AccountPreferences.getInstance(applicationContext).setValue(Constants.firebaseUserId, firebaseUserId)

                        checkOnboardingStatus()

                        logOTPFlow(AppToCloudLogger.LogLevel.LEVEL_INFO,
                            "[SIGN_IN_SUCCESS] sign in success", null, null)

                        val bundle = Bundle()
                        bundle.putString(STATUS, ANALYTICS_SUCCESS)
                        AnalyticsTrackingService.logEvent(context, SIGN_IN_THROUGH_OTP, bundle)
                    } else {
                        // Sign in failed, display a message and update the UI
                        Timber.d("signInWithCredential:failure")

                        val exception = task.exception

                        logOTPFlow(AppToCloudLogger.LogLevel.LEVEL_ERROR,
                            "[SIGN_IN_ERROR] ${exception?.message}", phoneNumber, exception?.stackTraceToString())

                        if (exception is FirebaseAuthInvalidCredentialsException) {
                            // The verification code entered was invalid
                            Timber.d("Invalid code was entered")
                            Utils.showAlert("The code you entered is invalid. Please check and try again", this)
                        }else if (!AppUtils.isNetworkConnected(application)) {
                            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
                        }else {
                            Timber.e("OTPCodeVerification Unable to process the request: ${task.exception}")
                            Utils.showAlert("Unable to process the request. Please try again", context)
                        }

                        val bundle = Bundle()
                        bundle.putString(STATUS, ANALYTICS_FAILURE)
                        exception?.let {
                            bundle.putString(ERROR_REASON, it.message)
                        }
                        AnalyticsTrackingService.logEvent(context, SIGN_IN_THROUGH_OTP, bundle)
                    }
                }
    }

    private fun resendVerificationCode(phoneNumber: String, token: PhoneAuthProvider.ForceResendingToken) {

        binding.otpProgressBar.visibility = View.VISIBLE

        val phoneAuthOptions = PhoneAuthOptions.newBuilder(FirebaseAuth.getInstance())
                .setPhoneNumber(phoneNumber)       // Phone number to verify
                .setTimeout(60L, TimeUnit.SECONDS) // Timeout and unit
                .setActivity(this)                 // Activity (for callback binding)
                .setCallbacks(mCallbacks)          // OnVerificationStateChangedCallbacks
                .setForceResendingToken(token)
                .build()

        PhoneAuthProvider.verifyPhoneNumber(phoneAuthOptions)

        logOTPFlow(AppToCloudLogger.LogLevel.LEVEL_INFO,
            "$OTP_INFO OTP resend code requested",phoneNumber, null)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putBoolean(keyVerifyInProgress, mVerificationInProgress)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        mVerificationInProgress = savedInstanceState.getBoolean(keyVerifyInProgress)
    }

    private fun savePhoneNumberToFirebase() {

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.phoneNumber, phoneNumber)

        //saving notification settings
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.deviceOs, Constants.android)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.showMessages, true)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.showNewMatches, true)
        // firebaseDatabaseReference.saveUserInfoToFirebase(Constants.showEverythingElse, true)
        Utils.setDeviceHardwareIdentifier(this)
        setVersion()

        val deviceDetails = android.os.Build.BRAND + "-" + android.os.Build.MODEL + "-" + android.os.Build.VERSION.SDK_INT + "-" + android.os.Build.VERSION.RELEASE
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.deviceDetails, deviceDetails)
    }

    private fun checkOnboardingStatus() {

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)

        firebaseDatabaseReference.readMainUserMapRawDataFromFirebaseFirestore(object :
            FirebaseRetrieveSingleUserRawDataListenerInterfaceFirestore {
            override fun onSuccess(userDocumentSnapShot: DocumentSnapshot) {

                if (!userDocumentSnapShot.contains(Constants.createdOn)) {

                    firebaseDatabaseReference.saveTimeStamp(Constants.createdOn)
                }

                if (!userDocumentSnapShot.contains(Constants.externalId)) {
                    var externalId = AccountPreferences.getInstance(applicationContext).getStringValue(Constants.externalId, "")
                    if (externalId.isNullOrEmpty()) {
                        externalId = UUID.randomUUID().toString()
                    }
                    firebaseDatabaseReference.saveUserInfoToFirebase(Constants.externalId, externalId)
                    AccountPreferences.getInstance(applicationContext).setValue(Constants.externalId, externalId)
                }

                val user = userDocumentSnapShot.toObject(User::class.java)
                val dob = user?.dob

                firebaseDatabaseReference.saveInfluencerSignupInFirebase(user?.freeTrailUsed?: false, this@CodeVerificationActivity) {}

                if (dob != null && Utils.isUserAgerestricted(dob.toString())) {

                    val intent = Intro1Activity.newIntent(context).launchModeWithNoBackStack()
                    intent.putExtra(IS_USER_AGE_RESTRICTED, true)
                    startActivity(intent)
                } else if (userDocumentSnapShot.contains(Constants.isOnboardingComplete)) {

                    val isOnboardinComplete: Boolean? = user?.isOnboardingComplete
                    val isUserReported: Boolean? = user?.isUserReported
                    val shouldDelete = user?.rsDel?: false

                    if (isUserReported == true) {

                        val reportedUserTitle: String? = user.rTitle as? String
                        val reportedUserMessage: String? = user.rMsg as? String

                        val intent = Intro1Activity.newIntent(context).launchModeWithNoBackStack()
                        intent.putExtra(IS_USER_REPORTED, true)
                        intent.putExtra(SHOULD_DELETE_USER, shouldDelete)
                        intent.putExtra(REPORTED_USER_TITLE, reportedUserTitle)
                        intent.putExtra(REPORTED_USER_MESSAGE, reportedUserMessage)
                        startActivity(intent)
                    } else if (isOnboardinComplete == true) {

                        if (isValidUser(user) && (user.isAMember != null)) {

                            setAccountPreferences(user, context)

                            UserObject.isServerOnboardingComplete = true
                            UserObject.userFirebaseId = firebaseUserId
                            UserObject.isHeightVerified = user.isHeightVerified

                            startLocationActivity()
                        } else {

                            startDobActivity()
                        }
                    } else {
                        startDobActivity()
                    }
                } else {
                    startDobActivity()
                }

                savePhoneNumberToFirebase()
            }

            override fun onFailure() {
                startDobActivity()
                savePhoneNumberToFirebase()
            }
        })
    }

    private fun reAuthenticateUserForDeletion(credential: PhoneAuthCredential) {

        val mainAuthUser = FirebaseAuth.getInstance().currentUser
        mainAuthUser?.reauthenticate(credential)?.addOnSuccessListener {

            AppLogic.deleteAccount(this, binding.otpProgressBar)
        }?.addOnFailureListener {

            val builder1 = AlertDialog.Builder(this)
            builder1.setMessage("Unable to delete your profile at this moment. Please try again later or contact us")
            builder1.setCancelable(true)
            builder1.create().show()
        }
    }

    private fun startLocationActivity() {

        val managePermissions = ManagePermissions(this, BackgroundLocationIntentService.locationPermissionsList, BackgroundLocationIntentService.locationPermissionRequestCode)

        if (managePermissions.isPermissionGranted()) {

            startFirstNameOrBrowseProfilesActivity()

            val intent = Intent(this, BackgroundLocationIntentService::class.java)
            BackgroundLocationIntentService.enqueueWork(this, intent)
        } else {

            this.startActivity(EnableLocationActivity.newIntent(this))
        }
    }

    private fun logOTPFlow(level: AppToCloudLogger.LogLevel, message: String, phone: String?, exception: String? = null) {
        externalId = AccountPreferences.getInstance(applicationContext).getStringValue(Constants.externalId, "")
        if (externalId.isEmpty()) {
            externalId = UUID.randomUUID().toString()
            AccountPreferences.getInstance(applicationContext).setValue(Constants.externalId, externalId)
        }
        AppToCloudLogger.logToCloud(context, externalId, level,
            message, phone, exception)
    }

    private fun startFirstNameOrBrowseProfilesActivity() {

        if (UserObject.isServerOnboardingComplete) {

            val intent = BrowseProfilesActivity.newIntent(this).launchModeWithNoBackStack()
            this.startActivity(intent)
        } else {
            this.startActivity(FirstNameActivity.newIntent(this))
        }
    }

    private fun setVersion() {
        if (!UserObject.isVersionSet) {
            val version = AppUtils.getAppVersion()
            val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
            firebaseDatabaseReference.saveUserInfoToFirebase(Constants.version, version)
            UserObject.isVersionSet = true
        }
    }
}
