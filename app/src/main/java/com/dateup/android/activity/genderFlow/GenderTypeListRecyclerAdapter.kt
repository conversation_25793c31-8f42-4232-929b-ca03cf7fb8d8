package com.dateup.android.activity.genderFlow

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.dateup.android.R
import com.dateup.android.databinding.GenderTypeViewHolderBinding
import com.dateup.android.models.OtherGenderType

class GenderTypeListRecyclerAdapter(private val genders: ArrayList<OtherGenderType>, val context: Context) : RecyclerView.Adapter<GenderTypeListRecyclerAdapter.GenderViewHolder>() {

    private lateinit var listener: OnItemClickListener

    override fun onBindViewHolder(holder: GenderViewHolder, position: Int) {
        holder.genderType?.text = genders[position].displayText
        holder.itemView.setOnClickListener {
            listener.onClick(it, genders[position])
        }
    }

    override fun onCreateViewHolder(parent: <PERSON>G<PERSON>, viewType: Int): GenderViewHolder {
        setOnItemClickListener(listener)
        val itemBinding = GenderTypeViewHolderBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return GenderViewHolder(itemBinding)
    }

    override fun getItemCount(): Int {
        return genders.size
    }

    interface OnItemClickListener {
        fun onClick(view: View, data: OtherGenderType)
    }

    fun setOnItemClickListener(listener: OnItemClickListener) {
        this.listener = listener
    }

    class GenderViewHolder(itemView: GenderTypeViewHolderBinding) : RecyclerView.ViewHolder(itemView.root) {
        val genderType: TextView? = itemView.genderType
    }
}