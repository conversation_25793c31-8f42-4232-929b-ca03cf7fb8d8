package com.dateup.android.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.BackgroundColorSpan
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.ScreenRouter.Companion.setLastSeenScreen
import com.dateup.android.databinding.ActivityDateUpPledgeBinding
import com.dateup.android.databinding.WelcomeActivityBinding

class WelcomeActivity : AppCompatActivity() {

    private lateinit var binding: WelcomeActivityBinding

    companion object {

        const val TAG = ScreenRouter.WELCOME_ACTIVITY

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, WelcomeActivity::class.java)
        }
    }

    private lateinit var buttonLargeActiveButton: Button

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = WelcomeActivityBinding.inflate(layoutInflater)
        setContentView(binding.root)
        this.init()

        setLastSeenScreen(TAG, this)
    }

    private fun init() {

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_large_active_button)
        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }

        val spannableString = SpannableString(getString(R.string.how_it_works_point_2))
        val backgroundSpan = BackgroundColorSpan(resources.getColor(R.color.members_color, null))
        spannableString.setSpan(backgroundSpan, 20, spannableString.trim().length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        binding.tallPeopleOnDateuTextView.text = spannableString

        val spannableString2 = SpannableString(getString(R.string.how_it_works_point_3))
        val backgroundSpan2 = BackgroundColorSpan(resources.getColor(R.color.guests_color, null))
        spannableString2.setSpan(backgroundSpan2, 23, spannableString2.trim().length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        binding.thirdPoint.text = spannableString2

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    fun onButtonLargeActivePressed() {

        this.startActivity(EnableLocationActivity.newIntent(this))
    }
}
