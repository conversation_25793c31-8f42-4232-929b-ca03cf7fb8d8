package com.dateup.android.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.SpannableString
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.ScreenRouter.Companion.setLastSeenScreen
import com.dateup.android.extensions.launchModeWithNoBackStack
import com.dateup.android.models.UserObject
import com.dateup.android.services.BackgroundLocationIntentService
import com.dateup.android.services.BackgroundLocationIntentService.Companion.locationPermissionRequestCode
import com.dateup.android.services.BackgroundLocationIntentService.Companion.locationPermissionsList
import com.dateup.android.ui.browseProfiles.BrowseProfilesActivity
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.ManagePermissions
import timber.log.Timber

class EnableLocationActivity : AppCompatActivity() {

    private lateinit var managePermissions: ManagePermissions

    companion object {

        const val TAG = ScreenRouter.LOCATION_ACTIVITY

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, EnableLocationActivity::class.java)
        }
    }

    private lateinit var whereAreYouLocateTextView: TextView
    private lateinit var enableYourLocationTextView: TextView
    private lateinit var buttonLargeActiveButton: Button

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.enable_location_activity)
        this.init()

        setLastSeenScreen(TAG, this)
        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        managePermissions = ManagePermissions(this, locationPermissionsList, locationPermissionRequestCode)

        // Configure Where are you locate component
        whereAreYouLocateTextView = this.findViewById(R.id.where_are_you_locate_text_view)
        val whereAreYouLocateTextViewText = SpannableString(this.getString(R.string.enable_location_activity_where_are_you_locate_text_view_text))
        whereAreYouLocateTextView.text = whereAreYouLocateTextViewText

        // Configure Enable your location component
        enableYourLocationTextView = this.findViewById(R.id.enable_your_location_text_view)
        val enableYourLocationTextViewText = SpannableString(this.getString(R.string.enable_location_activity_enable_your_location_text_view_text))
        enableYourLocationTextView.text = enableYourLocationTextViewText

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.setOnClickListener { view ->

            this.onButtonLargeActivePressed()
        }
    }

    fun onButtonLargeActivePressed() {

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {

            if (managePermissions.isPermissionGranted()) {

                startBrowseProfilesActivity()
            } else {

                managePermissions.checkPermissions()
            }
        }
    }

    private fun startBrowseProfilesActivity() {
        if (UserObject.isServerOnboardingComplete) {
            val intent = BrowseProfilesActivity.newIntent(this).launchModeWithNoBackStack()
            this.startActivity(intent)
        } else {
            this.startActivity(FirstNameActivity.newIntent(this))
        }
    }

    // Receive the permissions request result
    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>,
                                            grantResults: IntArray) {
        when (requestCode) {

            locationPermissionRequestCode -> {

                val isPermissionsGranted = managePermissions
                        .processPermissionsResult(requestCode, permissions, grantResults)

                if (isPermissionsGranted) {

                    startBrowseProfilesActivity()

                    saveLocationDetails()
                } else {

                    Timber.d("Location permission denied")

                    startBrowseProfilesActivity()
                }
                return
            }
        }
    }

    private fun saveLocationDetails() {

        val intent = Intent(this, BackgroundLocationIntentService::class.java)
        BackgroundLocationIntentService.enqueueWork(this, intent)
    }
}
