package com.dateup.android.activity

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.res.ResourcesCompat
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.ScreenRouter.Companion.setLastSeenScreen
import com.dateup.android.databinding.HeightCelebrationActivityMembersBinding
import com.dateup.android.models.GenderType
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Constants
import com.dateup.android.utils.FontSpan
import nl.dionsegijn.konfetti.models.Shape
import nl.dionsegijn.konfetti.models.Size

class HeightCelebrationMembersActivity : AppCompatActivity() {

    private lateinit var binding: HeightCelebrationActivityMembersBinding

    companion object {

        const val TAG = ScreenRouter.HEIGHT_CELEBRATION_MEMBERS_ACTIVITY

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, HeightCelebrationMembersActivity::class.java)
        }
    }

    private lateinit var congratsYouReAmtextView: TextView
    private lateinit var menOver61AndWoTextView: TextView
    private lateinit var buttonLargeActiveButton: Button
    private lateinit var membersImageView: ImageView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        HeightCelebrationGuestsActivity.hideStatusBar(this)

        binding = HeightCelebrationActivityMembersBinding.inflate(layoutInflater)
        this.setContentView(binding.root)

        this.init()

        setLastSeenScreen(TAG, this)
        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        // Configure Congrats, you're a m component
        congratsYouReAmtextView = this.findViewById(R.id.congrats_you_re_amtext_view)

        membersImageView = this.findViewById(R.id.height_celebration_members_image_view)

        val gender: String = AccountPreferences.getInstance(this).getStringValue(Constants.gender, "")
        if (gender == GenderType.woman.toString()) {
            membersImageView.setImageResource(R.drawable.ic_tall_woman_with_friends)
        }

        // Configure Men over 6'1" and wo component
        menOver61AndWoTextView = this.findViewById(R.id.men_over61_and_wo_text_view)
        val menOver61AndWoTextViewText = SpannableString(this.getString(R.string.height_celebration2_activity_men_over61_and_wo_text_view_text))
        menOver61AndWoTextViewText.setSpan(FontSpan(ResourcesCompat.getFont(this, R.font.font_nunitosans_extrabolditalic)), 48, 57, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        menOver61AndWoTextViewText.setSpan(ForegroundColorSpan(Color.parseColor("#25333D")), 48, 57, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        menOver61AndWoTextViewText.setSpan(FontSpan(ResourcesCompat.getFont(this, R.font.font_nunitosans_extrabolditalic)), 98, 106, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        menOver61AndWoTextViewText.setSpan(ForegroundColorSpan(Color.parseColor("#25333D")), 98, 106, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        menOver61AndWoTextView.text = menOver61AndWoTextViewText

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_large_active_button)
        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }

        startKonfetti()
    }

    private fun startKonfetti() {

        binding.viewKonfetti.build()
                .addColors(resources.getColor(R.color.color_primary, null), resources.getColor(R.color.konfetti_color2, null))
                .setDirection(0.0, 359.0)
                .setSpeed(1f, 5f)
                .setFadeOutEnabled(true)
                .setTimeToLive(2000L)
                .addShapes(Shape.Square, Shape.Circle)
                .addSizes(Size(12))
                .setPosition(-50f, AppUtils.getScreenWidth(this) + 50f, -50f, -50f)
                .streamFor(300, 2000L)
    }

    fun onButtonLargeActivePressed() {
        this.startActivity(GuestMatchMakingActivity.newIntent(this))
    }

    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {}
}
