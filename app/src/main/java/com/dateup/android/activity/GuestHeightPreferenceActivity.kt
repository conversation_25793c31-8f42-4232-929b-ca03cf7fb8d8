package com.dateup.android.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.SpannableString
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.ScreenRouter.Companion.setLastSeenScreen
import com.dateup.android.extensions.getDoubleValue
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.utils.Constants
import com.dateup.android.utils.Utils
import io.apptik.widget.MultiSlider

class GuestHeightPreferenceActivity : AppCompatActivity() {

    private lateinit var buttonLargeActiveButton: Button
    private lateinit var rangeSeekbar: MultiSlider
    private var minHeight: Double? = Constants.minHeightInInchForGuestPrefs
    private var maxHeight: Double? = Constants.maxHeightInInchForGuestPrefs
    private lateinit var guestHeightPrefTextView: TextView


    companion object {

        const val TAG = ScreenRouter.GUEST_HEIGHT_PREFERENCE_ACTIVITY

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, GuestHeightPreferenceActivity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_guest_height_preference)
        init()

        setLastSeenScreen(TAG, this)
        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }

        rangeSeekbar = this.findViewById(R.id.guest_height_pref_seekbar)
        rangeSeekbar.getThumb(0).value = 0
        rangeSeekbar.getThumb(1).value = 28

        guestHeightPrefTextView = this.findViewById(R.id.guest_height_pref_text_view)

        setHeightTextViewText("5'8\"", "6'0\"")

        rangeSeekbar.setOnThumbValueChangeListener(object : MultiSlider.SimpleChangeListener() {
            override fun onValueChanged(multiSlider: MultiSlider?, thumb: MultiSlider.Thumb?, thumbIndex: Int, value: Int) {

                var minHeightInches: Double = AccountPreferences.getInstance(applicationContext).getDoubleValue(Constants.minMatchHeight, minHeight).toDouble()
                var maxHeightInches: Double = AccountPreferences.getInstance(applicationContext).getDoubleValue(Constants.maxMatchHeight, maxHeight).toDouble()

                if (thumbIndex == 0) {
                    minHeightInches = value.times(Constants.mensHeightInInchMultiplierForGuest).plus(Constants.startingHeightInInchForGuest)
                } else {
                    maxHeightInches = value.times(Constants.mensHeightInInchMultiplierForGuest).plus(Constants.startingHeightInInchForGuest)
                }

                minHeight = minHeightInches.toString().getDoubleValue()
                maxHeight = maxHeightInches.toString().getDoubleValue()

                val minHeightString = Utils.heightInFeetFromInchesWithQuotes(minHeightInches)
                val maxHeightString = Utils.heightInFeetFromInchesWithQuotes(maxHeightInches)

                setHeightTextViewText(minHeightString, maxHeightString)
            }
        })
    }

    fun setHeightTextViewText(minHeightString: String, maxHeightString: String) {

        AccountPreferences.getInstance(this).setValue(Constants.minMatchHeight, minHeight)
        AccountPreferences.getInstance(this).setValue(Constants.maxMatchHeight, maxHeight)

        val heightText = SpannableString(this.getString(R.string.height_preference_men_activity_to64_text_view_text, minHeightString, maxHeightString))
        guestHeightPrefTextView.text = heightText
    }

    fun onButtonLargeActivePressed() {
        saveGuestHeightPrefToFirebase()
        startProfilePicturesActivity()
    }

    private fun startProfilePicturesActivity() {

        this.startActivity(Images3Activity.newIntent(this))
    }

    private fun saveGuestHeightPrefToFirebase() {

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.minMatchHeight, minHeight)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.maxMatchHeight, maxHeight)
    }
}
