package com.dateup.android.activity.genderFlow

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.activity.YourHeightActivity
import com.dateup.android.analytics.*
import com.dateup.android.databinding.ActivityHeightVerificationScanBinding
import com.dateup.android.databinding.ActivityInterestedInBinding
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.models.InterestedInGender
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Constants

class InterestedInActivity : AppCompatActivity() {

    companion object {

        const val TAG = ScreenRouter.INTERESTED_IN_ACTIVITY

        fun newIntent(context: Context): Intent {
            return Intent(context, InterestedInActivity::class.java)
        }
    }

    private var interestedIn: String = ""
    private var interestedInButtonSelected = false

    private lateinit var binding: ActivityInterestedInBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityInterestedInBinding.inflate(layoutInflater)
        setContentView(binding.root)

        ScreenRouter.setLastSeenScreen(TAG, this)

        init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        binding.notificationsHeaderLeftImageView.setOnClickListener {
            onBackPressed()
        }

        binding.buttonPreferenceMen.setOnClickListener {
            this.onManButtonPressed()
        }

        binding.buttonPreferenceWomen.setOnClickListener { view ->
            this.onWomanButtonPressed()
        }

        binding.buttonPreferenceEveryone.setOnClickListener { view ->
            this.onEveryoneButtonPressed()
        }

        binding.buttonNext.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }
    }

    private fun onManButtonPressed() {
        binding.buttonPreferenceMen.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_selected_button_selector)
        binding.buttonPreferenceWomen.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        binding.buttonPreferenceEveryone.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        interestedIn = InterestedInGender.man.toString()
        interestedInButtonSelected = true
        enableOrDisableButton()
    }

    private fun onWomanButtonPressed() {
        binding.buttonPreferenceWomen.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_selected_button_selector)
        binding.buttonPreferenceMen.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        binding.buttonPreferenceEveryone.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        interestedIn = InterestedInGender.woman.toString()
        interestedInButtonSelected = true
        enableOrDisableButton()
    }

    private fun onEveryoneButtonPressed() {
        binding.buttonPreferenceEveryone.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_selected_button_selector)
        binding.buttonPreferenceMen.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        binding.buttonPreferenceWomen.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        interestedIn = InterestedInGender.everyone.toString()
        interestedInButtonSelected = true
        enableOrDisableButton()
    }

    private fun enableOrDisableButton() {
        if (interestedInButtonSelected) {
            binding.buttonNext.isEnabled = true
            binding.buttonNext.setBackgroundResource(R.drawable.phone_number_activity_button_large_active_button_selector)
        }
    }

    fun onButtonLargeActivePressed() {
        if (!AppUtils.isNetworkConnected(application)) {
            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            saveInterestedInPrefs()
            startYourHeightActivity()
        }
    }

    private fun saveInterestedInPrefs() {
        AccountPreferences.getInstance(this).setValue(Constants.interestedIn, interestedIn)
        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.interestedIn, interestedIn)

        val gender: String = AccountPreferences.getInstance(this).getStringValue(Constants.gender, "")
        if (gender.isNotEmpty()) {
            if (gender == interestedIn) {
                AnalyticsTrackingService.setUserProperty(this, USER_ORIENTATION_PROPERTY, GAY)
            }else {
                AnalyticsTrackingService.setUserProperty(this, USER_ORIENTATION_PROPERTY, STRAIGHT)
            }
        }
    }

    private fun startYourHeightActivity() {
        this.startActivity(YourHeightActivity.newIntent(this))
    }
}