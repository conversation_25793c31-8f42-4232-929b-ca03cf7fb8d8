package com.dateup.android.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.ui.BottomNavigationBarActivity


class LocationLockedActivity : BottomNavigationBarActivity() {

    companion object {

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, LocationLockedActivity::class.java)
        }
    }

    private lateinit var dateupIsNotYetLiTextView: TextView
    private lateinit var weLlLetYouKnowAtextView: TextView
    private lateinit var mLayoutContainer: FrameLayout

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        this.init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    override fun getBottomNavigationBarItem(): Int {

        return R.id.navigation_home
    }

    private fun init() {

        mLayoutContainer = findViewById(R.id.layout_container)

        val locationLockedLayout = View.inflate(this, R.layout.location_locked_activity, null)
        mLayoutContainer.addView(locationLockedLayout)
    }
}
