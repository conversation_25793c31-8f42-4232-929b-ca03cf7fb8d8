package com.dateup.android.activity

import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.res.ResourcesCompat
import com.dateup.android.R
import com.dateup.android.databinding.ActivityBetaGroupBinding
import com.dateup.android.ScreenRouter
import com.dateup.android.utils.FontSpan

class BetaGroupActivity : AppCompatActivity() {

    private lateinit var binding: ActivityBetaGroupBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityBetaGroupBinding.inflate(layoutInflater)
        setContentView(binding.root)

        binding.buttonEnableLocation.setOnClickListener {

            showIntroActivity()
        }

        val betaDescriptionTextView: TextView = findViewById(R.id.enable_your_location_text_view)
        val betaText = SpannableString(this.getString(R.string.closed_beta_desc))
        betaText.setSpan(FontSpan(ResourcesCompat.getFont(this, R.font.font_nunitosans_bold)), 42, 56, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        betaText.setSpan(ForegroundColorSpan(Color.parseColor("#161E24")), 42, 56, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        betaText.setSpan(FontSpan(ResourcesCompat.getFont(this, R.font.font_nunitosans_bold)), 72, 82, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        betaText.setSpan(ForegroundColorSpan(Color.parseColor("#161E24")), 72, 82, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        betaDescriptionTextView.text = betaText
    }

    private fun showIntroActivity() {
        val intent = Intent(this, Intro1Activity::class.java)
        finish()
        this.startActivity(intent)

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }
}
