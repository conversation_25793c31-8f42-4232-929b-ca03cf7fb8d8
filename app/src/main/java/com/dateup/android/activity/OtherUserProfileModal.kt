package com.dateup.android.activity

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.ViewModelProvider
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.databinding.ActivityOtherUserProfileBinding
import com.dateup.android.ScreenRouter
import com.dateup.android.chat.dialog.ChatDialogListFragment.Companion.CHAT_ID
import com.dateup.android.chat.dialog.ChatDialogListFragment.Companion.DIALOG_PHOTO_URL
import com.dateup.android.chat.dialog.ChatDialogListFragment.Companion.DM_REQUEST
import com.dateup.android.chat.dialog.ChatDialogListFragment.Companion.EXTRA_LAST_MESSAGE
import com.dateup.android.chat.message.MessageListActivity
import com.dateup.android.extensions.getDoubleValue
import com.dateup.android.extensions.getIntValue
import com.dateup.android.extensions.launchModeWithNoBackStack
import com.dateup.android.extensions.launchModeWithSingleClearTop
import com.dateup.android.extensions.launchModeWithSingleTop
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.firebase.FirebaseRetrieveSingleUserListenerInterfaceFirestore
import com.dateup.android.fragment.SendMessageDialogFragment
import com.dateup.android.glide.ImageLoaderModule
import com.dateup.android.heightVerification.ui.HeightVerificationScan
import com.dateup.android.models.*
import com.dateup.android.paidVersion.likesYou.LikesYouActivity
import com.dateup.android.paidVersion.likesYou.LikesYouAdapter.Companion.EXTRA_LIKES_YOU_USER
import com.dateup.android.paidVersion.likesYou.LikesYouAdapter.Companion.EXTRA_PASSED_USER
import com.dateup.android.utils.*
import com.dateup.android.viewModels.SpacesImagesViewModel
import com.google.firebase.database.FirebaseDatabase
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.*

class OtherUserProfileModal : AppCompatActivity() {

    private lateinit var binding: ActivityOtherUserProfileBinding

    companion object {

        const val EXTRA_USER_FROM_OTHER_PROFILE_MODAL = "EXTRA_USER_FROM_OTHER_PROFILE_MODAL"
        const val EXTRA_MESSAGE_OTHER_PROFILE_MODAL = "EXTRA_MESSAGE_OTHER_PROFILE_MODAL"
    }

    private lateinit var professionTextView: TextView
    private lateinit var ageTextView: TextView
    private lateinit var heightTextView: TextView
    private lateinit var educationTextView: TextView
    private lateinit var hometownTextView: TextView
    private lateinit var iceBreakerQuestion1TextView: TextView
    private lateinit var iceBreakerAnswer1TextView: TextView
    private lateinit var iceBreakerQuestion2TextView: TextView
    private lateinit var iceBreakerAnswer2TextView: TextView
    private lateinit var userNameTextView: TextView
    private lateinit var firstPhotoImageView: ImageView
    private lateinit var secondPhotoImageView: ImageView
    private lateinit var thirdPhotoImageView: ImageView
    private lateinit var fourthPhotoImageView: ImageView
    private lateinit var fifthPhotoImageView: ImageView
    private lateinit var sixthPhotoImageView: ImageView
    private lateinit var firstIcebreakerBackgroundConstraintLayout: ConstraintLayout
    private lateinit var secondIcebreakerBackgroundConstraintLayout: ConstraintLayout
    private lateinit var firstIcebreakerBackgroundImageView: ImageView
    private lateinit var secondIcebreakerBackgroundImageView: ImageView
    private lateinit var backImageButton: ImageView
    private lateinit var distanceTextView: TextView
    private lateinit var reportUserButton: Button
    private lateinit var userLocationTextView: TextView
    private lateinit var locationImageView: ImageView

    private lateinit var topHeelsIcon: ImageView
    private lateinit var bottomHeelsIcon: ImageView
    private lateinit var bottomHeelsTextViewPreview: TextView
    private lateinit var bottomHeelsSectionLayout: ConstraintLayout

    var distanceBetweenUsers: Int? = null

    private lateinit var otherUserId: String
    private var conversationChatId: String? = null

    private var mainUserId = ""

    private var firebaseDatabaseUtil: FirebaseDatabaseUtil? = null

    private var mContext: Context? = null

    var firstPhotoRef = ""
    var secondPhotoRef = ""
    var thirdPhotoRef = ""
    var fourthPhotoRef = ""
    var fifthPhotoRef = ""
    var sixthPhotoRef = ""

    private lateinit var spacesImagesViewModel: SpacesImagesViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityOtherUserProfileBinding.inflate(layoutInflater)
        setContentView(binding.root)

        spacesImagesViewModel = ViewModelProvider(this).get(SpacesImagesViewModel::class.java)

        mainUserId = AccountPreferences.getInstance(this).getStringValue(Constants.firebaseUserId, "")

        mContext = this

        firebaseDatabaseUtil = FirebaseDatabaseUtil(this)

        checkForIntent()

        init()
    }

    private fun checkForIntent() {

        if (intent.getSerializableExtra(EXTRA_LIKES_YOU_USER) != null) {

            binding.likesYouUserSelectionLayout.visibility = View.VISIBLE

            val likesYouUser = intent.getSerializableExtra(EXTRA_LIKES_YOU_USER)

            otherUserId = (likesYouUser as? LikesTabUser)?.likedYouUserFirebaseId ?: ""

            binding.buttonLikeUser.setOnClickListener {

                firebaseDatabaseUtil?.likeUser(mainUserId, otherUserId)

                matchUsers(mainUserId, otherUserId)
            }

            binding.buttonPassUser.setOnClickListener {

                firebaseDatabaseUtil?.disLikeUser(mainUserId, otherUserId)

                val intent = Intent(this, LikesYouActivity::class.java).launchModeWithNoBackStack()
                intent.putExtra(EXTRA_USER_FROM_OTHER_PROFILE_MODAL, likesYouUser)
                startActivity(intent)
                finish()
            }
        } else if (intent.getSerializableExtra(EXTRA_PASSED_USER) != null) {

            binding.likesYouUserSelectionLayout.visibility = View.VISIBLE

            val likesYouUser = intent.getSerializableExtra(EXTRA_PASSED_USER)

            otherUserId = (likesYouUser as? LikesTabUser)?.uid.toString()
            val otherUserName = (likesYouUser as? LikesTabUser)?.name ?: ""

            binding.buttonPassUser.setOnClickListener {
                firebaseDatabaseUtil?.likeUser(mainUserId, otherUserId)
                firebaseDatabaseUtil?.removeDislike(mainUserId, otherUserId)

                val intent = Intent(this, LikesYouActivity::class.java).launchModeWithNoBackStack()
                intent.putExtra(EXTRA_USER_FROM_OTHER_PROFILE_MODAL, likesYouUser)
                startActivity(intent)
                finish()
            }

            binding.buttonPassUser.text = "Like"
            binding.buttonLikeUser.text = "Message"

            binding.buttonLikeUser.setOnClickListener {
                val dialog = SendMessageDialogFragment.newInstance(otherUserId, firstPhotoRef, otherUserName.toString(), spacesImagesViewModel) {
                    firebaseDatabaseUtil?.likeUser(mainUserId, otherUserId)
                    firebaseDatabaseUtil?.removeDislike(mainUserId, otherUserId)
                    val intent = Intent(this, LikesYouActivity::class.java).launchModeWithNoBackStack()
                    intent.putExtra(EXTRA_USER_FROM_OTHER_PROFILE_MODAL, likesYouUser)
                    startActivity(intent)
                    finish()
                }
                dialog.show(supportFragmentManager, "SendMessageDialog")
            }
        } else if (intent.getStringExtra(Constants.firebaseUserId) != null) {

            binding.likesYouUserSelectionLayout.visibility = View.GONE
            otherUserId = intent.getStringExtra(Constants.firebaseUserId) ?: ""
        }

        if (intent.getBooleanExtra(DM_REQUEST , false)) {
            conversationChatId = intent.getStringExtra(CHAT_ID)
            binding.chatRequestBottomSheet.visibility = View.VISIBLE
            binding.requestMessageTextView.text = intent.getStringExtra(EXTRA_LAST_MESSAGE)
        } else {
            binding.chatRequestBottomSheet.visibility = View.GONE
        }

        binding.ignoreRequestButton.setOnClickListener {
            firebaseDatabaseUtil?.disLikeUser(mainUserId, otherUserId)
            firebaseDatabaseUtil?.setDmRejected(mainUserId, otherUserId)
        }
    }

    private fun acceptChatRequest(otherUsername: String) {
        val user0Map = HashMap<String, String>()
        val user1Map = HashMap<String, String>()

        user0Map[Constants.name] = AccountPreferences.getInstance(this).getStringValue(Constants.name, "")
        user0Map[Constants.avatar] = AccountPreferences.getInstance(this).getStringValue(Constants.userProfilePhotoUrl, "")
        user0Map[Constants.firebaseUserId] = mainUserId

        user1Map[Constants.name] = otherUsername
        user1Map[Constants.avatar] = intent.getStringExtra(DIALOG_PHOTO_URL).toString()
        user1Map[Constants.firebaseUserId] = otherUserId

        firebaseDatabaseUtil?.likeUser(mainUserId, otherUserId)

        val intent = Intent(this, MessageListActivity::class.java)
        intent.putExtra(Constants.user0, user0Map)
        intent.putExtra(Constants.user1, user1Map)
        intent.putExtra(Constants.conversationId, conversationChatId)
        intent.putExtra(DM_REQUEST, true)
        intent.putExtra(EXTRA_LAST_MESSAGE, intent.getStringExtra(EXTRA_LAST_MESSAGE))
        startActivity(intent)
    }

    private fun init() {

        // Configure Product Designer component
        professionTextView = findViewById(R.id.user_profession_text_view)

        // Configure Age component
        ageTextView = findViewById(R.id.user_age_text_view)

        // Configure Education component
        educationTextView = findViewById(R.id.user_education_text_view)

        // Configure Hometown component
        hometownTextView = findViewById(R.id.user_home_town_text_view)

        backImageButton = findViewById(R.id.edit_profile_header_left_image_view)

        // Configure How do you describe component
        iceBreakerQuestion1TextView = findViewById(R.id.ice_breaker_1_question_text_view)

        // Configure I’m definitely fun a component
        iceBreakerAnswer1TextView = findViewById(R.id.ice_breaker_1_answer_text_view)

        // Configure How do you describe component
        iceBreakerQuestion2TextView = findViewById(R.id.ice_breaker_2_question_text_view)

        // Configure I’m definitely fun a component
        iceBreakerAnswer2TextView = findViewById(R.id.ice_breaker_2_answer_text_view)

        // Configure Jon component
        userNameTextView = findViewById(R.id.user_name_text_view)

        heightTextView = findViewById(R.id.user_height_text_view)

        firstPhotoImageView = findViewById(R.id.user_first_photo_image_view)

        secondPhotoImageView = findViewById(R.id.user_second_photo_image_view)

        thirdPhotoImageView = findViewById(R.id.user_third_photo_image_view)

        fourthPhotoImageView = findViewById(R.id.user_fourth_photo_image_view)

        fifthPhotoImageView = findViewById(R.id.user_fifth_photo_image_view)

        sixthPhotoImageView = findViewById(R.id.user_sixth_photo_image_view)

        firstIcebreakerBackgroundConstraintLayout = findViewById(R.id.profilecard_blank_photo_copy_constraint_layout)
        secondIcebreakerBackgroundConstraintLayout = findViewById(R.id.profilecard_blank_photo_copy3_constraint_layout)

        firstIcebreakerBackgroundImageView = findViewById(R.id.rectangle_image_view)
        secondIcebreakerBackgroundImageView = findViewById(R.id.rectangle_two_image_view)

        setImageViewHeightBasedOnDeviceWidth()

        reportUserButton = findViewById(R.id.report_user_button)

        locationImageView = findViewById(R.id.location_image_view)

        distanceTextView = findViewById(R.id.distance_text_view)

        userLocationTextView = findViewById(R.id.user_location_text_view)

        topHeelsIcon = findViewById(R.id.top_heels_icon)
        bottomHeelsIcon = findViewById(R.id.boots_bottom_image_view)
        bottomHeelsTextViewPreview = findViewById(R.id.user_match_boot_preview_text_view)
        bottomHeelsSectionLayout = findViewById(R.id.group10_constraint_layout)

        backImageButton.setOnClickListener {
            onBackPressed()
        }

        reportUserButton.setOnClickListener {

            reportUser()
        }

        readUserInfoFromFirebase()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun readUserInfoFromFirebase() {

        if (intent.getSerializableExtra(EXTRA_LIKES_YOU_USER) != null || intent.getSerializableExtra(EXTRA_PASSED_USER) != null) {

            var likesTabUser = intent.getSerializableExtra(EXTRA_LIKES_YOU_USER) as? LikesTabUser

            if (likesTabUser == null) {
                likesTabUser = intent.getSerializableExtra(EXTRA_PASSED_USER) as? LikesTabUser
            }

            val user = User(likesTabUser?.dob, likesTabUser?.email, likesTabUser?.gender, likesTabUser?.height, likesTabUser?.homeTown,
                    likesTabUser?.iceBreaker1, likesTabUser?.iceBreaker2, likesTabUser?.interestedIn, likesTabUser?.isAMember,
                    likesTabUser?.name, likesTabUser?.phone, likesTabUser?.profession, likesTabUser?.distance, likesTabUser?.showGuests,
                    likesTabUser?.isOnboardingComplete, likesTabUser?.isUserLocked, likesTabUser?.country, likesTabUser?.city, likesTabUser?.school, likesTabUser?.school2,
                    likesTabUser?.zip, likesTabUser?.minMatchAge, likesTabUser?.maxMatchAge, likesTabUser?.minGuestHeight, likesTabUser?.maxGuestHeight,
                    likesTabUser?.minMatchHeight, likesTabUser?.maxMatchHeight, Connections(), likesTabUser?.isUserReported, likesTabUser?.isHeightVerified, true, true, true, likesTabUser?.state, false, false)

            firebaseDatabaseUtil?.getDistanceBetweenTwoUsers(mainUserId, otherUserId) { distance ->
                distanceBetweenUsers = distance

                if (intent.getSerializableExtra(EXTRA_PASSED_USER) != null) {
                    showProfile(user)
                } else {
                    if (Utils.isValidLikesYouUser(likesTabUser)) {
                        showProfile(user)
                    } else {
                        Timber.d("Not a valid signup as user is missing some data")
                    }
                }
            }
        } else {
            firebaseDatabaseUtil?.readSingleUserInfoFromFirebaseFirestore(object :
                FirebaseRetrieveSingleUserListenerInterfaceFirestore {
                override fun onSuccess(user: User) {
                    firebaseDatabaseUtil?.getDistanceBetweenTwoUsers(mainUserId, otherUserId) { distance ->
                        distanceBetweenUsers = distance
                        if (Utils.isValidUser(user)) {
                            showProfile(user)
                        } else {
                            Timber.e("Not a valid signup as user is missing some data: $otherUserId")
                        }
                    }
                }

                override fun onFailure() {

                    Timber.d("retrieving single user data in other user profile modal failed")
                }
            }, otherUserId)
        }
    }

    private fun showProfile(user: User?) {

        firstPhotoRef = "$otherUserId/${Constants.photoFileName1}"
        secondPhotoRef = "$otherUserId/${Constants.photoFileName2}"
        thirdPhotoRef = "$otherUserId/${Constants.photoFileName3}"
        fourthPhotoRef = "$otherUserId/${Constants.photoFileName4}"
        fifthPhotoRef = "$otherUserId/${Constants.photoFileName5}"
        sixthPhotoRef = "$otherUserId/${Constants.photoFileName6}"

        try {

            setPhotoIntoImageViewWithIceBreaker(this, firstPhotoRef, firstPhotoImageView, firstIcebreakerBackgroundImageView)

            setPhotoIntoImageViewWithIceBreaker(this, secondPhotoRef, secondPhotoImageView, secondIcebreakerBackgroundImageView)

            setPhotoIntoImageViewWithIceBreaker(this, thirdPhotoRef, thirdPhotoImageView, null)

            setPhotoIntoImageViewWithIceBreaker(this, fourthPhotoRef, fourthPhotoImageView, null)

            setPhotoIntoImageViewWithIceBreaker(this, fifthPhotoRef, fifthPhotoImageView, null)

            setPhotoIntoImageViewWithIceBreaker(this, sixthPhotoRef, sixthPhotoImageView, null)

            val sdf = SimpleDateFormat("MM/dd/yyyy", Locale.US)
            val date = sdf.parse(user?.dob.toString())

            var school = ""

            val userGender = user?.gender ?: ""

            val mainUserGender = AccountPreferences.getInstance(this).getStringValue(Constants.gender, "")

            if (mainUserGender == userGender) {

                hideHeelsIcon()
            } else if (mainUserGender == GenderType.woman.toString() && userGender == GenderType.man.toString()) {

                // get mainuser height in inches
                // get other user height in inches
                // pass these two as parameters to HeelsLogic class
                // get back top-badge-id, bottom-badge-id and text copy from HeelsLogic class
                // assign them to these below views
                val mainUserHeightInInches = AccountPreferences.getInstance(this).getDoubleValue(Constants.height, 0.0).toString().getDoubleValue()?.getIntValue()
                val otherUserHeightInInches = user?.height.toString().getDoubleValue()?.getIntValue()

                if (otherUserHeightInInches != null &&
                        mainUserHeightInInches != null) {

                    HeelsLogic.getBadgeAndText(otherUserHeightInInches, mainUserHeightInInches, user?.name.toString()) { topHeelsIconId, bottomHeelsIconId, bottomHeelsText, shouldShowComponent ->

                        if (shouldShowComponent) {

                            showHeelsIcon()

                            topHeelsIcon.setImageResource(topHeelsIconId)
                            bottomHeelsIcon.setImageResource(bottomHeelsIconId)
                            bottomHeelsTextViewPreview.text = bottomHeelsText
                        } else {

                            hideHeelsIcon()
                        }
                    }
                } else {

                    hideHeelsIcon()
                }
            } else {

                hideHeelsIcon()
            }

            userNameTextView.text = user?.name.toString()
            heightTextView.text = Utils.heightInFeetFromInchesWithQuotes(user?.height.toString().toDouble())
            ageTextView.text = Utils.getAge(date).toString() + " yrs"

            school = user?.school.toString()

            if (!TextUtils.isEmpty(user?.school2.toString())) {
                school = school + "\n" + user?.school2.toString()
            }

            val userCity = user?.city
            val userState = user?.state
            var location = ""
            if (userCity != null && userCity != "" && userState != null && userState != "") {
                location = "$userCity, $userState"
            }

            if (!location.isNullOrEmpty()) {
                userLocationTextView.visibility = View.VISIBLE
                locationImageView.visibility = View.VISIBLE
                userLocationTextView.text = location
            } else {
                userLocationTextView.visibility = View.GONE
                locationImageView.visibility = View.GONE
            }
            if (distanceBetweenUsers != null) {
                if (distanceBetweenUsers == 0 || distanceBetweenUsers == 1) {

                    distanceTextView.text = "<1 mile away"
                } else {

                    distanceTextView.text = distanceBetweenUsers.toString() + " miles away"
                }
            }

            val iceBreaker1Questions = user?.iceBreaker1 as? HashMap<*, *>
            val iceBreaker2Questions = user?.iceBreaker2 as? HashMap<*, *>
            var iceBreaker1Question = ""
            var iceBreaker1Answer = ""
            var iceBreaker2Question = ""
            var iceBreaker2Answer = ""

            if (iceBreaker1Questions != null) {

                for (key in iceBreaker1Questions.keys) {

                    iceBreaker1Question = key.toString()
                }

                iceBreaker1Answer = iceBreaker1Questions[iceBreaker1Question].toString()

            }

            if (iceBreaker2Questions != null) {

                for (key2 in iceBreaker2Questions.keys) {

                    iceBreaker2Question = key2.toString()
                }

                iceBreaker2Answer = iceBreaker2Questions[iceBreaker2Question].toString()
            }

            iceBreakerQuestion1TextView.text = iceBreaker1Question
            iceBreakerAnswer1TextView.text = iceBreaker1Answer
            iceBreakerQuestion2TextView.text = iceBreaker2Question
            iceBreakerAnswer2TextView.text = iceBreaker2Answer

            if (user?.isHeightVerified == true) {

                showHeightVerifiedBadge()
            } else {

                hideHeightVerifiedBadge()
            }

            if (TextUtils.isEmpty(user?.profession.toString())) {
                hideProfession()
            } else {
                showProfession()
                professionTextView.text = user?.profession.toString()
            }

            if (TextUtils.isEmpty(school)) {
                hideSchool()
            } else {
                showSchool()
                educationTextView.text = school
            }

            if (TextUtils.isEmpty(user?.homeTown.toString())) {
                hideHomeTown()
            } else {
                showHomeTown()
                hometownTextView.text = user?.homeTown.toString()
            }

            binding.acceptRequestButton.setOnClickListener {
                acceptChatRequest(otherUsername = user?.name.toString())
            }

        } catch (e: Exception) {

            Timber.e("exception in showing user profile: $e")
        }
    }

    private fun hidePhotoImageView(photoView: ImageView) {

        photoView.visibility = View.GONE

        invalidateLayout()
    }

    private fun showPhotoImageView(photoView: ImageView) {

        photoView.visibility = View.VISIBLE

        invalidateLayout()
    }

    private fun invalidateLayout() {

        binding.activityOtherUserBrowseProfiles.invalidate()
    }

    private fun hideProfession() {
        binding.professionTextView.visibility = View.GONE
        professionTextView.visibility = View.GONE

        invalidateLayout()
    }

    private fun showProfession() {
        binding.professionTextView.visibility = View.VISIBLE
        professionTextView.visibility = View.VISIBLE

        invalidateLayout()
    }

    private fun hideSchool() {
        educationTextView.visibility = View.GONE
        binding.educationTextView.visibility = View.GONE

        invalidateLayout()
    }

    private fun showSchool() {
        binding.educationTextView.visibility = View.VISIBLE
        educationTextView.visibility = View.VISIBLE

        invalidateLayout()
    }

    private fun hideHomeTown() {
        binding.hometownTextView.visibility = View.GONE
        hometownTextView.visibility = View.GONE

        invalidateLayout()
    }

    private fun showHomeTown() {
        binding.hometownTextView.visibility = View.VISIBLE
        hometownTextView.visibility = View.VISIBLE

        invalidateLayout()
    }

    private fun showHeelsIcon() {

        topHeelsIcon.visibility = View.VISIBLE
        bottomHeelsSectionLayout.visibility = View.VISIBLE
    }

    private fun hideHeelsIcon() {

        topHeelsIcon.visibility = View.INVISIBLE
        bottomHeelsSectionLayout.visibility = View.GONE
    }

    private fun reportUser() {

        val appLogic = AppLogic(this, mainUserId, otherUserId)
        appLogic.reportUser(shouldShowUnmatchButton = false)
    }

    private fun matchUsers(mainUserFirebaseId: String, otherUserFirebaseId: String) {

        val firebaseDatabaseReference = FirebaseDatabase.getInstance().getReference(Constants.conversations).push()
        val chatId = firebaseDatabaseReference.key

        firebaseDatabaseUtil?.matchUsers(mainUserFirebaseId, otherUserFirebaseId, chatId)

        val likesYouUser = intent.getSerializableExtra(EXTRA_LIKES_YOU_USER)

        val intent = Intent(mContext, MutualMatchActivity::class.java)
        intent.putExtra(Constants.firebaseUserId, otherUserFirebaseId)
        intent.putExtra(Constants.conversationId, chatId)
        intent.putExtra(EXTRA_LIKES_YOU_USER, likesYouUser)
        intent.putExtra(EXTRA_MESSAGE_OTHER_PROFILE_MODAL, true)
        startActivity(intent)
        finish()
    }

    private fun showHeightVerifiedBadge() {

        binding.heightVerifiedBadge.visibility = View.VISIBLE
        binding.heightVerifiedTextView.visibility = View.VISIBLE

        if (UserObject.isHeightVerified == false) {

            binding.heightVerifiedTextView.paint?.isUnderlineText = true
            binding.heightVerifiedTextView.setOnClickListener {

                val intent = HeightVerificationScan.newIntent(this).launchModeWithSingleTop()
                startActivity(intent)
            }
        }
    }

    private fun hideHeightVerifiedBadge() {

        binding.heightVerifiedBadge.visibility = View.GONE
        binding.heightVerifiedTextView.visibility = View.GONE
    }

    private fun setImageViewHeightBasedOnDeviceWidth() {
        val height = AppUtils.getHeightForImages()

        val firstImageLayoutParams = firstPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        firstImageLayoutParams?.height = height
        firstImageLayoutParams?.let { params ->
            firstPhotoImageView.layoutParams = params
        }

        val firstIceBackgroundImage = firstIcebreakerBackgroundConstraintLayout.layoutParams as? ConstraintLayout.LayoutParams
        firstIceBackgroundImage?.height = height
        firstIceBackgroundImage?.let { params ->
            firstIcebreakerBackgroundConstraintLayout.layoutParams = params
        }

        val secondImageLayoutParams = secondPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        secondImageLayoutParams?.height = height
        secondImageLayoutParams?.let { params ->
            secondPhotoImageView.layoutParams = params
        }

        val secondIceBackgroundImage = secondIcebreakerBackgroundConstraintLayout.layoutParams as? ConstraintLayout.LayoutParams
        secondIceBackgroundImage?.height = height
        secondIceBackgroundImage?.let { params ->
            secondIcebreakerBackgroundConstraintLayout.layoutParams = params
        }

        val thirdImageLayoutParams = thirdPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        thirdImageLayoutParams?.height = height
        thirdImageLayoutParams?.let { params ->
            thirdPhotoImageView.layoutParams = params
        }

        val fourthImageLayoutParams = fourthPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        fourthImageLayoutParams?.height = height
        fourthImageLayoutParams?.let { params ->
            fourthPhotoImageView.layoutParams = params
        }

        val fifthImageLayoutParams = fifthPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        fifthImageLayoutParams?.height = height
        fifthImageLayoutParams?.let { params ->
            fifthPhotoImageView.layoutParams = params
        }

        val sixthImageLayoutParams = sixthPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        sixthImageLayoutParams?.height = height
        sixthImageLayoutParams?.let { params ->
            sixthPhotoImageView.layoutParams = params
        }
    }

    private fun setPhotoIntoImageViewWithIceBreaker(activity: Activity,
                                                    spacesPath: String,
                                                    actualImageView: ImageView,
                                                    icebreakerImageView: ImageView?) {

        ImageLoaderModule.loadImageIntoImageViewWithLoadingAndCallback(applicationContext, activity, spacesImagesViewModel, spacesPath, actualImageView,
                shouldCacheImage = true, onSuccess = {
            showPhotoImageView(actualImageView)
        }, onFailure = {
            hidePhotoImageView(actualImageView)
        })

        if (icebreakerImageView != null) {
            ImageLoaderModule.loadImageIntoImageViewWithLoadingAndCallback(applicationContext, activity, spacesImagesViewModel, spacesPath, icebreakerImageView,
                    shouldCacheImage = true, onSuccess = {
                showPhotoImageView(icebreakerImageView)
            }, onFailure = {
                hidePhotoImageView(icebreakerImageView)
            })
        }
    }
}
