package com.dateup.android.activity

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.adapter.Icebreaker1ActivityTableRecyclerViewAdapter
import com.dateup.android.utils.Utils

class SettingsIceBreakerListActivity : AppCompatActivity() {

    private lateinit var chooseTwoProfileQtextView: TextView
    private lateinit var tableRecyclerView: RecyclerView
    private lateinit var cancelTextView: TextView
    private lateinit var headerLeftImageView: ImageView

    private val questions: ArrayList<String> = ArrayList()
    private var icebreaker: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_settings_ice_breaker_list)

        icebreaker = intent.getStringExtra("icebreaker") ?: ""

        this.init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun addQuestions() {

        val questionsMap = Utils.getIceBreakerQuestions()

        for (key in questionsMap.keys) {
            questions.add(key)
        }
    }

    private fun init() {

        // Configure table component
        tableRecyclerView = this.findViewById(R.id.table_recycler_view)
        tableRecyclerView.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)

        cancelTextView = this.findViewById(R.id.cancel_text_view)

        headerLeftImageView = this.findViewById(R.id.edit_profile_header_left_image_view)

        addQuestions()

        val questionsAdapter = Icebreaker1ActivityTableRecyclerViewAdapter(questions, this)
        tableRecyclerView.adapter = questionsAdapter

        questionsAdapter.setOnItemClickListener(object : Icebreaker1ActivityTableRecyclerViewAdapter.OnItemClickListener {
            override fun onClick(view: View, data: String) {
                startIceBreakerActivity(data)
            }
        })

        cancelTextView.setOnClickListener {
            onCancelButtonPressed()
        }

        headerLeftImageView.setOnClickListener {
            startSettingsIceBreakerTitleActivity()
        }
    }

    private fun onCancelButtonPressed() {
        this.startActivity(EditProfileModalActivity.newIntent(this))
    }

    private fun startSettingsIceBreakerTitleActivity() {

        val intent = Intent(this, SettingsIceBreakerTitleActivity::class.java)
        startActivity(intent)
    }

    private fun startIceBreakerActivity(data: String) {

        val intent = Intent(this, SettingsIceBreakerTitleActivity::class.java)
        intent.putExtra("question", data)
        intent.putExtra("icebreaker", icebreaker)
        startActivity(intent)
    }
}
