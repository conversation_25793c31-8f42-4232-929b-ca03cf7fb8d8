package com.dateup.android.activity.genderFlow

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.analytics.AnalyticsTrackingService
import com.dateup.android.analytics.USER_GENDER_PROPERTY
import com.dateup.android.databinding.ActivityHeightVerificationScanBinding
import com.dateup.android.databinding.ActivityWhoShouldSeeBinding
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.models.GenderType
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Constants

class WhoShouldSeeActivity : AppCompatActivity() {

    private lateinit var binding: ActivityWhoShouldSeeBinding
    companion object {

        const val TAG = ScreenRouter.WHO_SHOULD_SEE_ACTIVITY

        fun newIntent(context: Context): Intent {
            return Intent(context, WhoShouldSeeActivity::class.java)
        }
    }

    private var gender: String = ""
    private var genderButtonSelected = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityWhoShouldSeeBinding.inflate(layoutInflater)
        setContentView(binding.root)

        ScreenRouter.setLastSeenScreen(TAG, this)

        init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        binding.notificationsHeaderLeftImageView.setOnClickListener {
            onBackPressed()
        }

        binding.buttonPreferenceMen.setOnClickListener {
            this.onMenButtonPressed()
        }

        binding.buttonPreferenceWomen.setOnClickListener { view ->
            this.onWomenButtonPressed()
        }

        binding.buttonNext.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }
    }

    private fun onMenButtonPressed() {
        binding.buttonPreferenceMen.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_selected_button_selector)
        binding.buttonPreferenceWomen.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        gender = GenderType.man.toString()
        genderButtonSelected = true
        enableOrDisableButton()
    }

    private fun onWomenButtonPressed() {
        binding.buttonPreferenceWomen.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_selected_button_selector)
        binding.buttonPreferenceMen.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        gender = GenderType.woman.toString()
        genderButtonSelected = true
        enableOrDisableButton()
    }

    private fun enableOrDisableButton() {
        if (genderButtonSelected) {
            binding.buttonNext.isEnabled = true
            binding.buttonNext.setBackgroundResource(R.drawable.phone_number_activity_button_large_active_button_selector)
        }
    }

    fun onButtonLargeActivePressed() {
        if (!AppUtils.isNetworkConnected(application)) {
            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            saveGenderPrefs()
            startInterestedInActivity()
        }
    }

    private fun saveGenderPrefs() {
        AccountPreferences.getInstance(this).setValue(Constants.gender, gender)

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.gender, gender)

        AnalyticsTrackingService.setUserProperty(this, USER_GENDER_PROPERTY, gender)
    }

    private fun startInterestedInActivity() {
        this.startActivity(InterestedInActivity.newIntent(this))
    }
}