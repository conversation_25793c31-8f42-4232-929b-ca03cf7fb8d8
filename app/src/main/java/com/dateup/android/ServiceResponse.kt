package com.dateup.android

class ServiceResponse {

    lateinit var responseStatus: ResponseStatus

    enum class ResponseStatus {
        Success,
        Error
    }

    companion object {
        fun getServiceResponseOnSuccess(): ServiceResponse {
            val serviceResponse = ServiceResponse()
            serviceResponse.responseStatus = ResponseStatus.Success
            return serviceResponse
        }

        fun getServiceResponseOnError(): ServiceResponse {
            val serviceResponse = ServiceResponse()
            serviceResponse.responseStatus = ResponseStatus.Error
            return serviceResponse
        }
    }

    fun isSuccess(): Boolean {
        if (::responseStatus.isInitialized && ResponseStatus.Success == responseStatus) {
            return true
        }
        return false
    }

    fun isError(): Boolean {
        if (::responseStatus.isInitialized && ResponseStatus.Error == responseStatus) {
            return true
        }
        return false
    }
}