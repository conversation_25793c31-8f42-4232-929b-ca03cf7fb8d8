package com.dateup.android.utils

import com.dateup.android.models.UserObject

class DateUpException() : Exception() {

    private var errorCode: Int? = 0
    private var errorCause: String? = ""

    constructor(errorCode: Int? = 0,
                errorCause: String = "") : this() {

        this.errorCode = errorCode
        this.errorCause = errorCause
    }

    override val message: String?
        get() = getExceptionMessage()

    private fun getExceptionMessage(): String {

        val userInfoMap = HashMap<String, Any>()

        val userId = UserObject.userFirebaseId

        if (!userId.isNullOrEmpty()) {

            userInfoMap["userFirebaseId"] = userId
        }

        userInfoMap["code"] = errorCode.toString().toInt()
        userInfoMap["errorCause"] = errorCause.toString()

        return userInfoMap.toString()
    }
}