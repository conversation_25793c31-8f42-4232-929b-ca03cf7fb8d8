package com.dateup.android.utils

import android.content.Context
import android.content.Intent
import android.view.View
import android.widget.ProgressBar
import androidx.appcompat.app.AlertDialog
import com.dateup.android.activity.Intro1Activity
import com.dateup.android.chat.message.MessageListActivity.Companion.REPORT
import com.dateup.android.chat.message.MessageListActivity.Companion.UNMATCH
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.ui.chat.ChatActivity

class AppLogic(val context: Context, val mainUserId: String, private val otherUserId: String) {

    companion object {

        fun deleteAccount(context: Context?, progressBar: ProgressBar) {
            val dialogBuilder = context?.let { AlertDialog.Builder(it) }

            dialogBuilder?.setMessage("This cannot be undone.")
                    ?.setCancelable(false)
                    ?.setPositiveButton("Delete") { dialog, id ->

                        progressBar.visibility = View.VISIBLE

                        val firebaseDatabaseUtil = FirebaseDatabaseUtil(context)
                        context.let {
                            firebaseDatabaseUtil.deleteAccountUsingCloudFunction { isUserDeleted ->

                                progressBar.visibility = View.GONE

                                if (isUserDeleted) {

                                    it.startActivity(Intro1Activity.newIntentWithClearBackStack(it))
                                } else {

                                    val builder1 = AlertDialog.Builder(it)
                                    builder1.setMessage("Unable to delete your profile at this moment. Please try again later or contact us")
                                    builder1.setCancelable(true)
                                    builder1.create().show()
                                }
                            }
                        }
                    }
                    ?.setNegativeButton("Cancel") { dialog, id ->

                        progressBar.visibility = View.GONE

                        dialog.cancel()
                    }

            val alert = dialogBuilder?.create()
            alert?.setTitle("Are you sure you want to delete your account?")
            alert?.show()
        }
    }

    fun unMatchUser() {

        val items = arrayOf<CharSequence>("Feels like spam", "Inappropriate photos", "I\'m just not interested")

        showDialogList(items, "Why would you like to unmatch?", UNMATCH)
    }

    private fun reportUserWithDesc() {

        val items = arrayOf<CharSequence>("Feels like spam", "Inappropriate photos")

        showDialogList(items, "Please tell us why.", REPORT)
    }

    fun reportUser(shouldShowUnmatchButton: Boolean) {

        val alertDialog = AlertDialog.Builder(context).create()

        alertDialog.setTitle("Report User")

        if (shouldShowUnmatchButton) {
            alertDialog.setTitle("Report or unmatch?")
            alertDialog.setMessage("We take reports seriously. We will investigate if this user violated our terms of service. If you simply do not want to see this person again, tap Unmatch.")

            alertDialog.setButton(AlertDialog.BUTTON_NEGATIVE, "Unmatch"
            ) { dialog, which ->
                unMatchUser()
                dialog.dismiss()
            }
        } else {
            alertDialog.setTitle("Report")
            alertDialog.setMessage("We take reports seriously. We will investigate if this user violated our terms of service.")
        }

        alertDialog.setButton(AlertDialog.BUTTON_POSITIVE, "Report"
        ) { dialog, which ->
            reportUserWithDesc()
            dialog.dismiss()
        }

        alertDialog.show()
    }

    private fun showDialogList(dialogList: Array<CharSequence>, title: String, action: String) {

        val builder = AlertDialog.Builder(context)
        builder.setTitle(title)
        builder.setItems(dialogList) { dialog, item -> // Do something with the selection

            if (action == UNMATCH) {

                val firebaseDatabaseUtil = FirebaseDatabaseUtil(context)

                firebaseDatabaseUtil.unMatchUsers(mainUserId, otherUserId, dialogList[item].toString()) {
                    val intent = Intent(context, ChatActivity::class.java)
                    intent.putExtra("MessageListActivityUnMatch", true)
                    context.startActivity(intent)
                }
            } else if (action == REPORT) {

                val firebaseDatabaseUtil = FirebaseDatabaseUtil(context)
                firebaseDatabaseUtil.reportUser(mainUserId, otherUserId, dialogList[item].toString())
                showAlert("We\'ll look into it", "Thanks for helping the community and reporting this issue. We\'ll be looking into it shortly.")
            }
        }
        val alert = builder.create()
        alert.show()
    }

    private fun showAlert(title: String, message: String) {

        val alertDialog = AlertDialog.Builder(context).create()
        alertDialog.setTitle(title)
        alertDialog.setMessage(message)
        alertDialog.setButton(AlertDialog.BUTTON_POSITIVE, "Done"
        ) { dialog, which -> dialog.dismiss() }
        alertDialog.show()
    }
}