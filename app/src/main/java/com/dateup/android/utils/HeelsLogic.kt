package com.dateup.android.utils

import com.dateup.android.R

class HeelsLogic {

    companion object {

        fun getBadgeAndText(mensHeightInInches: Int, womensHeightInInches: Int, mensUserName: String, callback: (Int, Int, String, Boolean) -> Unit) {

            when {
                mensHeightInInches < womensHeightInInches -> {

                    callback(0, 0, "", false)
                }
                mensHeightInInches == womensHeightInInches -> {

                    callback(0, 0, "", false)
                }
                mensHeightInInches == (womensHeightInInches + 1) -> {

                    callback(0, 0, "", false)
                }
                mensHeightInInches == (womensHeightInInches + 2) -> {

                    callback(R.drawable.ic_low_heel, R.drawable.ic_low_expanded_heel, "You’ll be shorter than $mensUserName while wearing up to 1-inch heels.", true)
                }
                mensHeightInInches == (womensHeightInInches + 3) -> {

                    callback(R.drawable.ic_mid_heel, R.drawable.ic_mid_expanded_heel, "You’ll be shorter than $mensUserName while wearing up to 2-inch heels.", true)

                }
                mensHeightInInches == (womensHeightInInches + 4) -> {

                    callback(R.drawable.ic_high_heel, R.drawable.ic_high_expanded_heel, "You’ll be shorter than $mensUserName while wearing up to 3-inch heels.", true)

                }
                mensHeightInInches == (womensHeightInInches + 5) -> {

                    callback(R.drawable.ic_high_heel, R.drawable.ic_high_expanded_heel, "You’ll be shorter than $mensUserName while wearing up to 4-inch heels.", true)

                }
                mensHeightInInches == (womensHeightInInches + 6) -> {

                    callback(R.drawable.ic_high_heel, R.drawable.ic_high_expanded_heel, "You'll be shorter than $mensUserName while wearing heels of any height.", true)

                }
                mensHeightInInches == (womensHeightInInches + 7) -> {

                    callback(R.drawable.ic_high_heel, R.drawable.ic_high_expanded_heel, "You'll be shorter than $mensUserName while wearing heels of any height.", true)

                }
                mensHeightInInches == (womensHeightInInches + 8) -> {

                    callback(R.drawable.ic_high_heel, R.drawable.ic_high_expanded_heel, "You'll be shorter than $mensUserName while wearing heels of any height.", true)

                }
                mensHeightInInches > (womensHeightInInches + 8) -> {

                    callback(R.drawable.ic_high_heel, R.drawable.ic_high_expanded_heel, "You'll be shorter than $mensUserName while wearing heels of any height.", true)
                }
            }
        }
    }

}