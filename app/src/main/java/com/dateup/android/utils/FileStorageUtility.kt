package com.dateup.android.utils

import android.content.Context
import android.graphics.Bitmap
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream

class FileStorageUtility {

    companion object {

        const val FILE_NAME_PROVIDER = "com.dateup.provider"

        fun getFileFromPath(context: Context, path: String?): File {

            val fullPath = context.filesDir.path + File.separator + path
            return File(fullPath)
        }

        fun saveFile(bitmap: Bitmap, file: File?) {

            val fileOutputStream: FileOutputStream?

            try {
                if (file != null &&
                        file.exists()) {

                    fileOutputStream = FileOutputStream(file)
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fileOutputStream)
                }
            } catch (ex: Exception) {

                Timber.d("exception in saving file: $ex")
            }
        }

        fun removeFile(context: Context, fileName: String?) {

            val file: File? = getFileFromPath(context, fileName)
            file?.delete()
        }
    }
}