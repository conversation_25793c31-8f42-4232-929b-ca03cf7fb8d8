package com.dateup.android.utils

import android.app.Activity
import android.app.Application
import android.content.Context.CONNECTIVITY_SERVICE
import android.content.res.Resources
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.util.TypedValue
import com.dateup.android.BuildConfig
import java.util.*
import java.util.concurrent.TimeUnit

class AppUtils {

    companion object {

        val appLocale: Locale = Locale.US

        fun isNetworkConnected(context: Application?): Boolean {

            (context?.getSystemService(CONNECTIVITY_SERVICE) as ConnectivityManager).apply {
                return getNetworkCapabilities(activeNetwork)?.run {
                    when {
                        hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> true
                        hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> true
                        hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> true
                        hasTransport(NetworkCapabilities.TRANSPORT_VPN) -> true
                        hasTransport(NetworkCapabilities.TRANSPORT_BLUETOOTH) -> true
                        else -> false
                    }
                } ?: false
            }
        }

        fun hourDifference(millisFirst: Long, millisSecond: Long): Long {
            return TimeUnit.MILLISECONDS.toHours(millisSecond - millisFirst)
        }

        fun getAppVersion(): String {

            return BuildConfig.VERSION_NAME
        }

        fun getScreenWidth(activity: Activity?): Int {

            return Resources.getSystem().displayMetrics.widthPixels
        }

        fun getDeviceHeight(): Int {

            return Resources.getSystem().displayMetrics.heightPixels
        }

        fun getHeightForImages(): Int {

            return Resources.getSystem().displayMetrics.widthPixels
        }

        fun getDpForImages(activity: Activity?, value: Float): Int {
            val resources = activity?.resources
            val pixels = TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP,
                value,
                resources?.displayMetrics
            )
            return pixels.toInt()
        }
    }
}