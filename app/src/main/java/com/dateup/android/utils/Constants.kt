package com.dateup.android.utils

object Constants {

    // user info keys
    const val phoneNumber = "phone"
    const val email = "email"
    const val latitude = "latitude"
    const val longitude = "longitude"
    const val city = "city"
    const val country = "country"
    const val state = "state"
    const val zip = "zip"
    const val name = "name"
    const val dob = "dob"
    const val gender = "gender"
    const val genderOrientation = "genderOrientation"
    const val height = "height"
    const val matchHeight = "matchHeight"
    const val matchTallOrShortPreference = "matchTallOrShortPreference"
    const val profession = "profession"
    const val school = "school"
    const val school2 = "school2"
    const val homeTown = "homeTown"
    const val iceBreaker1 = "iceBreaker1"
    const val iceBreaker2 = "iceBreaker2"
    const val isOnboardingComplete = "isOnboardingComplete"
    const val iceBreakersComplete = "iceBreakersComplete"
    const val createdOn = "createdOn"
    const val modifiedOn = "modifiedOn"
    const val externalId = "externalId"

    const val users = "users"
    const val fireStoreUsers = "users"
    const val geofire = "geofire"
    const val cities = "cities"
    const val likeCounter = "likeCounter"
    const val lCount = "lCount"
    const val lDate = "lDate"

    const val apiKeys = "apiKeys"
    const val places = "places"

    // city info
    const val lat = "lat"
    const val lng = "lng"

    // subscription status
    const val dateupPlus = "dateupPlus"
    const val isActive = "isActive"

    const val isAMember = "isAMember"
    const val connections = "connections"
    const val dislikedBy = "dislikedBy"
    const val likedBy = "likedBy"
    const val likes = "likes"
    const val dislikes = "dislikes"
    const val matches = "matches"
    const val unmatchedUsers = "unmatchedUsers"

    const val lSeen = "lSeen"
    const val lScreen = "lscreen"

    const val firebaseUserId = "firebaseUserId"
    const val userProfilePhotoUrl = "userProfilePhoto"

    const val dateUpAccountPrefs = "date_up_account_prefs"

    // Location locked keys
    const val unLockedLocations = "unLockedLocations"
    const val isUserLocationUnlocked = "isUserLocationUnlocked"

    //spaces
    const val spacesConfigs = "spacesConfigs"

    //settings
    const val showNewMatches = "showNewMatches"
    const val showMessages = "showMessages"
    const val showEverythingElse = "showEverythingElse"
    const val minGuestHeight = "minGuestHeight"
    const val maxGuestHeight = "maxGuestHeight"
    const val minMatchHeight = "minMatchHeight"
    const val maxMatchHeight = "maxMatchHeight"
    const val showGuests = "showGuests"
    const val minMatchAge = "minMatchAge"
    const val maxMatchAge = "maxMatchAge"
    const val interestedIn = "interestedIn"
    const val distance = "distance"

    const val likesSeenMap = "likesSeenMap"

    const val tutorialShown = "tutorialShown"
    const val reviewShown = "reviewShown"
    const val freeTrailShown = "freeTrailShown"

    const val reportedUsers = "reported_users"
    const val reportedBy = "reportedBy"

    //chat related
    const val conversations = "conversations"
    const val createdBy = "createdBy"
    const val lastMessage = "message"
    const val timestamp = "timestamp"
    const val sendTo = "sentTo"
    const val unread = "unread"
    const val dmRequest = "dmRequest"
    const val dmRequestBy = "dmRequestBy"
    const val dmRequestRejected = "dmRequestRejected"
    const val avatar = "avatar"
    const val user0 = "user0"
    const val user1 = "user1"
    const val conversationId = "conversationId"
    const val chatId = "chatId"
    const val message = "message"

    const val minDefaultAge = 18
    const val maxDefaultAge = 60
    const val defaultDistance = 100
    const val menMemberMinHeight = 72
    const val womenMemberMinHeight = 68
    const val startingHeightInInchMen = 71.0
    const val startingHeightInInchWomen = 67.0

    const val isNewMessageReceived = "isNewMessageReceived"

    const val android = "android"

    const val deviceToken = "deviceToken"
    const val deviceOs = "deviceOs"
    const val deviceHardwareIdentifier = "deviceHardwareIdentifier"
    const val deviceDetails = "deviceDetails"
    const val version = "version"

    const val noLimit = "No Limit"

    //1 foot = 30.48 cms
    const val cmMultiplier = 30.48

    //1 inch = 2.54 cms
    const val inchMultiplier = 2.54
    const val halfInch = 1.27

    //age multiplier
    const val startingAge = 18
    const val sliderMaxAge = 80

    // (maxage - startingage)/100
    const val ageMutiplier = 0.62

    //1 foot = 12 inches
    //here min height is 3'0" which is 36 and max height is 8'0'' which 96 so (96 - 36) = 60/100 = 0.6
    const val mensHeightInInchMultiplier = 0.6

    //here min height is 3'0" which is 36 and max height is 7'0'' which 84 so (84 - 36) = 48/100 = 0.48
    const val womensHeightInInchMultiplier = 0.48

    //starting height is the min height set which is 3'0"
    const val startingHeight: Double = 91.44
    const val startingHeightInInch: Int = 36
    const val startingHeightInFeetForMen: String = "5ft11in"
    const val startingHeightInFeetForWoMen: String = "5ft7in"


    //default heights for preferences screen

    const val sliderMaxHeightForMen = 90.0 //7'6"
    const val sliderMaxHeightForWomen = 79.0

    //Interested in women
    const val defaultMinHeightInInchForPrefsInterestedInWomen: Double = 68.0
    const val defaultMaxHeightInInchForPrefsInterestedInWomen: Double = 79.0
    const val defaultMinGuestsHeightInInchForPrefsInterestedInWomen: Double = 48.0
    const val defaultMaxGuestsHeightInInchForPrefsInterestedInWomen: Double = 67.0

    //preferences multipliers and starting heights
    //here min height is 5'8" which is 68 and max height is 6'7'' which 79 so (79 - 68) = 11/100 = 0.11
    const val preferencesMatchHeightSliderMultiplierInterestedInWomen = 0.11
    const val preferencesMatchStartingHeightInInchInterestedInWomen: Int = 68

    //here min height is 4'0" which is 48 and max height is 5'7'' which 67 so (67 - 48) = 19/100 = 0.19
    const val preferencesGuestHeightSliderMultiplierInterestedInWomen = 0.19
    const val preferencesGuestStartingHeightInInchInterestedInWomen: Int = 48

    //Interested in Men
    const val defaultMinHeightInInchForPrefsInterestedInMen: Double = 72.0
    const val defaultMaxHeightInInchForPrefsInterestedInMen: Double = 90.0
    const val defaultMinGuestsHeightInInchForPrefsInterestedInMen: Double = 48.0
    const val defaultMaxGuestsHeightInInchForPrefsInterestedInMen: Double = 71.0

    //here min height is 6'0" which is 72 and max height is 7'6'' which 90 so (90 - 72) = 18/100 = 0.18
    const val preferencesMatchHeightSliderMultiplierInterestedInMen = 0.18
    const val preferencesMatchStartingHeightInInchInterestedInMen: Int = 72

    //here min height is 4'0" which is 48 and max height is 5'11'' which 71 so (71 - 48) = 23/100 = 0.23
    const val preferencesGuestHeightSliderMultiplierInterestedInMen = 0.23
    const val preferencesGuestStartingHeightInInchInterestedInMen: Int = 48

    //1 foot = 12 inches
    //Guest height preference activity
    //here min height is 5'8" which is 68 and max height is 6'7'' which 79 so (79 - 68) = 11/100 = 0.11
    const val mensHeightInInchMultiplierForGuest = 0.11
    const val startingHeightInInchForGuest: Int = 68
    const val minHeightInInchForGuestPrefs: Double = 68.0
    const val maxHeightInInchForGuestPrefs: Double = 79.0

    //here min height is 3'0" which is 91.44 and max height is 8'0'' which 243.84 so (243.84 - 91.44) = 152.4/100 = 1.524
    const val mensHeightMultiplier = 1.524
    const val mensEndingHeightInCms = 243.84

    //Fragment Arguments
    const val PREFERENCES_FRAGMENT_ARG1 = "preferences_fragment_arg1"
    const val PREFERENCES_FRAGMENT_HOME_PROFILE_ACTIVITY = "homeprofiletabactivity"
    const val PREFERENCES_FRAGMENT_SETTINGS_SETTINGS_FRAGMENT = "settingssettingsfragment"
    const val PREFERENCES_FRAGMENT_TELEPORT_FRAGMENT = "hometeleportfragment"

    //time
    const val systemTimeSaved = "systemTimeSaved"

    // dateup email
    const val dateupAdminEmail = "<EMAIL>"

    // influencer promotions
    const val influencerSignups = "influencerSignups"
    const val influencerId = "influencerId"
    const val influencerName = "influencerName"
    const val freeTrailUsed = "freeTrailUsed"
    const val influencerPromoShown = "influencerPromoShown"
    const val promoCounter = "promoCounter"

    // Branch
    const val source = "source"
    const val installSource = "installSource"

    // Pagination constants
    const val DEFAULT_PAGE_SIZE = 20
    const val PASSED_USERS_PAGE_SIZE = 50
    const val INITIAL_PAGE = 1
    const val VISIBLE_THRESHOLD = 5  // Start loading more items when user is 5 items away from the end

    // Photo file names
    const val photoFileName1 = "1.webp"
    const val photoFileName2 = "2.webp"
    const val photoFileName3 = "3.webp"
    const val photoFileName4 = "4.webp"
    const val photoFileName5 = "5.webp"
    const val photoFileName6 = "6.webp"
}
