package com.dateup.android.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.telephony.TelephonyManager
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.util.Patterns
import android.widget.EditText
import androidx.appcompat.app.AlertDialog
import com.dateup.android.AccountPreferences
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.models.GenderType
import com.dateup.android.models.LikesTabUser
import com.dateup.android.models.User
import com.dateup.android.utils.Constants.cmMultiplier
import com.dateup.android.utils.Constants.inchMultiplier
import com.google.firebase.installations.FirebaseInstallations
import timber.log.Timber
import java.text.DateFormat
import java.text.ParseException
import java.text.ParsePosition
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import kotlin.math.roundToLong


class Utils {

    companion object {

        fun isValidEmail(email: String): Boolean = email.isNotEmpty() &&
                Patterns.EMAIL_ADDRESS.matcher(email).matches()

        fun isValidPhone(phone: String): Boolean = phone.isNotEmpty() &&
                Patterns.PHONE.matcher(phone).matches()

        fun isValidString(string: String): Boolean = !TextUtils.isEmpty(string)

        fun heightInFeetFromCms(heightInCms: Double): String {

            val feet = heightInCms / cmMultiplier
            val inches = heightInCms / inchMultiplier - feet.toInt() * 12
            return (feet.toInt().toString()).plus("ft").plus(inches.toInt().toString()).plus("in")
        }

        fun heightInFeetFromInches(heightInInches: Double): String {

            if (!TextUtils.isEmpty(heightInInches.toString())) {
                val feet = heightInInches / 12
                val inches = heightInInches % 12
                return (feet.toInt().toString()).plus("ft").plus(inches.toInt().toString())
                    .plus("in")
            }
            return ""
        }

        fun heightInFeetFromInchesWithQuotes(heightInInches: Double?): String {

            if (heightInInches != null) {

                val feet = heightInInches / 12
                val inches = heightInInches % 12
                return feet.toInt().toString() + "'" + inches.toInt().toString() + "\""
            }
            return ""
        }

        fun heightInFeet2(heightInCms: Double): String {

            if (!TextUtils.isEmpty(heightInCms.toString())) {
                val feet = heightInCms.div(cmMultiplier)
                val inches = heightInCms / inchMultiplier - feet.toInt() * 12

                val roundedInch = roundToHalf(inches)

                val inchAfterDecimal = roundedInch - roundedInch.toInt()

                var inchAfterDecimalString = ""
                if (inchAfterDecimal == 0.5) {
                    inchAfterDecimalString = " 1/2 "
                }

                val finalInches = roundedInch.toInt().toString() + inchAfterDecimalString
                return (feet.toInt().toString()).plus(" ft ").plus(finalInches).plus(" in")
            }
            return ""
        }

        private fun roundToHalf(d: Double): Double {

            return (d * 2).roundToLong() / 2.0
        }

        fun EditText.afterTextChanged(afterTextChanged: (String) -> Unit) {
            this.addTextChangedListener(object : TextWatcher {

                override fun afterTextChanged(s: Editable?) {
                    afterTextChanged.invoke(s.toString())
                }

                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            })
        }

        fun EditText.validate(message: String, validator: (String) -> Boolean) {
            this.afterTextChanged {
                validator(it)
                //this.error = if (validator(it)) null else message
            }
            //this.error = if (validator(this.text.toString())) null else message
        }

        fun isUserAgerestricted(dob: String): Boolean {

            if (dob.isEmpty()) {

                return false
            }

            try {

                val userFormattedDob = dob.replace('/', '-')
                val sdf = SimpleDateFormat("MM-dd-yyyy", Locale.US)
                val dobCalender = Calendar.getInstance()
                dobCalender.time = sdf.parse(userFormattedDob)

                if (!isUser18YearsOld(dobCalender)) {

                    return true
                }
            } catch (exception: java.lang.Exception) {

                Timber.d("exception in isUserAgerestricted: $exception")
            }

            return false
        }

        fun isAMember(height: Double, gender: String): Boolean {

            if (gender == GenderType.man.toString() && (height >= Constants.menMemberMinHeight)) {
                return true
            } else if (gender == GenderType.woman.toString() && (height >= Constants.womenMemberMinHeight)) {
                return true
            }
            return false
        }

        fun isValidDate(dateString: String): Boolean {

            try {

                val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.US)
                sdf.isLenient = false
                return sdf.parse(dateString, ParsePosition(0)) != null

            } catch (exception: java.lang.Exception) {

                Timber.e("exception in isValidDate: $exception")
            }

            return false
        }

        fun isUser18YearsOld(dob: Calendar): Boolean {

            try {

                val today = Calendar.getInstance()

                val curYear = today.get(Calendar.YEAR)
                val dobYear = dob.get(Calendar.YEAR)

                var age = curYear - dobYear

                // if dob is month or day is behind today's month or day
                // reduce age by 1
                val curMonth = today.get(Calendar.MONTH)
                val dobMonth = dob.get(Calendar.MONTH)
                if (dobMonth > curMonth) { // this year can't be counted!
                    age--
                } else if (dobMonth == curMonth) { // same month? check for day
                    val curDay = today.get(Calendar.DAY_OF_MONTH)
                    val dobDay = dob.get(Calendar.DAY_OF_MONTH)
                    if (dobDay > curDay) { // this year can't be counted!
                        age--
                    }
                }

                if (age < 18) {

                    return false
                }

            } catch (exception: java.lang.Exception) {

                Timber.e("exception in isUser18YearsOld: $exception")
            }

            return true
        }

        fun getAge(dateOfBirth: Date?): Int {

            var age = 0

            try {

                val born = Calendar.getInstance()
                val now = Calendar.getInstance()
                if (dateOfBirth != null) {
                    now.time = Date()
                    born.time = dateOfBirth
                    if (born.after(now)) {
                        throw IllegalArgumentException("Can't be born in the future")
                    }
                    age = now.get(Calendar.YEAR) - born.get(Calendar.YEAR)
                    if (now.get(Calendar.DAY_OF_YEAR) < born.get(Calendar.DAY_OF_YEAR)) {
                        age -= 1
                    }
                }
            } catch (exception: java.lang.Exception) {

                Timber.e("exception in getting age: $exception")
            }

            return age
        }

        fun isValidUser(userInfo: User?): Boolean {

            if (TextUtils.isEmpty(userInfo?.name.toString())
                || TextUtils.isEmpty(userInfo?.dob.toString())
                || TextUtils.isEmpty(userInfo?.gender.toString())
                || TextUtils.isEmpty(userInfo?.height.toString())
                || TextUtils.isEmpty(userInfo?.interestedIn.toString())
                || TextUtils.isEmpty(userInfo?.maxMatchHeight.toString())
                || TextUtils.isEmpty(userInfo?.minMatchHeight.toString())
                || TextUtils.isEmpty(userInfo?.maxMatchAge.toString())
                || TextUtils.isEmpty(userInfo?.minMatchAge.toString())
                || userInfo?.isOnboardingComplete == null
                || userInfo.isOnboardingComplete == false
            ) {

                return false
            }
            return true
        }

        fun isValidLikesYouUser(userInfo: LikesTabUser?): Boolean {

            if (TextUtils.isEmpty(userInfo?.name.toString())
                || TextUtils.isEmpty(userInfo?.gender.toString())
                || TextUtils.isEmpty(userInfo?.height.toString())
                || TextUtils.isEmpty(userInfo?.interestedIn.toString())
                || TextUtils.isEmpty(userInfo?.zip.toString())
                || TextUtils.isEmpty(userInfo?.maxMatchHeight.toString())
                || TextUtils.isEmpty(userInfo?.minMatchHeight.toString())
                || TextUtils.isEmpty(userInfo?.maxMatchAge.toString())
                || TextUtils.isEmpty(userInfo?.minMatchAge.toString())
                || userInfo?.isOnboardingComplete == null
                || userInfo.isOnboardingComplete == false
                || userInfo.isUserReported == true
            ) {
                return false
            }
            return true
        }

        fun isThisWeek(date: Date): Boolean {

            val c = Calendar.getInstance()
            c.firstDayOfWeek = Calendar.MONDAY

            c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
            c.set(Calendar.HOUR_OF_DAY, 0)
            c.set(Calendar.MINUTE, 0)
            c.set(Calendar.SECOND, 0)
            c.set(Calendar.MILLISECOND, 0)

            val monday = c.time

            val nextMonday = Date(monday.time + 7 * 24 * 60 * 60 * 1000)

            return date.after(monday) && date.before(nextMonday)
        }

        fun getDateFromTimestamp(time: Long): Date {
            val cal = Calendar.getInstance()
            val tz = cal.timeZone//get your local time zone.
            val sdf = SimpleDateFormat("dd/MM/yyyy hh:mm a", Locale.US)
            sdf.timeZone = tz//set time zone.
            val localTime = sdf.format(Date(time))
            var date = Date()
            try {
                date = sdf.parse(localTime)//get local date
            } catch (e: ParseException) {
                Timber.e("exception in get date from timestamp: $e")
            }
            return date
        }

        fun getDeviceDate(): String {
            val dateFormat: DateFormat = SimpleDateFormat("MM-dd-yyyy", Locale.US)
            return dateFormat.format(Calendar.getInstance().time)
        }

        fun extractDateStringFromDateObject(date: Date): String {
            val dateFormat: DateFormat = SimpleDateFormat("MM-dd-yyyy", Locale.US)
            return dateFormat.format(date)
        }

        fun openGmailApp(context: Context, subject: String) {
            try {
                val externalId = AccountPreferences.getInstance(context)
                    .getStringValue(Constants.externalId, "")
                val intent = Intent(Intent.ACTION_VIEW)
                intent.data = Uri.parse("mailto:${Constants.dateupAdminEmail}?subject=" + subject + "&body=\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n***********************\nDevice: " + externalId)
                context.startActivity(intent)
            } catch (e: Exception) {
                Timber.d("error opening gmail app: $e")
            }
        }

        fun getMilesFromMeters(meters: Int): Int {

            return (meters * 0.************).toInt()
        }

        fun getKmsFromMiles(miles: Double): Double {

            return (miles * 1.6)
        }

        fun setDeviceHardwareIdentifier(context: Context) {
            FirebaseInstallations.getInstance().id.addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    val firebaseDatabaseReference = FirebaseDatabaseUtil(context)
                    firebaseDatabaseReference.saveUserInfoToFirebase(Constants.deviceHardwareIdentifier, task.result)
                }
            }
        }

        fun getDeviceDataForLogging(context: Context, callback: (String) -> Unit) {
            FirebaseInstallations.getInstance().id.addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    val deviceId = task.result
                    val version = AppUtils.getAppVersion()
                    val locale: String = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        context.resources.configuration.locales[0].country
                    } else {
                        context.resources.configuration.locale.country
                    }
                    val tm = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager?
                    val countryISO = tm?.simCountryIso

                    val result = "deviceId: $deviceId, appVersion: $version, locale: $locale, countryISO: $countryISO"
                    callback(result)
                }
            }
        }

        fun getIceBreakerQuestions(): HashMap<String, String> {

            val questionsMap: HashMap<String, String> = HashMap()
            questionsMap["The title of my autobiography"] = "The Joy of Cooking.. Ramen"
            questionsMap["My entrance theme song if I was a wrestler"] = "Teach me how to Dougie."
            questionsMap["People tell me I look like"] = "I could use a drink."
            questionsMap["A hobby I've been meaning to pick up"] =
                "Singing. You would make band member #2."
            questionsMap["I'd like to learn more about"] =
                "Hiking. Where's the best trail around here?"
            questionsMap["My go-to karaoke song"] = "Passionfruit by drake. Can you handle it?"
            questionsMap["My hidden talent"] = "I can make the best moscow mule in town."
            questionsMap["If I could teleport, my day would look like"] =
                "Breakfast in New Orleans, snowboard in Tahoe all day, dinner in Rome, drinks in NYC."
            questionsMap["I couldn't live without"] =
                "Starbucks. Caffeine withdrawals are no joke. "
            questionsMap["I could teach a class on how to"] =
                "Stay out until 2:00am and function at work the next day."
            questionsMap["My greatest life achievement"] =
                "Eating 20 dollars worth of Taco Bell in one sitting."
            questionsMap["You probably can't beat me at"] =
                "Scrabble. I'm on a mean winning streak. "
            questionsMap["If I could be one animal"] =
                "A spider. So I could hang out all day and ignore the haters."
            questionsMap["One thing I would never try again"] =
                "Eating 20 dollars worth of Taco Bell in one sitting."
            questionsMap["Fact or fiction?"] = "I lived in 5 countries growing up."
            questionsMap["Teach me where to find the best"] = "Authentic street tacos."
            questionsMap["Remind me to tell you about the time I"] =
                "Nearly got kidnapped while traveling abroad."
            questionsMap["The strangest tradition in my family"] =
                "Singing holiday songs at the top of our lungs. It's embarrassing."
            questionsMap["Something about me that might surprise you"] =
                "I once took a spontaneous solo trip to Greece and ran out of money. "
            questionsMap["The best gift I've ever received"] = "A five pound gummy bear "
            questionsMap["I was once grounded for"] =
                "Watching too much TV. I really liked the Lion King okay?"
            questionsMap["If I had to spend 1,000,000 dollars today I would"] =
                "Pay off half of my student loans."
            questionsMap["My day usually consists of"] =
                "Drawing doodles on scrap paper and overdosing on caffeine."
            return questionsMap
        }

        fun showAlert(text: String, activity: Context) {

            if (activity is Activity &&
                !activity.isFinishing
            ) {

                val dialogBuilder = AlertDialog.Builder(activity)

                dialogBuilder.setMessage(text)
                    .setCancelable(false)
                    .setPositiveButton("ok") { dialog, id ->

                        dialog.dismiss()
                    }

                val alert = dialogBuilder.create()
                alert.show()
            }
        }

        fun showPermissionAlert(text: String, activity: Context) {
            if (activity is Activity &&
                !activity.isFinishing
            ) {

                val dialogBuilder = AlertDialog.Builder(activity)

                dialogBuilder.setMessage(text)
                    .setCancelable(false)
                    .setPositiveButton("Go to settings") { dialog, id ->
                        activity.startActivity(Intent().apply {
                            action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                            data = Uri.fromParts("package", activity.packageName, null)
                        })
                    }
                    .setNegativeButton("cancel") { dialog, id ->
                        dialog.dismiss()
                    }

                val alert = dialogBuilder.create()
                alert.show()
            }
        }
    }
}
