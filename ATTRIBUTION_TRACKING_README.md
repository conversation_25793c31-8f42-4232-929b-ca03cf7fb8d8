# Attribution Tracking Implementation

This document describes the end-to-end attribution tracking implementation for the DateUp Android app.

## Overview

The attribution tracking system detects and tracks user acquisition sources from Google Ads and Meta Ads (Facebook/Instagram) campaigns, storing the attribution data and including it in key analytics events.

## Architecture

### Core Components

1. **AttributionManager** - Centralized manager for detecting and storing attribution
2. **AttributionAnalytics** - Utility for logging events with attribution data

### SDKs Used

- **Firebase Analytics** - For event logging and analytics
- **Google Play Install Referrer API** - For detecting Google Ads installs
- **Facebook SDK** - For Meta Ads deferred deep linking detection
- **Google Play Billing** - For subscription purchase tracking

## Implementation Details

### 1. Attribution Detection

#### Google Ads Detection
- Uses Google Play Install Referrer API
- Detects referrer URLs containing Google Ads parameters:
  - `utm_source=google`
  - `utm_medium=cpc`
  - `gclid=`
  - `utm_campaign=`

#### Meta Ads Detection
- Uses Facebook SDK's deferred deep linking
- Detects deep links containing Meta Ads parameters:
  - `utm_source=facebook`
  - `utm_source=instagram`
  - `utm_medium=cpc`
  - `utm_medium=social`
  - `fbclid=`
  - `fb_action_ids=`
  - `fb_action_types=`

#### Fallback to Organic
- If no attribution is detected from either source, marks as "organic"
- Attribution is only detected once per app install

### 2. Data Storage

Attribution data is stored in SharedPreferences:
- `ad_source`: The detected source (`google_ads`, `meta_ads`, or `organic`)
- `attribution_detected`: Boolean flag to prevent re-detection

### 3. Analytics Events

#### App Install Event
- Event name: `app_install`
- Parameters:
  - `ad_source`: Attribution source
- Fires: Immediately when attribution is first detected

#### Onboarding Complete Event
- Event name: `onboarding_complete`
- Parameters:
  - `ad_source`: Attribution source

#### Subscription Success Event
- Event name: `subscription_success`
- Parameters:
  - `ad_source`: Attribution source
  - `sku`: Subscription SKU
  - `price`: Subscription price (when available)

#### Subscription Failed Event
- Event name: `subscription_failed`
- Parameters:
  - `ad_source`: Attribution source
  - `response_code`: Billing response code or purchase state
  - `failure_reason`: Detailed failure reason
  - `sku`: Subscription SKU (when available)

## Usage

### Initialization

Attribution tracking is automatically initialized in `DateUpApplication.onCreate()`:

```kotlin
AttributionManager.getInstance().initializeAttribution(this)
```

### Logging Events

Use `AttributionAnalytics` for consistent event logging:

```kotlin
// App install is logged automatically when attribution is detected

// Log onboarding completion
AttributionAnalytics.logOnboardingComplete(context)

// Log subscription success
AttributionAnalytics.logSubscriptionSuccess(context, purchase, skuDetails)

// Log subscription failure
AttributionAnalytics.logSubscriptionFailure(context, responseCode, debugMessage, sku)
```

### Getting Attribution Source

```kotlin
val source = AttributionManager.getInstance().getAttributionSource(context)
```

## Testing

### Debug Testing

For testing attribution, you can manually set SharedPreferences values:

```kotlin
if (BuildConfig.DEBUG) {
    val prefs = AccountPreferences.getInstance(context)
    prefs.setValue("ad_source", "google_ads") // or "meta_ads", "organic"
    prefs.setValue("attribution_detected", true)
}
```

### Production Testing

1. **Google Ads Testing**:
   - Install app via Google Ads campaign
   - Check logs for "Detected Google Ads install"
   - Verify onboarding and subscription events include `ad_source=google_ads`

2. **Meta Ads Testing**:
   - Install app via Facebook/Instagram ads
   - Check logs for "Detected Meta Ads install"
   - Verify events include `ad_source=meta_ads`

3. **Organic Testing**:
   - Install app directly from Play Store
   - Verify events include `ad_source=organic`

## Error Handling

- Retry logic for Install Referrer API failures
- Graceful fallback to organic attribution
- Comprehensive error logging with Timber
- Exception handling in all attribution methods

## Performance Considerations

- Attribution detection runs asynchronously
- One-time detection per app install
- Minimal impact on app startup time
- Efficient SharedPreferences usage

## Firebase Analytics Integration

Events are automatically sent to Firebase Analytics for:
- Campaign performance analysis
- Value-based optimization
- Attribution reporting
- Conversion tracking

## Dependencies Added

```gradle
// Google Play Install Referrer API
implementation 'com.android.installreferrer:installreferrer:2.2'

// Meta Ads SDK for attribution
implementation 'com.facebook.android:facebook-android-sdk:17.0.0'
```

## Files Modified/Created

### New Files
- `app/src/main/java/com/dateup/android/attribution/AttributionManager.kt`
- `app/src/main/java/com/dateup/android/attribution/AttributionAnalytics.kt`

### Modified Files
- `app/build.gradle` - Added dependencies
- `app/src/main/java/com/dateup/android/DateUpApplication.kt` - Added attribution initialization
- `app/src/main/java/com/dateup/android/analytics/AnalyticsTrackingService.kt` - Added constants
- `app/src/main/java/com/dateup/android/ui/BottomNavigationBarActivity.kt` - Enhanced onboarding tracking
- `app/src/main/java/com/dateup/android/activity/Images3Activity.kt` - Enhanced onboarding tracking
- `app/src/main/java/com/dateup/android/subscriptions/views/SubscriptionActivity.kt` - Added subscription tracking
- `app/src/main/java/com/dateup/android/utils/Constants.kt` - Added attribution constants

## Best Practices

1. **Always use AttributionAnalytics** for event logging to ensure consistent attribution data
2. **Test thoroughly** with real ad campaigns before production deployment
3. **Monitor logs** for attribution detection success/failure
4. **Use debug helpers** for development and testing
5. **Verify Firebase Analytics** receives events with correct attribution parameters

## Troubleshooting

### Common Issues

1. **Attribution not detected**:
   - Check network connectivity during app install
   - Verify ad campaign parameters are correctly set
   - Check logs for API errors

2. **Events missing attribution**:
   - Ensure AttributionAnalytics is used instead of direct AnalyticsTrackingService calls
   - Verify attribution was detected before event logging

3. **Install Referrer API failures**:
   - Check device compatibility
   - Verify Google Play Services availability
   - Review retry logic in logs

### Debug Logging

Enable verbose logging to troubleshoot:
```kotlin
if (BuildConfig.DEBUG) {
    val source = AttributionManager.getInstance().getAttributionSource(context)
    Timber.d("Current attribution source: $source")
}
```

## Summary of Analytics Events

The implementation now tracks the following events with attribution:

1. **app_install** ⭐ NEW
   - Parameters: `ad_source`
   - Fires: Immediately when attribution is first detected

2. **onboarding_complete**
   - Parameters: `ad_source`

3. **subscription_success**
   - Parameters: `ad_source`, `sku`, `price`

4. **subscription_failed**
   - Parameters: `ad_source`, `response_code`, `failure_reason`, `sku`
   - Tracks: User cancellations, billing errors, pending states, developer errors, and other failures

## Subscription Failure Scenarios Tracked

- **User Canceled**: When user cancels during purchase flow
- **Item Already Owned**: When user tries to buy something they already have
- **Developer Error**: Configuration issues with Google Play Console
- **Purchase Pending**: When purchase is in pending state
- **Purchase Unspecified**: When purchase state is unknown
- **Empty Purchase List**: When no purchases are returned
- **Billing Service Errors**: Network, service unavailable, etc.

## Future Enhancements

1. **Additional Attribution Sources**: Support for other ad networks
2. **Enhanced Price Tracking**: Better integration with billing client for accurate pricing
3. **Attribution Persistence**: Cloud backup of attribution data
4. **Advanced Analytics**: Custom dimensions and metrics for deeper insights
